# 文件上传方式说明

迷星 Mizzy Star 提供了两种不同的文件导入方式，适用于不同的使用场景。

## 📤 Web上传 (Upload)

**API端点**: `POST /api/v1/cases/{case_id}/files/upload`

**使用场景**: 
- 从浏览器或应用界面上传文件
- 文件来源于用户的本地设备，需要传输到服务器

**行为特点**:
- ✅ 将文件复制到案例目录的 `uploads/` 文件夹中
- ✅ 生成缩略图到 `thumbnails/` 文件夹
- ✅ 提取EXIF元数据和应用规则引擎
- ⚠️ **会创建原始文件的副本**

**文件存储结构**:
```
data/case_{id}/
├── uploads/
│   ├── image1.jpg          # 原始文件副本
│   ├── image2.jpg
│   └── ...
└── thumbnails/
    ├── image1_thumb.jpg    # 缩略图
    ├── image2_thumb.jpg
    └── ...
```

**前端调用**:
```javascript
// 使用FilePond或其他文件上传组件
await api.uploadFile(caseId, file);
```

---

## 📁 本地导入 (Import Local)

**API端点**: `POST /api/v1/cases/{case_id}/files/import-local`

**使用场景**:
- 导入已存在于服务器文件系统中的文件
- 批量导入大量本地文件
- 避免文件重复，节省存储空间

**行为特点**:
- ✅ 只记录原始文件的绝对路径
- ✅ 生成缩略图到 `thumbnails/` 文件夹
- ✅ 提取EXIF元数据和应用规则引擎
- 🎯 **不复制原始文件，节省存储空间**

**文件存储结构**:
```
data/case_{id}/
└── thumbnails/
    ├── image1_thumb.jpg    # 缩略图
    ├── image2_thumb.jpg
    └── ...

# 原始文件保持在原位置
/path/to/original/
├── image1.jpg              # 原始文件位置
├── image2.jpg
└── ...
```

**前端调用**:
```javascript
// 导入单个本地文件
await api.importLocalFile(caseId, '/path/to/image.jpg');
```

---

## 🔄 批量导入 (Batch Import)

**API端点**: `POST /api/v1/cases/{case_id}/files/batch-import`

**使用场景**:
- 批量导入整个目录的文件
- 大量文件的高效处理

**行为特点**:
- ✅ 使用本地导入逻辑（不复制文件）
- ✅ 支持递归目录扫描
- ✅ 异步后台处理
- ✅ 批量优化，提高性能

**前端调用**:
```javascript
// 批量导入目录
await api.post(`/cases/${caseId}/files/batch-import`, {
    directory_path: '/path/to/directory',
    recursive: true,
    batch_size: 100
});
```

---

## 🎯 选择建议

### 使用Web上传的情况:
- 用户从设备上传少量文件
- 文件需要传输到服务器
- 不介意创建文件副本

### 使用本地导入的情况:
- 文件已存在于服务器文件系统中
- 希望节省存储空间
- 批量处理大量文件
- 文件位置固定，不会移动

### 使用批量导入的情况:
- 需要导入整个目录
- 文件数量很大（>50个）
- 希望后台异步处理

---

## ⚙️ 配置说明

两种方式都会：
1. 在数据库中创建文件记录
2. 生成300px缩略图
3. 提取EXIF元数据
4. 应用规则引擎进行标签分析
5. 支持图像质量分析

区别仅在于是否复制原始文件到案例目录中。

---

## 🔧 开发者注意事项

如果用户抱怨"上传后文件被复制了"，请引导他们使用：
1. **本地导入功能** - 适用于单个文件
2. **批量导入功能** - 适用于目录导入

Web上传功能的文件复制行为是设计如此，因为它处理的是临时上传文件。
