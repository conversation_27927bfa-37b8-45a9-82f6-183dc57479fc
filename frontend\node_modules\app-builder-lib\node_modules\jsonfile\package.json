{"name": "jsonfile", "version": "6.1.0", "description": "Easily read/write JSON files.", "repository": {"type": "git", "url": "**************:jprichardson/node-jsonfile.git"}, "keywords": ["read", "write", "file", "json", "fs", "fs-extra"], "author": "<PERSON> <jp<PERSON><PERSON><EMAIL>>", "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}, "devDependencies": {"mocha": "^8.2.0", "rimraf": "^2.4.0", "standard": "^16.0.1"}, "main": "index.js", "files": ["index.js", "utils.js"], "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "mocha"}}