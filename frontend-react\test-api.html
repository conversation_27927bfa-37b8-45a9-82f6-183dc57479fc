<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Novak - API 测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #111827;
            color: #f3f4f6;
        }
        .test-section {
            background: #1f2937;
            border: 1px solid #374151;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
        .result {
            background: #0f172a;
            border: 1px solid #334155;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { border-color: #10b981; }
        .error { border-color: #ef4444; }
        .loading { border-color: #f59e0b; }
    </style>
</head>
<body>
    <h1>🔬 Project Novak - API 测试</h1>
    <p>验证 MSW (Mock Service Worker) 是否正常工作</p>

    <div class="test-section">
        <h2>📚 Cases API 测试</h2>
        <button onclick="testCasesAPI()">测试 GET /api/cases</button>
        <div id="cases-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📁 Files API 测试</h2>
        <button onclick="testFilesAPI()">测试 GET /api/files</button>
        <button onclick="testFilesWithParams()">测试 GET /api/files?case_id=1</button>
        <div id="files-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🏷️ Tags API 测试</h2>
        <button onclick="testTagsAPI()">测试 GET /api/tags</button>
        <div id="tags-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🔍 Search API 测试</h2>
        <button onclick="testSearchAPI()">测试 GET /api/search/files?q=城市</button>
        <div id="search-result" class="result"></div>
    </div>

    <script>
        // 通用 API 测试函数
        async function testAPI(url, resultElementId) {
            const resultElement = document.getElementById(resultElementId);
            resultElement.className = 'result loading';
            resultElement.textContent = '🔄 正在测试 ' + url + '...';

            try {
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.className = 'result success';
                    resultElement.textContent = `✅ 成功 (${response.status})\n\n` + JSON.stringify(data, null, 2);
                } else {
                    resultElement.className = 'result error';
                    resultElement.textContent = `❌ 错误 (${response.status})\n\n` + JSON.stringify(data, null, 2);
                }
            } catch (error) {
                resultElement.className = 'result error';
                resultElement.textContent = `❌ 网络错误\n\n${error.message}`;
            }
        }

        // 具体测试函数
        function testCasesAPI() {
            testAPI('/api/cases', 'cases-result');
        }

        function testFilesAPI() {
            testAPI('/api/files', 'files-result');
        }

        function testFilesWithParams() {
            testAPI('/api/files?case_id=1', 'files-result');
        }

        function testTagsAPI() {
            testAPI('/api/tags', 'tags-result');
        }

        function testSearchAPI() {
            testAPI('/api/search/files?q=城市', 'search-result');
        }

        // 页面加载时自动测试
        window.addEventListener('load', () => {
            console.log('🚀 API 测试页面已加载');
            console.log('📡 检查 MSW 是否已启动...');
            
            // 自动测试 Cases API
            setTimeout(() => {
                testCasesAPI();
            }, 1000);
        });
    </script>
</body>
</html>
