import type { Identifier } from 'dnd-core';
import { type CSSProperties, type MutableRefObject } from 'react';
import { type DragLayerMonitor } from 'react-dnd';
import { type Point, type PreviewPlacement } from './offsets';
export type usePreviewState<T = unknown, El extends Element = Element> = {
    display: false;
} | usePreviewStateFull<T, El>;
export type usePreviewStateFull<T = unknown, El extends Element = Element> = {
    display: true;
} & usePreviewStateContent<T, El>;
export type usePreviewStateContent<T = unknown, El extends Element = Element> = {
    ref: MutableRefObject<El | null>;
    itemType: Identifier | null;
    item: T;
    style: CSSProperties;
    monitor: DragLayerMonitor;
};
export type usePreviewOptions = {
    placement?: PreviewPlacement;
    padding?: Point;
};
export declare const usePreview: <T = unknown, El extends Element = Element>(options?: usePreviewOptions) => usePreviewState<T, El>;
