# src/routers/cases.py
import os
import shutil
import logging
from typing import List
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, BackgroundTasks, Query, Request, Form, Body
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

# 导入我们的数据库工具、模型、蓝图和 CRUD 层
from .. import models, schemas
from ..database import get_master_db
from ..crud import (
    create_case,
    get_cases,
    get_case,
    get_case_basic,
    delete_case,
    update_case,
    create_file_for_case,
    get_files_for_case,
    get_file,
    delete_file
)
from ..crud.file_crud import soft_delete_file
from ..services.cover_service import get_cover_service, CoverImageService

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/cases",
    tags=["Cases"]
)

@router.post("/", response_model=schemas.Case, status_code=201)
def create_case_endpoint(
    case: schemas.CaseCreate,
    db: Session = Depends(get_master_db),
    cover_service: CoverImageService = Depends(get_cover_service)
):
    """
    创建一个新案例。
    使用 CRUD 层处理业务逻辑，并设置默认封面。
    """
    try:
        # 创建案例
        new_case = create_case(db, case)

        # 设置默认占位图封面
        try:
            cover_service.set_placeholder_cover(db, new_case.id)
            # 重新获取更新后的案例信息
            updated_case = get_case(db, new_case.id)
            return updated_case if updated_case else new_case
        except Exception as cover_error:
            logger.warning(f"设置默认封面失败: {cover_error}")
            return new_case

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/", response_model=List[schemas.Case])
def read_cases_endpoint(skip: int = 0, limit: int = 100, db: Session = Depends(get_master_db)):
    """
    从主数据库中读取所有案例的基本信息（不包含文件列表）。
    """
    return get_cases(db, skip, limit)

@router.get("/{case_id}", response_model=schemas.Case)
def read_case_endpoint(case_id: int, db: Session = Depends(get_master_db)):
    """
    读取单个案例的详细信息，包括其所有文件。
    """
    db_case = get_case(db, case_id)
    if db_case is None:
        raise HTTPException(status_code=404, detail="案例未找到或其数据库文件不存在")
    return db_case

@router.put("/{case_id}", response_model=schemas.Case)
def update_case_endpoint(case_id: int, case_update: schemas.CaseUpdate, db: Session = Depends(get_master_db)):
    """
    更新指定案例的信息。
    """
    try:
        updated_case = update_case(db, case_id, case_update)
        if not updated_case:
            raise HTTPException(status_code=404, detail="案例未找到")
        return updated_case
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{case_id}/basic", response_model=schemas.CaseBasic)
def get_case_basic_info(case_id: int, db: Session = Depends(get_master_db)):
    """
    获取案例基本信息（优化版本，不包含文件列表）
    """
    try:
        case = get_case_basic(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例未找到")
        return case
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{case_id}", response_model=schemas.OperationResponse)
def delete_case_endpoint(case_id: int, db: Session = Depends(get_master_db)):
    """
    删除指定案例。
    这将删除案例记录以及其对应的数据库文件。
    """
    success = delete_case(db, case_id)
    if not success:
        raise HTTPException(status_code=404, detail="案例未找到")
    return schemas.OperationResponse(
        success=True,
        message=f"案例 {case_id} 已成功删除"
    )

@router.post("/{case_id}/files", response_model=schemas.File, status_code=201)
def create_file_for_case_endpoint(case_id: int, file: schemas.FileCreate, db: Session = Depends(get_master_db)):
    """
    为指定案例创建一个新的文件记录。
    使用 CRUD 层处理业务逻辑。
    """
    try:
        db_file = create_file_for_case(db, case_id, file)
        if db_file is None:
            raise HTTPException(status_code=404, detail="案例未找到或其数据库未配置")
        return db_file
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{case_id}/files/", response_model=schemas.FileListResponse)
@router.get("/{case_id}/files", response_model=schemas.FileListResponse)
def get_case_files_endpoint(
    case_id: int,
    request: Request,
    limit: int = Query(50, ge=1, le=200, description="返回结果数量限制"),
    offset: int = Query(0, ge=0, description="分页偏移量"),
    db: Session = Depends(get_master_db)
):
    """
    根据标签筛选指定案例下的文件

    支持多种标签类型的组合筛选，包括元数据标签、用户标签、AI标签等。
    使用tag_前缀的查询参数进行筛选。

    示例：
    - /cases/123/files?tag_camera=Nikon%20D850&tag_year=2024
    - /cases/123/files?tag_photographer=张三&limit=20
    """
    # 验证案例是否存在
    case = get_case(db, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="案例不存在")

    # 提取tag_前缀的查询参数（优化性能）
    tag_filters = {
        param_name[4:]: param_value
        for param_name, param_value in request.query_params.items()
        if param_name.startswith("tag_") and param_value
    }

    # 获取文件列表
    files = get_files_for_case(db, case_id)

    # 应用标签筛选
    if tag_filters:
        filtered_files = []
        for file in files:
            if _file_matches_tags(file, tag_filters):
                filtered_files.append(file)
        files = filtered_files

    # 应用分页
    total = len(files)
    paginated_files = files[offset:offset + limit]

    return schemas.FileListResponse(
        files=paginated_files,
        total=total,
        limit=limit,
        offset=offset,
        filters=tag_filters if tag_filters else None
    )

@router.get("/{case_id}/files/{file_id}", response_model=schemas.File)
def get_case_file_endpoint(case_id: int, file_id: int, db: Session = Depends(get_master_db)):
    """
    获取指定案例的特定文件信息
    """
    file = get_file(db, file_id, case_id)
    if not file:
        raise HTTPException(status_code=404, detail="文件未找到")
    return file

@router.delete("/{case_id}/files/{file_id}", response_model=schemas.OperationResponse)
def delete_case_file_endpoint(case_id: int, file_id: int, db: Session = Depends(get_master_db)):
    """
    软删除指定案例的特定文件
    文件将被移动到案例目录下的trash文件夹中，保留缩略图
    """
    print(f"DEBUG API: 收到删除请求 - 案例 {case_id}, 文件 {file_id}")
    try:
        print(f"DEBUG API: 调用 soft_delete_file")
        success = soft_delete_file(db, case_id, file_id)
        print(f"DEBUG API: soft_delete_file 返回: {success}")

        if not success:
            print(f"DEBUG API: 删除失败，返回404")
            raise HTTPException(status_code=404, detail="文件未找到或删除失败")

        print(f"DEBUG API: 删除成功")
        return schemas.OperationResponse(
            success=True,
            message="文件已删除并移动到回收站",
            data={"case_id": case_id, "file_id": file_id, "status": "soft_deleted"}
        )
    except Exception as e:
        print(f"DEBUG API: 异常: {e}")
        logger.error(f"删除文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除文件失败: {str(e)}")

from fastapi import UploadFile, File
from fastapi.responses import FileResponse
from PIL import Image
import io
import os
from pathlib import Path

def _process_exif_value(tag_name: str, value):
    """处理EXIF值为可读格式"""
    try:
        import fractions
        from datetime import datetime

        # 处理光圈
        if tag_name == 'FNumber':
            if isinstance(value, tuple) and len(value) == 2 and value[1] != 0:
                aperture = value[0] / value[1]
                return f"f/{aperture:.1f}"
            return f"f/{float(value):.1f}"

        # 处理快门速度
        elif tag_name == 'ExposureTime':
            if isinstance(value, tuple) and len(value) == 2 and value[1] != 0:
                exposure = value[0] / value[1]
                if exposure < 1:
                    return f"1/{int(1/exposure)} 秒"
                else:
                    return f"{exposure:.1f} 秒"
            elif isinstance(value, float):
                if value < 1:
                    return f"1/{int(1/value)} 秒"
                else:
                    return f"{value:.1f} 秒"

        # 处理ISO
        elif tag_name == 'ISOSpeedRatings':
            return f"ISO {int(value)}"

        # 处理焦距
        elif tag_name == 'FocalLength':
            if isinstance(value, tuple) and len(value) == 2 and value[1] != 0:
                focal = value[0] / value[1]
                return f"{focal:.1f}mm"
            return f"{float(value):.1f}mm"

        # 处理拍摄日期
        elif tag_name == 'DateTimeOriginal':
            if isinstance(value, str):
                try:
                    dt = datetime.strptime(value, '%Y:%m:%d %H:%M:%S')
                    return dt.strftime('%Y/%m/%d')
                except:
                    return value

        # 处理色彩空间
        elif tag_name == 'ColorSpace':
            color_spaces = {1: 'sRGB', 2: 'AdobeRGB'}
            return color_spaces.get(int(value), f'Unknown({value})')

        # 处理闪光灯
        elif tag_name == 'Flash':
            flash_value = int(value)
            return "闪光灯开启" if (flash_value & 0x01) else "闪光灯关闭"

        # 处理白平衡
        elif tag_name == 'WhiteBalance':
            return "自动" if int(value) == 0 else "手动"

        # 其他字段
        else:
            return str(value)

    except Exception as e:
        logger.warning(f"处理EXIF值失败 {tag_name}: {e}")
        return str(value)

@router.post("/{case_id}/files/upload-and-copy", response_model=schemas.File, status_code=201)
def upload_and_copy_file_endpoint(
    case_id: int,
    file: UploadFile = File(...),
    db: Session = Depends(get_master_db)
):
    """
    Web上传文件到指定案例并复制到案例目录

    ⚠️ 警告：此端点会将上传的文件复制到案例的 'uploads' 目录中。
    这是处理 Web 临时文件的预期行为。
    如果要实现"只记录路径而不复制"，请务必使用 /import-by-path 端点。

    适用场景：
    - 网页文件上传
    - 需要将文件复制到案例目录的场景
    """
    import tempfile
    import shutil
    import os
    import mimetypes
    from pathlib import Path
    from PIL import Image
    from ..services.rule_engine import process_file_with_rules
    from ..database import create_case_directory

    # 验证案例存在
    from ..crud import get_case
    db_case = get_case(db, case_id)
    if not db_case:
        raise HTTPException(status_code=404, detail="案例未找到")

    # 获取原始文件名
    original_filename = file.filename or "unknown"

    # 创建临时文件来处理上传的文件
    with tempfile.NamedTemporaryFile(delete=False, suffix=Path(original_filename).suffix) as temp_file:
        # 将上传的文件内容写入临时文件
        shutil.copyfileobj(file.file, temp_file)
        temp_file_path = temp_file.name

    try:
        # 验证文件类型
        file_type, _ = mimetypes.guess_type(original_filename)
        if not file_type:
            file_type = file.content_type

        # 创建案例目录结构（只创建必要的目录，不复制文件）
        case_dir = create_case_directory(case_id)
        thumbnails_dir = case_dir / "thumbnails"
        thumbnails_dir.mkdir(exist_ok=True)

        # 检查是否为图像文件
        is_image = file_type and file_type.startswith("image/")

        # 初始化文件信息
        width, height, taken_at = None, None, None
        thumbnail_path = None
        exif_metadata = {}  # 在正确的作用域中初始化

        if is_image:
            try:
                # 处理图像文件
                with Image.open(temp_file_path) as img:
                    # 获取图像尺寸
                    width, height = img.size

                    # 提取完整EXIF数据（包括拍摄时间和其他元数据）
                    try:
                        from PIL.ExifTags import TAGS
                        from datetime import datetime
                        import fractions

                        exif_data = img.getexif()
                        if exif_data:
                            logger.info(f"📊 发现EXIF数据，共 {len(exif_data)} 个字段")

                            # EXIF字段映射
                            exif_mapping = {
                                'Make': 'camera_make',
                                'Model': 'camera_model',
                                'FNumber': 'aperture',
                                'ExposureTime': 'shutter_speed',
                                'ISOSpeedRatings': 'iso',
                                'FocalLength': 'focal_length',
                                'DateTimeOriginal': 'date_time_original',
                                'ColorSpace': 'color_space',
                                'Flash': 'flash',
                                'WhiteBalance': 'white_balance',
                                'Software': 'software'
                            }

                            # 处理主EXIF数据
                            for tag_id, value in exif_data.items():
                                tag_name = TAGS.get(tag_id)

                                # 处理拍摄时间（用于taken_at字段）
                                if tag_name == 'DateTimeOriginal':
                                    try:
                                        taken_at = datetime.strptime(value, '%Y:%m:%d %H:%M:%S')
                                    except:
                                        pass

                                # 处理所有映射的EXIF字段
                                if tag_name in exif_mapping:
                                    field_name = exif_mapping[tag_name]
                                    processed_value = _process_exif_value(tag_name, value)
                                    if processed_value:
                                        exif_metadata[field_name] = processed_value
                                        logger.info(f"✅ 提取EXIF字段: {field_name} = {processed_value}")

                            # 如果主EXIF中没有找到拍摄时间，检查子IFD
                            if taken_at is None:
                                try:
                                    exif_ifd = exif_data.get_ifd(0x8769)  # EXIF IFD
                                    for tag_id, value in exif_ifd.items():
                                        tag_name = TAGS.get(tag_id)
                                        if tag_name == 'DateTimeOriginal':
                                            try:
                                                taken_at = datetime.strptime(value, '%Y:%m:%d %H:%M:%S')
                                                break
                                            except:
                                                pass

                                        # 也处理子IFD中的其他EXIF字段
                                        if tag_name in exif_mapping:
                                            field_name = exif_mapping[tag_name]
                                            processed_value = _process_exif_value(tag_name, value)
                                            if processed_value:
                                                exif_metadata[field_name] = processed_value
                                                logger.info(f"✅ 提取子IFD EXIF字段: {field_name} = {processed_value}")
                                except KeyError:
                                    pass  # 没有子IFD

                            logger.info(f"🎉 成功提取 {len(exif_metadata)} 个EXIF字段，taken_at: {taken_at}")
                        else:
                            logger.info("📋 图片没有EXIF数据")

                    except Exception as e:
                        logger.warning(f"无法提取EXIF信息: {e}")

                    # 完整的EXIF元数据已在上传时提取

                    # 生成300px缩略图
                    img_copy = img.copy()

                    # 计算缩略图尺寸（长边为300px）
                    max_size = 300
                    if width > height:
                        new_width = max_size
                        new_height = int((height * max_size) / width)
                    else:
                        new_height = max_size
                        new_width = int((width * max_size) / height)

                    img_copy = img_copy.resize((new_width, new_height), Image.Resampling.LANCZOS)

                    # 转换为RGB模式（如果需要）
                    if img_copy.mode in ("RGBA", "P"):
                        img_copy = img_copy.convert("RGB")

                    # 保存缩略图
                    thumbnail_filename = f"{Path(original_filename).stem}_thumb.jpg"
                    thumbnail_full_path = thumbnails_dir / thumbnail_filename
                    img_copy.save(thumbnail_full_path, "JPEG", quality=85)

                    thumbnail_path = str(thumbnail_full_path)
                    logger.info(f"✅ 生成缩略图: {thumbnail_filename} ({new_width}x{new_height})")

            except Exception as e:
                logger.error(f"处理图像文件失败: {e}")
                # 即使图像处理失败，也继续创建文件记录

        # 对于Web上传，我们需要保存文件到永久位置
        # 创建案例uploads目录用于存储上传的文件
        uploads_dir = case_dir / "uploads"
        uploads_dir.mkdir(exist_ok=True)

        # 生成永久文件路径
        permanent_file_path = uploads_dir / original_filename

        # 检查文件是否已存在
        counter = 1
        base_name = Path(original_filename).stem
        extension = Path(original_filename).suffix
        while permanent_file_path.exists():
            new_filename = f"{base_name}_{counter}{extension}"
            permanent_file_path = uploads_dir / new_filename
            counter += 1

        # 复制临时文件到永久位置
        shutil.copy2(temp_file_path, permanent_file_path)

        # 创建文件记录（存储永久文件路径）
        file_create = schemas.FileCreate(
            file_name=permanent_file_path.name,
            file_path=str(permanent_file_path.absolute()),  # 存储永久文件路径
            file_type=file_type,
            width=width,
            height=height,
            taken_at=taken_at,
            thumbnail_small_path=thumbnail_path  # 存储缩略图路径
        )

        from ..crud import create_file_for_case
        db_file = create_file_for_case(db, case_id, file_create)
        if db_file is None:
            raise HTTPException(status_code=404, detail="案例未找到")

        logger.info(f"文件处理完成，准备应用规则引擎: {db_file.file_name if db_file else 'None'}")

        # 应用规则引擎处理文件标签，并合并EXIF数据
        try:
            logger.info(f"开始应用规则引擎处理文件: {db_file.file_name}")
            tags_data = process_file_with_rules(db, case_id, db_file)
            logger.info(f"规则引擎生成的标签数据: {tags_data}")

            # 🔧 关键修复：合并EXIF数据到规则引擎结果
            if exif_metadata:
                logger.info(f"🔄 合并EXIF数据到规则引擎结果: {len(exif_metadata)} 个字段")

                # 确保metadata部分存在
                if "tags" not in tags_data:
                    tags_data["tags"] = {}
                if "metadata" not in tags_data["tags"]:
                    tags_data["tags"]["metadata"] = {}

                # 合并EXIF数据，EXIF数据优先级更高
                tags_data["tags"]["metadata"].update(exif_metadata)
                logger.info(f"✅ EXIF数据合并完成，最终metadata字段数: {len(tags_data['tags']['metadata'])}")

            # 更新文件的tags字段（PostgreSQL模式）
            file_obj = db.query(models.File).filter(
                models.File.id == db_file.id,
                models.File.case_id == case_id
            ).first()
            if file_obj:
                import json
                file_obj.tags = json.dumps(tags_data, ensure_ascii=False)
                db.commit()
                db.refresh(file_obj)
                logger.info(f"🎉 规则引擎处理完成，包含EXIF数据: {db_file.file_name}")
            else:
                logger.error(f"未找到文件对象: {db_file.id}")

        except Exception as e:
            logger.error(f"规则引擎处理失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            # 规则引擎失败不影响文件上传，只记录错误

        return db_file

    except Exception as e:
        logger.error(f"文件上传处理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        # 清理临时文件
        try:
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        except Exception as e:
            logger.warning(f"清理临时文件失败: {e}")

@router.post("/{case_id}/files/import-by-path", response_model=schemas.File, status_code=201)
def import_by_path_file_endpoint(
    case_id: int,
    file_path: str = Form(...),
    db: Session = Depends(get_master_db)
):
    """
    按路径导入本地文件到指定案例

    ✅ 此端点只记录文件路径，不复制原始文件，只生成缩略图。
    适用于导入已存在于本地文件系统中的文件。

    适用场景：
    - 本地文件导入
    - 需要保持原始文件位置不变的场景
    - 大文件导入（避免重复存储）
    """
    import mimetypes
    from pathlib import Path
    from PIL import Image
    from ..services.rule_engine import process_file_with_rules
    from ..database import create_case_directory

    # 验证案例存在
    from ..crud import get_case
    db_case = get_case(db, case_id)
    if not db_case:
        raise HTTPException(status_code=404, detail="案例未找到")

    # 验证文件路径存在
    original_path = Path(file_path)
    if not original_path.exists() or not original_path.is_file():
        raise HTTPException(status_code=400, detail=f"文件路径不存在: {file_path}")

    # 获取文件信息
    original_filename = original_path.name
    file_type, _ = mimetypes.guess_type(original_path)

    try:
        # 创建案例目录结构
        case_dir = create_case_directory(case_id)
        thumbnails_dir = case_dir / "thumbnails"
        thumbnails_dir.mkdir(exist_ok=True)

        # 检查是否为图像文件
        is_image = file_type and file_type.startswith("image/")

        # 初始化文件信息
        width, height, taken_at = None, None, None
        thumbnail_path = None

        if is_image:
            try:
                # 处理图像文件
                with Image.open(original_path) as img:
                    # 获取图像尺寸
                    width, height = img.size

                    # 尝试获取EXIF拍摄时间（保持原有逻辑用于taken_at字段）
                    try:
                        from PIL.ExifTags import TAGS
                        from datetime import datetime
                        exif_data = img.getexif()
                        if exif_data:
                            # 首先检查主EXIF数据
                            for tag, value in exif_data.items():
                                tag_name = TAGS.get(tag)
                                if tag_name == 'DateTimeOriginal':
                                    taken_at = datetime.strptime(value, '%Y:%m:%d %H:%M:%S')
                                    break

                            # 如果主EXIF中没有找到，检查子IFD
                            if taken_at is None:
                                try:
                                    exif_ifd = exif_data.get_ifd(0x8769)  # EXIF IFD
                                    for tag_id, value in exif_ifd.items():
                                        tag_name = TAGS.get(tag_id)
                                        if tag_name == 'DateTimeOriginal':
                                            taken_at = datetime.strptime(value, '%Y:%m:%d %H:%M:%S')
                                            break
                                except KeyError:
                                    pass  # 没有子IFD
                    except Exception as e:
                        logger.warning(f"无法提取EXIF时间信息: {e}")

                    # 注意：完整的EXIF元数据将在规则引擎处理时提取

                    # 生成300px缩略图
                    img_copy = img.copy()

                    # 计算缩略图尺寸（长边为300px）
                    max_size = 300
                    if width > height:
                        new_width = max_size
                        new_height = int((height * max_size) / width)
                    else:
                        new_height = max_size
                        new_width = int((width * max_size) / height)

                    img_copy = img_copy.resize((new_width, new_height), Image.Resampling.LANCZOS)

                    # 转换为RGB模式（如果需要）
                    if img_copy.mode in ("RGBA", "P"):
                        img_copy = img_copy.convert("RGB")

                    # 保存缩略图
                    thumbnail_filename = f"{Path(original_filename).stem}_thumb.jpg"
                    thumbnail_full_path = thumbnails_dir / thumbnail_filename
                    img_copy.save(thumbnail_full_path, "JPEG", quality=85)

                    thumbnail_path = str(thumbnail_full_path)
                    logger.info(f"✅ 生成缩略图: {thumbnail_filename} ({new_width}x{new_height})")

            except Exception as e:
                logger.error(f"处理图像文件失败: {e}")
                # 即使图像处理失败，也继续创建文件记录

        # 创建文件记录（存储原始文件路径）
        file_create = schemas.FileCreate(
            file_name=original_filename,
            file_path=str(original_path.absolute()),  # 存储原始文件的绝对路径
            file_type=file_type,
            width=width,
            height=height,
            taken_at=taken_at,
            thumbnail_small_path=thumbnail_path  # 存储缩略图路径
        )

        from ..crud import create_file_for_case
        db_file = create_file_for_case(db, case_id, file_create)
        if db_file is None:
            raise HTTPException(status_code=404, detail="案例未找到")

        logger.info(f"文件导入完成，准备应用规则引擎: {db_file.file_name}")

        # 应用规则引擎处理文件标签
        try:
            logger.info(f"开始应用规则引擎处理文件: {db_file.file_name}")
            tags_data = process_file_with_rules(db, case_id, db_file)
            logger.info(f"规则引擎生成的标签数据: {tags_data}")

            # 更新文件的tags字段（PostgreSQL模式）
            file_obj = db.query(models.File).filter(
                models.File.id == db_file.id,
                models.File.case_id == case_id
            ).first()
            if file_obj:
                import json
                file_obj.tags = json.dumps(tags_data, ensure_ascii=False)
                db.commit()
                db.refresh(file_obj)
                logger.info(f"规则引擎处理完成: {db_file.file_name}")
            else:
                logger.error(f"未找到文件对象: {db_file.id}")

        except Exception as e:
            logger.error(f"规则引擎处理失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            # 规则引擎失败不影响文件导入，只记录错误

        return db_file

    except Exception as e:
        logger.error(f"文件导入处理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{case_id}/files/{file_id}/download")
def download_file_endpoint(case_id: int, file_id: int, db: Session = Depends(get_master_db)):
    """
    下载指定案例的特定文件
    """
    file = get_file(db, file_id, case_id)
    if not file:
        raise HTTPException(status_code=404, detail="文件未找到")

    file_path = getattr(file, 'file_path', None)
    if not file_path or not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="文件不存在")

    file_name = getattr(file, 'file_name', 'unknown')
    file_type = getattr(file, 'file_type', None)

    # 处理文件名编码问题
    try:
        # 确保文件名是有效的UTF-8字符串
        if isinstance(file_name, str):
            file_name.encode('utf-8')
    except UnicodeEncodeError:
        logger.warning(f"文件名编码问题，使用默认名称: {file_name}")
        file_name = 'download_file'

    return FileResponse(
        path=file_path,
        filename=file_name,
        media_type=file_type or 'application/octet-stream'
    )

@router.get("/{case_id}/files/{file_id}/view")
def view_file_endpoint(case_id: int, file_id: int, db: Session = Depends(get_master_db)):
    """
    在线查看指定案例的特定文件（原始大图）
    """
    try:
        file = get_file(db, file_id, case_id)
        if not file:
            raise HTTPException(status_code=404, detail="文件未找到")

        file_path = getattr(file, 'file_path', None)
        logger.info(f"查看文件路径: {file_path}")

        if not file_path:
            raise HTTPException(status_code=404, detail="文件路径为空")

        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            raise HTTPException(status_code=404, detail="原始文件不存在")

        # 检查是否为图片文件
        file_type = getattr(file, 'file_type', None)
        if not file_type or not file_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="文件不是图片类型")

        logger.info(f"返回文件: {file_path}, 类型: {file_type}")

        # 处理文件名编码问题
        file_name = getattr(file, 'file_name', 'image')
        try:
            # 尝试对文件名进行URL编码以处理中文字符
            from urllib.parse import quote
            encoded_filename = quote(file_name.encode('utf-8'))
            content_disposition = f"inline; filename*=UTF-8''{encoded_filename}"
        except Exception as e:
            logger.warning(f"文件名编码失败: {e}, 使用默认文件名")
            content_disposition = "inline; filename=\"image\""

        return FileResponse(
            path=file_path,
            media_type=file_type,
            headers={
                "Cache-Control": "public, max-age=3600",  # 缓存1小时
                "Content-Disposition": content_disposition
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查看文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"查看文件失败: {str(e)}")

@router.post("/{case_id}/files/batch-import", response_model=dict, status_code=202)
def batch_import_files_endpoint(
    case_id: int,
    directory_path: str,
    recursive: bool = True,
    batch_size: int = 100,
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_master_db)
):
    """
    批量导入目录中的图片文件

    Args:
        case_id: 案例ID
        directory_path: 要导入的目录路径
        recursive: 是否递归搜索子目录
        batch_size: 批处理大小
        background_tasks: 后台任务
        db: 数据库会话

    Returns:
        导入任务信息
    """
    # 验证案例存在
    db_case = get_case(db, case_id)
    if not db_case:
        raise HTTPException(status_code=404, detail="案例未找到")

    # 获取目录中的图像文件
    try:
        # 临时实现：简单的图像文件获取
        import os
        from pathlib import Path

        def get_image_files_from_directory_local(directory_path: str, recursive: bool = True):
            """本地实现的图像文件获取函数"""
            image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
            image_files = []

            directory = Path(directory_path)
            if not directory.exists():
                return []

            if recursive:
                for file_path in directory.rglob('*'):
                    if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                        image_files.append(str(file_path))
            else:
                for file_path in directory.iterdir():
                    if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                        image_files.append(str(file_path))

            return image_files

        def batch_process_files_local(db, case_id, image_files, batch_size):
            """本地实现的批量处理函数"""
            import mimetypes
            from pathlib import Path
            from PIL import Image
            from .. import schemas
            from ..crud import create_file_for_case

            results = {
                'processed': 0,
                'skipped': 0,
                'failed': 0,
                'files': []
            }

            for file_path in image_files:
                try:
                    original_path = Path(file_path)

                    # 验证文件是否存在且为图片
                    if not original_path.is_file():
                        results['skipped'] += 1
                        continue

                    file_type, _ = mimetypes.guess_type(original_path)
                    if not file_type or not file_type.startswith("image/"):
                        results['skipped'] += 1
                        continue

                    # 提取图片元数据
                    width, height = None, None
                    try:
                        with Image.open(original_path) as img:
                            width, height = img.size
                    except Exception:
                        pass

                    # 创建文件记录
                    file_data = {
                        "file_name": original_path.name,
                        "file_path": str(original_path),
                        "file_type": file_type,
                        "width": width,
                        "height": height,
                        "taken_at": None,
                        "thumbnail_small_path": None,
                    }

                    file_create = schemas.FileCreate(**file_data)
                    db_file = create_file_for_case(db=db, case_id=case_id, file=file_create)

                    if db_file:
                        results['processed'] += 1
                        results['files'].append({
                            'id': db_file.id,
                            'file_name': db_file.file_name,
                            'file_path': db_file.file_path
                        })
                    else:
                        results['skipped'] += 1

                except Exception as e:
                    print(f"处理文件失败 {file_path}: {e}")
                    results['failed'] += 1

            return results

        image_files = get_image_files_from_directory_local(directory_path, recursive)
        if not image_files:
            raise HTTPException(status_code=400, detail="目录中没有找到图像文件")

        # 如果文件数量较少，直接处理
        if len(image_files) <= 50:
            results = batch_process_files_local(db, case_id, image_files, batch_size)
            return {
                "status": "completed",
                "message": f"批量导入完成",
                "results": results
            }
        else:
            # 大量文件使用后台任务处理
            background_tasks.add_task(
                batch_process_files_local,
                db, case_id, image_files, batch_size
            )
            return {
                "status": "processing",
                "message": f"已启动后台批量导入任务，共 {len(image_files)} 个文件",
                "file_count": len(image_files),
                "estimated_time_minutes": len(image_files) // 100  # 估算时间：每分钟100张
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量导入失败: {str(e)}")

@router.post("/{case_id}/files/async-batch-import", response_model=dict, status_code=202)
async def async_batch_import_files_endpoint(
    case_id: int,
    directory_path: str,
    recursive: bool = True,
    max_workers: int = 4,
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_master_db)
):
    """
    异步批量导入目录中的图片文件（高性能版本）

    Args:
        case_id: 案例ID
        directory_path: 要导入的目录路径
        recursive: 是否递归搜索子目录
        max_workers: 最大工作线程数
        background_tasks: 后台任务
        db: 数据库会话

    Returns:
        导入任务信息
    """
    # 验证案例存在
    db_case = get_case(db, case_id)
    if not db_case:
        raise HTTPException(status_code=404, detail="案例未找到")

    # 获取目录中的图像文件
    try:
        # 使用前面定义的本地函数
        from pathlib import Path

        def get_image_files_from_directory_local(directory_path: str, recursive: bool = True):
            """本地实现的图像文件获取函数"""
            image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
            image_files = []

            directory = Path(directory_path)
            if not directory.exists():
                return []

            if recursive:
                for file_path in directory.rglob('*'):
                    if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                        image_files.append(str(file_path))
            else:
                for file_path in directory.iterdir():
                    if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                        image_files.append(str(file_path))

            return image_files

        async def async_batch_process_files_local(db, case_id, image_files, max_workers):
            """本地实现的异步批量处理函数"""
            import asyncio
            from concurrent.futures import ThreadPoolExecutor
            import mimetypes
            from pathlib import Path
            from PIL import Image
            from .. import schemas
            from ..crud import create_file_for_case

            results = {
                'processed': 0,
                'skipped': 0,
                'failed': 0,
                'files': []
            }

            def process_single_file(file_path):
                try:
                    original_path = Path(file_path)

                    # 验证文件是否存在且为图片
                    if not original_path.is_file():
                        return {'status': 'skipped'}

                    file_type, _ = mimetypes.guess_type(original_path)
                    if not file_type or not file_type.startswith("image/"):
                        return {'status': 'skipped'}

                    # 提取图片元数据
                    width, height = None, None
                    try:
                        with Image.open(original_path) as img:
                            width, height = img.size
                    except Exception:
                        pass

                    # 创建文件记录
                    file_data = {
                        "file_name": original_path.name,
                        "file_path": str(original_path),
                        "file_type": file_type,
                        "width": width,
                        "height": height,
                        "taken_at": None,
                        "thumbnail_small_path": None,
                    }

                    file_create = schemas.FileCreate(**file_data)
                    db_file = create_file_for_case(db=db, case_id=case_id, file=file_create)

                    if db_file:
                        return {
                            'status': 'success',
                            'file': {
                                'id': db_file.id,
                                'file_name': db_file.file_name,
                                'file_path': db_file.file_path
                            }
                        }
                    else:
                        return {'status': 'skipped'}

                except Exception as e:
                    return {'status': 'failed', 'error': str(e)}

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                loop = asyncio.get_event_loop()
                tasks = [
                    loop.run_in_executor(executor, process_single_file, file_path)
                    for file_path in image_files
                ]

                for task in asyncio.as_completed(tasks):
                    result = await task
                    if result['status'] == 'success':
                        results['processed'] += 1
                        results['files'].append(result['file'])
                    elif result['status'] == 'skipped':
                        results['skipped'] += 1
                    else:
                        results['failed'] += 1

            return results

        image_files = get_image_files_from_directory_local(directory_path, recursive)
        if not image_files:
            raise HTTPException(status_code=400, detail="目录中没有找到图像文件")

        # 使用异步处理
        results = await async_batch_process_files_local(db, case_id, image_files, max_workers)

        return {
            "status": "completed",
            "message": f"异步批量导入完成",
            "results": results,
            "performance_note": f"使用了 {max_workers} 个工作线程"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"异步批量导入失败: {str(e)}")

@router.get("/{case_id}/files/{file_id}/thumbnail")
def get_file_thumbnail(case_id: int, file_id: int, size: int = 300, db: Session = Depends(get_master_db)):
    """
    获取文件缩略图，优先使用预生成的缩略图

    Args:
        case_id: 案例ID
        file_id: 文件ID
        size: 缩略图尺寸（默认300px）

    Returns:
        JPEG格式的缩略图
    """
    try:
        # 获取文件信息
        file_info = get_file(db, file_id, case_id)
        if not file_info:
            raise HTTPException(status_code=404, detail="文件未找到")

        # 检查是否为图片文件
        if not file_info.file_type or not file_info.file_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="文件不是图片类型")

        # 优先使用预生成的缩略图
        if file_info.thumbnail_small_path and Path(file_info.thumbnail_small_path).exists():
            thumbnail_path = Path(file_info.thumbnail_small_path)

            # 如果请求的尺寸与预生成的缩略图尺寸接近（300px），直接返回
            if size <= 350:  # 允许一定的尺寸容差
                try:
                    from fastapi.responses import FileResponse
                    return FileResponse(
                        path=thumbnail_path,
                        media_type="image/jpeg",
                        headers={
                            "Cache-Control": "public, max-age=3600",  # 缓存1小时
                            "Content-Disposition": f"inline; filename=thumbnail_{file_id}.jpg"
                        }
                    )
                except Exception as e:
                    logger.warning(f"返回预生成缩略图失败: {e}")

        # 如果没有预生成缩略图或需要不同尺寸，则动态生成
        # 检查原始文件是否存在
        original_path = Path(file_info.file_path)
        if not original_path.exists():
            raise HTTPException(status_code=404, detail="原始文件不存在")

        # 动态生成缩略图
        try:
            with Image.open(original_path) as img:
                # 创建副本以避免修改原图
                img_copy = img.copy()

                # 生成缩略图
                img_copy.thumbnail((size, size), Image.Resampling.LANCZOS)

                # 转换为RGB模式（如果需要）
                if img_copy.mode in ("RGBA", "P"):
                    img_copy = img_copy.convert("RGB")

                # 保存到内存中的字节流
                img_bytes = io.BytesIO()
                img_copy.save(img_bytes, format='JPEG', quality=85)
                img_bytes.seek(0)

                # 返回图片响应
                from fastapi.responses import StreamingResponse
                return StreamingResponse(
                    io.BytesIO(img_bytes.read()),
                    media_type="image/jpeg",
                    headers={
                        "Cache-Control": "public, max-age=3600",  # 缓存1小时
                        "Content-Disposition": f"inline; filename=thumbnail_{file_id}.jpg"
                    }
                )

        except Exception as e:
            logger.error(f"生成缩略图失败 {original_path}: {e}")
            raise HTTPException(status_code=500, detail="缩略图生成失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取缩略图失败: {e}")
        raise HTTPException(status_code=500, detail="获取缩略图失败")


def _file_matches_tags(file: schemas.File, tag_filters: dict) -> bool:
    """
    检查文件是否匹配标签筛选条件

    Args:
        file: 文件对象
        tag_filters: 标签筛选条件字典

    Returns:
        是否匹配所有筛选条件
    """
    if not file.tags:
        return False

    for tag_name, tag_value in tag_filters.items():
        if not _file_has_tag_value(file, tag_name, tag_value):
            return False

    return True


def _file_has_tag_value(file: schemas.File, tag_name: str, tag_value: str) -> bool:
    """
    检查文件是否包含指定的标签值

    Args:
        file: 文件对象
        tag_name: 标签名称
        tag_value: 标签值

    Returns:
        是否包含该标签值
    """
    if not file.tags:
        return False

    # 解析标签数据（可能是JSON字符串）
    import json
    tags_data = file.tags
    if isinstance(tags_data, str):
        try:
            tags_data = json.loads(tags_data)
        except (json.JSONDecodeError, TypeError):
            return False

    # 如果tags_data不是字典，尝试获取其属性
    if not isinstance(tags_data, dict):
        if hasattr(tags_data, 'dict'):
            tags_data = tags_data.dict()
        elif hasattr(tags_data, '__dict__'):
            tags_data = tags_data.__dict__
        else:
            return False

    # 在properties中查找
    if 'properties' in tags_data and tags_data['properties']:
        properties_dict = tags_data['properties']
        if isinstance(properties_dict, dict):
            for key, value in properties_dict.items():
                if key.lower() == tag_name.lower() and str(value).lower() == tag_value.lower():
                    return True

    # 在tags中查找
    if 'tags' in tags_data and tags_data['tags']:
        tags_dict = tags_data['tags']
        if isinstance(tags_dict, dict):
            # 在metadata中查找
            if 'metadata' in tags_dict and isinstance(tags_dict['metadata'], dict):
                metadata = tags_dict['metadata']
                for key, value in metadata.items():
                    if key.lower() == tag_name.lower() and str(value).lower() == tag_value.lower():
                        return True

            # 在cv中查找
            if 'cv' in tags_dict and isinstance(tags_dict['cv'], dict):
                cv = tags_dict['cv']
                for key, value in cv.items():
                    if key.lower() == tag_name.lower():
                        if isinstance(value, list):
                            return tag_value.lower() in [str(v).lower() for v in value]
                        else:
                            return str(value).lower() == tag_value.lower()

            # 在user标签中查找
            if 'user' in tags_dict and isinstance(tags_dict['user'], list):
                if tag_name.lower() == 'user':
                    return tag_value.lower() in [str(tag).lower() for tag in tags_dict['user']]

            # 在ai标签中查找
            if 'ai' in tags_dict and isinstance(tags_dict['ai'], list):
                if tag_name.lower() == 'ai':
                    return tag_value.lower() in [str(tag).lower() for tag in tags_dict['ai']]

    return False


@router.post("/{case_id}/files/{file_id}/reprocess", response_model=schemas.File)
def reprocess_file_metadata(
    case_id: int,
    file_id: int,
    db: Session = Depends(get_master_db)
):
    """
    重新处理文件元数据，提取完整的EXIF数据
    """
    from ..services.rule_engine import process_file_with_rules

    # 验证案例存在
    case = get_case(db, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="案例不存在")

    # 获取文件
    file = get_file(db, file_id, case_id)
    if not file:
        raise HTTPException(status_code=404, detail="文件不存在")

    try:
        # 重新处理文件，提取完整元数据
        logger.info(f"开始重新处理文件元数据: {file.file_name}")

        # 重新提取taken_at字段（如果是图像文件）
        taken_at = None
        if file.file_path and file.file_type and file.file_type.startswith('image/'):
            try:
                from PIL import Image, ExifTags
                from datetime import datetime
                from pathlib import Path

                file_path = Path(file.file_path)
                if file_path.exists():
                    with Image.open(file_path) as img:
                        exif_data = img.getexif()
                        if exif_data:
                            # 首先检查主EXIF数据
                            for tag, value in exif_data.items():
                                tag_name = ExifTags.TAGS.get(tag)
                                if tag_name == 'DateTimeOriginal':
                                    try:
                                        taken_at = datetime.strptime(value, '%Y:%m:%d %H:%M:%S')
                                        break
                                    except:
                                        pass

                            # 如果主EXIF中没有找到，检查子IFD
                            if taken_at is None:
                                try:
                                    exif_ifd = exif_data.get_ifd(0x8769)  # EXIF IFD
                                    for tag_id, value in exif_ifd.items():
                                        tag_name = ExifTags.TAGS.get(tag_id)
                                        if tag_name == 'DateTimeOriginal':
                                            try:
                                                taken_at = datetime.strptime(value, '%Y:%m:%d %H:%M:%S')
                                                break
                                            except:
                                                pass
                                except KeyError:
                                    pass  # 没有子IFD

                        logger.info(f"重新提取taken_at: {taken_at}")
            except Exception as e:
                logger.warning(f"重新提取taken_at失败: {e}")

        # 使用规则引擎重新处理文件
        tags_data = process_file_with_rules(db, case_id, file)

        # 更新文件的标签数据和taken_at字段 - 使用主数据库（PostgreSQL模式）
        # 在主数据库中查找文件
        file_obj = db.query(models.File).filter(
            models.File.id == file.id,
            models.File.case_id == case_id
        ).first()

        if file_obj:
            import json
            file_obj.tags = json.dumps(tags_data, ensure_ascii=False)
            if taken_at is not None:
                file_obj.taken_at = taken_at
                logger.info(f"✅ 更新taken_at字段: {taken_at}")
            db.commit()
            db.refresh(file_obj)
            logger.info(f"✅ 文件元数据重新处理完成: {file.file_name}")

            # 返回更新后的文件对象
            return file_obj
        else:
            logger.error(f"在主数据库中未找到文件: {file.id}")
            raise HTTPException(status_code=404, detail="在主数据库中未找到文件")

    except Exception as e:
        logger.error(f"重新处理文件元数据失败: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"重新处理失败: {str(e)}")


@router.post("/{case_id}/files/reprocess-all")
def reprocess_all_files_metadata(
    case_id: int,
    db: Session = Depends(get_master_db)
):
    """
    重新处理案例中所有文件的元数据
    """
    from ..services.rule_engine import process_file_with_rules

    # 验证案例存在
    case = get_case(db, case_id)
    if not case:
        raise HTTPException(status_code=404, detail="案例不存在")

    try:
        # 获取案例中的所有文件
        files = get_files_for_case(db, case_id)

        processed_count = 0
        error_count = 0

        logger.info(f"开始重新处理案例 {case_id} 中的 {len(files)} 个文件")

        # 使用主数据库（PostgreSQL模式）
        for file in files:
            try:
                # 重新提取taken_at字段（如果是图像文件）
                taken_at = None
                if file.file_path and file.file_type and file.file_type.startswith('image/'):
                    try:
                        from PIL import Image, ExifTags
                        from datetime import datetime
                        from pathlib import Path

                        file_path = Path(file.file_path)
                        if file_path.exists():
                            with Image.open(file_path) as img:
                                exif_data = img.getexif()
                                if exif_data:
                                    # 首先检查主EXIF数据
                                    for tag, value in exif_data.items():
                                        tag_name = ExifTags.TAGS.get(tag)
                                        if tag_name == 'DateTimeOriginal':
                                            try:
                                                taken_at = datetime.strptime(value, '%Y:%m:%d %H:%M:%S')
                                                break
                                            except:
                                                pass

                                    # 如果主EXIF中没有找到，检查子IFD
                                    if taken_at is None:
                                        try:
                                            exif_ifd = exif_data.get_ifd(0x8769)  # EXIF IFD
                                            for tag_id, value in exif_ifd.items():
                                                tag_name = ExifTags.TAGS.get(tag_id)
                                                if tag_name == 'DateTimeOriginal':
                                                    try:
                                                        taken_at = datetime.strptime(value, '%Y:%m:%d %H:%M:%S')
                                                        break
                                                    except:
                                                        pass
                                        except KeyError:
                                            pass  # 没有子IFD
                    except Exception as e:
                        logger.warning(f"重新提取taken_at失败 {file.file_name}: {e}")

                # 重新处理文件标签
                tags_data = process_file_with_rules(db, case_id, file)

                # 在主数据库中查找并更新文件
                file_obj = db.query(models.File).filter(
                    models.File.id == file.id,
                    models.File.case_id == case_id
                ).first()
                if file_obj:
                    import json
                    file_obj.tags = json.dumps(tags_data, ensure_ascii=False)
                    if taken_at is not None:
                        file_obj.taken_at = taken_at
                        logger.info(f"✅ 更新taken_at: {file.file_name} -> {taken_at}")
                    processed_count += 1
                    logger.info(f"✅ 已处理文件: {file.file_name}")
                else:
                    logger.warning(f"在主数据库中未找到文件: {file.file_name}")

            except Exception as e:
                error_count += 1
                logger.error(f"处理文件失败 {file.file_name}: {e}")

        # 提交所有更改
        db.commit()

        result = {
            "message": f"批量重新处理完成",
            "total_files": len(files),
            "processed_count": processed_count,
            "error_count": error_count
        }

        logger.info(f"批量重新处理完成: {result}")
        return result

    except Exception as e:
        logger.error(f"批量重新处理失败: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"批量重新处理失败: {str(e)}")

# ==================== 文件回收站相关端点 ====================

@router.get("/{case_id}/trash", response_model=dict)
def get_trash_files(case_id: int, db: Session = Depends(get_master_db)):
    """
    获取指定案例的回收站文件列表
    """
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")

        # 获取案例目录下的trash文件夹
        from ..database import DATA_DIR
        case_dir = DATA_DIR / f"case_{case_id}"
        trash_dir = case_dir / "trash"

        trash_files = []

        if trash_dir.exists():
            # 读取所有删除记录文件
            import json
            for record_file in trash_dir.glob("deleted_file_*.json"):
                try:
                    with open(record_file, 'r', encoding='utf-8') as f:
                        file_record = json.load(f)
                        trash_files.append(file_record)
                except Exception as e:
                    logger.warning(f"读取删除记录文件失败 {record_file}: {e}")

        # 按删除时间排序（最新的在前）
        trash_files.sort(key=lambda x: x.get('deleted_at', ''), reverse=True)

        return {
            "case_id": case_id,
            "files": trash_files,
            "count": len(trash_files)
        }

    except Exception as e:
        logger.error(f"获取回收站文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取回收站文件失败: {str(e)}")

@router.post("/{case_id}/trash/{file_id}/restore", response_model=schemas.OperationResponse)
def restore_file(case_id: int, file_id: int, db: Session = Depends(get_master_db)):
    """
    从回收站恢复单个文件
    """
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")

        # 获取删除记录
        from ..database import DATA_DIR
        import json
        case_dir = DATA_DIR / f"case_{case_id}"
        trash_dir = case_dir / "trash"
        record_file = trash_dir / f"deleted_file_{file_id}.json"

        if not record_file.exists():
            raise HTTPException(status_code=404, detail="删除记录不存在")

        # 读取删除记录
        with open(record_file, 'r', encoding='utf-8') as f:
            file_record = json.load(f)

        # 重新创建文件记录（明确排除id和file_id字段，让PostgreSQL自动生成新ID）
        # 从删除记录中提取数据，但排除任何可能导致ID冲突的字段
        file_data = {}

        # 基本文件信息
        file_data['file_name'] = file_record.get('file_name')
        file_data['file_type'] = file_record.get('file_type')
        file_data['file_path'] = file_record.get('file_path')
        file_data['thumbnail_small_path'] = file_record.get('thumbnail_small_path')

        # 图像属性
        if file_record.get('width') is not None:
            file_data['width'] = file_record['width']
        if file_record.get('height') is not None:
            file_data['height'] = file_record['height']

        # 时间信息
        if file_record.get('created_at'):
            from datetime import datetime
            if isinstance(file_record['created_at'], str):
                file_data['created_at'] = datetime.fromisoformat(file_record['created_at'].replace('Z', '+00:00'))
            else:
                file_data['created_at'] = file_record['created_at']

        if file_record.get('taken_at'):
            if isinstance(file_record['taken_at'], str):
                file_data['taken_at'] = datetime.fromisoformat(file_record['taken_at'].replace('Z', '+00:00'))
            else:
                file_data['taken_at'] = file_record['taken_at']

        # 质量和分析数据
        if file_record.get('quality_score') is not None:
            file_data['quality_score'] = file_record['quality_score']
        if file_record.get('sharpness') is not None:
            file_data['sharpness'] = file_record['sharpness']
        if file_record.get('brightness') is not None:
            file_data['brightness'] = file_record['brightness']
        if file_record.get('dynamic_range') is not None:
            file_data['dynamic_range'] = file_record['dynamic_range']
        if file_record.get('num_faces') is not None:
            file_data['num_faces'] = file_record['num_faces']
        if file_record.get('face_sharpness') is not None:
            file_data['face_sharpness'] = file_record['face_sharpness']
        if file_record.get('face_quality') is not None:
            file_data['face_quality'] = file_record['face_quality']
        if file_record.get('cluster_id') is not None:
            file_data['cluster_id'] = file_record['cluster_id']
        if file_record.get('phash'):
            file_data['phash'] = file_record['phash']
        if file_record.get('group_id'):
            file_data['group_id'] = file_record['group_id']
        if file_record.get('frame_number') is not None:
            file_data['frame_number'] = file_record['frame_number']

        # 标签数据
        if file_record.get('tags'):
            file_data['tags'] = file_record['tags']

        # 案例ID
        file_data['case_id'] = case_id

        # 使用SQL直接插入，避免任何ORM相关的ID冲突
        from sqlalchemy import text

        # 构建SQL插入语句
        insert_sql = text("""
            INSERT INTO files (
                case_id, file_name, file_type, file_path, thumbnail_small_path,
                width, height, created_at, taken_at, quality_score, tags
            ) VALUES (
                :case_id, :file_name, :file_type, :file_path, :thumbnail_small_path,
                :width, :height, :created_at, :taken_at, :quality_score, :tags
            ) RETURNING id
        """)

        # 准备插入参数
        insert_params = {
            'case_id': file_data['case_id'],
            'file_name': file_data.get('file_name'),
            'file_type': file_data.get('file_type'),
            'file_path': file_data.get('file_path'),
            'thumbnail_small_path': file_data.get('thumbnail_small_path'),
            'width': file_data.get('width'),
            'height': file_data.get('height'),
            'created_at': file_data.get('created_at'),
            'taken_at': file_data.get('taken_at'),
            'quality_score': file_data.get('quality_score'),
            'tags': json.dumps(file_data.get('tags')) if file_data.get('tags') else None
        }

        # 执行插入并获取新ID
        result = db.execute(insert_sql, insert_params)
        new_file_id = result.fetchone()[0]

        # 同步序列值，确保下次插入不会有ID冲突
        sync_sequence_sql = text("SELECT setval('files_id_seq', (SELECT MAX(id) FROM files) + 1)")
        db.execute(sync_sequence_sql)

        db.commit()

        logger.info(f"文件 {file_record['file_name']} 已恢复，新ID: {new_file_id}")

        # 删除删除记录文件
        record_file.unlink()

        logger.info(f"文件 {file_id} 已从回收站恢复")

        return schemas.OperationResponse(
            success=True,
            message="文件恢复成功",
            data={"case_id": case_id, "file_id": new_file_id, "status": "restored"}
        )

    except Exception as e:
        logger.error(f"恢复文件失败: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"恢复文件失败: {str(e)}")

@router.delete("/{case_id}/trash/{file_id}", response_model=schemas.OperationResponse)
def delete_permanently(case_id: int, file_id: int, db: Session = Depends(get_master_db)):
    """
    永久删除回收站中的文件
    """
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")

        # 获取删除记录
        from ..database import DATA_DIR
        import json
        from pathlib import Path
        case_dir = DATA_DIR / f"case_{case_id}"
        trash_dir = case_dir / "trash"
        record_file = trash_dir / f"deleted_file_{file_id}.json"

        if not record_file.exists():
            raise HTTPException(status_code=404, detail="删除记录不存在")

        # 读取删除记录
        with open(record_file, 'r', encoding='utf-8') as f:
            file_record = json.load(f)

        # 删除缩略图文件（如果存在）
        if file_record.get('thumbnail_small_path'):
            thumbnail_path = Path(file_record['thumbnail_small_path'])
            if thumbnail_path.exists():
                try:
                    thumbnail_path.unlink()
                    logger.info(f"删除缩略图: {thumbnail_path}")
                except Exception as e:
                    logger.warning(f"删除缩略图失败: {e}")

        # 删除删除记录文件
        record_file.unlink()

        logger.info(f"文件 {file_id} 已永久删除")

        return schemas.OperationResponse(
            success=True,
            message="文件已永久删除",
            data={"case_id": case_id, "file_id": file_id, "status": "permanently_deleted"}
        )

    except Exception as e:
        logger.error(f"永久删除文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"永久删除文件失败: {str(e)}")

@router.post("/{case_id}/trash/restore-all", response_model=schemas.OperationResponse)
def restore_all_files(case_id: int, db: Session = Depends(get_master_db)):
    """
    恢复回收站中的所有文件
    """
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")

        # 获取案例目录下的trash文件夹
        from ..database import DATA_DIR
        import json
        case_dir = DATA_DIR / f"case_{case_id}"
        trash_dir = case_dir / "trash"

        restored_count = 0

        if trash_dir.exists():
            # 恢复所有删除记录文件
            for record_file in trash_dir.glob("deleted_file_*.json"):
                try:
                    with open(record_file, 'r', encoding='utf-8') as f:
                        file_record = json.load(f)

                    # 使用SQL直接插入，避免任何ORM相关的ID冲突
                    from sqlalchemy import text

                    # 构建SQL插入语句
                    insert_sql = text("""
                        INSERT INTO files (
                            case_id, file_name, file_type, file_path, thumbnail_small_path,
                            width, height, created_at, taken_at, quality_score, tags
                        ) VALUES (
                            :case_id, :file_name, :file_type, :file_path, :thumbnail_small_path,
                            :width, :height, :created_at, :taken_at, :quality_score, :tags
                        ) RETURNING id
                    """)

                    # 准备插入参数
                    insert_params = {
                        'case_id': case_id,
                        'file_name': file_record.get('file_name'),
                        'file_type': file_record.get('file_type'),
                        'file_path': file_record.get('file_path'),
                        'thumbnail_small_path': file_record.get('thumbnail_small_path'),
                        'width': file_record.get('width'),
                        'height': file_record.get('height'),
                        'created_at': file_record.get('created_at'),
                        'taken_at': file_record.get('taken_at'),
                        'quality_score': file_record.get('quality_score'),
                        'tags': json.dumps(file_record.get('tags')) if file_record.get('tags') else None
                    }

                    # 执行插入并获取新ID
                    result = db.execute(insert_sql, insert_params)
                    new_file_id = result.fetchone()[0]

                    # 删除删除记录文件
                    record_file.unlink()

                    restored_count += 1

                except Exception as e:
                    logger.warning(f"恢复文件失败 {record_file}: {e}")

        # 同步序列值，确保下次插入不会有ID冲突
        if restored_count > 0:
            from sqlalchemy import text
            sync_sequence_sql = text("SELECT setval('files_id_seq', (SELECT MAX(id) FROM files) + 1)")
            db.execute(sync_sequence_sql)

        db.commit()

        logger.info(f"已恢复 {restored_count} 个文件")

        return schemas.OperationResponse(
            success=True,
            message=f"已恢复 {restored_count} 个文件",
            data={"case_id": case_id, "restored_count": restored_count, "status": "all_restored"}
        )

    except Exception as e:
        logger.error(f"恢复所有文件失败: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"恢复所有文件失败: {str(e)}")

@router.delete("/{case_id}/trash", response_model=schemas.OperationResponse)
def empty_trash(case_id: int, db: Session = Depends(get_master_db)):
    """
    清空回收站
    """
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")

        # 获取案例目录下的trash文件夹
        from ..database import DATA_DIR
        import json
        from pathlib import Path
        case_dir = DATA_DIR / f"case_{case_id}"
        trash_dir = case_dir / "trash"

        deleted_count = 0

        if trash_dir.exists():
            # 删除所有删除记录文件和缩略图
            for record_file in trash_dir.glob("deleted_file_*.json"):
                try:
                    # 读取删除记录
                    with open(record_file, 'r', encoding='utf-8') as f:
                        file_record = json.load(f)

                    # 删除缩略图文件（如果存在）
                    if file_record.get('thumbnail_small_path'):
                        thumbnail_path = Path(file_record['thumbnail_small_path'])
                        if thumbnail_path.exists():
                            try:
                                thumbnail_path.unlink()
                            except Exception as e:
                                logger.warning(f"删除缩略图失败: {e}")

                    # 删除删除记录文件
                    record_file.unlink()
                    deleted_count += 1

                except Exception as e:
                    logger.warning(f"删除文件失败 {record_file}: {e}")

        logger.info(f"已清空回收站，删除 {deleted_count} 个文件")

        return schemas.OperationResponse(
            success=True,
            message=f"回收站已清空，删除了 {deleted_count} 个文件",
            data={"case_id": case_id, "deleted_count": deleted_count, "status": "trash_emptied"}
        )

    except Exception as e:
        logger.error(f"清空回收站失败: {e}")
        raise HTTPException(status_code=500, detail=f"清空回收站失败: {str(e)}")

@router.get("/{case_id}/custom-tags", response_model=List[schemas.CustomTag])
def get_case_custom_tags(case_id: int, db: Session = Depends(get_master_db)):
    """
    获取案例的自定义标签列表
    """
    try:
        # 验证案例是否存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")

        # 使用CustomTagManager获取标签
        from ..services.custom_tag_manager import CustomTagManager
        manager = CustomTagManager(case_id)
        custom_tags = manager.get_custom_tags()

        logger.info(f"✅ 获取案例 {case_id} 的自定义标签: {len(custom_tags)} 个")
        return custom_tags

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 获取案例 {case_id} 自定义标签失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取自定义标签失败: {str(e)}")

@router.post("/{case_id}/custom-tags", response_model=schemas.CustomTag)
def create_case_custom_tag(case_id: int, tag_data: schemas.CustomTagCreate, db: Session = Depends(get_master_db)):
    """
    为案例创建自定义标签
    """
    try:
        # 验证案例是否存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")

        # 使用CustomTagManager创建标签
        from ..services.custom_tag_manager import CustomTagManager
        manager = CustomTagManager(case_id)
        result = manager.create_custom_tag(tag_data)

        logger.info(f"✅ 为案例 {case_id} 创建自定义标签成功: {result.name}")
        return result

    except ValueError as e:
        logger.error(f"❌ 创建自定义标签参数错误: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 为案例 {case_id} 创建自定义标签失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"创建自定义标签失败: {str(e)}")

@router.put("/{case_id}/custom-tags/{tag_id}", response_model=schemas.CustomTag)
def update_case_custom_tag(case_id: int, tag_id: int, tag_data: schemas.CustomTagUpdate, db: Session = Depends(get_master_db)):
    """
    更新案例的自定义标签
    """
    try:
        # 验证案例是否存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")

        # 使用CustomTagManager更新标签
        from ..services.custom_tag_manager import CustomTagManager
        manager = CustomTagManager(case_id)
        result = manager.update_custom_tag(tag_id, tag_data)

        if not result:
            raise HTTPException(status_code=404, detail="标签不存在")

        logger.info(f"✅ 更新案例 {case_id} 的自定义标签 {tag_id} 成功")
        return result

    except ValueError as e:
        logger.error(f"❌ 更新自定义标签参数错误: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 更新案例 {case_id} 自定义标签 {tag_id} 失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"更新自定义标签失败: {str(e)}")

@router.delete("/{case_id}/custom-tags/{tag_id}", response_model=dict)
def delete_case_custom_tag(case_id: int, tag_id: int, db: Session = Depends(get_master_db)):
    """
    删除案例的自定义标签
    """
    try:
        # 验证案例是否存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")

        # 使用CustomTagManager删除标签
        from ..services.custom_tag_manager import CustomTagManager
        manager = CustomTagManager(case_id)
        success = manager.delete_custom_tag(tag_id)

        if not success:
            raise HTTPException(status_code=404, detail="标签不存在")

        logger.info(f"✅ 删除案例 {case_id} 的自定义标签 {tag_id} 成功")
        return {
            "success": True,
            "message": "自定义标签删除成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 删除案例 {case_id} 自定义标签 {tag_id} 失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"删除自定义标签失败: {str(e)}")

# ==================== 文件自定义标签相关API ====================

@router.post("/{case_id}/files/{file_id}/custom-tags")
def add_custom_tags_to_file(
    case_id: int,
    file_id: int,
    tag_names: List[str] = Body(...),
    db: Session = Depends(get_master_db)
):
    """为文件添加自定义标签"""
    try:
        # 验证案例是否存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")

        # 使用CustomTagManager为文件添加标签
        from ..services.custom_tag_manager import CustomTagManager
        manager = CustomTagManager(case_id)
        result = manager.add_tags_to_file(file_id, tag_names)

        logger.info(f"✅ 为文件 {file_id} 添加自定义标签成功: {tag_names}")
        return result

    except ValueError as e:
        logger.error(f"❌ 添加自定义标签参数错误: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 为文件 {file_id} 添加自定义标签失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="为文件添加自定义标签失败")

@router.delete("/{case_id}/files/{file_id}/custom-tags")
def remove_custom_tags_from_file(
    case_id: int,
    file_id: int,
    tag_names: List[str] = Body(...),
    db: Session = Depends(get_master_db)
):
    """从文件移除自定义标签"""
    try:
        # 验证案例是否存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")

        # 使用CustomTagManager从文件移除标签
        from ..services.custom_tag_manager import CustomTagManager
        manager = CustomTagManager(case_id)

        success_count = 0
        for tag_name in tag_names:
            if manager.remove_tag_from_file(file_id, tag_name):
                success_count += 1

        logger.info(f"✅ 从文件 {file_id} 移除自定义标签成功: {success_count}/{len(tag_names)}")
        return {
            "success": True,
            "message": f"成功移除 {success_count} 个标签",
            "removed_count": success_count,
            "total_count": len(tag_names)
        }

    except ValueError as e:
        logger.error(f"❌ 移除自定义标签参数错误: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 从文件 {file_id} 移除自定义标签失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="从文件移除自定义标签失败")

@router.get("/{case_id}/files/{file_id}/custom-tags", response_model=List[schemas.CustomTag])
def get_file_custom_tags(
    case_id: int,
    file_id: int,
    db: Session = Depends(get_master_db)
):
    """获取文件的自定义标签"""
    try:
        # 验证案例是否存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")

        # 使用CustomTagManager获取文件的标签
        from ..services.custom_tag_manager import CustomTagManager
        manager = CustomTagManager(case_id)
        custom_tags = manager.get_file_custom_tags(file_id)

        logger.info(f"✅ 获取文件 {file_id} 的自定义标签: {len(custom_tags)} 个")
        return custom_tags

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 获取文件 {file_id} 自定义标签失败: {e}")
        raise HTTPException(status_code=500, detail="获取文件自定义标签失败")
