# src/routers/tag_service_api.py
"""
标签服务API路由 - 使用TagService类
提供统一的标签管理功能
"""

from fastapi import APIRouter, Depends, HTTPException, Body, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
import logging

from ..database import get_master_db
from ..services.tag_service import TagService
from .. import schemas

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v2/tags", tags=["标签服务"])


@router.get("/{case_id}/tree")
def get_tag_tree(case_id: int, include_empty: bool = False, db: Session = Depends(get_master_db)):
    """
    获取案例的标签树结构
    
    使用TagService获取优化的标签树结构
    """
    try:
        tag_service = TagService(case_id, db)
        return tag_service.get_tag_tree(include_empty)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取标签树失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取标签树失败: {str(e)}")


@router.get("/{case_id}/search")
def search_tags(case_id: int, q: str, limit: int = 100, db: Session = Depends(get_master_db)):
    """
    搜索案例标签
    
    使用TagService搜索标签
    """
    try:
        tag_service = TagService(case_id, db)
        return tag_service.search_tags(q, limit)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"搜索标签失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索标签失败: {str(e)}")


@router.get("/{case_id}/files")
def get_files_by_tag(
    case_id: int,
    category: str,
    name: str,
    value: str = None,
    db: Session = Depends(get_master_db)
):
    """
    根据标签获取文件列表
    
    使用TagService根据标签获取文件
    """
    try:
        tag_service = TagService(case_id, db)
        return tag_service.get_files_by_tag(category, name, value)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"根据标签获取文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"根据标签获取文件失败: {str(e)}")


# ==================== 自定义标签相关API ====================

@router.get("/{case_id}/custom")
def get_custom_tags(case_id: int, db: Session = Depends(get_master_db)):
    """
    获取案例的所有自定义标签
    
    使用TagService获取自定义标签
    """
    try:
        tag_service = TagService(case_id, db)
        return tag_service.get_custom_tags()
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取自定义标签失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取自定义标签失败: {str(e)}")


@router.post("/{case_id}/custom", response_model=Dict[str, Any])
def create_custom_tag(
    case_id: int,
    tag_data: schemas.CustomTagCreate,
    db: Session = Depends(get_master_db)
):
    """
    创建自定义标签
    
    使用TagService创建自定义标签
    """
    try:
        tag_service = TagService(case_id, db)
        return tag_service.create_custom_tag(tag_data)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"创建自定义标签失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建自定义标签失败: {str(e)}")


@router.put("/{case_id}/custom/{tag_id}", response_model=Dict[str, Any])
def update_custom_tag(
    case_id: int,
    tag_id: int,
    tag_data: schemas.CustomTagUpdate,
    db: Session = Depends(get_master_db)
):
    """
    更新自定义标签
    
    使用TagService更新自定义标签
    """
    try:
        tag_service = TagService(case_id, db)
        return tag_service.update_custom_tag(tag_id, tag_data)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"更新自定义标签失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新自定义标签失败: {str(e)}")


@router.delete("/{case_id}/custom/{tag_id}")
def delete_custom_tag(
    case_id: int,
    tag_id: int,
    db: Session = Depends(get_master_db)
):
    """
    删除自定义标签
    
    使用TagService删除自定义标签
    """
    try:
        tag_service = TagService(case_id, db)
        success = tag_service.delete_custom_tag(tag_id)
        if not success:
            raise HTTPException(status_code=404, detail="标签不存在或删除失败")
        return {"success": True, "message": "标签已删除"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除自定义标签失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除自定义标签失败: {str(e)}")


@router.post("/{case_id}/files/{file_id}/custom")
def add_tags_to_file(
    case_id: int,
    file_id: int,
    tag_names: List[str] = Body(...),
    db: Session = Depends(get_master_db)
):
    """
    为文件添加标签
    
    使用TagService为文件添加标签
    """
    try:
        tag_service = TagService(case_id, db)
        return tag_service.add_tags_to_file(file_id, tag_names)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"为文件添加标签失败: {e}")
        raise HTTPException(status_code=500, detail=f"为文件添加标签失败: {str(e)}")


@router.delete("/{case_id}/files/{file_id}/custom/{tag_name}")
def remove_tag_from_file(
    case_id: int,
    file_id: int,
    tag_name: str,
    db: Session = Depends(get_master_db)
):
    """
    从文件中移除标签
    
    使用TagService从文件中移除标签
    """
    try:
        tag_service = TagService(case_id, db)
        success = tag_service.remove_tag_from_file(file_id, tag_name)
        if not success:
            raise HTTPException(status_code=404, detail="标签不存在或移除失败")
        return {"success": True, "message": "标签已移除"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"从文件中移除标签失败: {e}")
        raise HTTPException(status_code=500, detail=f"从文件中移除标签失败: {str(e)}")


@router.post("/{case_id}/custom/{tag_name}/batch")
def batch_tag_operation(
    case_id: int,
    tag_name: str,
    operation: schemas.BatchTagOperation,
    db: Session = Depends(get_master_db)
):
    """
    批量标签操作
    
    使用TagService进行批量标签操作
    """
    try:
        tag_service = TagService(case_id, db)
        return tag_service.batch_tag_operation(operation.file_ids, tag_name, operation.action)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"批量标签操作失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量标签操作失败: {str(e)}")


@router.get("/{case_id}/files/{file_id}/custom")
def get_file_custom_tags(
    case_id: int,
    file_id: int,
    db: Session = Depends(get_master_db)
):
    """
    获取文件的自定义标签
    
    使用TagService获取文件的自定义标签
    """
    try:
        tag_service = TagService(case_id, db)
        return tag_service.get_file_custom_tags(file_id)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取文件自定义标签失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取文件自定义标签失败: {str(e)}")