/* Project Novak - Spike A: react-mosaic 主题样式 */
/* 深色主题，符合 Project Novak 设计语言 */

/* ============================================================================
   Mosaic 全局主题变量
   ============================================================================ */

.mosaic-blueprint-theme {
  /* 背景色 */
  --mosaic-background: #111827; /* bg-gray-900 */
  --mosaic-control-background: #374151; /* bg-gray-700 */
  --mosaic-control-color: #f3f4f6; /* text-gray-100 */
  
  /* 分割线 */
  --mosaic-separator-color: #4b5563; /* bg-gray-600 */
  --mosaic-separator-hover-color: #6b7280; /* bg-gray-500 */
  
  /* 窗口样式 */
  --mosaic-window-background: #1f2937; /* bg-gray-800 */
  --mosaic-window-header-background: #1f2937; /* bg-gray-800 */
  --mosaic-window-header-color: #d1d5db; /* text-gray-300 */
  --mosaic-window-header-border: 1px solid #4b5563; /* border-gray-600 */
  --mosaic-window-body-border: 1px solid #4b5563; /* border-gray-600 */
  
  /* 拖拽预览 */
  --mosaic-drop-target-background: rgba(59, 130, 246, 0.3); /* bg-blue-500/30 */
  --mosaic-drop-target-border: 2px solid #3b82f6; /* border-blue-500 */
}

/* ============================================================================
   Mosaic 容器样式
   ============================================================================ */

.mosaic-blueprint-theme .mosaic {
  background-color: var(--mosaic-background);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* ============================================================================
   窗口标题栏样式
   ============================================================================ */

.mosaic-blueprint-theme .mosaic-window {
  background-color: var(--mosaic-window-background);
  border: var(--mosaic-window-body-border);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.mosaic-blueprint-theme .mosaic-window-toolbar {
  background-color: var(--mosaic-window-header-background);
  border-bottom: var(--mosaic-window-header-border);
  color: var(--mosaic-window-header-color);
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mosaic-blueprint-theme .mosaic-window-title {
  color: var(--mosaic-window-header-color);
  font-weight: 600;
  font-size: 14px;
  flex: 1;
  margin: 0;
  padding: 0;
}

.mosaic-blueprint-theme .mosaic-window-controls {
  display: flex;
  align-items: center;
  gap: 4px;
}

.mosaic-blueprint-theme .mosaic-window-body {
  background-color: var(--mosaic-window-background);
  height: calc(100% - 40px); /* 减去标题栏高度 */
  overflow: hidden;
}

/* ============================================================================
   控制按钮样式
   ============================================================================ */

.mosaic-blueprint-theme .mosaic-default-control {
  background-color: transparent;
  border: 1px solid #4b5563;
  color: var(--mosaic-control-color);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
}

.mosaic-blueprint-theme .mosaic-default-control:hover {
  background-color: var(--mosaic-control-background);
  border-color: #6b7280;
}

.mosaic-blueprint-theme .mosaic-default-control:active {
  background-color: #4b5563;
}

/* ============================================================================
   分割线样式
   ============================================================================ */

.mosaic-blueprint-theme .mosaic-split {
  background-color: var(--mosaic-separator-color);
  transition: background-color 0.2s ease;
}

.mosaic-blueprint-theme .mosaic-split:hover {
  background-color: var(--mosaic-separator-hover-color);
}

.mosaic-blueprint-theme .mosaic-split.-row {
  width: 4px;
  cursor: col-resize;
}

.mosaic-blueprint-theme .mosaic-split.-column {
  height: 4px;
  cursor: row-resize;
}

/* ============================================================================
   拖拽目标样式
   ============================================================================ */

.mosaic-blueprint-theme .mosaic-drop-target {
  background-color: var(--mosaic-drop-target-background);
  border: var(--mosaic-drop-target-border);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.mosaic-blueprint-theme .mosaic-drop-target.drop-target-hover {
  background-color: rgba(59, 130, 246, 0.4);
  border-color: #2563eb;
}

/* ============================================================================
   拖拽预览样式
   ============================================================================ */

.mosaic-blueprint-theme .mosaic-preview {
  background-color: rgba(59, 130, 246, 0.2);
  border: 2px dashed #3b82f6;
  border-radius: 8px;
}

/* ============================================================================
   空状态样式
   ============================================================================ */

.mosaic-blueprint-theme .mosaic-zero-state {
  background-color: var(--mosaic-background);
  color: var(--mosaic-control-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
}

/* ============================================================================
   响应式调整
   ============================================================================ */

@media (max-width: 768px) {
  .mosaic-blueprint-theme .mosaic-window-toolbar {
    padding: 6px 8px;
    font-size: 12px;
  }
  
  .mosaic-blueprint-theme .mosaic-window-title {
    font-size: 12px;
  }
  
  .mosaic-blueprint-theme .mosaic-default-control {
    min-width: 20px;
    height: 20px;
    font-size: 10px;
    padding: 2px 4px;
  }
  
  .mosaic-blueprint-theme .mosaic-split.-row {
    width: 6px;
  }
  
  .mosaic-blueprint-theme .mosaic-split.-column {
    height: 6px;
  }
}

/* ============================================================================
   动画效果
   ============================================================================ */

.mosaic-blueprint-theme .mosaic-window {
  transition: box-shadow 0.2s ease;
}

.mosaic-blueprint-theme .mosaic-window:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* ============================================================================
   自定义滚动条
   ============================================================================ */

.mosaic-blueprint-theme .mosaic-window-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.mosaic-blueprint-theme .mosaic-window-body::-webkit-scrollbar-track {
  background: #1f2937;
}

.mosaic-blueprint-theme .mosaic-window-body::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 3px;
}

.mosaic-blueprint-theme .mosaic-window-body::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}
