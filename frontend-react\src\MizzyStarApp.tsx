import { useState } from 'react';
import { Button } from '@/components/ui';
import { MizzyStarLayout } from '@/components/layout';
import { CatalogPanel, GalleryPanel, WorkbenchPanel, InfoPanel } from '@/components/panels';
import { useCases, useFiles, useTags, useDeleteFile } from '@/hooks';

/**
 * MizzyStar 新星计划 - 主应用组件
 *
 * 基于原始蓝图重新构建的全新应用界面：
 * 1. 四栏可调整大小布局
 * 2. MizzyStar 配色方案
 * 3. 完整的数据流和状态管理
 * 4. 现代化的用户体验
 */
function MizzyStarApp() {
  // ========================================
  // 简化的本地状态管理 (临时解决方案)
  // ========================================
  const [showDevControls, setShowDevControls] = useState(true);
  const [showCatalogPanel, setShowCatalogPanel] = useState(true);
  const [showInfoPanel, setShowInfoPanel] = useState(true);
  const [showWorkbench, setShowWorkbench] = useState(false);
  const [isFullscreenGallery, setIsFullscreenGallery] = useState(false);
  const [selectedCaseId, setSelectedCaseId] = useState<number | null>(null);
  const [selectedFileIds, setSelectedFileIds] = useState<number[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeWorkbench, setActiveWorkbench] = useState('clipboard');

  // 面板尺寸
  const catalogPanelWidth = 280;
  const infoPanelWidth = 320;
  const workbenchHeight = 200;

  // ========================================
  // TanStack Query 数据获取
  // ========================================
  const { data: cases, isLoading: casesLoading, error: casesError } = useCases();

  // 当前选中的案例（默认选择第一个）
  const currentCaseId = selectedCaseId || cases?.[0]?.id || null;

  // 获取文件列表（基于当前案例和搜索条件）
  const { data: filesData, isLoading: filesLoading, error: filesError } = useFiles({
    case_id: currentCaseId || undefined,
    search: searchQuery || undefined,
  });

  // 获取标签列表
  const { isLoading: tagsLoading, error: tagsError } = useTags(currentCaseId || undefined);

  // 删除文件 mutation
  const deleteFileMutation = useDeleteFile();

  // ========================================
  // 数据处理和错误处理
  // ========================================

  // 处理加载状态
  const isLoading = casesLoading || filesLoading || tagsLoading;

  // 处理错误状态
  const hasError = casesError || filesError || tagsError;

  // 获取文件列表
  const files = filesData?.files || [];

  // 获取当前选中的文件
  const selectedFile = files.find(file =>
    selectedFileIds.includes(file.id)
  );

  // ========================================
  // 事件处理函数
  // ========================================

  const handleFileSelect = (fileId: number, selected: boolean) => {
    if (selected) {
      setSelectedFileIds(prev => [...prev, fileId]);
    } else {
      setSelectedFileIds(prev => prev.filter(id => id !== fileId));
    }
  };

  const handleFileDoubleClick = (file: any) => {
    console.log('预览文件:', file.fileName);
    // 这里可以添加文件预览逻辑
  };

  const handleFileDelete = (file: any) => {
    // 确认删除对话框
    const confirmed = window.confirm(`确定要删除文件 "${file.fileName}" 吗？\n\n此操作将把文件移动到回收站。`);

    if (confirmed && currentCaseId) {
      console.log('🗑️ 删除文件:', file.fileName, 'from case', currentCaseId);

      deleteFileMutation.mutate(
        { caseId: currentCaseId, fileId: file.id },
        {
          onSuccess: () => {
            console.log('✅ 文件删除成功:', file.fileName);
            // 如果删除的是当前选中的文件，清除选择
            if (selectedFileIds.includes(file.id)) {
              setSelectedFileIds(prev => prev.filter(id => id !== file.id));
            }
          },
          onError: (error) => {
            console.error('❌ 文件删除失败:', error);
            alert(`删除文件失败: ${error.message}`);
          },
        }
      );
    }
  };

  // 布局切换控制
  const togglePanel = (panel: 'catalog' | 'workbench' | 'info' | 'fullscreen') => {
    switch (panel) {
      case 'catalog':
        setShowCatalogPanel(prev => !prev);
        break;
      case 'workbench':
        setShowWorkbench(prev => !prev);
        if (!showWorkbench) {
          setActiveWorkbench('clipboard');
        }
        break;
      case 'info':
        setShowInfoPanel(prev => !prev);
        break;
      case 'fullscreen':
        setIsFullscreenGallery(prev => !prev);
        break;
    }
  };

  // ========================================
  // 错误和加载状态处理
  // ========================================

  if (hasError) {
    return (
      <div className="h-screen w-screen bg-[#191012] flex items-center justify-center">
        <div className="text-center text-[#A49F9A]">
          <h1 className="text-2xl font-bold mb-4">⚠️ 加载错误</h1>
          <p className="text-[#6B6B6B] mb-4">
            {casesError?.message || filesError?.message || tagsError?.message || '未知错误'}
          </p>
          <Button onClick={() => window.location.reload()}>
            重新加载
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen w-screen bg-[#191012]">
      {/* 开发控制面板 - 仅在非全屏模式显示 */}
      {!isFullscreenGallery && showDevControls && (
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-50 bg-[#040709] border border-[#2A2A2A] rounded-lg p-2 shadow-lg">
          <div className="flex gap-2">
            <Button
              variant={showCatalogPanel ? "primary" : "outline"}
              size="sm"
              onClick={() => togglePanel('catalog')}
            >
              📚 目录栏
            </Button>
            <Button
              variant={showWorkbench ? "primary" : "outline"}
              size="sm"
              onClick={() => togglePanel('workbench')}
            >
              🛠️ 工作台
            </Button>
            <Button
              variant={showInfoPanel ? "primary" : "outline"}
              size="sm"
              onClick={() => togglePanel('info')}
            >
              📄 信息栏
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => togglePanel('fullscreen')}
            >
              🔍 全屏
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setShowDevControls(false)}
            >
              ✕ 隐藏
            </Button>
            {isLoading && (
              <div className="flex items-center gap-2 px-2">
                <div className="w-4 h-4 border-2 border-[#E8E3E0] border-t-transparent rounded-full animate-spin" />
                <span className="text-sm text-[#6B6B6B]">加载中...</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* MizzyStar 主布局 */}
      <MizzyStarLayout
        catalogPanel={
          <CatalogPanel
            currentLibraryName={cases?.find(c => c.id === currentCaseId)?.case_name || "加载中..."}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            tagCategories={[]} // TODO: 将标签数据转换为分类格式
          />
        }
        galleryPanel={
          <GalleryPanel
            files={files.map(file => ({
              id: file.id,
              fileName: file.file_name,
              filePath: file.file_path,
              fileType: file.file_type,
              fileSize: file.file_size,
              width: file.width || undefined,
              height: file.height || undefined,
              thumbnailPath: file.thumbnail_small_path || undefined,
            }))}
            selectedFileIds={selectedFileIds}
            layout="grid"
            zoomLevel={50}
            searchQuery={searchQuery}
            showFileName={true}
            showFileInfo={false}
            loading={filesLoading}
            onFileSelect={handleFileSelect}
            onFileDoubleClick={handleFileDoubleClick}
            onFileDelete={handleFileDelete}
            onSearchChange={setSearchQuery}
          />
        }
        workbenchPanel={
          <WorkbenchPanel
            activeWorkbench={activeWorkbench}
            onWorkbenchChange={setActiveWorkbench}
          />
        }
        infoPanel={
          <InfoPanel
            selectedFile={selectedFile ? {
              id: selectedFile.id,
              fileName: selectedFile.file_name,
              filePath: selectedFile.file_path,
              fileType: selectedFile.file_type,
              fileSize: selectedFile.file_size,
              width: selectedFile.width || undefined,
              height: selectedFile.height || undefined,
              thumbnailPath: selectedFile.thumbnail_small_path || undefined,
              createdAt: selectedFile.created_at,
              tags: selectedFile.tags?.tags || undefined,
            } : undefined}
            selectedCount={selectedFileIds.length}
          />
        }
        showCatalogPanel={showCatalogPanel}
        showWorkbench={showWorkbench}
        showInfoPanel={showInfoPanel}
        isFullscreenGallery={isFullscreenGallery}
        catalogPanelWidth={catalogPanelWidth}
        infoPanelWidth={infoPanelWidth}
        workbenchHeight={workbenchHeight}
      />

      {/* 全屏模式退出按钮 */}
      {isFullscreenGallery && (
        <Button
          variant="primary"
          size="sm"
          className="absolute top-4 right-4 z-50"
          onClick={() => setIsFullscreenGallery(false)}
        >
          ✕ 退出全屏
        </Button>
      )}

      {/* 隐藏的开发控制按钮 */}
      {!showDevControls && !isFullscreenGallery && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-4 right-4 z-50 opacity-50 hover:opacity-100"
          onClick={() => setShowDevControls(true)}
        >
          ⚙️
        </Button>
      )}
    </div>
  );
}

export default MizzyStarApp;
