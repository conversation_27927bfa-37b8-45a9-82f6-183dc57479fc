# src/crud/trash_crud.py
"""
回收站相关的CRUD操作 - PostgreSQL单一数据库架构
"""
import os
from typing import List, Optional
from datetime import datetime
from sqlalchemy.orm import Session
import logging

from .. import models
from ..database import restore_case_from_trash
from .case_crud import restore_case, permanently_delete_case

logger = logging.getLogger(__name__)


def get_trash_cases(db: Session, skip: int = 0, limit: int = 100) -> List[models.Case]:
    """
    获取回收站中的所有案例 - PostgreSQL单一数据库架构
    """
    try:
        trash_cases = db.query(models.Case)\
                     .filter(models.Case.status == models.CaseStatus.DELETED)\
                     .order_by(models.Case.deleted_at.desc())\
                     .offset(skip)\
                     .limit(limit)\
                     .all()

        logger.info(f"✅ 获取回收站案例: {len(trash_cases)} 个案例")
        return trash_cases

    except Exception as e:
        logger.error(f"❌ 获取回收站案例失败: {e}")
        return []


def get_trash_case(db: Session, case_id: int) -> Optional[models.Case]:
    """
    获取回收站中的特定案例 - PostgreSQL单一数据库架构
    """
    try:
        db_case = db.query(models.Case)\
                  .filter(models.Case.id == case_id)\
                  .filter(models.Case.status == models.CaseStatus.DELETED)\
                  .first()

        if db_case:
            logger.info(f"✅ 获取回收站案例: ID={case_id}")
        else:
            logger.warning(f"⚠️ 回收站案例不存在: ID={case_id}")

        return db_case

    except Exception as e:
        logger.error(f"❌ 获取回收站案例失败: ID={case_id}, 错误={e}")
        return None


# restore_case和permanently_delete_case函数已在case_crud.py中实现，这里不需要重复

def empty_trash(db: Session) -> dict:
    """
    清空回收站 - PostgreSQL单一数据库架构
    """
    result = {
        'deleted': 0,
        'failed': 0,
        'errors': []
    }

    try:
        # 获取所有已删除的案例
        trash_cases = get_trash_cases(db)

        for case in trash_cases:
            if permanently_delete_case(db, case.id):
                result['deleted'] += 1
            else:
                result['failed'] += 1
                result['errors'].append(f"永久删除案例 {case.id} 失败")

        logger.info(f"✅ 清空回收站完成: 删除={result['deleted']}, 失败={result['failed']}")
        return result

    except Exception as e:
        logger.error(f"❌ 清空回收站失败: {e}")
        result['failed'] = len(get_trash_cases(db))
        result['errors'].append(str(e))
        return result