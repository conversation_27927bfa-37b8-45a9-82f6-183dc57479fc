{"version": 3, "file": "pkg.js", "sourceRoot": "", "sources": ["../../src/targets/pkg.ts"], "names": [], "mappings": ";;;AAAA,+CAAqD;AACrD,4CAAgD;AAEhD,mDAA2F;AAC3F,6CAA4D;AAC5D,0CAAyD;AACzD,6BAA4B;AAC5B,wCAAqD;AACrD,yDAAgE;AAChE,kCAAgC;AAEhC,2BAAgC;AAEhC,MAAM,QAAQ,GAAG,wBAAwB,CAAA;AAEzC,kEAAkE;AAClE,wEAAwE;AACxE,8FAA8F;AAC9F,2FAA2F;AAC3F,MAAa,SAAU,SAAQ,aAAM;IAQnC,YACmB,QAAqB,EAC7B,MAAc;QAEvB,KAAK,CAAC,KAAK,CAAC,CAAA;QAHK,aAAQ,GAAR,QAAQ,CAAa;QAC7B,WAAM,GAAN,MAAM,CAAQ;QAThB,YAAO,GAAe;YAC7B,aAAa,EAAE,IAAI;YACnB,oBAAoB,EAAE,IAAI;YAC1B,kBAAkB,EAAE,IAAI;YACxB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG;SAC5B,CAAA;IAOD,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,OAAe,EAAE,IAAU;QACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC5B,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAA;QAEhC,8IAA8I;QAC9I,MAAM,YAAY,GAAG,QAAQ,CAAC,yBAAyB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;QAC7E,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;QAEzD,MAAM,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC;YAC3C,qBAAqB,EAAE,KAAK;YAC5B,IAAI,EAAE,YAAY;YAClB,IAAI;SACL,CAAC,CAAA;QAEF,MAAM,YAAY,GAAG,CAAC,MAAM,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,YAAY,CAAA;QAExE,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAA;QAC7B,kJAAkJ;QAClJ,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAA;QAE7D,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,IAAA,kCAAwB,EAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAA;QAC5F,MAAM,yBAAyB,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,IAAA,kCAAwB,EAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAA;QACvG,MAAM,QAAQ,GAAG,CACf,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,IAAA,0BAAY,EAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC,4BAA4B,CAAC,QAAQ,EAAE,YAAY,CAAC;YACxG,IAAI,CAAC,kCAAkC,CAAC,YAAY,EAAE,OAAO,CAAC;YAC9D,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,yBAAyB,EAAE,gBAAgB,CAAC;SACjF,CAAC,CACH,CAAC,CAAC,CAAC,CAAA;QAEJ,IAAI,QAAQ,IAAI,IAAI,IAAI,QAAQ,CAAC,gBAAgB,EAAE,CAAC;YAClD,MAAM,IAAI,KAAK,CAAC,sBAAsB,QAAQ,gFAAgF,CAAC,CAAA;QACjI,CAAC;QAED,MAAM,IAAI,GAAG,uBAAuB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAA;QAC5D,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAA;QACzC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QACvB,IAAA,kBAAG,EAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,GAAI,EAAU,CAAC,CAAC,CAAA;QAC1D,MAAM,IAAA,mBAAI,EAAC,cAAc,EAAE,IAAI,EAAE;YAC/B,GAAG,EAAE,SAAS;SACf,CAAC,CAAA;QACF,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,IAAA,iBAAM,EAAC,gBAAgB,CAAC,EAAE,IAAA,iBAAM,EAAC,YAAY,CAAC,CAAC,CAAC,CAAA;QAEnE,MAAM,QAAQ,CAAC,uBAAuB,CAAC,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,uBAAuB,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAA;IAC/H,CAAC;IAEO,KAAK,CAAC,kCAAkC,CAAC,YAAoB,EAAE,OAAe;QACpF,MAAM,IAAA,mBAAI,EAAC,cAAc,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE,OAAO,EAAE,YAAY,CAAC,EAAE;YACjF,GAAG,EAAE,IAAI,CAAC,MAAM;SACjB,CAAC,CAAA;QAEF,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC5B,IAAI,QAAQ,GAAG,MAAM,IAAA,mBAAQ,EAAC,YAAY,EAAE,OAAO,CAAC,CAAA;QAEpD,IAAI,OAAO,CAAC,SAAS,IAAI,IAAI,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChE,MAAM,YAAY,GAAG,oBAAoB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,4BAA4B,CAAA;YAC7F,MAAM,UAAU,GAAG,gEAAgE,CAAA;YACnF,IAAI,gBAAgB,GAAG,EAAE,CAAA;YACzB,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAChC,gBAAgB,IAAI,wBAAwB,KAAK,OAAO,CAAA;YAC1D,CAAC,CAAC,CAAA;YACF,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,yBAAyB,EAAE,GAAG,YAAY,GAAG,gBAAgB,GAAG,UAAU,EAAE,CAAC,CAAA;QAC3G,CAAC;QAED,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC,yBAAyB,CAAC,CAAA;QACnE,QAAQ;YACN,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,CAAC;gBAClC,iCAAiC,OAAO,CAAC,aAAa,6BAA6B,OAAO,CAAC,oBAAoB,yBAAyB,OAAO,CAAC,kBAAkB,QAAQ;gBAC1K,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,CAAA;QAEjC,IAAI,OAAO,CAAC,UAAU,IAAI,IAAI,EAAE,CAAC;YAC/B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;YAC3E,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;gBACvB,MAAM,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,SAAS,IAAI,QAAQ,CAAA;gBAC1D,uCAAuC;gBACvC,MAAM,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,IAAI,OAAO,CAAA;gBACrD,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,CAAC,GAAG,yBAAyB,UAAU,gBAAgB,SAAS,cAAc,OAAO,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,CAAA;gBAC1K,QAAQ;oBACN,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,CAAC,GAAG,kCAAkC,UAAU,gBAAgB,SAAS,cAAc,OAAO,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,CAAA;YAC5K,CAAC;QACH,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QAChE,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;YACpB,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,CAAC,GAAG,sBAAsB,OAAO,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,CAAA;QACxH,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,IAAA,oCAA0B,EAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;QAChF,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;YACpB,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,CAAC,GAAG,sBAAsB,OAAO,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,CAAA;QACxH,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;QACtE,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;YACvB,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,CAAC,GAAG,yBAAyB,UAAU,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,CAAA;QAC9H,CAAC;QAED,IAAA,oBAAK,EAAC,QAAQ,CAAC,CAAA;QACf,MAAM,IAAA,oBAAS,EAAC,YAAY,EAAE,QAAQ,CAAC,CAAA;IACzC,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,OAAe,EAAE,sBAA8B,EAAE,iBAAyB;;QAC5G,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QAEtC,2CAA2C;QAC3C,MAAM,IAAA,mBAAI,EAAC,UAAU,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,sBAAsB,CAAC,CAAC,CAAA;QAEjF,6BAA6B;QAC7B,MAAM,SAAS,GAAG,CAAC,MAAM,IAAA,oCAAuB,EAAa,CAAC,cAAc,EAAE,IAAI,EAAE,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CACrH,CAAC,EAAO,EAAE,EAAE,CAAC,EAAE,CAAC,sBAAsB,KAAK,eAAe,CAC3D,CAAA;QACD,IAAI,WAAW,GAAQ,EAAE,CAAA;QACzB,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzB,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;YAE1B,+DAA+D;YAC/D,4FAA4F;YAC5F,sEAAsE;YACtE,OAAO,WAAW,CAAC,YAAY,CAAA;YAE/B,IAAI,OAAO,CAAC,aAAa,IAAI,IAAI,EAAE,CAAC;gBAClC,WAAW,CAAC,mBAAmB,GAAG,OAAO,CAAC,aAAa,CAAA;YACzD,CAAC;YAED,IAAI,OAAO,CAAC,gBAAgB,IAAI,IAAI,EAAE,CAAC;gBACrC,WAAW,CAAC,sBAAsB,GAAG,OAAO,CAAC,gBAAgB,CAAA;YAC/D,CAAC;YAED,IAAI,OAAO,CAAC,mBAAmB,IAAI,IAAI,EAAE,CAAC;gBACxC,WAAW,CAAC,yBAAyB,GAAG,OAAO,CAAC,mBAAmB,CAAA;YACrE,CAAC;YAED,IAAI,OAAO,CAAC,eAAe,IAAI,IAAI,EAAE,CAAC;gBACpC,WAAW,CAAC,qBAAqB,GAAG,OAAO,CAAC,eAAe,CAAA;YAC7D,CAAC;QACH,CAAC;QAED,wBAAwB;QACxB,MAAM,IAAI,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,EAAE,mBAAmB,EAAE,sBAAsB,CAAC,CAAA;QAExH,IAAA,kBAAG,EAAC,IAAI,CAAC,OAAO,CAAC,eAAe,IAAI,eAAe,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC,CAAA;QAE/F,2DAA2D;QAC3D,MAAM,UAAU;QACd,4BAA4B;QAC5B,OAAO,CAAC,OAAO,IAAI,IAAI;YACrB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,OAAO,CAAC;YACrE,CAAC,CAAC,uDAAuD;gBACvD,OAAO,CAAC,OAAO,KAAK,IAAI;oBACxB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,aAAa,CAAC;oBAChE,CAAC,CAAC,IAAI,CAAA;QACZ,IAAI,UAAU,KAAI,MAAA,CAAC,MAAM,IAAA,eAAU,EAAC,UAAU,CAAC,CAAC,0CAAE,WAAW,EAAE,CAAA,EAAE,CAAC;YAChE,MAAM,WAAW,GAAG,IAAA,gBAAW,EAAC,UAAU,CAAC,CAAA;YAC3C,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACzB,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;oBAChC,WAAW,CAAC,0BAA0B,GAAG,IAAI,CAAA;gBAC/C,CAAC;qBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;oBACxC,WAAW,CAAC,2BAA2B,GAAG,IAAI,CAAA;gBAChD,CAAC;YACH,CAAC,CAAC,CAAA;YACF,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAA;QACpC,CAAC;QACD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzB,MAAM,IAAA,0CAA6B,EAAC,CAAC,cAAc,CAAC,EAAE,EAAE,CAAC,sBAAsB,CAAC,EAAE,SAAS,EAAE,CAAC,CAAA;QAChG,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;QAE5B,MAAM,IAAA,mBAAI,EAAC,UAAU,EAAE,IAAI,CAAC,CAAA;IAC9B,CAAC;CACF;AA3LD,8BA2LC;AAED,SAAgB,uBAAuB,CAAC,QAAyB,EAAE,QAAmC;IACpG,MAAM,IAAI,GAAkB,EAAE,CAAA;IAC9B,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAK,CAAC,CAAA;QACnC,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAA;QACnC,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AATD,0DASC", "sourcesContent": ["import { Arch, debug, exec, use } from \"builder-util\"\nimport { statOrNull } from \"builder-util/out/fs\"\nimport { PkgOptions } from \"../options/pkgOptions\"\nimport { executeAppBuilderAndWrite<PERSON>son, executeAppBuilderAs<PERSON>son } from \"../util/appBuilder\"\nimport { getNotLocalizedLicenseFile } from \"../util/license\"\nimport { readFile, unlink, writeFile } from \"fs/promises\"\nimport * as path from \"path\"\nimport { filterCFBundleIdentifier } from \"../appInfo\"\nimport { findIdentity, Identity } from \"../codeSign/macCodeSign\"\nimport { Target } from \"../core\"\nimport MacPackager from \"../macPackager\"\nimport { readdirSync } from \"fs\"\n\nconst certType = \"Developer ID Installer\"\n\n// http://www.shanekirk.com/2013/10/creating-flat-packages-in-osx/\n// to use --scripts, we must build .app bundle separately using pkgbuild\n// productbuild --scripts doesn't work (because scripts in this case not added to our package)\n// https://github.com/electron-userland/@electron/osx-sign/issues/96#issuecomment-274986942\nexport class PkgTarget extends Target {\n  readonly options: PkgOptions = {\n    allowAnywhere: true,\n    allowCurrentUserHome: true,\n    allowRootDirectory: true,\n    ...this.packager.config.pkg,\n  }\n\n  constructor(\n    private readonly packager: MacPackager,\n    readonly outDir: string\n  ) {\n    super(\"pkg\")\n  }\n\n  async build(appPath: string, arch: Arch): Promise<any> {\n    const packager = this.packager\n    const options = this.options\n    const appInfo = packager.appInfo\n\n    // pkg doesn't like not ASCII symbols (Could not open package to list files: /Volumes/test/t-gIjdGK/test-project-0/dist/Test App ßW-1.1.0.pkg)\n    const artifactName = packager.expandArtifactNamePattern(options, \"pkg\", arch)\n    const artifactPath = path.join(this.outDir, artifactName)\n\n    await packager.info.callArtifactBuildStarted({\n      targetPresentableName: \"pkg\",\n      file: artifactPath,\n      arch,\n    })\n\n    const keychainFile = (await packager.codeSigningInfo.value).keychainFile\n\n    const appOutDir = this.outDir\n    // https://developer.apple.com/library/content/documentation/DeveloperTools/Reference/DistributionDefinitionRef/Chapters/Distribution_XML_Ref.html\n    const distInfoFile = path.join(appOutDir, \"distribution.xml\")\n\n    const innerPackageFile = path.join(appOutDir, `${filterCFBundleIdentifier(appInfo.id)}.pkg`)\n    const componentPropertyListFile = path.join(appOutDir, `${filterCFBundleIdentifier(appInfo.id)}.plist`)\n    const identity = (\n      await Promise.all([\n        findIdentity(certType, options.identity || packager.platformSpecificBuildOptions.identity, keychainFile),\n        this.customizeDistributionConfiguration(distInfoFile, appPath),\n        this.buildComponentPackage(appPath, componentPropertyListFile, innerPackageFile),\n      ])\n    )[0]\n\n    if (identity == null && packager.forceCodeSigning) {\n      throw new Error(`Cannot find valid \"${certType}\" to sign standalone installer, please see https://electron.build/code-signing`)\n    }\n\n    const args = prepareProductBuildArgs(identity, keychainFile)\n    args.push(\"--distribution\", distInfoFile)\n    args.push(artifactPath)\n    use(options.productbuild, it => args.push(...(it as any)))\n    await exec(\"productbuild\", args, {\n      cwd: appOutDir,\n    })\n    await Promise.all([unlink(innerPackageFile), unlink(distInfoFile)])\n\n    await packager.dispatchArtifactCreated(artifactPath, this, arch, packager.computeSafeArtifactName(artifactName, \"pkg\", arch))\n  }\n\n  private async customizeDistributionConfiguration(distInfoFile: string, appPath: string) {\n    await exec(\"productbuild\", [\"--synthesize\", \"--component\", appPath, distInfoFile], {\n      cwd: this.outDir,\n    })\n\n    const options = this.options\n    let distInfo = await readFile(distInfoFile, \"utf-8\")\n\n    if (options.mustClose != null && options.mustClose.length !== 0) {\n      const startContent = `    <pkg-ref id=\"${this.packager.appInfo.id}\">\\n        <must-close>\\n`\n      const endContent = \"        </must-close>\\n    </pkg-ref>\\n</installer-gui-script>\"\n      let mustCloseContent = \"\"\n      options.mustClose.forEach(appId => {\n        mustCloseContent += `            <app id=\"${appId}\"/>\\n`\n      })\n      distInfo = distInfo.replace(\"</installer-gui-script>\", `${startContent}${mustCloseContent}${endContent}`)\n    }\n\n    const insertIndex = distInfo.lastIndexOf(\"</installer-gui-script>\")\n    distInfo =\n      distInfo.substring(0, insertIndex) +\n      `    <domains enable_anywhere=\"${options.allowAnywhere}\" enable_currentUserHome=\"${options.allowCurrentUserHome}\" enable_localSystem=\"${options.allowRootDirectory}\" />\\n` +\n      distInfo.substring(insertIndex)\n\n    if (options.background != null) {\n      const background = await this.packager.getResource(options.background.file)\n      if (background != null) {\n        const alignment = options.background.alignment || \"center\"\n        // noinspection SpellCheckingInspection\n        const scaling = options.background.scaling || \"tofit\"\n        distInfo = distInfo.substring(0, insertIndex) + `    <background file=\"${background}\" alignment=\"${alignment}\" scaling=\"${scaling}\"/>\\n` + distInfo.substring(insertIndex)\n        distInfo =\n          distInfo.substring(0, insertIndex) + `    <background-darkAqua file=\"${background}\" alignment=\"${alignment}\" scaling=\"${scaling}\"/>\\n` + distInfo.substring(insertIndex)\n      }\n    }\n\n    const welcome = await this.packager.getResource(options.welcome)\n    if (welcome != null) {\n      distInfo = distInfo.substring(0, insertIndex) + `    <welcome file=\"${welcome}\"/>\\n` + distInfo.substring(insertIndex)\n    }\n\n    const license = await getNotLocalizedLicenseFile(options.license, this.packager)\n    if (license != null) {\n      distInfo = distInfo.substring(0, insertIndex) + `    <license file=\"${license}\"/>\\n` + distInfo.substring(insertIndex)\n    }\n\n    const conclusion = await this.packager.getResource(options.conclusion)\n    if (conclusion != null) {\n      distInfo = distInfo.substring(0, insertIndex) + `    <conclusion file=\"${conclusion}\"/>\\n` + distInfo.substring(insertIndex)\n    }\n\n    debug(distInfo)\n    await writeFile(distInfoFile, distInfo)\n  }\n\n  private async buildComponentPackage(appPath: string, propertyListOutputFile: string, packageOutputFile: string) {\n    const options = this.options\n    const rootPath = path.dirname(appPath)\n\n    // first produce a component plist template\n    await exec(\"pkgbuild\", [\"--analyze\", \"--root\", rootPath, propertyListOutputFile])\n\n    // process the template plist\n    const plistInfo = (await executeAppBuilderAsJson<Array<any>>([\"decode-plist\", \"-f\", propertyListOutputFile]))[0].filter(\n      (it: any) => it.RootRelativeBundlePath !== \"Electron.dSYM\"\n    )\n    let packageInfo: any = {}\n    if (plistInfo.length > 0) {\n      packageInfo = plistInfo[0]\n\n      // ChildBundles lists all of electron binaries within the .app.\n      // There is no particular reason for removing that key, except to be as close as possible to\n      // the PackageInfo generated by previous versions of electron-builder.\n      delete packageInfo.ChildBundles\n\n      if (options.isRelocatable != null) {\n        packageInfo.BundleIsRelocatable = options.isRelocatable\n      }\n\n      if (options.isVersionChecked != null) {\n        packageInfo.BundleIsVersionChecked = options.isVersionChecked\n      }\n\n      if (options.hasStrictIdentifier != null) {\n        packageInfo.BundleHasStrictIdentifier = options.hasStrictIdentifier\n      }\n\n      if (options.overwriteAction != null) {\n        packageInfo.BundleOverwriteAction = options.overwriteAction\n      }\n    }\n\n    // now build the package\n    const args = [\"--root\", rootPath, \"--identifier\", this.packager.appInfo.id, \"--component-plist\", propertyListOutputFile]\n\n    use(this.options.installLocation || \"/Applications\", it => args.push(\"--install-location\", it))\n\n    // nasty nested ternary-statement, probably should optimize\n    const scriptsDir =\n      // user-provided scripts dir\n      options.scripts != null\n        ? path.resolve(this.packager.info.buildResourcesDir, options.scripts)\n        : // fallback to default unless user explicitly sets null\n          options.scripts !== null\n          ? path.join(this.packager.info.buildResourcesDir, \"pkg-scripts\")\n          : null\n    if (scriptsDir && (await statOrNull(scriptsDir))?.isDirectory()) {\n      const dirContents = readdirSync(scriptsDir)\n      dirContents.forEach(name => {\n        if (name.includes(\"preinstall\")) {\n          packageInfo.BundlePreInstallScriptPath = name\n        } else if (name.includes(\"postinstall\")) {\n          packageInfo.BundlePostInstallScriptPath = name\n        }\n      })\n      args.push(\"--scripts\", scriptsDir)\n    }\n    if (plistInfo.length > 0) {\n      await executeAppBuilderAndWriteJson([\"encode-plist\"], { [propertyListOutputFile]: plistInfo })\n    }\n\n    args.push(packageOutputFile)\n\n    await exec(\"pkgbuild\", args)\n  }\n}\n\nexport function prepareProductBuildArgs(identity: Identity | null, keychain: string | null | undefined): Array<string> {\n  const args: Array<string> = []\n  if (identity != null) {\n    args.push(\"--sign\", identity.hash!)\n    if (keychain != null) {\n      args.push(\"--keychain\", keychain)\n    }\n  }\n  return args\n}\n"]}