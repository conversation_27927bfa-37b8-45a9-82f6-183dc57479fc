# tests/unit/test_crud_files.py
"""
文件CRUD操作的单元测试
"""
import os
from sqlalchemy.orm import Session

from src.crud.file_crud import create_file_for_case, get_files_for_case, get_file, delete_file
from src.crud.case_crud import create_case
from src import schemas, models


class TestFileCRUD:
    """文件CRUD操作测试类"""
    
    def test_create_file_for_case_success(self, test_db: Session, sample_file_data: dict, test_data_dir: str):
        """测试成功为案例创建文件"""
        # 临时修改DATA_DIR
        import src.database
        original_data_dir = src.database.DATA_DIR
        src.database.DATA_DIR = test_data_dir
        
        try:
            # 首先创建一个案例
            case_create = schemas.CaseCreate(case_name="测试案例", description="测试描述")
            created_case = create_case(test_db, case_create)
            
            # 为案例创建文件
            file_create = schemas.FileCreate(**sample_file_data)
            created_file = create_file_for_case(test_db, created_case.id, file_create)  # type: ignore
            
            # 验证文件创建成功
            assert created_file is not None
            assert created_file.file_name == sample_file_data["file_name"]
            assert created_file.file_type == sample_file_data["file_type"]
            assert created_file.width == sample_file_data["width"]
            assert created_file.height == sample_file_data["height"]
            
        finally:
            src.database.DATA_DIR = original_data_dir
    
    def test_create_file_for_nonexistent_case(self, test_db: Session, sample_file_data: dict):
        """测试为不存在的案例创建文件"""
        file_create = schemas.FileCreate(**sample_file_data)
        created_file = create_file_for_case(test_db, 999, file_create)
        
        # 应该返回None，因为案例不存在
        assert created_file is None
    
    def test_get_files_for_case_empty(self, test_db: Session, test_data_dir: str):
        """测试获取空文件列表"""
        import src.database
        original_data_dir = src.database.DATA_DIR
        src.database.DATA_DIR = test_data_dir
        
        try:
            # 创建案例但不添加文件
            case_create = schemas.CaseCreate(case_name="测试案例_空文件列表", description="测试空文件列表")
            created_case = create_case(test_db, case_create)
            
            # 获取文件列表
            files = get_files_for_case(test_db, created_case.id)  # type: ignore
            assert files == []
            
        finally:
            src.database.DATA_DIR = original_data_dir
    
    def test_get_files_for_case_with_data(self, test_db: Session, sample_file_data: dict, test_data_dir: str):
        """测试获取有数据的文件列表"""
        import src.database
        original_data_dir = src.database.DATA_DIR
        src.database.DATA_DIR = test_data_dir
        
        try:
            # 创建案例
            case_create = schemas.CaseCreate(case_name="测试案例_多文件", description="测试多文件列表")
            created_case = create_case(test_db, case_create)
            
            # 创建多个文件
            file_create1 = schemas.FileCreate(**sample_file_data)
            file_create2 = schemas.FileCreate(
                file_name="test_image2.jpg",
                file_path="/path/to/test_image2.jpg",
                file_type="image/jpeg"
            )
            
            create_file_for_case(test_db, created_case.id, file_create1)  # type: ignore
            create_file_for_case(test_db, created_case.id, file_create2)  # type: ignore
            
            # 获取文件列表
            files = get_files_for_case(test_db, created_case.id)  # type: ignore
            assert len(files) == 2
            
        finally:
            src.database.DATA_DIR = original_data_dir
    
    def test_get_file_by_id_success(self, test_db: Session, sample_file_data: dict, test_data_dir: str):
        """测试通过ID成功获取文件"""
        import src.database
        original_data_dir = src.database.DATA_DIR
        src.database.DATA_DIR = test_data_dir
        
        try:
            # 创建案例和文件
            case_create = schemas.CaseCreate(case_name="测试案例_获取文件", description="测试获取单个文件")
            created_case = create_case(test_db, case_create)
            
            file_create = schemas.FileCreate(**sample_file_data)
            created_file = create_file_for_case(test_db, created_case.id, file_create)  # type: ignore
            
            # 通过ID获取文件
            retrieved_file = get_file(test_db, created_case.id, created_file.id)  # type: ignore
            
            assert retrieved_file is not None
            assert retrieved_file.file_name == sample_file_data["file_name"]
            
        finally:
            src.database.DATA_DIR = original_data_dir
    
    def test_get_file_by_id_not_found(self, test_db: Session, test_data_dir: str):
        """测试获取不存在的文件"""
        import src.database
        original_data_dir = src.database.DATA_DIR
        src.database.DATA_DIR = test_data_dir
        
        try:
            # 创建案例但不添加文件
            case_create = schemas.CaseCreate(case_name="测试案例", description="测试描述")
            created_case = create_case(test_db, case_create)
            
            # 尝试获取不存在的文件
            retrieved_file = get_file(test_db, created_case.id, 999)  # type: ignore
            assert retrieved_file is None
            
        finally:
            src.database.DATA_DIR = original_data_dir
    
    def test_delete_file_success(self, test_db: Session, sample_file_data: dict, test_data_dir: str):
        """测试成功删除文件"""
        import src.database
        original_data_dir = src.database.DATA_DIR
        src.database.DATA_DIR = test_data_dir
        
        try:
            # 创建案例和文件
            case_create = schemas.CaseCreate(case_name="测试案例", description="测试描述")
            created_case = create_case(test_db, case_create)
            
            file_create = schemas.FileCreate(**sample_file_data)
            created_file = create_file_for_case(test_db, created_case.id, file_create)  # type: ignore
            
            # 删除文件
            deleted_file = delete_file(test_db, created_case.id, created_file.id)  # type: ignore
            
            assert deleted_file is not None
            assert deleted_file.file_name == sample_file_data["file_name"]
            
            # 验证文件已被删除
            remaining_files = get_files_for_case(test_db, created_case.id)  # type: ignore
            assert len(remaining_files) == 0
            
        finally:
            src.database.DATA_DIR = original_data_dir 