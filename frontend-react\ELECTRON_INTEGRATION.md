# Project Novak - Electron 集成指南

## 🎯 目标

为 Project Novak 建立 Electron 桌面应用工作流，在不破坏 Vite 热重载体验的前提下，让 React 应用在桌面环境中运行。

## 📋 完成状态

### ✅ 已完成
1. **Electron 主进程文件** - `electron/main.js` 和 `electron/main.ts`
2. **TypeScript 配置** - `electron/tsconfig.json`
3. **基础工作流脚本** - package.json 脚本配置
4. **开发环境配置** - 支持 Vite 热重载的 Electron 集成

### 🔄 待完成（手动步骤）
由于网络环境限制，Electron 依赖安装未完成。以下是手动完成集成的步骤：

## 🛠️ 手动集成步骤

### 步骤 1：安装 Electron 依赖

```bash
# 安装核心依赖
npm install --save-dev electron@latest

# 安装工作流依赖
npm install --save-dev concurrently cross-env wait-on

# 可选：安装打包工具
npm install --save-dev electron-builder
```

### 步骤 2：更新 package.json

将以下配置添加到 `package.json`：

```json
{
  "main": "electron/main.js",
  "scripts": {
    "dev:web": "vite",
    "dev": "concurrently \"npm run dev:web\" \"wait-on http://localhost:5173 && cross-env NODE_ENV=development electron .\"",
    "build": "tsc -b && vite build",
    "build:electron": "npm run build && electron-builder",
    "electron": "cross-env NODE_ENV=development electron .",
    "electron:prod": "cross-env NODE_ENV=production electron ."
  }
}
```

### 步骤 3：验证工作流

1. **启动 Web 开发服务器**：
   ```bash
   npm run dev:web
   ```
   确认 Vite 在 http://localhost:5173 正常运行

2. **启动 Electron 应用**：
   ```bash
   npm run dev
   ```
   这将同时启动 Vite 和 Electron，Electron 窗口将加载 Vite 开发服务器

3. **验证热重载**：
   - 修改 React 组件代码
   - 确认 Electron 窗口中的应用自动更新

## 📁 文件结构

```
frontend-react/
├── electron/
│   ├── main.js          # JavaScript 版本（即用）
│   ├── main.ts          # TypeScript 版本（生产）
│   └── tsconfig.json    # Electron TypeScript 配置
├── src/                 # React 应用源码
├── dist/                # Vite 构建输出
└── package.json         # 项目配置
```

## 🔧 核心特性

### 1. 双模式支持
- **开发模式**：Electron 加载 Vite 开发服务器 (http://localhost:5173)
- **生产模式**：Electron 加载构建后的静态文件 (dist/index.html)

### 2. 热重载保持
- Vite 的热重载功能完全保留
- 代码修改即时反映在 Electron 窗口中
- 开发体验与纯 Web 开发一致

### 3. 窗口管理
- 响应式窗口尺寸 (1400x900，最小 1200x800)
- 开发环境自动打开 DevTools
- 跨平台窗口样式适配

### 4. 安全配置
- 禁用 Node.js 集成 (`nodeIntegration: false`)
- 启用上下文隔离 (`contextIsolation: true`)
- 禁用远程模块 (`enableRemoteModule: false`)

## 🎛️ 菜单系统

Electron 主进程包含完整的应用菜单：

- **File**: 新建案例、打开案例、导入图片
- **Edit**: 标准编辑操作
- **View**: 缩放、全屏、开发者工具
- **Window**: 窗口管理
- **Help**: 关于、文档

## 🔌 IPC 通信

预配置的 IPC 处理器：

- `get-app-version`: 获取应用版本
- `get-app-path`: 获取应用路径
- `show-message-box`: 显示消息框
- `show-open-dialog`: 文件选择对话框
- `show-save-dialog`: 文件保存对话框

## 📦 打包配置

electron-builder 配置支持：

- **Windows**: NSIS 安装包
- **macOS**: DMG 磁盘映像
- **Linux**: AppImage 便携应用

## 🚀 下一步

1. 完成 Electron 依赖安装
2. 验证完整工作流
3. 测试热重载功能
4. 配置应用图标和元数据
5. 设置代码签名（生产环境）

## 💡 开发提示

- 使用 `npm run dev:web` 进行纯 Web 开发
- 使用 `npm run dev` 进行 Electron 开发
- Electron 窗口中的 DevTools 与浏览器 DevTools 功能相同
- 所有 React 开发工具和扩展都可正常使用

---

**Project Novak 的第一层战甲已准备就绪！** ⚡
