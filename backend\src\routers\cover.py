# src/routers/cover.py
"""
封面图管理API路由
"""

import logging
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from .. import schemas
from ..database import get_master_db
from ..services.cover_service import get_cover_service, CoverImageService, CoverImageError
from ..crud.case_crud import get_case

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/cases",
    tags=["Cover Management"]
)


@router.put("/{case_id}/cover")
async def set_manual_cover(
    case_id: int,
    request: schemas.SetCoverRequest,
    db: Session = Depends(get_master_db),
    cover_service: CoverImageService = Depends(get_cover_service)
):
    """
    手动设置案例封面图
    
    Args:
        case_id: 案例ID
        request: 设置封面请求
        db: 数据库会话
        cover_service: 封面服务
        
    Returns:
        更新后的案例信息
    """
    try:
        # 检查案例是否存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例未找到")
        
        logger.info(f"设置案例 {case_id} 封面，文件ID: {request.fileId}")
        
        # 设置手动封面
        cover_url = cover_service.set_manual_cover(db, case_id, request.fileId)
        
        # 重新获取更新后的案例信息
        updated_case = get_case(db, case_id)
        
        return updated_case
        
    except CoverImageError as e:
        if e.error_code == "FILE_NOT_FOUND":
            raise HTTPException(status_code=422, detail={
                "error": e.error_code,
                "message": e.message,
                "details": e.details
            })
        elif e.error_code == "INVALID_FILE_TYPE":
            raise HTTPException(status_code=422, detail={
                "error": e.error_code,
                "message": e.message,
                "details": e.details
            })
        else:
            raise HTTPException(status_code=500, detail={
                "error": e.error_code,
                "message": e.message,
                "details": e.details
            })
    except Exception as e:
        logger.error(f"设置封面失败: {e}")
        raise HTTPException(status_code=500, detail="设置封面失败")


@router.delete("/{case_id}/cover")
async def remove_manual_cover(
    case_id: int,
    db: Session = Depends(get_master_db),
    cover_service: CoverImageService = Depends(get_cover_service)
):
    """
    移除手动封面选择，回退到自动选择
    
    Args:
        case_id: 案例ID
        db: 数据库会话
        cover_service: 封面服务
        
    Returns:
        操作结果和更新后的案例信息
    """
    try:
        # 检查案例是否存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例未找到")
        
        logger.info(f"移除案例 {case_id} 的手动封面")
        
        # 移除手动封面
        result = cover_service.remove_manual_cover(db, case_id)
        
        # 重新获取更新后的案例信息
        updated_case = get_case(db, case_id)
        
        return {
            "case": updated_case,
            "revertedTo": result["revertedTo"]
        }
        
    except Exception as e:
        logger.error(f"移除手动封面失败: {e}")
        raise HTTPException(status_code=500, detail="移除手动封面失败")


@router.post("/{case_id}/cover/reselect")
async def reselect_cover(
    case_id: int,
    db: Session = Depends(get_master_db),
    cover_service: CoverImageService = Depends(get_cover_service)
):
    """
    重新选择封面
    
    Args:
        case_id: 案例ID
        db: 数据库会话
        cover_service: 封面服务
        
    Returns:
        重选结果
    """
    try:
        # 检查案例是否存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例未找到")
        
        logger.info(f"重新选择案例 {case_id} 的封面")
        
        # 尝试自动选择封面
        new_cover_url = cover_service.auto_select_cover(db, case_id)
        
        if new_cover_url:
            if new_cover_url == cover_service.get_placeholder_url():
                return {
                    "coverReselected": True,
                    "reselectionStatus": "FAILED_NO_IMAGES",
                    "requiresManualSelection": True,
                    "newCoverUrl": new_cover_url
                }
            else:
                return {
                    "coverReselected": True,
                    "reselectionStatus": "SUCCESS",
                    "requiresManualSelection": False,
                    "newCoverUrl": new_cover_url
                }
        else:
            return {
                "coverReselected": False,
                "reselectionStatus": "FAILED_ERROR",
                "requiresManualSelection": True,
                "newCoverUrl": None
            }
        
    except Exception as e:
        logger.error(f"重新选择封面失败: {e}")
        raise HTTPException(status_code=500, detail="重新选择封面失败")


# 注意：文件删除API已在 cases.py 中实现，此处不重复定义以避免路由冲突
