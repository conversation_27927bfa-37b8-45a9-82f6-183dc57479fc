{"name": "frontend-react", "private": true, "version": "0.0.0", "type": "module", "main": "electron/main.js", "scripts": {"dev:web": "vite", "dev": "concurrently \"npm run dev:web\" \"wait-on http://localhost:3000 && cross-env NODE_ENV=development electron .\"", "build": "tsc -b && vite build", "build:electron": "npm run build && electron-builder", "lint": "eslint .", "preview": "vite preview", "electron": "cross-env NODE_ENV=development electron .", "electron:prod": "cross-env NODE_ENV=production electron ."}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@types/node": "^24.1.0", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.1", "tailwindcss": "^4.1.11", "zod": "^4.0.10", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.6.0", "concurrently": "^9.1.0", "cross-env": "^7.0.3", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "msw": "^2.10.4", "prettier": "^3.6.2", "tailwindcss-animate": "^1.0.7", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4", "wait-on": "^8.0.1"}, "msw": {"workerDirectory": ["public"]}}