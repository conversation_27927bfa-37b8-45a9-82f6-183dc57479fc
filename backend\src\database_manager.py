# src/database_manager.py
"""
PostgreSQL数据库连接管理器
统一管理PostgreSQL连接，支持主数据库和案例数据库
"""

import asyncio
from typing import Dict, Optional, Union, Any, AsyncGenerator
from contextlib import asynccontextmanager
from sqlalchemy import create_engine, Engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.asyncio import create_async_engine, AsyncEngine, AsyncSession, async_sessionmaker
from sqlalchemy.pool import QueuePool
import logging

try:
    from .database_config import db_config, DatabaseType
    from .models import Base
except ImportError:
    from database_config import db_config, DatabaseType
    from models import Base

logger = logging.getLogger(__name__)

class DatabaseManager:
    """数据库连接管理器"""
    
    def __init__(self):
        self._master_engine: Optional[Engine] = None
        self._async_master_engine: Optional[AsyncEngine] = None
        self._master_session_factory: Optional[sessionmaker] = None
        self._async_master_session_factory: Optional[async_sessionmaker] = None
        
        # 案例数据库引擎缓存
        self._case_engines: Dict[str, Engine] = {}
        self._async_case_engines: Dict[str, AsyncEngine] = {}
        self._case_session_factories: Dict[str, sessionmaker] = {}
        self._async_case_session_factories: Dict[str, async_sessionmaker] = {}
        
        # 异步锁
        self._engine_lock = asyncio.Lock()
        
    @property
    def master_engine(self) -> Engine:
        """获取主数据库引擎"""
        if self._master_engine is None:
            self._create_master_engine()
        return self._master_engine
    
    @property
    def async_master_engine(self) -> AsyncEngine:
        """获取异步主数据库引擎（暂时禁用）"""
        # 暂时返回None而不是抛出异常，避免cleanup时出错
        # 不调用_create_async_master_engine()
        return None
    
    def _create_master_engine(self):
        """创建主数据库引擎"""
        database_url = db_config.get_master_database_url()
        engine_kwargs = db_config.get_engine_kwargs(db_config.master_db_type)
        
        logger.info(f"创建主数据库引擎: {db_config.master_db_type.value}")
        self._master_engine = create_engine(database_url, **engine_kwargs)
        
        # 创建会话工厂
        self._master_session_factory = sessionmaker(
            autocommit=False, 
            autoflush=False, 
            bind=self._master_engine
        )
        
        # 创建表结构
        self._create_master_tables()
    
    def _create_async_master_engine(self):
        """创建异步主数据库引擎"""
        database_url = db_config.get_async_master_database_url()

        # 异步引擎专用配置，简化配置避免连接池问题
        engine_kwargs = {
            "echo": db_config.echo_sql,
            "future": True
        }

        # PostgreSQL异步引擎配置
        engine_kwargs.update({
            "connect_args": db_config.get_postgresql_connect_args(),
            "pool_size": db_config.pool_size,
            "max_overflow": db_config.max_overflow,
            "pool_timeout": db_config.pool_timeout,
            "pool_recycle": db_config.pool_recycle,
            "pool_pre_ping": True
        })

        logger.info(f"创建异步主数据库引擎: {db_config.master_db_type.value}")
        self._async_master_engine = create_async_engine(database_url, **engine_kwargs)
        
        # 创建异步会话工厂
        self._async_master_session_factory = async_sessionmaker(
            self._async_master_engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
    
    def _create_master_tables(self):
        """创建主数据库表结构"""
        try:
            # 导入模型以确保表定义被加载
            from . import models
            
            # 只创建主数据库相关的表
            master_tables = [
                models.SystemConfig.__table__,
                models.Case.__table__,
                models.CaseProcessingRule.__table__
            ]
            
            Base.metadata.create_all(bind=self._master_engine, tables=master_tables)
            logger.info("主数据库表结构创建完成")
            
        except Exception as e:
            logger.error(f"创建主数据库表结构失败: {e}")
            raise
    
    def get_case_engine(self, case_id: Union[int, str]) -> Engine:
        """获取案例数据库引擎"""
        case_key = str(case_id)
        
        if case_key not in self._case_engines:
            database_url = db_config.get_case_database_url(case_id)
            engine_kwargs = db_config.get_engine_kwargs(db_config.case_db_type)
            
            logger.info(f"创建案例数据库引擎: case_{case_id} ({db_config.case_db_type.value})")
            self._case_engines[case_key] = create_engine(database_url, **engine_kwargs)
            
            # 创建会话工厂
            self._case_session_factories[case_key] = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self._case_engines[case_key]
            )
            
            # 创建案例数据库表结构
            self._create_case_tables(case_key)
        
        return self._case_engines[case_key]
    
    async def get_async_case_engine(self, case_id: Union[int, str]) -> AsyncEngine:
        """获取异步案例数据库引擎"""
        case_key = str(case_id)
        
        async with self._engine_lock:
            if case_key not in self._async_case_engines:
                database_url = db_config.get_async_case_database_url(case_id)
                engine_kwargs = db_config.get_engine_kwargs(db_config.case_db_type)
                
                # PostgreSQL异步引擎配置
                engine_kwargs["poolclass"] = QueuePool
                
                engine_kwargs["future"] = True
                
                logger.info(f"创建异步案例数据库引擎: case_{case_id} (PostgreSQL)")
                self._async_case_engines[case_key] = create_async_engine(database_url, **engine_kwargs)
                
                # 创建异步会话工厂
                self._async_case_session_factories[case_key] = async_sessionmaker(
                    self._async_case_engines[case_key],
                    class_=AsyncSession,
                    expire_on_commit=False
                )
                
                # 创建案例数据库表结构
                await self._create_async_case_tables(case_key)
        
        return self._async_case_engines[case_key]
    
    def _create_case_tables(self, case_key: str):
        """创建案例数据库表结构"""
        try:
            # 导入模型以确保表定义被加载
            from . import models
            
            # 案例数据库相关的表
            case_tables = [
                models.File.__table__,
                models.CustomTags.__table__,
                models.FileCustomTags.__table__,
                models.TagCache.__table__,
                models.DeletedFiles.__table__
            ]
            
            engine = self._case_engines[case_key]
            Base.metadata.create_all(bind=engine, tables=case_tables)
            logger.info(f"案例数据库表结构创建完成: case_{case_key}")
            
        except Exception as e:
            logger.error(f"创建案例数据库表结构失败 case_{case_key}: {e}")
            raise
    
    async def _create_async_case_tables(self, case_key: str):
        """创建异步案例数据库表结构"""
        try:
            # 导入模型以确保表定义被加载
            from . import models
            
            # 案例数据库相关的表
            case_tables = [
                models.File.__table__,
                models.CustomTag.__table__,
                models.FileCustomTag.__table__,
                models.TagCache.__table__,
                models.DeletedFile.__table__
            ]
            
            engine = self._async_case_engines[case_key]
            async with engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all, tables=case_tables)
            
            logger.info(f"异步案例数据库表结构创建完成: case_{case_key}")
            
        except Exception as e:
            logger.error(f"创建异步案例数据库表结构失败 case_{case_key}: {e}")
            raise
    
    def get_master_session(self) -> Session:
        """获取主数据库会话"""
        if self._master_session_factory is None:
            self._create_master_engine()
        return self._master_session_factory()
    
    async def get_async_master_session(self) -> AsyncSession:
        """获取异步主数据库会话"""
        if self._async_master_session_factory is None:
            self._create_async_master_engine()
        return self._async_master_session_factory()
    
    def get_case_session(self, case_id: Union[int, str]) -> Session:
        """获取案例数据库会话"""
        case_key = str(case_id)
        self.get_case_engine(case_id)  # 确保引擎已创建
        return self._case_session_factories[case_key]()
    
    async def get_async_case_session(self, case_id: Union[int, str]) -> AsyncSession:
        """获取异步案例数据库会话"""
        case_key = str(case_id)
        await self.get_async_case_engine(case_id)  # 确保引擎已创建
        return self._async_case_session_factories[case_key]()
    
    async def cleanup_connections(self):
        """清理所有数据库连接"""
        logger.info("清理数据库连接...")
        
        # 清理异步引擎
        if self._async_master_engine:
            await self._async_master_engine.dispose()
        
        for engine in self._async_case_engines.values():
            await engine.dispose()
        
        # 清理同步引擎
        if self._master_engine:
            self._master_engine.dispose()
        
        for engine in self._case_engines.values():
            engine.dispose()
        
        logger.info("数据库连接清理完成")

# 全局数据库管理器实例
db_manager = DatabaseManager()

# 依赖注入函数
def get_master_db() -> Session:
    """获取主数据库会话的依赖注入函数"""
    db = db_manager.get_master_session()
    try:
        yield db
    finally:
        db.close()

async def get_async_master_db() -> AsyncGenerator[AsyncSession, None]:
    """获取异步主数据库会话的依赖注入函数"""
    async with db_manager.get_async_master_session() as session:
        try:
            yield session
        finally:
            await session.close()

# PostgreSQL模式下不再需要案例数据库会话函数
# 所有数据都存储在主数据库中
