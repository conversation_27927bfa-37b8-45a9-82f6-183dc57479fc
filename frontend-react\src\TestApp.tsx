import { useAppStore } from '@/store';

/**
 * 最小化测试应用 - 用于隔离 ReferenceError 问题
 */
function TestApp() {
  console.log('TestApp rendering...');
  
  try {
    // 测试 useAppStore 是否正常工作
    const storeState = useAppStore();
    console.log('Store state:', storeState);
    
    const {
      isCatalogVisible,
      isInfoPanelVisible,
      isWorkbenchVisible,
      isFullscreenGallery,
    } = storeState;
    
    console.log('Destructured state:', {
      isCatalogVisible,
      isInfoPanelVisible,
      isWorkbenchVisible,
      isFullscreenGallery,
    });
    
    return (
      <div className="h-screen w-screen bg-[#191012] p-8 text-[#A49F9A]">
        <h1 className="text-2xl font-bold mb-4">🧪 TestApp - Store 状态测试</h1>
        
        <div className="space-y-4">
          <div className="p-4 bg-[#040709] rounded">
            <h2 className="text-lg font-semibold mb-2">面板状态:</h2>
            <ul className="space-y-1">
              <li>目录栏可见: {isCatalogVisible ? '✅' : '❌'}</li>
              <li>信息栏可见: {isInfoPanelVisible ? '✅' : '❌'}</li>
              <li>工作台可见: {isWorkbenchVisible ? '✅' : '❌'}</li>
              <li>全屏画廊: {isFullscreenGallery ? '✅' : '❌'}</li>
            </ul>
          </div>
          
          <div className="p-4 bg-[#040709] rounded">
            <h2 className="text-lg font-semibold mb-2">测试结果:</h2>
            <p className="text-green-400">✅ useAppStore 正常工作</p>
            <p className="text-green-400">✅ 状态解构成功</p>
            <p className="text-green-400">✅ 组件渲染成功</p>
          </div>
        </div>
      </div>
    );
    
  } catch (error) {
    console.error('TestApp error:', error);
    
    return (
      <div className="h-screen w-screen bg-red-900 p-8 text-white">
        <h1 className="text-2xl font-bold mb-4">❌ TestApp 错误</h1>
        <div className="p-4 bg-red-800 rounded">
          <p className="font-semibold">错误信息:</p>
          <p className="text-red-200">{error instanceof Error ? error.message : String(error)}</p>
        </div>
      </div>
    );
  }
}

export default TestApp;
