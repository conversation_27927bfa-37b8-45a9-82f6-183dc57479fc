import * as React from 'react';

/**
 * 简单的测试布局 - 用于验证分栏是否正常工作
 */
export interface SimpleTestLayoutProps {
  catalogPanel?: React.ReactNode;
  galleryPanel?: React.ReactNode;
  workbenchPanel?: React.ReactNode;
  infoPanel?: React.ReactNode;
  showCatalogPanel?: boolean;
  showInfoPanel?: boolean;
  showWorkbench?: boolean;
  isFullscreenGallery?: boolean;
}

const SimpleTestLayout: React.FC<SimpleTestLayoutProps> = ({
  catalogPanel,
  galleryPanel,
  workbenchPanel,
  infoPanel,
  showCatalogPanel = true,
  showInfoPanel = true,
  showWorkbench = false,
  isFullscreenGallery = false,
}) => {
  
  console.log('SimpleTestLayout render:', {
    showCatalogPanel,
    showInfoPanel,
    showWorkbench,
    isFullscreenGallery
  });

  // 全屏模式
  if (isFullscreenGallery) {
    return (
      <div className="h-screen w-screen bg-[#191012]">
        {galleryPanel}
      </div>
    );
  }

  return (
    <div className="h-screen w-screen bg-[#191012] flex flex-col">
      {/* 顶部区域 - 水平分栏 */}
      <div className="flex-1 flex">
        {/* 左侧目录栏 */}
        {showCatalogPanel && (
          <div 
            className="w-80 bg-[#040709] border-r border-[#2A2A2A] flex-shrink-0"
            style={{ minHeight: '100%' }}
          >
            {catalogPanel}
          </div>
        )}
        
        {/* 中央画廊 */}
        <div className="flex-1 bg-[#191012]">
          {galleryPanel}
        </div>
        
        {/* 右侧信息栏 */}
        {showInfoPanel && (
          <div 
            className="w-80 bg-[#040709] border-l border-[#2A2A2A] flex-shrink-0"
            style={{ minHeight: '100%' }}
          >
            {infoPanel}
          </div>
        )}
      </div>
      
      {/* 底部工作台 */}
      {showWorkbench && (
        <div 
          className="h-48 bg-[#040709] border-t border-[#2A2A2A] flex-shrink-0"
          style={{ width: '100%' }}
        >
          {workbenchPanel}
        </div>
      )}
    </div>
  );
};

export { SimpleTestLayout };
