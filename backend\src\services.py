# src/services.py
import os
import uuid
import logging
import mimetypes # 用于验证文件类型
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

# 日志配置已在main.py中统一管理
logger = logging.getLogger(__name__)

# 依赖检查和导入
PANDAS_AVAILABLE = False
OPENCV_AVAILABLE = False
IMAGEHASH_AVAILABLE = False
pd = None
cv2 = None
imagehash = None

def check_dependencies():
    """检查质量分析所需的依赖包"""
    global PANDAS_AVAILABLE, OPENCV_AVAILABLE, IMAGEHASH_AVAILABLE, pd, cv2, imagehash

    # 检查pandas
    try:
        import pandas as pd_module
        pd = pd_module
        PANDAS_AVAILABLE = True
        logger.info("✅ pandas可用，质量分析功能已启用")
    except ImportError:
        PANDAS_AVAILABLE = False
        logger.warning("❌ pandas不可用，质量分析功能将被禁用")

    # 检查opencv
    try:
        import cv2 as cv2_module
        cv2 = cv2_module
        OPENCV_AVAILABLE = True
        logger.info("✅ opencv-python可用，图像处理功能已启用")
    except ImportError:
        OPENCV_AVAILABLE = False
        logger.warning("❌ opencv-python不可用，图像处理功能将被禁用")

    # 检查imagehash
    try:
        import imagehash as imagehash_module
        imagehash = imagehash_module
        IMAGEHASH_AVAILABLE = True
        logger.info("✅ imagehash可用，感知哈希功能已启用")
    except ImportError:
        IMAGEHASH_AVAILABLE = False
        logger.warning("❌ imagehash不可用，感知哈希功能将被禁用")

# 初始化依赖检查
check_dependencies()

from PIL import Image, ExifTags
from sqlalchemy.orm import Session

# 统一使用相对导入（项目结构已固定）
from .crud.file_crud import create_file_for_case
from .database import create_case_directory
from . import schemas, models

def _report_progress(processed: int, total: int, interval: int = 100, prefix: str = "处理进度") -> None:
    """统一的进度报告函数"""
    if processed % interval == 0:
        progress = processed / total * 100
        logger.info(f"{prefix}: {processed}/{total} ({progress:.1f}%)")

import asyncio
from concurrent.futures import ThreadPoolExecutor

# 目录结构常量
THUMBNAIL_SIZE = (200, 200)

# 辅助函数 _get_image_metadata 和 _create_thumbnail 保持不变，它们已经处理Path对象，可复用

def _get_image_metadata(image_path: Path):
    """从图像文件中提取元数据（尺寸、拍摄日期）。"""
    width, height, taken_at = None, None, None
    try:
        with Image.open(image_path) as img:
            width, height = img.size
            exif_data = img.getexif()
            if exif_data:
                for tag, value in exif_data.items():
                    tag_name = ExifTags.TAGS.get(tag)
                    if tag_name == 'DateTimeOriginal':
                        taken_at = datetime.strptime(value, '%Y:%m:%d %H:%M:%S')
                        break
    except Exception as e:
        logger.warning(f"无法处理图像元数据 {image_path}: {e}")
    return width, height, taken_at

def _create_thumbnail(image_path: Path, thumbnail_dir: Path, file_stem: str):
    """为图像创建缩略图并返回其相对路径。"""
    try:
        with Image.open(image_path) as img:
            img_copy = img.copy()
            img_copy.thumbnail(THUMBNAIL_SIZE)
            
            thumbnail_filename = f"{file_stem}_thumb_small.jpg"
            full_thumbnail_path = thumbnail_dir / thumbnail_filename
            
            if img_copy.mode in ("RGBA", "P"):
                img_copy = img_copy.convert("RGB")
            
            img_copy.save(full_thumbnail_path, "JPEG")
            
            # 返回绝对路径而不是相对路径，避免路径计算问题
            return str(full_thumbnail_path)
            
    except Exception as e:
        logger.error(f"无法创建缩略图 {image_path}: {e}")
        return None

# --- 主要服务函数 (已完全重构以处理本地文件路径) ---
def process_local_file_path(db: Session, case_id: int, original_file_path_str: str) -> Dict[str, Any] | None:
    """
    接收一个本地文件路径，处理它并存入数据库：
    1. 验证路径有效性及文件类型。
    2. 提取图片元数据。
    3. 将【原始文件路径】存入数据库，不创建副本。

    注意：此函数不再创建缩略图副本，只引用原始文件路径。
    缩略图将在需要时动态生成。
    """
    original_path = Path(original_file_path_str)

    # 1. 稳定性增强：验证路径是否存在且为文件
    if not original_path.is_file():
        logger.warning(f"路径无效或不是一个文件，已跳过: {original_file_path_str}")
        return None

    # 2. 功能扩展：验证文件是否为图片
    file_type, _ = mimetypes.guess_type(original_path)
    if not file_type or not file_type.startswith("image/"):
        logger.info(f"非图片文件，已跳过: {original_path.name} (类型: {file_type})")
        return None

    # 3. 确保案例目录存在（但不创建缩略图目录）
    case_dir = create_case_directory(case_id)

    # 4. 提取元数据（不创建缩略图）
    width, height, taken_at = _get_image_metadata(original_path)

    # 5. 准备要存入数据库的数据（不包含缩略图路径）
    file_data = {
        "file_name": original_path.name,  # 确保使用原始文件名
        "file_path": original_file_path_str,  # 原始文件路径
        "file_type": file_type,
        "width": width,
        "height": height,
        "taken_at": taken_at,
        "thumbnail_small_path": None,  # 不创建缩略图副本
    }

    # 6. 使用CRUD函数创建数据库记录
    file_create = schemas.FileCreate(**file_data)
    db_file = create_file_for_case(db=db, case_id=case_id, file=file_create)

    # 7. 应用规则引擎处理文件，生成标签数据
    try:
        from .services.rule_engine import process_file_with_rules
        tags_data = process_file_with_rules(db, case_id, db_file)
        
        if tags_data:
            # 更新文件的标签数据
            db_file.tags = tags_data
            db.commit()
            logger.info(f"✅ 已生成标签数据: {original_path.name}, {len(tags_data.get('tags', {}))} 个标签类别")
        else:
            logger.warning(f"⚠️ 未生成标签数据: {original_path.name}")
            
    except Exception as e:
        logger.error(f"❌ 规则引擎处理失败 {original_path.name}: {e}")
        # 不影响文件导入，继续执行

    logger.info(f"✅ 已引用原始文件（无副本）: {original_path.name}")
    return db_file


def process_local_file_path_with_original_name(db: Session, case_id: int, original_file_path_str: str, original_filename: str) -> Dict[str, Any] | None:
    """
    接收一个本地文件路径和原始文件名，处理它并存入数据库：
    1. 验证路径有效性及文件类型。
    2. 提取图片元数据。
    3. 将【原始文件路径】存入数据库，不创建副本。
    4. 使用提供的原始文件名而不是临时文件名。

    注意：此函数不再创建缩略图副本，只引用原始文件路径。
    缩略图将在需要时动态生成。
    """
    original_path = Path(original_file_path_str)

    # 1. 稳定性增强：验证路径是否存在且为文件
    if not original_path.is_file():
        logger.warning(f"路径无效或不是一个文件，已跳过: {original_file_path_str}")
        return None

    # 2. 功能扩展：验证文件是否为图片
    file_type, _ = mimetypes.guess_type(original_path)
    if not file_type or not file_type.startswith("image/"):
        logger.info(f"非图片文件，已跳过: {original_filename} (类型: {file_type})")
        return None

    # 3. 确保案例目录存在（但不创建缩略图目录）
    case_dir = create_case_directory(case_id)

    # 4. 提取元数据（不创建缩略图）
    width, height, taken_at = _get_image_metadata(original_path)

    # 5. 准备要存入数据库的数据（不包含缩略图路径）
    file_data = {
        "file_name": original_filename,  # 使用提供的原始文件名
        "file_path": original_file_path_str,  # 原始文件路径
        "file_type": file_type,
        "width": width,
        "height": height,
        "taken_at": taken_at,
        "thumbnail_small_path": None,  # 不创建缩略图副本
    }

    # 6. 使用CRUD函数创建数据库记录
    file_create = schemas.FileCreate(**file_data)
    db_file = create_file_for_case(db=db, case_id=case_id, file=file_create)

    # 7. 应用规则引擎处理文件，生成标签数据
    try:
        from .services.rule_engine import process_file_with_rules
        tags_data = process_file_with_rules(db, case_id, db_file)
        
        if tags_data:
            # 更新文件的标签数据
            db_file.tags = tags_data
            db.commit()
            logger.info(f"✅ 已生成标签数据: {original_filename}, {len(tags_data.get('tags', {}))} 个标签类别")
        else:
            logger.warning(f"⚠️ 未生成标签数据: {original_filename}")
            
    except Exception as e:
        logger.error(f"❌ 规则引擎处理失败 {original_filename}: {e}")
        # 不影响文件导入，继续执行

    logger.info(f"✅ 已引用原始文件（无副本）: {original_filename}")
    return db_file


def batch_process_files(db: Session, case_id: int, file_paths: List[str], batch_size: int = 100) -> Dict[str, Any]:
    """
    批量处理文件，提升大量文件导入的性能
    
    Args:
        db: 数据库会话
        case_id: 案例ID
        file_paths: 文件路径列表
        batch_size: 批处理大小
        
    Returns:
        处理结果统计
    """
    results = {
        "total": len(file_paths),
        "processed": 0,
        "success": 0,
        "failed": 0,
        "skipped": 0,
        "errors": []
    }
    
    logger.info(f"开始批量处理 {len(file_paths)} 个文件，批次大小: {batch_size}")
    
    # 分批处理
    for i in range(0, len(file_paths), batch_size):
        batch = file_paths[i:i + batch_size]
        batch_num = i // batch_size + 1
        total_batches = (len(file_paths) + batch_size - 1) // batch_size
        
        logger.info(f"处理批次 {batch_num}/{total_batches}, 包含 {len(batch)} 个文件")
        
        batch_results = []
        for file_path in batch:
            try:
                result = process_local_file_path(db, case_id, file_path)
                if result:
                    batch_results.append(result)
                    results["success"] += 1
                else:
                    results["skipped"] += 1
                results["processed"] += 1
                
                # 进度报告
                _report_progress(results["processed"], results["total"], 100)
                              
            except Exception as e:
                results["failed"] += 1
                results["errors"].append(f"{file_path}: {str(e)}")
                logger.error(f"处理文件失败 {file_path}: {e}")
        
        # 批次提交，减少数据库事务开销
        try:
            db.commit()
            logger.info(f"批次 {batch_num} 提交成功，处理了 {len(batch)} 个文件")
        except Exception as e:
            db.rollback()
            logger.error(f"批次 {batch_num} 提交失败: {e}")
            results["failed"] += len(batch)
    
    logger.info(f"批量处理完成: 总计 {results['total']}, "
                f"成功 {results['success']}, "
                f"跳过 {results['skipped']}, "
                f"失败 {results['failed']}")
    
    return results

async def async_batch_process_files(db: Session, case_id: int, file_paths: List[str], 
                                  max_workers: int = 4) -> Dict[str, Any]:
    """
    异步批量处理文件，进一步提升性能
    
    Args:
        db: 数据库会话
        case_id: 案例ID  
        file_paths: 文件路径列表
        max_workers: 最大工作线程数
        
    Returns:
        处理结果统计
    """
    results = {
        "total": len(file_paths),
        "processed": 0,
        "success": 0,
        "failed": 0,
        "skipped": 0,
        "errors": []
    }
    
    logger.info(f"开始异步批量处理 {len(file_paths)} 个文件，工作线程数: {max_workers}")
    
    def process_single_file(file_path: str) -> tuple[str, bool, Optional[str]]:
        """处理单个文件的线程函数"""
        try:
            result = process_local_file_path(db, case_id, file_path)
            return file_path, bool(result), None
        except Exception as e:
            return file_path, False, str(e)
    
    # 使用线程池异步处理
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        futures = [executor.submit(process_single_file, path) for path in file_paths]
        
        # 收集结果
        for i, future in enumerate(futures):
            try:
                file_path, success, error = future.result()
                results["processed"] += 1
                
                if success:
                    results["success"] += 1
                elif error:
                    results["failed"] += 1
                    results["errors"].append(f"{file_path}: {error}")
                else:
                    results["skipped"] += 1
                
                # 进度报告
                _report_progress(results["processed"], results["total"], 500, "异步处理进度")
                    
            except Exception as e:
                results["failed"] += 1
                logger.error(f"异步处理任务失败: {e}")
    
    # 最终提交
    try:
        db.commit()
        logger.info("异步批量处理提交成功")
    except Exception as e:
        db.rollback()
        logger.error(f"异步批量处理提交失败: {e}")
    
    logger.info(f"异步批量处理完成: 总计 {results['total']}, "
                f"成功 {results['success']}, "
                f"跳过 {results['skipped']}, " 
                f"失败 {results['failed']}")
    
    return results

def get_image_files_from_directory(directory_path: str, recursive: bool = True) -> List[str]:
    """
    从目录中获取所有图像文件路径
    
    Args:
        directory_path: 目录路径
        recursive: 是否递归搜索子目录
        
    Returns:
        图像文件路径列表
    """
    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
    image_files = []
    
    directory = Path(directory_path)
    if not directory.exists():
        logger.error(f"目录不存在: {directory_path}")
        return []
    
    if recursive:
        pattern = "**/*"
    else:
        pattern = "*"
    
    for file_path in directory.glob(pattern):
        if file_path.is_file() and file_path.suffix.lower() in image_extensions:
            image_files.append(str(file_path.absolute()))
    
    logger.info(f"在目录 {directory_path} 中找到 {len(image_files)} 个图像文件")
    return image_files


# ===============================
# 图像质量分析与相似性聚类服务
# ===============================

def generate_quality_report(
    db: Session, 
    case_id: int, 
    file_ids: Optional[List[int]] = None,
    weights: Optional[Dict[str, float]] = None,
    phash_threshold: int = 18,
    generate_excel: bool = True
) -> Dict[str, Any]:
    """
    为指定案例生成图像质量分析报告
    
    Args:
        db: 数据库会话
        case_id: 案例ID
        file_ids: 可选，指定要分析的文件ID列表
        weights: 质量评估权重
        phash_threshold: pHash相似度阈值
        generate_excel: 是否生成Excel报告
        
    Returns:
        dict: 分析结果和报告信息
    """
    
    # 检查必要的依赖
    if not PANDAS_AVAILABLE:
        return {
            "success": False,
            "message": "质量分析功能不可用：缺少pandas依赖包。请运行 pip install pandas",
            "total_files": 0,
            "clusters_count": 0
        }
    
    if not OPENCV_AVAILABLE:
        return {
            "success": False,
            "message": "质量分析功能不可用：缺少opencv-python依赖包。请运行 pip install opencv-python",
            "total_files": 0,
            "clusters_count": 0
        }
    
    if not IMAGEHASH_AVAILABLE:
        return {
            "success": False,
            "message": "质量分析功能不可用：缺少imagehash依赖包。请运行 pip install imagehash",
            "total_files": 0,
            "clusters_count": 0
        }
    
    try:
        # 尝试相对导入
        from .analysis.image_quality import calculate_metrics
        logger.info("成功导入 image_quality 模块")
    except ImportError as e:
        logger.error(f"相对导入 image_quality 模块失败: {e}")
        try:
            # 尝试绝对导入路径
            import sys
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            analysis_dir = os.path.join(current_dir, 'analysis')
            if analysis_dir not in sys.path:
                sys.path.insert(0, analysis_dir)

            from image_quality import calculate_metrics
            logger.info("通过备用路径成功导入 image_quality 模块")
        except ImportError as e2:
            logger.error(f"备用路径导入也失败: {e2}")
            try:
                # 尝试直接从analysis目录导入
                sys.path.insert(0, os.path.join(current_dir, 'analysis'))
                import image_quality
                calculate_metrics = image_quality.calculate_metrics
                logger.info("通过直接导入成功导入 image_quality 模块")
            except ImportError as e3:
                logger.error(f"所有导入方式都失败: {e3}")
                return {
                    "success": False,
                    "message": f"质量分析功能不可用：无法导入image_quality模块。错误: {str(e3)}",
                    "total_files": 0,
                    "clusters_count": 0
                }
    
    try:
        # 获取案例信息
        case = db.query(models.Case).filter(models.Case.id == case_id).first()
        if not case:
            raise ValueError(f"案例 {case_id} 不存在")

        # 使用PostgreSQL数据库管理器获取案例数据库
        from database_manager import DatabaseManager
        db_manager = DatabaseManager()
        # PostgreSQL模式：使用主数据库会话
        if not db:
            raise ValueError(f"无法连接到案例 {case_id} 的数据库")
        
        try:
            # 获取要分析的文件列表（PostgreSQL模式）
            query = db.query(models.File).filter(models.File.case_id == case_id)
            if file_ids:
                query = query.filter(models.File.id.in_(file_ids))

            files_to_analyze = query.all()
            
            if not files_to_analyze:
                return {
                    "success": False,
                    "message": "未找到可分析的图片文件",
                    "total_files": 0,
                    "clusters_count": 0
                }
            
            # 分析每个文件
            logger.info(f"开始为案例 {case_id} 分析 {len(files_to_analyze)} 个文件...")
            analysis_results = []
            
            for file_record in files_to_analyze:
                # 决定使用哪个文件路径进行分析
                analysis_path = None
                
                # 优先使用原始文件路径（如果存在）
                file_path_str = getattr(file_record, 'file_path', None)
                thumbnail_path_str = getattr(file_record, 'thumbnail_small_path', None)
                
                if file_path_str and os.path.exists(file_path_str):
                    analysis_path = file_path_str
                # 否则使用缩略图路径
                elif thumbnail_path_str and os.path.exists(thumbnail_path_str):
                    analysis_path = thumbnail_path_str
                
                if not analysis_path:
                    logger.warning(f"文件 {file_record.file_name} 的路径不存在，跳过分析")
                    continue
                
                # 计算质量指标
                metrics = calculate_metrics(analysis_path)
                if metrics:
                    # 使用原始文件名而不是分析路径的文件名
                    metrics['filename'] = getattr(file_record, 'file_name', '')
                    metrics['file_id'] = getattr(file_record, 'id', None)
                    analysis_results.append(metrics)
            
            if not analysis_results:
                return {
                    "success": False,
                    "message": "没有成功分析任何文件",
                    "total_files": 0,
                    "clusters_count": 0
                }
            
            # 创建DataFrame并进行质量分析处理
            df_result = pd.DataFrame(analysis_results)
            
            # 使用默认权重如果未提供
            if weights is None:
                weights = {
                    'sharpness': 0.5, 
                    'face_quality': 0.3, 
                    'dynamic_range': 0.15, 
                    'brightness': 0.05
                }
            
            # 归一化各项指标
            for col in weights.keys():
                if col in df_result.columns:
                    min_val = df_result[col].min()
                    max_val = df_result[col].max()
                    if max_val > min_val:
                        df_result[f'{col}_norm'] = (df_result[col] - min_val) / (max_val - min_val)
                    else:
                        df_result[f'{col}_norm'] = 0
            
            df_result.fillna(0, inplace=True)
            
            # 计算综合质量分数
            df_result['quality_score'] = sum(
                df_result[f'{key}_norm'] * weight 
                for key, weight in weights.items() 
                if f'{key}_norm' in df_result.columns
            )
            
            # 执行相似性聚类
            logger.info("开始执行相似性聚类...")
            df_result['cluster_id'] = -1
            cluster_counter = 0
            
            # 按质量分数降序排列
            df_sorted = df_result.sort_values(by='quality_score', ascending=False).reset_index(drop=True)
            
            for i in range(len(df_sorted)):
                if df_sorted.at[i, 'cluster_id'] != -1:
                    continue
                
                current_hash = df_sorted.at[i, 'phash']
                current_group = df_sorted.at[i, 'group_id']
                
                # 为当前图片分配聚类ID
                df_sorted.at[i, 'cluster_id'] = cluster_counter
                
                # 如果没有组ID，跳过相似性检查
                if current_group is None:
                    cluster_counter += 1
                    continue
                
                # 寻找相似图片
                for j in range(i + 1, len(df_sorted)):
                    if df_sorted.at[j, 'cluster_id'] != -1:
                        continue
                    
                    other_hash = df_sorted.at[j, 'phash']
                    other_group = df_sorted.at[j, 'group_id']
                    
                    # 同一卷宗且pHash相似
                    if (other_group == current_group and 
                        current_hash - other_hash <= phash_threshold):
                        df_sorted.at[j, 'cluster_id'] = cluster_counter
                
                cluster_counter += 1
            
            # 更新数据库中的文件记录
            update_count = _update_file_quality_metrics_by_id(db, case_id, df_sorted)
            
            result = {
                "success": True,
                "message": f"质量分析完成！处理了 {len(df_sorted)} 个文件，更新了 {update_count} 条记录",
                "total_files": len(df_sorted),
                "clusters_count": df_sorted['cluster_id'].nunique() if 'cluster_id' in df_sorted.columns else 0
            }
            
            # 生成Excel报告
            if generate_excel:
                case_dir = create_case_directory(case_id)
                reports_dir = case_dir / "reports"
                reports_dir.mkdir(exist_ok=True)
                
                report_path = reports_dir / f"quality_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                # 这里不生成Excel报告，因为需要重构
                result["message"] += f"，分析完成"
            
            logger.info(f"案例 {case_id} 的质量分析完成")
            return result
            
        finally:
            # PostgreSQL模式下使用主数据库会话，无需手动关闭
            pass
        
    except Exception as e:
        logger.error(f"案例 {case_id} 的质量分析失败: {e}")
        return {
            "success": False,
            "message": f"质量分析失败: {str(e)}",
            "total_files": 0,
            "clusters_count": 0
        }


def _update_file_quality_metrics_by_id(db: Session, case_id: int, df_result) -> int:
    """
    基于文件ID更新数据库中文件的质量指标

    Args:
        db: 主数据库会话
        case_id: 案例ID
        df_result: 分析结果DataFrame

    Returns:
        int: 更新的记录数
    """
    try:
        update_count = 0

        logger.info(f"开始更新质量指标，DataFrame形状: {df_result.shape}")
        logger.info(f"DataFrame列名: {list(df_result.columns)}")

        for index, row in df_result.iterrows():
            file_id = row.get('file_id')
            logger.debug(f"处理行 {index}: file_id={file_id}, quality_score={row.get('quality_score')}")

            if not file_id:
                logger.warning(f"行 {index} 缺少file_id，跳过")
                continue

            # 查找对应的文件记录（PostgreSQL模式）
            file_record = db.query(models.File).filter(
                models.File.id == file_id,
                models.File.case_id == case_id
            ).first()

            if file_record:
                logger.debug(f"找到文件记录 ID={file_id}, 文件名={file_record.file_name}")

                # 更新质量指标
                file_record.quality_score = row.get('quality_score')
                file_record.sharpness = row.get('sharpness')
                file_record.brightness = row.get('brightness')
                file_record.dynamic_range = row.get('dynamic_range')
                file_record.num_faces = row.get('num_faces')
                file_record.face_sharpness = row.get('face_sharpness')
                file_record.face_quality = row.get('face_quality')
                file_record.cluster_id = row.get('cluster_id')
                # 转换phash为字符串
                phash_value = row.get('phash')
                if phash_value is not None:
                    file_record.phash = str(phash_value)
                file_record.group_id = row.get('group_id')
                file_record.frame_number = row.get('frame_number')

                update_count += 1
                logger.debug(f"已更新文件 {file_id} 的质量指标，质量分数: {row.get('quality_score')}")
            else:
                logger.warning(f"未找到ID为 {file_id} 的文件记录")

        db.commit()

        logger.info(f"已更新 {update_count} 条文件记录的质量指标")
        return update_count

    except Exception as e:
        logger.error(f"更新文件质量指标失败: {e}")
        db.rollback()
        return 0


def _update_file_quality_metrics(db: Session, case_id: int, df_result) -> int:
    """
    更新数据库中文件的质量指标
    
    Args:
        db: 主数据库会话
        case_id: 案例ID
        df_result: 分析结果DataFrame
        
    Returns:
        int: 更新的记录数
    """
    # PostgreSQL模式：不需要案例数据库会话导入
    pass
    
    try:
        # 先获取案例信息
        case = db.query(models.Case).filter(models.Case.id == case_id).first()
        if not case:
            logger.error(f"无法找到案例 {case_id}")
            return 0

        # 使用PostgreSQL数据库管理器获取案例数据库
        from database_manager import DatabaseManager
        db_manager = DatabaseManager()
        # PostgreSQL模式：使用主数据库会话
        if not db:
            logger.error(f"无法获取案例 {case_id} 的数据库会话")
            return 0
        
        update_count = 0
        
        for _, row in df_result.iterrows():
            filename = row.get('filename')
            if not filename:
                continue
            
            # 查找对应的文件记录（PostgreSQL模式）
            file_record = db.query(models.File).filter(
                models.File.file_name == filename,
                models.File.case_id == case_id
            ).first()
            
            if file_record:
                # 更新质量指标
                file_record.quality_score = row.get('quality_score')
                file_record.sharpness = row.get('sharpness')
                file_record.brightness = row.get('brightness')
                file_record.dynamic_range = row.get('dynamic_range')
                file_record.num_faces = row.get('num_faces')
                file_record.face_sharpness = row.get('face_sharpness')
                file_record.face_quality = row.get('face_quality')
                file_record.cluster_id = row.get('cluster_id')
                file_record.phash = str(row.get('phash', ''))
                file_record.group_id = row.get('group_id')
                file_record.frame_number = row.get('frame_number')
                
                update_count += 1
        
        db.commit()

        logger.info(f"已更新 {update_count} 条文件记录的质量指标")
        return update_count

    except Exception as e:
        logger.error(f"更新文件质量指标失败: {e}")
        db.rollback()
        return 0

# ... existing code ...

# ===============================
# 图像质量分析与相似性聚类服务
# ===============================
# 重复函数已删除，使用第409行的版本







async def async_generate_quality_report(
    db: Session,
    case_id: int,
    file_ids: Optional[List[int]] = None,
    weights: Optional[Dict[str, float]] = None,
    phash_threshold: int = 18,
    generate_excel: bool = True
) -> Dict[str, Any]:
    """
    异步生成质量分析报告
    
    Args:
        db: 数据库会话
        case_id: 案例ID
        file_ids: 可选，指定要分析的文件ID列表
        weights: 质量评估权重
        phash_threshold: pHash相似度阈值
        generate_excel: 是否生成Excel报告
        
    Returns:
        dict: 分析结果和报告信息
    """
    
    # 检查必要的依赖
    if not PANDAS_AVAILABLE:
        return {
            "success": False,
            "message": "质量分析功能不可用：缺少pandas依赖包。请运行 pip install pandas",
            "total_files": 0,
            "clusters_count": 0
        }
    
    if not OPENCV_AVAILABLE:
        return {
            "success": False,
            "message": "质量分析功能不可用：缺少opencv-python依赖包。请运行 pip install opencv-python",
            "total_files": 0,
            "clusters_count": 0
        }
    
    if not IMAGEHASH_AVAILABLE:
        return {
            "success": False,
            "message": "质量分析功能不可用：缺少imagehash依赖包。请运行 pip install imagehash",
            "total_files": 0,
            "clusters_count": 0
        }
    
    try:
        # 尝试相对导入
        from .analysis.image_quality import calculate_metrics
        logger.info("异步版本成功导入 image_quality 模块")
    except ImportError as e:
        logger.error(f"异步版本相对导入 image_quality 模块失败: {e}")
        try:
            # 尝试绝对导入路径
            import sys
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            analysis_dir = os.path.join(current_dir, 'analysis')
            if analysis_dir not in sys.path:
                sys.path.insert(0, analysis_dir)

            from image_quality import calculate_metrics
            logger.info("异步版本通过备用路径成功导入 image_quality 模块")
        except ImportError as e2:
            logger.error(f"异步版本备用路径导入也失败: {e2}")
            try:
                # 尝试直接从analysis目录导入
                sys.path.insert(0, os.path.join(current_dir, 'analysis'))
                import image_quality
                calculate_metrics = image_quality.calculate_metrics
                logger.info("异步版本通过直接导入成功导入 image_quality 模块")
            except ImportError as e3:
                logger.error(f"异步版本所有导入方式都失败: {e3}")
                return {
                    "success": False,
                    "message": f"质量分析功能不可用：无法导入image_quality模块。错误: {str(e3)}",
                    "total_files": 0,
                    "clusters_count": 0
                }
    
    try:
        import pandas as pd  # 添加局部导入，确保 pd 在函数作用域内定义
        # 获取案例信息
        case = db.query(models.Case).filter(models.Case.id == case_id).first()
        if not case:
            raise ValueError(f"案例 {case_id} 不存在")

        # 使用PostgreSQL数据库管理器获取案例数据库
        from database_manager import DatabaseManager
        db_manager = DatabaseManager()
        # PostgreSQL模式：使用主数据库会话
        if not db:
            raise ValueError(f"无法连接到案例 {case_id} 的数据库")
        
        try:
            # 获取要分析的文件列表（PostgreSQL模式）
            query = db.query(models.File).filter(models.File.case_id == case_id)
            if file_ids:
                query = query.filter(models.File.id.in_(file_ids))

            files_to_analyze = query.all()
            
            if not files_to_analyze:
                return {
                    "success": False,
                    "message": "未找到可分析的图片文件",
                    "total_files": 0,
                    "clusters_count": 0
                }
            
            # 异步分析每个文件
            logger.info(f"开始为案例 {case_id} 异步分析 {len(files_to_analyze)} 个文件...")
            analysis_results = []
            
            # 使用线程池加速计算密集型任务
            with ThreadPoolExecutor(max_workers=os.cpu_count() or 4) as executor:
                loop = asyncio.get_running_loop()
                tasks = []
                
                for file_record in files_to_analyze:
                    # 决定使用哪个文件路径进行分析
                    analysis_path = None
                    
                    file_path_str = getattr(file_record, 'file_path', None)
                    thumbnail_path_str = getattr(file_record, 'thumbnail_small_path', None)
                    
                    if file_path_str and os.path.exists(file_path_str):
                        analysis_path = file_path_str
                    elif thumbnail_path_str and os.path.exists(thumbnail_path_str):
                        analysis_path = thumbnail_path_str
                    
                    if not analysis_path:
                        logger.warning(f"文件 {file_record.file_name} 的路径不存在，跳过分析")
                        continue
                    
                    # 异步提交计算任务
                    task = loop.run_in_executor(
                        executor,
                        calculate_metrics,
                        analysis_path
                    )
                    tasks.append((task, file_record))
                
                # 等待所有任务完成
                for task, file_record in tasks:
                    metrics = await task
                    if metrics:
                        metrics['filename'] = getattr(file_record, 'file_name', '')
                        metrics['file_id'] = getattr(file_record, 'id', None)
                        analysis_results.append(metrics)
            
            if not analysis_results:
                return {
                    "success": False,
                    "message": "没有成功分析任何文件",
                    "total_files": 0,
                    "clusters_count": 0
                }
            
            # 创建DataFrame并进行质量分析处理
            df_result = pd.DataFrame(analysis_results)
            
            # 使用默认权重如果未提供
            if weights is None:
                weights = {
                    'sharpness': 0.5, 
                    'face_quality': 0.3, 
                    'dynamic_range': 0.15, 
                    'brightness': 0.05
                }
            
            # 归一化各项指标
            for col in weights.keys():
                if col in df_result.columns:
                    min_val = df_result[col].min()
                    max_val = df_result[col].max()
                    if max_val > min_val:
                        df_result[f'{col}_norm'] = (df_result[col] - min_val) / (max_val - min_val)
                    else:
                        df_result[f'{col}_norm'] = 0
            
            df_result.fillna(0, inplace=True)
            
            # 计算综合质量分数
            df_result['quality_score'] = sum(
                df_result[f'{key}_norm'] * weight 
                for key, weight in weights.items() 
                if f'{key}_norm' in df_result.columns
            )
            
            # 执行相似性聚类
            logger.info("开始执行相似性聚类...")
            df_result['cluster_id'] = -1
            cluster_counter = 0
            
            # 按质量分数降序排列
            df_sorted = df_result.sort_values(by='quality_score', ascending=False).reset_index(drop=True)
            
            for i in range(len(df_sorted)):
                if df_sorted.at[i, 'cluster_id'] != -1:
                    continue
                
                current_hash = df_sorted.at[i, 'phash']
                current_group = df_sorted.at[i, 'group_id']
                
                # 为当前图片分配聚类ID
                df_sorted.at[i, 'cluster_id'] = cluster_counter
                
                # 如果没有组ID，跳过相似性检查
                if current_group is None:
                    cluster_counter += 1
                    continue
                
                # 寻找相似图片
                for j in range(i + 1, len(df_sorted)):
                    if df_sorted.at[j, 'cluster_id'] != -1:
                        continue
                    
                    other_hash = df_sorted.at[j, 'phash']
                    other_group = df_sorted.at[j, 'group_id']
                    
                    # 同一卷宗且pHash相似
                    if (other_group == current_group and 
                        current_hash - other_hash <= phash_threshold):
                        df_sorted.at[j, 'cluster_id'] = cluster_counter
                
                cluster_counter += 1
            
            # 更新数据库中的文件记录
            update_count = _update_file_quality_metrics_by_id(db, case_id, df_sorted)
            
            result = {
                "success": True,
                "message": f"质量分析完成！处理了 {len(df_sorted)} 个文件，更新了 {update_count} 条记录",
                "total_files": len(df_sorted),
                "clusters_count": df_sorted['cluster_id'].nunique() if 'cluster_id' in df_sorted.columns else 0
            }
            
            # 生成Excel报告
            if generate_excel:
                case_dir = create_case_directory(case_id)
                reports_dir = case_dir / "reports"
                reports_dir.mkdir(exist_ok=True)
                
                report_path = reports_dir / f"quality_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                # 这里不生成Excel报告，因为需要重构
                result["message"] += f"，分析完成"
            
            logger.info(f"案例 {case_id} 的质量分析完成")
            return result
            
        finally:
            # PostgreSQL模式下使用主数据库会话，无需手动关闭
            pass
        
    except Exception as e:
        logger.error(f"案例 {case_id} 的质量分析失败: {e}")
        return {
            "success": False,
            "message": f"质量分析失败: {str(e)}",
            "total_files": 0,
            "clusters_count": 0
        }

# ... existing code ...