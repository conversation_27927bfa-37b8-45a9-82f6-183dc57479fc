{"version": 3, "sources": ["../../../src/hooks/useDrag/useDragSourceMonitor.ts"], "sourcesContent": ["import { useMemo } from 'react'\n\nimport { DragSourceMonitorImpl } from '../../internals/index.js'\nimport type { DragSourceMonitor } from '../../types/index.js'\nimport { useDragDropManager } from '../useDragDropManager.js'\n\nexport function useDragSourceMonitor<O, R>(): DragSourceMonitor<O, R> {\n\tconst manager = useDragDropManager()\n\treturn useMemo<DragSourceMonitor<O, R>>(\n\t\t() => new DragSourceMonitorImpl(manager),\n\t\t[manager],\n\t)\n}\n"], "names": ["useMemo", "DragSourceMonitorImpl", "useDragDropManager", "useDragSourceMonitor", "manager"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO,CAAA;AAE/B,SAASC,qBAAqB,QAAQ,0BAA0B,CAAA;AAEhE,SAASC,kBAAkB,QAAQ,0BAA0B,CAAA;AAE7D,OAAO,SAASC,oBAAoB,GAAkC;IACrE,MAAMC,OAAO,GAAGF,kBAAkB,EAAE;IACpC,OAAOF,OAAO,CACb,IAAM,IAAIC,qBAAqB,CAACG,OAAO,CAAC;IAAA,EACxC;QAACA,OAAO;KAAC,CACT,CAAA;CACD"}