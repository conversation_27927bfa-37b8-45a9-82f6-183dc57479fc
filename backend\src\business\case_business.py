# src/business/case_business.py
"""
案例相关的业务逻辑
"""
from typing import List, Dict, Any
from sqlalchemy.orm import Session

from .. import models, schemas
from ..crud import create_case, get_cases, delete_case


def create_case_with_validation(db: Session, case: schemas.CaseCreate) -> models.Case:
    """
    创建案例并进行业务验证
    """
    # 业务验证：检查案例名称是否重复
    existing_cases = get_cases(db, limit=1000)
    for existing_case in existing_cases:
        if existing_case.case_name == case.case_name:  # type: ignore
            raise ValueError(f"案例名称 '{case.case_name}' 已存在")
    
    # 创建案例
    return create_case(db, case)


def batch_delete_cases(db: Session, case_ids: List[int]) -> Dict[str, Any]:
    """
    批量删除案例
    """
    results = {
        "success": [],
        "failed": [],
        "total": len(case_ids)
    }
    
    for case_id in case_ids:
        try:
            success = delete_case(db, case_id)
            if success:
                results["success"].append(case_id)
            else:
                results["failed"].append({"case_id": case_id, "reason": "案例不存在"})
        except Exception as e:
            results["failed"].append({"case_id": case_id, "reason": str(e)})
    
    return results


def get_case_statistics(db: Session) -> Dict[str, Any]:
    """
    获取案例统计信息
    """
    # 获取活跃案例数量
    active_cases = get_cases(db, limit=10000)
    
    # 获取回收站案例数量
    deleted_cases = db.query(models.Case).filter(
        models.Case.status == models.CaseStatus.DELETED
    ).all()
    
    # 计算文件统计
    total_files = 0
    for case in active_cases:
        total_files += len(case.files)
    
    return {
        "active_cases": len(active_cases),
        "deleted_cases": len(deleted_cases),
        "total_files": total_files,
        "total_cases": len(active_cases) + len(deleted_cases)
    } 