# src/routers/filename_extraction.py
"""
文件名标签提取API路由
提供文件名标签提取的HTTP接口
"""
import logging
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session

from ..database import get_master_db
from ..services.filename_extraction import filename_extraction_service
from .. import schemas

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1", tags=["filename_extraction"])

@router.post(
    "/cases/{case_id}/files/extract-filename-tags",
    response_model=schemas.FilenameExtractionResponse,
    status_code=202
)
async def extract_filename_tags(
    case_id: int,
    request: schemas.FilenameExtractionRequest,
    db: Session = Depends(get_master_db)
):
    """
    从文件名提取标签

    这是一个异步操作，会立即返回任务ID，实际处理在后台进行。
    """
    try:
        # 验证案例是否存在
        from ..crud.case_crud import get_case
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")

        # 验证提取规则
        validation_errors = filename_extraction_service.validate_extraction_rules(
            request.extraction_rules
        )
        if validation_errors:
            raise HTTPException(
                status_code=400,
                detail={
                    "message": "提取规则验证失败",
                    "errors": validation_errors
                }
            )

        # 验证文件是否存在且属于该案例
        from ..crud.file_crud import get_file
        valid_file_ids = []
        for file_id in request.file_ids:
            file_record = get_file(db, file_id, case_id)
            if not file_record:
                logger.warning(f"文件 {file_id} 不存在或不属于案例 {case_id}，跳过")
                continue
            valid_file_ids.append(file_id)

        if not valid_file_ids:
            raise HTTPException(
                status_code=400,
                detail="没有找到有效的文件"
            )

        # 生成任务ID
        task_id = filename_extraction_service.generate_task_id()

        # 直接执行任务（同步方式）
        try:
            filename_extraction_service.process_filename_extraction_task(
                task_id,
                case_id,
                valid_file_ids,
                request.extraction_rules,
                request.options
            )
        except Exception as e:
            logger.error(f"任务执行失败: {e}")
            # 更新任务状态为失败
            filename_extraction_service.active_tasks[task_id]["status"] = "failed"
            filename_extraction_service.active_tasks[task_id]["error"] = str(e)

        # 估算处理时间（每个文件约0.1秒）
        estimated_seconds = len(valid_file_ids) * 0.1
        estimated_time = f"{estimated_seconds:.1f}秒" if estimated_seconds < 60 else f"{estimated_seconds/60:.1f}分钟"

        logger.info(f"启动文件名提取任务 {task_id}，处理 {len(valid_file_ids)} 个文件")

        return schemas.FilenameExtractionResponse(
            task_id=task_id,
            status="accepted",
            message="标签提取任务已启动",
            estimated_time=estimated_time,
            files_count=len(valid_file_ids),
            rules_count=len(request.extraction_rules)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动文件名提取任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动任务失败: {str(e)}")

@router.get(
    "/tasks/filename-extraction/{task_id}/status",
    response_model=Dict[str, Any]
)
async def get_extraction_task_status(task_id: str):
    """
    获取文件名提取任务状态
    """
    try:
        task_status = filename_extraction_service.get_task_status(task_id)

        if not task_status:
            raise HTTPException(status_code=404, detail="任务不存在")

        return {
            "task_id": task_id,
            "status": task_status["status"],
            "progress": {
                "completed": task_status["progress"],
                "total": task_status["total"],
                "current_file": task_status.get("current_file")
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")

@router.post(
    "/cases/{case_id}/files/{file_id}/preview-filename-extraction",
    response_model=Dict[str, str]
)
async def preview_filename_extraction(
    case_id: int,
    file_id: int,
    rules: schemas.FilenameExtractionRequest,
    db: Session = Depends(get_master_db)
):
    """
    预览文件名提取结果

    用于在实际执行前预览单个文件的提取结果
    """
    try:
        # 验证案例和文件
        from ..crud.case_crud import get_case
        from ..crud.file_crud import get_file

        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")

        file_record = get_file(db, file_id, case_id)
        if not file_record:
            raise HTTPException(status_code=404, detail="文件不存在或不属于指定案例")

        # 验证提取规则
        validation_errors = filename_extraction_service.validate_extraction_rules(
            rules.extraction_rules
        )
        if validation_errors:
            raise HTTPException(
                status_code=400,
                detail={
                    "message": "提取规则验证失败",
                    "errors": validation_errors
                }
            )

        # 预览提取结果
        extracted_tags = filename_extraction_service.extract_tags_from_filename(
            file_record.file_name,
            rules.extraction_rules
        )

        return extracted_tags

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"预览文件名提取失败: {e}")
        raise HTTPException(status_code=500, detail=f"预览失败: {str(e)}")
