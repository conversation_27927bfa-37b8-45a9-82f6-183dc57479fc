// Project Novak - MSW Request Handlers
// 真实可用的 API 端点模拟，完全符合服务层期望

import { http, HttpResponse } from 'msw';
import {
  mockCases,
  mockFiles,
  mockTags,
  getFilesByCase,
  searchFiles,
  getTagsByCase
} from './data';
import type { ApiResponse, PaginatedResponse } from '@/types';

// ============================================================================
// Helper Functions
// ============================================================================

/**
 * 创建标准 API 响应
 */
function createApiResponse<T>(data: T, message?: string): ApiResponse<T> {
  return {
    data,
    message: message || 'Success',
    status: 'success'
  };
}

/**
 * 创建分页响应
 */
function createPaginatedResponse<T>(
  items: T[],
  page: number = 1,
  limit: number = 50
): PaginatedResponse<T> {
  const start = (page - 1) * limit;
  const end = start + limit;
  const paginatedItems = items.slice(start, end);

  return {
    data: paginatedItems,
    total: items.length,
    page,
    limit,
    hasNext: end < items.length,
    hasPrev: page > 1
  };
}

/**
 * 模拟网络延迟
 */
function delay(ms: number = 300): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// ============================================================================
// API Request Handlers
// ============================================================================

export const handlers = [
  // ========================================
  // Cases API
  // ========================================

  /**
   * GET /api/cases - 获取所有案例
   */
  http.get('/api/cases', async () => {
    await delay(200);

    console.log('🔄 MSW: GET /api/cases');

    return HttpResponse.json(
      createApiResponse(mockCases),
      { status: 200 }
    );
  }),

  /**
   * GET /api/cases/:id - 获取单个案例
   */
  http.get('/api/cases/:id', async ({ params }) => {
    await delay(150);

    const caseId = parseInt(params.id as string);
    const case_ = mockCases.find(c => c.id === caseId);

    console.log(`🔄 MSW: GET /api/cases/${caseId}`);

    if (!case_) {
      return HttpResponse.json(
        { error: 'Case not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json(
      createApiResponse(case_),
      { status: 200 }
    );
  }),

  /**
   * POST /api/cases - 创建新案例
   */
  http.post('/api/cases', async ({ request }) => {
    await delay(400);

    const body = await request.json() as any;
    console.log('🔄 MSW: POST /api/cases', body);

    const newCase = {
      id: mockCases.length + 1,
      case_name: body.case_name || 'New Case',
      description: body.description || null,
      created_at: new Date().toISOString(),
      status: 'active' as const,
      deleted_at: null,
      db_path: `/data/cases/case_${mockCases.length + 1}.db`
    };

    mockCases.push(newCase);

    return HttpResponse.json(
      createApiResponse(newCase, 'Case created successfully'),
      { status: 201 }
    );
  }),

  // ========================================
  // Files API
  // ========================================

  /**
   * GET /api/files - 获取文件列表（支持筛选和分页）
   */
  http.get('/api/files', async ({ request }) => {
    await delay(300);

    const url = new URL(request.url);
    const caseId = url.searchParams.get('case_id');
    const search = url.searchParams.get('search');
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '50');

    console.log('🔄 MSW: GET /api/files', { caseId, search, page, limit });

    let files = mockFiles;

    // 按案例筛选
    if (caseId) {
      files = getFilesByCase(parseInt(caseId));
    }

    // 按搜索查询筛选
    if (search) {
      files = searchFiles(search, caseId ? parseInt(caseId) : undefined);
    }

    const paginatedResponse = createPaginatedResponse(files, page, limit);

    return HttpResponse.json(
      paginatedResponse,
      { status: 200 }
    );
  }),

  /**
   * GET /api/files/:id - 获取单个文件详情
   */
  http.get('/api/files/:id', async ({ params }) => {
    await delay(150);

    const fileId = parseInt(params.id as string);
    const file = mockFiles.find(f => f.id === fileId);

    console.log(`🔄 MSW: GET /api/files/${fileId}`);

    if (!file) {
      return HttpResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json(
      createApiResponse(file),
      { status: 200 }
    );
  }),

  /**
   * POST /api/files/upload - 上传文件
   */
  http.post('/api/files/upload', async ({ request: _request }) => {
    await delay(1000); // 模拟上传时间

    console.log('🔄 MSW: POST /api/files/upload');

    // 模拟上传成功，返回新文件
    const newFile = {
      id: mockFiles.length + 1,
      case_id: 1, // 默认案例
      file_name: 'uploaded_image.jpg',
      file_path: `https://picsum.photos/1920/1080?random=${mockFiles.length + 1}`,
      file_type: 'image/jpeg',
      file_size: 2048000,
      width: 1920,
      height: 1080,
      created_at: new Date().toISOString(),
      taken_at: new Date().toISOString(),
      thumbnail_small_path: `https://picsum.photos/200/200?random=${mockFiles.length + 1}`,
      tags: null,
      quality_score: null,
      sharpness: null,
      brightness: null,
      dynamic_range: null,
      num_faces: null,
      face_sharpness: null,
      face_quality: null,
      cluster_id: null,
      phash: null
    };

    mockFiles.push(newFile);

    return HttpResponse.json(
      createApiResponse([newFile], 'File uploaded successfully'),
      { status: 201 }
    );
  }),

  // ========================================
  // Tags API
  // ========================================

  /**
   * GET /api/tags - 获取标签列表
   */
  http.get('/api/tags', async ({ request }) => {
    await delay(200);

    const url = new URL(request.url);
    const caseId = url.searchParams.get('case_id');

    console.log('🔄 MSW: GET /api/tags', { caseId });

    const tags = caseId ? getTagsByCase(parseInt(caseId)) : mockTags;

    return HttpResponse.json(
      createApiResponse(tags),
      { status: 200 }
    );
  }),

  /**
   * POST /api/tags/update - 更新文件标签
   */
  http.post('/api/tags/update', async ({ request }) => {
    await delay(300);

    const body = await request.json() as any;
    console.log('🔄 MSW: POST /api/tags/update', body);

    // 模拟标签更新成功
    return HttpResponse.json(
      createApiResponse(null, 'Tag updated successfully'),
      { status: 200 }
    );
  }),

  /**
   * POST /api/tags/batch-update - 批量更新标签
   */
  http.post('/api/tags/batch-update', async ({ request }) => {
    await delay(500);

    const body = await request.json() as any;
    console.log('🔄 MSW: POST /api/tags/batch-update', body);

    return HttpResponse.json(
      createApiResponse(null, 'Tags updated successfully'),
      { status: 200 }
    );
  }),

  // ========================================
  // Search API
  // ========================================

  /**
   * GET /api/search/files - 搜索文件
   */
  http.get('/api/search/files', async ({ request }) => {
    await delay(400);

    const url = new URL(request.url);
    const query = url.searchParams.get('q') || '';
    const caseId = url.searchParams.get('case_id');

    console.log('🔄 MSW: GET /api/search/files', { query, caseId });

    const results = searchFiles(query, caseId ? parseInt(caseId) : undefined);

    return HttpResponse.json(
      createApiResponse(results),
      { status: 200 }
    );
  }),

  /**
   * GET /api/search/tags - 搜索标签
   */
  http.get('/api/search/tags', async ({ request }) => {
    await delay(300);

    const url = new URL(request.url);
    const query = url.searchParams.get('q') || '';
    const caseId = url.searchParams.get('case_id');

    console.log('🔄 MSW: GET /api/search/tags', { query, caseId });

    let tags = caseId ? getTagsByCase(parseInt(caseId)) : mockTags;

    if (query) {
      const lowerQuery = query.toLowerCase();
      tags = tags.filter(tag =>
        tag.key.toLowerCase().includes(lowerQuery) ||
        tag.value.toString().toLowerCase().includes(lowerQuery)
      );
    }

    return HttpResponse.json(
      createApiResponse(tags),
      { status: 200 }
    );
  }),

  // ========================================
  // Fallback Handler
  // ========================================

  /**
   * 捕获所有未匹配的 API 请求
   */
  http.all('/api/*', async ({ request }) => {
    console.warn('🚨 MSW: Unhandled API request:', request.method, request.url);

    return HttpResponse.json(
      { error: 'API endpoint not implemented in mock' },
      { status: 501 }
    );
  })
];

export default handlers;
