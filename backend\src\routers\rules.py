# src/routers/rules.py
"""
规则管理路由 - 案例处理规则的完整CRUD API
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
import logging

from ..database import get_master_db
from ..crud.case_crud import get_case
from ..crud.rule_crud import (
    create_rule, get_rules_by_case, get_rule_by_id, 
    update_rule, delete_rule, get_active_rules_for_case
)
from .. import models, schemas

logger = logging.getLogger(__name__)

router = APIRouter()

# ==================== 规则类型管理 ====================

@router.get("/rule-types", response_model=List[str])
def get_rule_types():
    """
    获取支持的规则类型列表
    """
    try:
        # 从枚举中获取所有规则类型
        rule_types = [rule_type.value for rule_type in models.RuleType]
        return rule_types
    except Exception as e:
        logger.error(f"获取规则类型失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取规则类型失败: {str(e)}")

@router.post("/rule-config/validate")
def validate_rule_config(
    rule_type: str,
    rule_config: Dict[str, Any],
    db: Session = Depends(get_master_db)
):
    """
    验证规则配置的有效性
    """
    try:
        # 验证规则类型是否支持
        valid_types = [rt.value for rt in models.RuleType]
        if rule_type not in valid_types:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的规则类型: {rule_type}。支持的类型: {valid_types}"
            )
        
        # 根据规则类型验证配置
        validation_result = _validate_rule_config_by_type(rule_type, rule_config)
        
        return {
            "valid": validation_result["valid"],
            "message": validation_result["message"],
            "suggestions": validation_result.get("suggestions", [])
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证规则配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"验证规则配置失败: {str(e)}")

# ==================== 案例规则管理 ====================

@router.post("/cases/{case_id}/rules", response_model=schemas.CaseProcessingRule, status_code=201)
def create_case_rule(
    case_id: int,
    rule: schemas.CreateRuleRequest,
    db: Session = Depends(get_master_db)
):
    """
    为指定案例创建处理规则
    """
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")
        
        # 验证规则配置
        validation = _validate_rule_config_by_type(rule.rule_type.value, rule.rule_config)
        if not validation["valid"]:
            raise HTTPException(status_code=400, detail=f"规则配置无效: {validation['message']}")
        
        # 创建规则
        new_rule = create_rule(db, case_id, rule)
        logger.info(f"成功创建规则: {new_rule.id} (案例: {case_id})")
        
        return new_rule
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建规则失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建规则失败: {str(e)}")

@router.get("/cases/{case_id}/rules", response_model=List[schemas.CaseProcessingRule])
def get_case_rules(
    case_id: int,
    is_active: Optional[bool] = Query(None, description="筛选激活状态的规则"),
    rule_type: Optional[str] = Query(None, description="筛选特定类型的规则"),
    db: Session = Depends(get_master_db)
):
    """
    获取指定案例的所有处理规则
    """
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")
        
        # 转换rule_type字符串为枚举
        rule_type_enum = None
        if rule_type:
            try:
                rule_type_enum = models.RuleType(rule_type)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"无效的规则类型: {rule_type}")
        
        # 获取规则列表
        rules = get_rules_by_case(db, case_id, is_active, rule_type_enum)
        logger.info(f"获取案例 {case_id} 的规则列表: {len(rules)} 个规则")
        
        return rules
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取规则列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取规则列表失败: {str(e)}")

# ==================== 单个规则管理 ====================

@router.get("/rules/{rule_id}", response_model=schemas.CaseProcessingRule)
def get_rule_detail(
    rule_id: int,
    db: Session = Depends(get_master_db)
):
    """
    获取规则详细信息
    """
    try:
        rule = get_rule_by_id(db, rule_id)
        if not rule:
            raise HTTPException(status_code=404, detail="规则不存在")
        
        return rule
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取规则详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取规则详情失败: {str(e)}")

@router.put("/rules/{rule_id}", response_model=schemas.CaseProcessingRule)
def update_rule_endpoint(
    rule_id: int,
    rule_update: schemas.UpdateRuleRequest,
    db: Session = Depends(get_master_db)
):
    """
    更新规则配置
    """
    try:
        # 验证规则存在
        existing_rule = get_rule_by_id(db, rule_id)
        if not existing_rule:
            raise HTTPException(status_code=404, detail="规则不存在")
        
        # 如果更新了规则配置，需要验证
        if rule_update.rule_config is not None:
            validation = _validate_rule_config_by_type(
                existing_rule.rule_type.value, 
                rule_update.rule_config
            )
            if not validation["valid"]:
                raise HTTPException(status_code=400, detail=f"规则配置无效: {validation['message']}")
        
        # 更新规则
        updated_rule = update_rule(db, rule_id, rule_update)
        if not updated_rule:
            raise HTTPException(status_code=404, detail="规则不存在")
        
        logger.info(f"成功更新规则: {rule_id}")
        return updated_rule
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新规则失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新规则失败: {str(e)}")

@router.delete("/rules/{rule_id}", response_model=schemas.OperationResponse)
def delete_rule_endpoint(
    rule_id: int,
    db: Session = Depends(get_master_db)
):
    """
    删除规则
    """
    try:
        # 验证规则存在
        existing_rule = get_rule_by_id(db, rule_id)
        if not existing_rule:
            raise HTTPException(status_code=404, detail="规则不存在")
        
        # 删除规则
        success = delete_rule(db, rule_id)
        if not success:
            raise HTTPException(status_code=404, detail="规则不存在")
        
        logger.info(f"成功删除规则: {rule_id}")
        return schemas.OperationResponse(
            success=True,
            message=f"规则 {rule_id} 已成功删除"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除规则失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除规则失败: {str(e)}")

# ==================== 规则测试 ====================

@router.post("/rules/{rule_id}/test")
def test_rule(
    rule_id: int,
    test_filename: str = Query(..., description="测试文件名"),
    db: Session = Depends(get_master_db)
):
    """
    测试规则对指定文件名的处理结果
    """
    try:
        # 获取规则
        rule = get_rule_by_id(db, rule_id)
        if not rule:
            raise HTTPException(status_code=404, detail="规则不存在")
        
        # 创建模拟文件对象进行测试
        from ..services.rule_engine import rule_engine
        
        # 创建临时文件对象
        mock_file = type('MockFile', (), {
            'file_name': test_filename,
            'file_path': f'/mock/path/{test_filename}',
            'quality_score': 85.0
        })()
        
        # 测试规则处理
        if rule.rule_type.value == "FILENAME_PARSING":
            result = rule_engine._process_filename_parsing(mock_file, rule.rule_config)
        elif rule.rule_type.value == "DATE_TAGGING_FORMAT":
            result = rule_engine._process_date_tagging(mock_file, rule.rule_config)
        else:
            raise HTTPException(status_code=400, detail=f"不支持测试的规则类型: {rule.rule_type.value}")
        
        return {
            "rule_id": rule_id,
            "test_filename": test_filename,
            "result": result,
            "success": result is not None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"测试规则失败: {e}")
        raise HTTPException(status_code=500, detail=f"测试规则失败: {str(e)}")

# ==================== 辅助函数 ====================

def _validate_rule_config_by_type(rule_type: str, rule_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    根据规则类型验证配置
    """
    if rule_type == "FILENAME_PARSING":
        return _validate_filename_parsing_config(rule_config)
    elif rule_type == "DATE_TAGGING_FORMAT":
        return _validate_date_tagging_config(rule_config)
    else:
        return {"valid": False, "message": f"未知的规则类型: {rule_type}"}

def _validate_filename_parsing_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """验证文件名解析规则配置"""
    required_fields = ["pattern", "tags"]
    
    for field in required_fields:
        if field not in config:
            return {"valid": False, "message": f"缺少必需字段: {field}"}
    
    # 验证正则表达式
    try:
        import re
        re.compile(config["pattern"])
    except re.error as e:
        return {"valid": False, "message": f"无效的正则表达式: {str(e)}"}
    
    # 验证标签映射
    if not isinstance(config["tags"], dict):
        return {"valid": False, "message": "tags字段必须是字典类型"}
    
    return {"valid": True, "message": "配置有效"}

def _validate_date_tagging_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """验证日期标签格式化规则配置"""
    required_fields = ["date_format"]
    
    for field in required_fields:
        if field not in config:
            return {"valid": False, "message": f"缺少必需字段: {field}"}
    
    # 验证日期格式
    valid_formats = ["YYYY-MM-DD", "YYYY/MM/DD", "DD-MM-YYYY", "MM/DD/YYYY"]
    if config["date_format"] not in valid_formats:
        return {
            "valid": False, 
            "message": f"不支持的日期格式: {config['date_format']}。支持的格式: {valid_formats}"
        }
    
    return {"valid": True, "message": "配置有效"}
