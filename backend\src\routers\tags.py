"""
标签管理路由 - PostgreSQL版本
只包含PostgreSQL兼容的标签功能
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
import logging
import json

from ..database import get_master_db
from ..crud.case_crud import get_case
from .. import models, schemas

logger = logging.getLogger(__name__)

router = APIRouter()

# 为了兼容前端，添加cases路径的路由
@router.get("/{case_id}/tags/tree")
def get_tag_tree_for_case(
    case_id: int,
    include_empty: bool = False,
    db: Session = Depends(get_master_db)
):
    """
    获取案例的标签树结构（兼容前端路径）
    """
    return get_optimized_tag_tree(case_id, db)

@router.get("/{case_id}/tags/search")
def search_tags_for_case(
    case_id: int,
    q: str,
    limit: int = 20,
    db: Session = Depends(get_master_db)
):
    """
    搜索案例标签（兼容前端路径）
    """
    return search_tags(case_id, q, limit, db)

@router.get("/{case_id}/tags/files/{file_id}/all")
def get_file_all_tags_for_case(
    case_id: int,
    file_id: int,
    db: Session = Depends(get_master_db)
):
    """
    获取文件的所有标签信息（兼容前端路径）
    """
    return get_file_all_tags(case_id, file_id, db)

@router.get("/tree-simple")
def get_tag_tree_simple(
    case_id: int,
    db: Session = Depends(get_master_db)
):
    """
    获取案例的精简标签树结构（用于标签浏览器）

    返回按类别组织的标签层级结构，专门为精简标签浏览器优化
    """
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")

        # PostgreSQL模式：从主数据库查询文件
        files = db.query(models.File).filter(models.File.case_id == case_id).all()

        # PostgreSQL模式下暂时不处理已删除文件
        deleted_file_ids = set()
        active_files = files

        # 构建精简标签树
        tag_tree = _build_simple_tag_tree(active_files)

        return tag_tree

    except Exception as e:
        logger.error(f"获取精简标签树失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取精简标签树失败: {str(e)}")

def get_optimized_tag_tree(case_id: int, db: Session):
    """
    获取优化的标签树结构 - 使用数据库聚合查询而不是客户端计算

    这个函数使用SQL聚合查询直接从数据库获取标签统计信息，
    避免了在客户端进行大量的数据处理，显著提升性能。
    """
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")

        logger.info(f"🚀 开始获取案例 {case_id} 的优化标签树")

        # 使用SQL聚合查询获取标签统计信息
        tag_tree = {
            "tags": {
                "metadata": {},
                "cv": {},
                "user": {},
                "ai": {}
            },
            "properties": {},
            "custom": []  # 自定义标签将在后面单独处理
        }

        # 1. 获取元数据标签统计（直接在这里处理）
        metadata_stats = {}
        try:
            # 获取所有文件的元数据标签
            files = db.query(models.File).filter(models.File.case_id == case_id).all()
            logger.info(f"开始处理案例 {case_id} 的 {len(files)} 个文件的元数据标签")

            for file in files:
                if file.tags:
                    try:
                        import json
                        tags_data = json.loads(file.tags) if isinstance(file.tags, str) else file.tags
                        # 元数据标签路径：tags.tags.metadata
                        metadata = tags_data.get('tags', {}).get('metadata', {})

                        for key, value in metadata.items():
                            tag_key = f"{key}: {value}"
                            if tag_key in metadata_stats:
                                metadata_stats[tag_key] += 1
                            else:
                                metadata_stats[tag_key] = 1
                    except Exception as e:
                        logger.warning(f"解析文件 {file.id} 的元数据标签失败: {e}")

            logger.info(f"元数据标签统计完成，生成 {len(metadata_stats)} 个标签")
        except Exception as e:
            logger.error(f"获取元数据标签统计失败: {e}")

        tag_tree["tags"]["metadata"] = metadata_stats

        # 2. 获取CV标签统计
        cv_stats = _get_cv_tag_stats(db, case_id)
        tag_tree["tags"]["cv"] = cv_stats

        # 3. 获取属性标签统计（直接在这里处理）
        properties_stats = {}
        try:
            # 获取所有文件的属性标签
            files = db.query(models.File).filter(models.File.case_id == case_id).all()
            logger.info(f"开始处理案例 {case_id} 的 {len(files)} 个文件的属性标签")

            for file in files:
                if file.tags:
                    try:
                        import json
                        tags_data = json.loads(file.tags) if isinstance(file.tags, str) else file.tags
                        properties = tags_data.get('properties', {})

                        for key, value in properties.items():
                            tag_key = f"{key}: {value}"
                            if tag_key in properties_stats:
                                properties_stats[tag_key] += 1
                            else:
                                properties_stats[tag_key] = 1
                    except Exception as e:
                        logger.warning(f"解析文件 {file.id} 的属性标签失败: {e}")

            logger.info(f"属性标签统计完成，生成 {len(properties_stats)} 个标签")
        except Exception as e:
            logger.error(f"获取属性标签统计失败: {e}")

        # 获取质量分数统计
        quality_stats = _get_quality_stats(db, case_id)
        # 合并属性标签和质量分数
        tag_tree["properties"] = {**properties_stats, **quality_stats}

        # 4. 获取用户标签统计（自定义标签）
        user_stats = _get_user_tag_stats(db, case_id)
        tag_tree["tags"]["user"] = user_stats

        # 5. 获取AI标签统计
        ai_stats = _get_ai_tag_stats(db, case_id)
        tag_tree["tags"]["ai"] = ai_stats

        logger.info(f"✅ 优化标签树获取完成，案例 {case_id}")
        return tag_tree

    except Exception as e:
        logger.error(f"获取优化标签树失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取优化标签树失败: {str(e)}")

@router.get("/")
def get_tags(
    case_id: int,
    include_empty: bool = False,
    db: Session = Depends(get_master_db)
):
    """
    获取案例的所有标签信息
    
    返回按类别组织的标签统计信息，包括每个标签的文件数量
    """
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")
        
        # PostgreSQL数据库架构
        
        # PostgreSQL模式：从主数据库查询文件
        files = db.query(models.File).filter(models.File.case_id == case_id).all()

        # PostgreSQL模式下暂时不处理已删除文件（可以后续扩展）
        deleted_file_ids = set()
        active_files = files
        
        # 获取自定义标签（PostgreSQL模式暂不支持）
        custom_tag_data = []
        logger.info("PostgreSQL模式：跳过自定义标签查询")
        
        # 构建标签统计
        tag_stats = _build_tag_statistics(active_files, include_empty)
        
        return {
            "ai_tags": tag_stats.get("ai_tags", []),
            "user_tags": tag_stats.get("user_tags", []),
            "properties": tag_stats.get("properties", []),
            "custom_tags": custom_tag_data,
            "total_files": len(active_files),
            "active_files": len(active_files)
        }
        
    except Exception as e:
        logger.error(f"获取标签信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取标签信息失败: {str(e)}")

@router.get("/files")
def get_files_by_tag(
    case_id: int,
    category: str,
    name: str,
    value: str,
    db: Session = Depends(get_master_db)
):
    """
    根据标签获取文件列表
    
    返回包含指定标签的所有文件信息
    """
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")
        
        # PostgreSQL模式：从主数据库查询文件
        files = db.query(models.File).filter(models.File.case_id == case_id).all()
        
        # PostgreSQL模式下暂时不处理已删除文件
        deleted_file_ids = set()
        active_files = files
        
        # 根据标签筛选文件
        matching_files = _filter_files_by_tag(active_files, category, name, value)
        
        # 构建文件信息列表
        file_list = []
        for file in matching_files:
            # 构建缩略图路径
            thumbnail_path = f"/data/case_{case_id}/thumbnails/{file.file_name}_thumb.jpg"
            
            file_info = {
                "id": file.id,
                "name": file.file_name,
                "path": file.file_path,
                "thumbnail": thumbnail_path,
                "size": getattr(file, 'size', None),  # File模型可能没有size字段
                "created_at": file.created_at.isoformat() if file.created_at else None
            }
            file_list.append(file_info)
        
        return {
            "files": file_list,
            "total": len(file_list),
            "tag_info": {
                "category": category,
                "name": name,
                "value": value
            }
        }
            
    except Exception as e:
        logger.error(f"根据标签获取文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"根据标签获取文件失败: {str(e)}")

@router.get("/search")
def search_tags(
    case_id: int,
    q: str,
    limit: int = 20,
    db: Session = Depends(get_master_db)
):
    """
    搜索标签
    
    在所有标签类别中搜索匹配的标签
    """
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")
        
        # PostgreSQL模式：从主数据库查询文件
        files = db.query(models.File).filter(models.File.case_id == case_id).all()
        deleted_file_ids = set()
        active_files = files
        
        # 搜索标签
        search_results = _search_tags_in_files(active_files, q, limit)
        
        # PostgreSQL模式：暂时不支持自定义标签搜索
        # 可以后续扩展自定义标签功能
        
        return {
            "results": search_results,
            "total": len(search_results),
            "query": q
        }
        
    except Exception as e:
        logger.error(f"搜索标签失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索标签失败: {str(e)}")

# 自定义标签相关的端点都标记为不支持
@router.post("/custom")
def create_custom_tag(
    case_id: int,
    tag_data: schemas.CustomTagCreate,
    db: Session = Depends(get_master_db)
):
    """
    创建自定义标签（PostgreSQL模式暂不支持）
    """
    raise HTTPException(status_code=501, detail="PostgreSQL模式下暂不支持自定义标签功能")

@router.put("/custom/{tag_id}")
def update_custom_tag(
    case_id: int,
    tag_id: int,
    tag_data: schemas.CustomTagUpdate,
    db: Session = Depends(get_master_db)
):
    """
    更新自定义标签（PostgreSQL模式暂不支持）
    """
    raise HTTPException(status_code=501, detail="PostgreSQL模式下暂不支持自定义标签功能")

@router.delete("/custom/{tag_id}")
def delete_custom_tag(
    case_id: int,
    tag_id: int,
    db: Session = Depends(get_master_db)
):
    """
    删除自定义标签（PostgreSQL模式暂不支持）
    """
    raise HTTPException(status_code=501, detail="PostgreSQL模式下暂不支持自定义标签功能")

@router.get("/files/{file_id}/all")
def get_file_all_tags(
    case_id: int,
    file_id: int,
    db: Session = Depends(get_master_db)
):
    """
    获取文件的所有标签信息
    """
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")

        # 获取文件
        file = db.query(models.File).filter(
            models.File.id == file_id,
            models.File.case_id == case_id
        ).first()

        if not file:
            raise HTTPException(status_code=404, detail="文件不存在")

        # 返回文件的标签数据
        if file.tags:
            if isinstance(file.tags, dict):
                return file.tags
            else:
                return json.loads(file.tags)
        else:
            return {
                "properties": {},
                "tags": {
                    "metadata": {},
                    "cv": {},
                    "user": [],
                    "ai": []
                }
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文件标签失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取文件标签失败: {str(e)}")

@router.post("/files/{file_id}/custom")
def add_file_custom_tags(
    case_id: int,
    file_id: int,
    tag_data: schemas.FileCustomTagsAdd,
    db: Session = Depends(get_master_db)
):
    """
    为文件添加自定义标签（PostgreSQL模式暂不支持）
    """
    raise HTTPException(status_code=501, detail="PostgreSQL模式下暂不支持自定义标签功能")

@router.delete("/files/{file_id}/custom/{tag_id}")
def remove_file_custom_tag(
    case_id: int,
    file_id: int,
    tag_id: int,
    db: Session = Depends(get_master_db)
):
    """
    移除文件的自定义标签（PostgreSQL模式暂不支持）
    """
    raise HTTPException(status_code=501, detail="PostgreSQL模式下暂不支持自定义标签功能")

@router.post("/batch")
def batch_tag_operation(
    case_id: int,
    operation_data: schemas.BatchTagOperation,
    db: Session = Depends(get_master_db)
):
    """
    批量标签操作（PostgreSQL模式暂不支持）
    """
    raise HTTPException(status_code=501, detail="PostgreSQL模式下暂不支持自定义标签功能")

@router.get("/database/schema")
def check_tag_database_schema(db: Session = Depends(get_master_db)):
    """
    检查标签数据库结构
    """
    try:
        # PostgreSQL模式下，标签数据存储在files表的tags字段中
        # 检查files表是否存在tags字段
        from sqlalchemy import text

        result = db.execute(text("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'files' AND column_name = 'tags'
        """))

        has_tags_column = result.fetchone() is not None

        return {
            "status": "ok",
            "database_type": "postgresql",
            "has_tags_column": has_tags_column,
            "message": "PostgreSQL模式，标签数据存储在files.tags字段中"
        }

    except Exception as e:
        logger.error(f"检查数据库结构失败: {e}")
        raise HTTPException(status_code=500, detail=f"检查数据库结构失败: {str(e)}")

@router.get("/{case_id}/tags/database/schema")
def check_tag_database_schema_for_case(case_id: int, db: Session = Depends(get_master_db)):
    """
    检查标签数据库结构（兼容前端路径）
    """
    return check_tag_database_schema(db)

# 优化的标签统计函数
def _get_metadata_tag_stats(db: Session, case_id: int) -> Dict[str, Any]:
    """获取元数据标签统计 - 使用SQL聚合查询"""
    try:
        from sqlalchemy import text

        # 使用SQL聚合查询获取元数据标签统计
        result = db.execute(text("""
            WITH metadata_tags AS (
                SELECT
                    f.id as file_id,
                    jsonb_each_text(f.tags->'tags'->'metadata') as tag_pair
                FROM files f
                WHERE f.case_id = :case_id
                AND f.tags->'tags'->'metadata' IS NOT NULL
                AND jsonb_typeof(f.tags->'tags'->'metadata') = 'object'
            )
            SELECT
                (tag_pair).key as tag_name,
                (tag_pair).value as tag_value,
                COUNT(*) as file_count,
                ARRAY_AGG(file_id) as file_ids
            FROM metadata_tags
            GROUP BY (tag_pair).key, (tag_pair).value
            ORDER BY tag_name, file_count DESC
        """), {"case_id": case_id})

        metadata_stats = {}
        for row in result:
            tag_name = row.tag_name
            tag_value = row.tag_value
            file_count = row.file_count
            file_ids = row.file_ids

            if tag_name not in metadata_stats:
                metadata_stats[tag_name] = {}

            metadata_stats[tag_name][tag_value] = {
                "count": file_count,
                "file_ids": file_ids
            }

        return metadata_stats

    except Exception as e:
        logger.warning(f"获取元数据标签统计失败: {e}")
        return {}

def _get_cv_tag_stats(db: Session, case_id: int) -> Dict[str, Any]:
    """获取CV标签统计 - 使用SQL聚合查询"""
    try:
        from sqlalchemy import text

        # 获取CV标签统计
        result = db.execute(text("""
            WITH cv_tags AS (
                SELECT
                    f.id as file_id,
                    jsonb_each_text(f.tags->'tags'->'cv') as tag_pair
                FROM files f
                WHERE f.case_id = :case_id
                AND f.tags->'tags'->'cv' IS NOT NULL
                AND jsonb_typeof(f.tags->'tags'->'cv') = 'object'
            )
            SELECT
                (tag_pair).key as tag_name,
                (tag_pair).value as tag_value,
                COUNT(*) as file_count,
                ARRAY_AGG(file_id) as file_ids
            FROM cv_tags
            GROUP BY (tag_pair).key, (tag_pair).value
            ORDER BY tag_name, file_count DESC
        """), {"case_id": case_id})

        cv_stats = {}
        for row in result:
            tag_name = row.tag_name
            tag_value = row.tag_value
            file_count = row.file_count
            file_ids = row.file_ids

            if tag_name not in cv_stats:
                cv_stats[tag_name] = {}

            cv_stats[tag_name][tag_value] = {
                "count": file_count,
                "file_ids": file_ids
            }

        return cv_stats

    except Exception as e:
        logger.warning(f"获取CV标签统计失败: {e}")
        return {}

def _get_properties_tag_stats(db: Session, case_id: int) -> Dict[str, Any]:
    """获取属性标签统计 - 使用SQL聚合查询"""
    try:
        from sqlalchemy import text

        logger.info(f"开始获取案例 {case_id} 的属性标签统计")

        # 使用SQL聚合查询获取属性标签统计
        result = db.execute(text("""
            WITH properties_tags AS (
                SELECT
                    f.id as file_id,
                    jsonb_each_text(f.tags->'properties') as tag_pair
                FROM files f
                WHERE f.case_id = :case_id
                AND f.tags->'properties' IS NOT NULL
                AND jsonb_typeof(f.tags->'properties') = 'object'
            )
            SELECT
                (tag_pair).key as tag_name,
                (tag_pair).value as tag_value,
                COUNT(*) as file_count,
                ARRAY_AGG(file_id) as file_ids
            FROM properties_tags
            GROUP BY (tag_pair).key, (tag_pair).value
            ORDER BY tag_name, file_count DESC
        """), {"case_id": case_id})

        properties_stats = {}
        row_count = 0
        for row in result:
            row_count += 1
            tag_name = row.tag_name
            tag_value = row.tag_value
            file_count = row.file_count
            file_ids = row.file_ids

            # 构建标签键，格式：tag_name: tag_value
            tag_key = f"{tag_name}: {tag_value}"
            properties_stats[tag_key] = file_count

        logger.info(f"属性标签统计完成，获取到 {row_count} 行数据，生成 {len(properties_stats)} 个标签")
        return properties_stats

    except Exception as e:
        logger.error(f"获取属性标签统计失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return {}

def _get_quality_stats(db: Session, case_id: int) -> Dict[str, Any]:
    """获取质量分数统计 - 使用SQL聚合查询"""
    try:
        from sqlalchemy import text

        # 获取质量分数统计
        result = db.execute(text("""
            WITH quality_scores AS (
                SELECT
                    f.id as file_id,
                    (f.tags->'properties'->>'qualityScore')::int as quality_score
                FROM files f
                WHERE f.case_id = :case_id
                AND f.tags->'properties'->>'qualityScore' IS NOT NULL
                AND f.tags->'properties'->>'qualityScore' ~ '^[0-9]+$'
            ),
            quality_ranges AS (
                SELECT
                    file_id,
                    quality_score,
                    CASE
                        WHEN quality_score >= 90 THEN '优秀 (90-100)'
                        WHEN quality_score >= 80 THEN '良好 (80-89)'
                        WHEN quality_score >= 70 THEN '一般 (70-79)'
                        WHEN quality_score >= 60 THEN '较差 (60-69)'
                        ELSE '很差 (<60)'
                    END as quality_range
                FROM quality_scores
            )
            SELECT
                quality_range,
                COUNT(*) as file_count,
                ARRAY_AGG(file_id) as file_ids,
                MIN(quality_score) as min_score,
                MAX(quality_score) as max_score,
                AVG(quality_score)::int as avg_score
            FROM quality_ranges
            GROUP BY quality_range
            ORDER BY min_score DESC
        """), {"case_id": case_id})

        quality_stats = {}
        for row in result:
            quality_range = row.quality_range
            file_count = row.file_count
            file_ids = row.file_ids

            quality_stats[quality_range] = {
                "count": file_count,
                "file_ids": file_ids,
                "min_score": row.min_score,
                "max_score": row.max_score,
                "avg_score": row.avg_score
            }

        return quality_stats

    except Exception as e:
        logger.warning(f"获取质量统计失败: {e}")
        return {}

def _get_user_tag_stats(db: Session, case_id: int) -> Dict[str, Any]:
    """获取用户标签统计 - 使用SQL聚合查询"""
    try:
        from sqlalchemy import text

        # 获取用户自定义标签统计
        result = db.execute(text("""
            WITH user_tags AS (
                SELECT
                    f.id as file_id,
                    jsonb_array_elements_text(f.tags->'tags'->'user') as tag_name
                FROM files f
                WHERE f.case_id = :case_id
                AND f.tags->'tags'->'user' IS NOT NULL
                AND jsonb_typeof(f.tags->'tags'->'user') = 'array'
                AND jsonb_array_length(f.tags->'tags'->'user') > 0
            )
            SELECT
                tag_name,
                COUNT(*) as file_count,
                ARRAY_AGG(file_id) as file_ids
            FROM user_tags
            GROUP BY tag_name
            ORDER BY file_count DESC, tag_name
        """), {"case_id": case_id})

        user_stats = {}
        for row in result:
            tag_name = row.tag_name
            file_count = row.file_count
            file_ids = row.file_ids

            user_stats[tag_name] = {
                "count": file_count,
                "file_ids": file_ids
            }

        return user_stats

    except Exception as e:
        logger.warning(f"获取用户标签统计失败: {e}")
        return {}

def _get_ai_tag_stats(db: Session, case_id: int) -> Dict[str, Any]:
    """获取AI标签统计 - 使用SQL聚合查询"""
    try:
        from sqlalchemy import text

        # 获取AI标签统计
        result = db.execute(text("""
            WITH ai_tags AS (
                SELECT
                    f.id as file_id,
                    jsonb_each_text(f.tags->'tags'->'ai') as tag_pair
                FROM files f
                WHERE f.case_id = :case_id
                AND f.tags->'tags'->'ai' IS NOT NULL
                AND jsonb_typeof(f.tags->'tags'->'ai') = 'object'
            )
            SELECT
                (tag_pair).key as tag_name,
                (tag_pair).value as tag_value,
                COUNT(*) as file_count,
                ARRAY_AGG(file_id) as file_ids
            FROM ai_tags
            GROUP BY (tag_pair).key, (tag_pair).value
            ORDER BY tag_name, file_count DESC
        """), {"case_id": case_id})

        ai_stats = {}
        for row in result:
            tag_name = row.tag_name
            tag_value = row.tag_value
            file_count = row.file_count
            file_ids = row.file_ids

            if tag_name not in ai_stats:
                ai_stats[tag_name] = {}

            ai_stats[tag_name][tag_value] = {
                "count": file_count,
                "file_ids": file_ids
            }

        return ai_stats

    except Exception as e:
        logger.warning(f"获取AI标签统计失败: {e}")
        return {}

# 辅助函数
def _build_simple_tag_tree(files: List[models.File]) -> Dict[str, Any]:
    """构建精简标签树"""
    tag_tree = {
        "ai_tags": {},
        "user_tags": {},
        "properties": {},
        "total_files": len(files)
    }

    for file in files:
        if not file.tags:
            continue

        try:
            # 解析JSONB标签数据
            tags_data = file.tags if isinstance(file.tags, dict) else json.loads(file.tags)

            # 获取实际的标签数据（在tags字段内）
            actual_tags = tags_data.get("tags", {})
            properties_data = tags_data.get("properties", {})

            # 处理AI标签 (从 "tags.ai" 字段)
            if "ai" in actual_tags and actual_tags["ai"]:
                ai_tags = actual_tags["ai"]
                if isinstance(ai_tags, list):
                    # 如果ai是列表格式
                    for tag_item in ai_tags:
                        if isinstance(tag_item, dict):
                            for category, tags in tag_item.items():
                                if category not in tag_tree["ai_tags"]:
                                    tag_tree["ai_tags"][category] = {}

                                if isinstance(tags, dict):
                                    for tag_name, tag_value in tags.items():
                                        if tag_name not in tag_tree["ai_tags"][category]:
                                            tag_tree["ai_tags"][category][tag_name] = set()

                                        if isinstance(tag_value, list):
                                            tag_tree["ai_tags"][category][tag_name].update(tag_value)
                                        else:
                                            tag_tree["ai_tags"][category][tag_name].add(str(tag_value))
                elif isinstance(ai_tags, dict):
                    # 如果ai是字典格式
                    for category, tags in ai_tags.items():
                        if category not in tag_tree["ai_tags"]:
                            tag_tree["ai_tags"][category] = {}

                        if isinstance(tags, dict):
                            for tag_name, tag_value in tags.items():
                                if tag_name not in tag_tree["ai_tags"][category]:
                                    tag_tree["ai_tags"][category][tag_name] = set()

                                if isinstance(tag_value, list):
                                    tag_tree["ai_tags"][category][tag_name].update(tag_value)
                                else:
                                    tag_tree["ai_tags"][category][tag_name].add(str(tag_value))

            # 处理用户标签 (从 "tags.user" 字段)
            if "user" in actual_tags and actual_tags["user"]:
                user_tags = actual_tags["user"]
                if isinstance(user_tags, list):
                    for tag_item in user_tags:
                        if isinstance(tag_item, dict):
                            for category, tags in tag_item.items():
                                if category not in tag_tree["user_tags"]:
                                    tag_tree["user_tags"][category] = {}

                                if isinstance(tags, dict):
                                    for tag_name, tag_value in tags.items():
                                        if tag_name not in tag_tree["user_tags"][category]:
                                            tag_tree["user_tags"][category][tag_name] = set()

                                        if isinstance(tag_value, list):
                                            tag_tree["user_tags"][category][tag_name].update(tag_value)
                                        else:
                                            tag_tree["user_tags"][category][tag_name].add(str(tag_value))

            # 处理metadata标签作为属性标签 (从 "tags.metadata" 字段)
            if "metadata" in actual_tags and actual_tags["metadata"]:
                metadata = actual_tags["metadata"]
                if isinstance(metadata, dict):
                    for prop_name, prop_value in metadata.items():
                        if prop_name not in tag_tree["properties"]:
                            tag_tree["properties"][prop_name] = set()

                        if isinstance(prop_value, list):
                            tag_tree["properties"][prop_name].update(str(v) for v in prop_value)
                        else:
                            tag_tree["properties"][prop_name].add(str(prop_value))

            # 处理CV标签作为AI标签的一个类别 (从 "tags.cv" 字段)
            if "cv" in actual_tags and actual_tags["cv"]:
                cv_tags = actual_tags["cv"]
                if isinstance(cv_tags, dict):
                    if "cv" not in tag_tree["ai_tags"]:
                        tag_tree["ai_tags"]["cv"] = {}

                    for tag_name, tag_value in cv_tags.items():
                        if tag_name not in tag_tree["ai_tags"]["cv"]:
                            tag_tree["ai_tags"]["cv"][tag_name] = set()

                        if isinstance(tag_value, list):
                            tag_tree["ai_tags"]["cv"][tag_name].update(tag_value)
                        else:
                            tag_tree["ai_tags"]["cv"][tag_name].add(str(tag_value))

            # 处理根级别的properties字段
            if properties_data:
                for prop_name, prop_value in properties_data.items():
                    if prop_name not in tag_tree["properties"]:
                        tag_tree["properties"][prop_name] = set()

                    if isinstance(prop_value, list):
                        tag_tree["properties"][prop_name].update(str(v) for v in prop_value)
                    else:
                        tag_tree["properties"][prop_name].add(str(prop_value))

        except (json.JSONDecodeError, TypeError, AttributeError) as e:
            logger.warning(f"解析文件标签失败 {file.id}: {e}")
            continue

    # 转换set为list以便JSON序列化
    for category in tag_tree["ai_tags"]:
        for tag_name in tag_tree["ai_tags"][category]:
            tag_tree["ai_tags"][category][tag_name] = list(tag_tree["ai_tags"][category][tag_name])

    for category in tag_tree["user_tags"]:
        for tag_name in tag_tree["user_tags"][category]:
            tag_tree["user_tags"][category][tag_name] = list(tag_tree["user_tags"][category][tag_name])

    for prop_name in tag_tree["properties"]:
        tag_tree["properties"][prop_name] = list(tag_tree["properties"][prop_name])

    return tag_tree

def _build_tag_statistics(files: List[models.File], include_empty: bool) -> Dict[str, Any]:
    """构建标签统计信息"""
    stats = {
        "ai_tags": [],
        "user_tags": [],
        "properties": []
    }

    # 统计计数器
    ai_tag_counts = {}
    user_tag_counts = {}
    property_counts = {}

    for file in files:
        if not file.tags:
            continue

        try:
            # 解析JSONB标签数据
            tags_data = file.tags if isinstance(file.tags, dict) else json.loads(file.tags)

            # 统计AI标签
            if "ai_tags" in tags_data:
                for category, tags in tags_data["ai_tags"].items():
                    if isinstance(tags, dict):
                        for tag_name, tag_value in tags.items():
                            key = f"{category}.{tag_name}"
                            if key not in ai_tag_counts:
                                ai_tag_counts[key] = {"category": category, "name": tag_name, "values": {}, "file_count": 0}

                            ai_tag_counts[key]["file_count"] += 1

                            # 统计标签值
                            if isinstance(tag_value, list):
                                for value in tag_value:
                                    value_str = str(value)
                                    ai_tag_counts[key]["values"][value_str] = ai_tag_counts[key]["values"].get(value_str, 0) + 1
                            else:
                                value_str = str(tag_value)
                                ai_tag_counts[key]["values"][value_str] = ai_tag_counts[key]["values"].get(value_str, 0) + 1

            # 统计用户标签
            if "user_tags" in tags_data:
                for category, tags in tags_data["user_tags"].items():
                    if isinstance(tags, dict):
                        for tag_name, tag_value in tags.items():
                            key = f"{category}.{tag_name}"
                            if key not in user_tag_counts:
                                user_tag_counts[key] = {"category": category, "name": tag_name, "values": {}, "file_count": 0}

                            user_tag_counts[key]["file_count"] += 1

                            # 统计标签值
                            if isinstance(tag_value, list):
                                for value in tag_value:
                                    value_str = str(value)
                                    user_tag_counts[key]["values"][value_str] = user_tag_counts[key]["values"].get(value_str, 0) + 1
                            else:
                                value_str = str(tag_value)
                                user_tag_counts[key]["values"][value_str] = user_tag_counts[key]["values"].get(value_str, 0) + 1

            # 统计属性
            if "properties" in tags_data:
                for prop_name, prop_value in tags_data["properties"].items():
                    if prop_name not in property_counts:
                        property_counts[prop_name] = {"name": prop_name, "values": {}, "file_count": 0}

                    property_counts[prop_name]["file_count"] += 1

                    # 统计属性值
                    if isinstance(prop_value, list):
                        for value in prop_value:
                            value_str = str(value)
                            property_counts[prop_name]["values"][value_str] = property_counts[prop_name]["values"].get(value_str, 0) + 1
                    else:
                        value_str = str(prop_value)
                        property_counts[prop_name]["values"][value_str] = property_counts[prop_name]["values"].get(value_str, 0) + 1

        except (json.JSONDecodeError, TypeError, AttributeError) as e:
            logger.warning(f"解析文件标签失败 {file.id}: {e}")
            continue

    # 转换为列表格式
    for key, data in ai_tag_counts.items():
        if include_empty or data["file_count"] > 0:
            # 转换values字典为列表
            values_list = [{"value": k, "count": v} for k, v in data["values"].items()]
            values_list.sort(key=lambda x: x["count"], reverse=True)

            stats["ai_tags"].append({
                "category": data["category"],
                "name": data["name"],
                "file_count": data["file_count"],
                "values": values_list
            })

    for key, data in user_tag_counts.items():
        if include_empty or data["file_count"] > 0:
            values_list = [{"value": k, "count": v} for k, v in data["values"].items()]
            values_list.sort(key=lambda x: x["count"], reverse=True)

            stats["user_tags"].append({
                "category": data["category"],
                "name": data["name"],
                "file_count": data["file_count"],
                "values": values_list
            })

    for prop_name, data in property_counts.items():
        if include_empty or data["file_count"] > 0:
            values_list = [{"value": k, "count": v} for k, v in data["values"].items()]
            values_list.sort(key=lambda x: x["count"], reverse=True)

            stats["properties"].append({
                "name": data["name"],
                "file_count": data["file_count"],
                "values": values_list
            })

    # 按文件数量排序
    stats["ai_tags"].sort(key=lambda x: x["file_count"], reverse=True)
    stats["user_tags"].sort(key=lambda x: x["file_count"], reverse=True)
    stats["properties"].sort(key=lambda x: x["file_count"], reverse=True)

    return stats

def _filter_files_by_tag(files: List[models.File], category: str, name: str, value: str) -> List[models.File]:
    """根据标签筛选文件"""
    matching_files = []

    for file in files:
        if not file.tags:
            continue

        try:
            # 解析JSONB标签数据
            tags_data = file.tags if isinstance(file.tags, dict) else json.loads(file.tags)

            # 检查AI标签
            if category == "ai_tags" and "ai_tags" in tags_data:
                for tag_category, tags in tags_data["ai_tags"].items():
                    if isinstance(tags, dict) and name in tags:
                        tag_value = tags[name]
                        if _match_tag_value(tag_value, value):
                            matching_files.append(file)
                            break

            # 检查用户标签
            elif category == "user_tags" and "user_tags" in tags_data:
                for tag_category, tags in tags_data["user_tags"].items():
                    if isinstance(tags, dict) and name in tags:
                        tag_value = tags[name]
                        if _match_tag_value(tag_value, value):
                            matching_files.append(file)
                            break

            # 检查属性
            elif category == "properties" and "properties" in tags_data:
                if name in tags_data["properties"]:
                    prop_value = tags_data["properties"][name]
                    if _match_tag_value(prop_value, value):
                        matching_files.append(file)

            # 检查特定类别的标签
            elif category in ["ai_tags", "user_tags"]:
                tag_type = category
                if tag_type in tags_data:
                    # 在所有子类别中查找
                    for sub_category, tags in tags_data[tag_type].items():
                        if isinstance(tags, dict) and name in tags:
                            tag_value = tags[name]
                            if _match_tag_value(tag_value, value):
                                matching_files.append(file)
                                break

        except (json.JSONDecodeError, TypeError, AttributeError) as e:
            logger.warning(f"解析文件标签失败 {file.id}: {e}")
            continue

    return matching_files

def _match_tag_value(tag_value: Any, target_value: str) -> bool:
    """匹配标签值"""
    if isinstance(tag_value, list):
        return any(str(v).lower() == target_value.lower() for v in tag_value)
    else:
        return str(tag_value).lower() == target_value.lower()

def _search_tags_in_files(files: List[models.File], query: str, limit: int) -> List[Dict[str, Any]]:
    """在文件中搜索标签"""
    search_results = []
    query_lower = query.lower()

    # 用于去重的集合
    seen_tags = set()

    for file in files:
        if not file.tags:
            continue

        try:
            # 解析JSONB标签数据
            tags_data = file.tags if isinstance(file.tags, dict) else json.loads(file.tags)

            # 搜索AI标签
            if "ai_tags" in tags_data:
                for category, tags in tags_data["ai_tags"].items():
                    if isinstance(tags, dict):
                        for tag_name, tag_value in tags.items():
                            # 搜索标签名
                            if query_lower in tag_name.lower():
                                tag_key = f"ai_tags.{category}.{tag_name}"
                                if tag_key not in seen_tags:
                                    seen_tags.add(tag_key)
                                    search_results.append({
                                        "type": "ai_tag",
                                        "category": category,
                                        "name": tag_name,
                                        "value": tag_value,
                                        "match_type": "name"
                                    })

                            # 搜索标签值
                            if isinstance(tag_value, list):
                                for value in tag_value:
                                    if query_lower in str(value).lower():
                                        tag_key = f"ai_tags.{category}.{tag_name}.{value}"
                                        if tag_key not in seen_tags:
                                            seen_tags.add(tag_key)
                                            search_results.append({
                                                "type": "ai_tag",
                                                "category": category,
                                                "name": tag_name,
                                                "value": value,
                                                "match_type": "value"
                                            })
                            else:
                                if query_lower in str(tag_value).lower():
                                    tag_key = f"ai_tags.{category}.{tag_name}.{tag_value}"
                                    if tag_key not in seen_tags:
                                        seen_tags.add(tag_key)
                                        search_results.append({
                                            "type": "ai_tag",
                                            "category": category,
                                            "name": tag_name,
                                            "value": tag_value,
                                            "match_type": "value"
                                        })

            # 搜索用户标签
            if "user_tags" in tags_data:
                for category, tags in tags_data["user_tags"].items():
                    if isinstance(tags, dict):
                        for tag_name, tag_value in tags.items():
                            # 搜索标签名
                            if query_lower in tag_name.lower():
                                tag_key = f"user_tags.{category}.{tag_name}"
                                if tag_key not in seen_tags:
                                    seen_tags.add(tag_key)
                                    search_results.append({
                                        "type": "user_tag",
                                        "category": category,
                                        "name": tag_name,
                                        "value": tag_value,
                                        "match_type": "name"
                                    })

                            # 搜索标签值
                            if isinstance(tag_value, list):
                                for value in tag_value:
                                    if query_lower in str(value).lower():
                                        tag_key = f"user_tags.{category}.{tag_name}.{value}"
                                        if tag_key not in seen_tags:
                                            seen_tags.add(tag_key)
                                            search_results.append({
                                                "type": "user_tag",
                                                "category": category,
                                                "name": tag_name,
                                                "value": value,
                                                "match_type": "value"
                                            })
                            else:
                                if query_lower in str(tag_value).lower():
                                    tag_key = f"user_tags.{category}.{tag_name}.{tag_value}"
                                    if tag_key not in seen_tags:
                                        seen_tags.add(tag_key)
                                        search_results.append({
                                            "type": "user_tag",
                                            "category": category,
                                            "name": tag_name,
                                            "value": tag_value,
                                            "match_type": "value"
                                        })

            # 搜索属性
            if "properties" in tags_data:
                for prop_name, prop_value in tags_data["properties"].items():
                    # 搜索属性名
                    if query_lower in prop_name.lower():
                        tag_key = f"properties.{prop_name}"
                        if tag_key not in seen_tags:
                            seen_tags.add(tag_key)
                            search_results.append({
                                "type": "property",
                                "category": "properties",
                                "name": prop_name,
                                "value": prop_value,
                                "match_type": "name"
                            })

                    # 搜索属性值
                    if isinstance(prop_value, list):
                        for value in prop_value:
                            if query_lower in str(value).lower():
                                tag_key = f"properties.{prop_name}.{value}"
                                if tag_key not in seen_tags:
                                    seen_tags.add(tag_key)
                                    search_results.append({
                                        "type": "property",
                                        "category": "properties",
                                        "name": prop_name,
                                        "value": value,
                                        "match_type": "value"
                                    })
                    else:
                        if query_lower in str(prop_value).lower():
                            tag_key = f"properties.{prop_name}.{prop_value}"
                            if tag_key not in seen_tags:
                                seen_tags.add(tag_key)
                                search_results.append({
                                    "type": "property",
                                    "category": "properties",
                                    "name": prop_name,
                                    "value": prop_value,
                                    "match_type": "value"
                                })

        except (json.JSONDecodeError, TypeError, AttributeError) as e:
            logger.warning(f"解析文件标签失败 {file.id}: {e}")
            continue

        # 如果已经达到限制，停止搜索
        if len(search_results) >= limit:
            break

    return search_results[:limit]
