// Project Novak - Electron Main Process
// Electron 主进程文件 - 应用的"壳"

import { app, BrowserWindow, Menu, shell, ipcMain } from 'electron';
import path from 'path';
import { fileURLToPath } from 'url';

// ============================================================================
// Environment Detection
// ============================================================================

const isDev = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// ============================================================================
// Path Configuration
// ============================================================================

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ============================================================================
// Window Management
// ============================================================================

let mainWindow: BrowserWindow | null = null;

/**
 * 创建主窗口
 */
function createMainWindow(): BrowserWindow {
  // 创建浏览器窗口
  const win = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false, // 安全考虑，禁用 Node.js 集成
      contextIsolation: true, // 启用上下文隔离
      enableRemoteModule: false, // 禁用远程模块
      // 预加载脚本（未来与 Node.js 交互的桥梁）
      // preload: path.join(__dirname, 'preload.js'),
    },
    // 窗口样式
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
    show: false, // 初始不显示，等待加载完成
    icon: process.platform === 'linux'
      ? path.join(__dirname, '../public/icon.png')
      : undefined,
  });

  // 窗口加载完成后显示
  win.once('ready-to-show', () => {
    win.show();

    // 开发环境下打开开发者工具
    if (isDev) {
      win.webContents.openDevTools();
    }
  });

  // 加载应用内容
  if (isDev) {
    // 开发环境：加载 Vite dev server
    win.loadURL('http://localhost:5175');

    // 开发环境下的热重载支持
    win.webContents.on('did-frame-finish-load', () => {
      if (isDev) {
        win.webContents.once('devtools-opened', () => {
          win.focus();
        });
      }
    });
  } else {
    // 生产环境：加载构建好的静态文件
    const indexPath = path.join(__dirname, '../dist/index.html');
    win.loadFile(indexPath);
  }

  // 处理外部链接
  win.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // 窗口关闭事件
  win.on('closed', () => {
    mainWindow = null;
  });

  return win;
}

// ============================================================================
// Application Menu
// ============================================================================

/**
 * 创建应用菜单
 */
function createMenu(): void {
  const template: Electron.MenuItemConstructorOptions[] = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New Case',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            // TODO: 发送创建新案例的消息到渲染进程
            mainWindow?.webContents.send('menu-new-case');
          },
        },
        {
          label: 'Open Case',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            // TODO: 发送打开案例的消息到渲染进程
            mainWindow?.webContents.send('menu-open-case');
          },
        },
        { type: 'separator' },
        {
          label: 'Import Images',
          accelerator: 'CmdOrCtrl+I',
          click: () => {
            // TODO: 发送导入图片的消息到渲染进程
            mainWindow?.webContents.send('menu-import-images');
          },
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          },
        },
      ],
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'selectall' },
      ],
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' },
      ],
    },
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' },
      ],
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About Project Novak',
          click: () => {
            // TODO: 显示关于对话框
            mainWindow?.webContents.send('menu-about');
          },
        },
        {
          label: 'Documentation',
          click: () => {
            shell.openExternal('https://github.com/your-repo/project-novak');
          },
        },
      ],
    },
  ];

  // macOS 特殊处理
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideOthers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' },
      ],
    });
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// ============================================================================
// IPC Handlers
// ============================================================================

/**
 * 设置 IPC 处理器
 */
function setupIpcHandlers(): void {
  // 获取应用版本
  ipcMain.handle('get-app-version', () => {
    return app.getVersion();
  });

  // 获取应用路径
  ipcMain.handle('get-app-path', (_, name: string) => {
    return app.getPath(name as any);
  });

  // 显示消息框
  ipcMain.handle('show-message-box', async (_, options) => {
    const { dialog } = await import('electron');
    if (mainWindow) {
      return dialog.showMessageBox(mainWindow, options);
    }
    return dialog.showMessageBox(options);
  });

  // 显示文件选择对话框
  ipcMain.handle('show-open-dialog', async (_, options) => {
    const { dialog } = await import('electron');
    if (mainWindow) {
      return dialog.showOpenDialog(mainWindow, options);
    }
    return dialog.showOpenDialog(options);
  });

  // 显示文件保存对话框
  ipcMain.handle('show-save-dialog', async (_, options) => {
    const { dialog } = await import('electron');
    if (mainWindow) {
      return dialog.showSaveDialog(mainWindow, options);
    }
    return dialog.showSaveDialog(options);
  });
}

// ============================================================================
// Application Event Handlers
// ============================================================================

/**
 * 应用准备就绪
 */
app.whenReady().then(() => {
  // 创建主窗口
  mainWindow = createMainWindow();

  // 创建菜单
  createMenu();

  // 设置 IPC 处理器
  setupIpcHandlers();

  // macOS 特殊处理：点击 dock 图标时重新创建窗口
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      mainWindow = createMainWindow();
    }
  });
});

/**
 * 所有窗口关闭
 */
app.on('window-all-closed', () => {
  // macOS 下除非用户明确退出，否则保持应用运行
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

/**
 * 应用即将退出
 */
app.on('before-quit', (event) => {
  // 这里可以添加退出前的清理逻辑
  console.log('Application is about to quit');
});

/**
 * 安全：阻止新窗口创建
 */
app.on('web-contents-created', (_, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});

// ============================================================================
// Development Helpers
// ============================================================================

if (isDev) {
  // 开发环境下的额外配置
  console.log('Running in development mode');

  // 启用实时重载（如果需要）
  try {
    require('electron-reload')(__dirname, {
      electron: path.join(__dirname, '..', 'node_modules', '.bin', 'electron'),
      hardResetMethod: 'exit'
    });
  } catch (err) {
    console.log('electron-reload not available');
  }
}

// ============================================================================
// Export for Testing
// ============================================================================

export { createMainWindow, createMenu, setupIpcHandlers };
