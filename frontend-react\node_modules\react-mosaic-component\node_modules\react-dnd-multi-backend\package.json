{"name": "react-dnd-multi-backend", "version": "8.1.2", "sideEffects": false, "description": "Multi Backend system compatible with React DnD", "author": "<PERSON> <<EMAIL>> (https://github.com/LouisBrunner)", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/LouisBrunner/dnd-multi-backend.git", "directory": "packages/react-dnd-multi-backend"}, "homepage": "https://louisbrunner.github.io/dnd-multi-backend/packages/react-dnd-multi-backend/", "keywords": ["react", "dnd", "drag", "drop", "html5", "touch", "react-dnd"], "funding": {"type": "individual", "url": "https://github.com/sponsors/LouisBrunner"}, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/index.js", "dependencies": {"dnd-multi-backend": "^8.1.2", "react-dnd-preview": "^8.1.2"}, "peerDependencies": {"dnd-core": "^16.0.1", "react": "^16.14.0 || ^17.0.2 || ^18.0.0", "react-dnd": "^16.0.1", "react-dom": "^16.14.0 || ^17.0.2 || ^18.0.0"}}