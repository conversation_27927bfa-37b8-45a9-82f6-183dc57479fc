# 生产环境配置 - PostgreSQL
MASTER_DB_TYPE=postgresql
CASE_DB_TYPE=postgresql
POSTGRES_HOST=127.0.0.1
POSTGRES_PORT=5432
POSTGRES_USER=mizzy_user
POSTGRES_PASSWORD=MizzyStarProd2024!
POSTGRES_DB=mizzy_main

# 生产环境设置
DEBUG=false
DB_ECHO_SQL=false
LOG_LEVEL=INFO

# 性能配置
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# 缓存配置
CACHE_ENABLED=true
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# 安全配置
SECRET_KEY=mizzy_star_production_secret_key_2024
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# 应用配置
APP_NAME=Mizzy Star Production
APP_VERSION=1.0.0
API_PREFIX=/api/v1
