{"version": 3, "file": "AppxTarget.js", "sourceRoot": "", "sources": ["../../src/targets/AppxTarget.ts"], "names": [], "mappings": ";;AAAA,+CAA0C;AAC1C,+CAAwF;AACxF,4CAA0D;AAC1D,uCAAiE;AACjE,6BAA4B;AAE5B,iEAA0E;AAC1E,kCAAgC;AAChC,qDAAqD;AAGrD,6CAA6C;AAE7C,MAAM,oBAAoB,GAAG,MAAM,CAAA;AAEnC,MAAM,4BAA4B,GAA8B;IAC9D,eAAe,EAAE,sBAAsB;IACvC,uBAAuB,EAAE,wBAAwB;IACjD,qBAAqB,EAAE,sBAAsB;IAC7C,qBAAqB,EAAE,wBAAwB;CAChD,CAAA;AAED,MAAM,qBAAqB,GAAG,OAAO,CAAA;AAErC,MAAqB,UAAW,SAAQ,aAAM;IAG5C,YACmB,QAAqB,EAC7B,MAAc;QAEvB,KAAK,CAAC,MAAM,CAAC,CAAA;QAHI,aAAQ,GAAR,QAAQ,CAAa;QAC7B,WAAM,GAAN,MAAM,CAAQ;QAJhB,YAAO,GAAgB,IAAA,yBAAU,EAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,4BAA4B,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QAQnH,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,IAAA,2BAAS,GAAE,CAAC,EAAE,CAAC;YACnF,MAAM,IAAI,KAAK,CAAC,sFAAsF,CAAC,CAAA;QACzG,CAAC;IACH,CAAC;IAED,6GAA6G;IAC7G,KAAK,CAAC,KAAK,CAAC,SAAiB,EAAE,IAAU;QACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,YAAY,GAAG,QAAQ,CAAC,+BAA+B,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;QACzF,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;QACzD,MAAM,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC;YAC3C,qBAAqB,EAAE,MAAM;YAC7B,IAAI,EAAE,YAAY;YAClB,IAAI;SACL,CAAC,CAAA;QAEF,MAAM,UAAU,GAAG,MAAM,IAAA,mCAAiB,GAAE,CAAA;QAC5C,MAAM,EAAE,GAAG,MAAM,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAA;QAElC,MAAM,QAAQ,GAAG,MAAM,IAAA,2BAAc,EAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QAE3D,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC,aAAa,CAAC,CAAA;QACvD,MAAM,YAAY,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,4CAA4C,EAAE,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAA;QACjJ,IAAI,QAAQ,CAAC,WAAW,KAAK,OAAO,EAAE,CAAC;YACrC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC1B,CAAC;QAED,MAAM,WAAW,GAAyB,EAAE,CAAA;QAC5C,WAAW,CAAC,IAAI,CACd,MAAM,sBAAe,CAAC,GAAG,CAAC,IAAA,SAAI,EAAC,SAAS,CAAC,EAAE,IAAI,CAAC,EAAE;YAChD,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;YACnD,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,EAAE,CAAC;gBACtB,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;YAC1C,CAAC;YACD,OAAO,IAAI,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,QAAQ,GAAG,CAAA;QACpD,CAAC,CAAC,CACH,CAAA;QAED,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAA;QACrF,MAAM,SAAS,GAAG,MAAM,UAAU,CAAC,iBAAiB,CAAC,EAAE,EAAE,UAAU,EAAE,YAAY,CAAC,CAAA;QAClF,MAAM,UAAU,GAAG,SAAS,CAAC,UAAU,CAAA;QAEvC,MAAM,YAAY,GAAG,QAAQ,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAA;QAC7D,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,IAAI,EAAE,MAAM,IAAI,CAAC,oBAAoB,EAAE,EAAE,UAAU,CAAC,CAAA;QAC3F,MAAM,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAA;QACzD,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;QACpC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAA;QACvE,MAAM,YAAY,GAAG,IAAI,KAAK,mBAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,mBAAI,CAAC,IAAI,CAAC,CAAA;QAE7D,IAAI,sBAAsB,CAAC,UAAU,CAAC,EAAE,CAAC;YACvC,MAAM,OAAO,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC,CAAA;YAClE,MAAM,WAAW,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC,CAAA;YAEjG,MAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,CAAC,aAAa,CAAC,CAAA;YACrD,MAAM,IAAA,mBAAQ,EAAC,SAAS,CAAC,CAAA;YACzB,MAAM,sBAAe,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE,CAAC,IAAA,mBAAc,EAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;YAEjH,MAAM,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE;gBACzB,KAAK;gBACL,YAAY;gBACZ,WAAW;gBACX,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC;gBACzB,cAAc;gBACd,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBACpC,YAAY;gBACZ,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAA,6BAAe,EAAC,MAAM,CAAC,EAAE,eAAe,CAAC,CAAC;gBAChE,aAAa;gBACb,OAAO;aACR,CAAC,CAAA;YAEF,+FAA+F;YAC/F,KAAK,MAAM,YAAY,IAAI,CAAC,MAAM,IAAA,kBAAO,EAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC;gBAC1G,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,MAAM,YAAY,GAAG,CAAC,CAAC,CAAA;YAC9F,CAAC;YACD,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACzB,CAAC;QAED,IAAI,OAAO,GAAG,SAAS,CAAA;QACvB,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC/B,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACvC,CAAC;QACD,MAAM,IAAA,oBAAS,EAAC,WAAW,EAAE,OAAO,CAAC,CAAA;QACrC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,CAAA;QAEjD,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI,EAAE,CAAC;YACtC,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;QACjD,CAAC;QACD,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC,EAAE,YAAY,CAAC,CAAA;QAC3G,MAAM,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QAEjC,MAAM,QAAQ,CAAC,OAAO,EAAE,CAAA;QAExB,MAAM,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC;YAC7C,IAAI,EAAE,YAAY;YAClB,QAAQ;YACR,IAAI;YACJ,gBAAgB,EAAE,QAAQ,CAAC,uBAAuB,CAAC,YAAY,EAAE,MAAM,CAAC;YACxE,MAAM,EAAE,IAAI;YACZ,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,oBAAoB;SACrD,CAAC,CAAA;IACJ,CAAC;IAEO,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,EAAa,EAAE,UAAkB,EAAE,YAA2B;QACnG,MAAM,QAAQ,GAAkB,EAAE,CAAA;QAClC,IAAI,UAAyB,CAAA;QAC7B,MAAM,SAAS,GAAkB,EAAE,CAAA;QACnC,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;YACzB,UAAU,GAAG,EAAE,CAAA;QACjB,CAAC;aAAM,CAAC;YACN,UAAU,GAAG,CAAC,MAAM,IAAA,kBAAO,EAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAA;YACvH,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;gBAC9B,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,OAAO,GAAG,IAAI,cAAc,IAAI,GAAG,CAAC,CAAA;gBACrF,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAA;YAC/C,CAAC;QACH,CAAC;QAED,KAAK,MAAM,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,EAAE,CAAC;YACrE,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,YAAY,CAAC,EAAE,CAAC;gBACjF,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,EAAE,4BAA4B,CAAC,YAAY,CAAC,CAAC,CAAA;gBAC5F,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,YAAY,GAAG,CAAC,CAAA;gBACjE,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACtB,CAAC;QACH,CAAC;QAED,4IAA4I;QAC5I,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAA;IAC5C,CAAC;IAED,2FAA2F;IACnF,KAAK,CAAC,oBAAoB;QAChC,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC;YAChD,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,0BAA0B,EAAE,EAAE,oBAAoB,CAAC,CAAA;YACtE,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAA;QAC1C,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAA;QACvD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAA;QACzG,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAA;QAClF,CAAC;QACD,OAAO,SAAS,CAAA;IAClB,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,OAAe,EAAE,IAAU,EAAE,SAAiB,EAAE,UAAyB;QACnG,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAA;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC5B,MAAM,UAAU,GAAG,QAAQ,OAAO,CAAC,eAAe,MAAM,CAAA;QACxD,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAA;QAC9D,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;QAEpE,MAAM,QAAQ,GAAG,CAAC,MAAM,IAAA,mBAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,IAAA,6BAAe,EAAC,MAAM,CAAC,EAAE,kBAAkB,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC,KAAK,EAAE,EAAE,EAAU,EAAE;YACrJ,QAAQ,EAAE,EAAE,CAAC;gBACX,KAAK,WAAW;oBACd,OAAO,SAAS,CAAA;gBAElB,KAAK,sBAAsB,CAAC,CAAC,CAAC;oBAC5B,MAAM,IAAI,GAAG,OAAO,CAAC,oBAAoB,IAAI,OAAO,CAAC,WAAW,CAAA;oBAChE,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;wBACjB,MAAM,IAAI,wCAAyB,CAAC,0HAA0H,CAAC,CAAA;oBACjK,CAAC;oBACD,OAAO,IAAI,CAAA;gBACb,CAAC;gBAED,KAAK,SAAS;oBACZ,OAAO,OAAO,CAAC,4BAA4B,CAAC,OAAO,CAAC,cAAc,KAAK,IAAI,CAAC,CAAA;gBAE9E,KAAK,eAAe,CAAC,CAAC,CAAC;oBACrB,MAAM,MAAM,GAAG,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,IAAI,CAAA;oBAC5E,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;wBACpC,IAAI,OAAO,GAAG,kDAAkD,MAAM,GAAG,CAAA;wBACzE,IAAI,OAAO,CAAC,aAAa,IAAI,IAAI,EAAE,CAAC;4BAClC,OAAO,IAAI,wEAAwE,CAAA;wBACrF,CAAC;wBACD,MAAM,IAAI,wCAAyB,CAAC,OAAO,CAAC,CAAA;oBAC9C,CAAC;oBACD,OAAO,MAAM,CAAA;gBACf,CAAC;gBAED,KAAK,cAAc;oBACjB,OAAO,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,IAAI,CAAA;gBAE7C,KAAK,YAAY;oBACf,OAAO,UAAU,CAAA;gBAEnB,KAAK,aAAa;oBAChB,OAAO,WAAW,CAAA;gBAEpB,KAAK,aAAa;oBAChB,OAAO,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAA;gBAEnD,KAAK,iBAAiB;oBACpB,OAAO,OAAO,CAAC,eAAe,IAAI,SAAS,CAAA;gBAE7C,KAAK,MAAM;oBACT,OAAO,uBAAuB,CAAA;gBAEhC,KAAK,mBAAmB;oBACtB,OAAO,+BAA+B,CAAA;gBAExC,KAAK,iBAAiB;oBACpB,OAAO,6BAA6B,CAAA;gBAEtC,KAAK,YAAY;oBACf,OAAO,aAAa,CAAC,UAAU,CAAC,CAAA;gBAElC,KAAK,aAAa;oBAChB,OAAO,cAAc,CAAC,UAAU,EAAE,OAAO,CAAC,eAAe,IAAI,KAAK,CAAC,CAAA;gBAErE,KAAK,cAAc;oBACjB,OAAO,eAAe,CAAC,UAAU,CAAC,CAAA;gBAEpC,KAAK,MAAM;oBACT,OAAO,IAAI,KAAK,mBAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,mBAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAA;gBAE3E,KAAK,mBAAmB;oBACtB,OAAO,mBAAmB,CAAC,IAAA,sBAAO,EAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAA;gBAExD,KAAK,YAAY;oBACf,OAAO,UAAU,CAAA;gBAEnB,KAAK,YAAY;oBACf,OAAO,IAAI,KAAK,mBAAI,CAAC,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAA;gBAE9D,KAAK,kBAAkB;oBACrB,OAAO,IAAI,KAAK,mBAAI,CAAC,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAA;gBAE9D;oBACE,MAAM,IAAI,KAAK,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAA;YACjD,CAAC;QACH,CAAC,CAAC,CAAA;QACF,MAAM,IAAA,oBAAS,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;IACpC,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,UAAkB,EAAE,WAAmB;QACjE,MAAM,UAAU,GAAG,IAAA,sBAAO,EAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,IAAA,sBAAO,EAAC,IAAI,CAAC,QAAQ,CAAC,4BAA4B,CAAC,SAAS,CAAC,CAAC,CAAA;QAEhI,MAAM,gBAAgB,GAAG,IAAA,sBAAO,EAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,IAAA,sBAAO,EAAC,IAAI,CAAC,QAAQ,CAAC,4BAA4B,CAAC,gBAAgB,CAAC,CAAC,CAAA;QAEpJ,IAAI,wBAAwB,GAAG,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAA;QAClE,IAAI,wBAAwB,KAAK,SAAS,EAAE,CAAC;YAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAA;YACrD,wBAAwB,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,+BAA+B,CAAC,IAAI,IAAI,CAAA;QAC1F,CAAC;QAED,IAAI,CAAC,wBAAwB,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,KAAK,SAAS,EAAE,CAAC;YAC7I,OAAO,EAAE,CAAA;QACX,CAAC;QAED,IAAI,UAAU,GAAG,cAAc,CAAA;QAE/B,IAAI,wBAAwB,EAAE,CAAC;YAC7B,UAAU,IAAI;wEACoD,UAAU;mFACC,WAAW;6BACjE,CAAA;QACzB,CAAC;QAED,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE,CAAC;YAClC,KAAK,MAAM,MAAM,IAAI,IAAA,sBAAO,EAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC/C,UAAU,IAAI;;kCAEY,MAAM;kCACN,QAAQ,CAAC,IAAI;;2BAEpB,CAAA;YACrB,CAAC;QACH,CAAC;QAED,KAAK,MAAM,eAAe,IAAI,gBAAgB,EAAE,CAAC;YAC/C,KAAK,MAAM,GAAG,IAAI,IAAA,sBAAO,EAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/C,UAAU,IAAI;;6CAEuB,GAAG;;iCAEf,GAAG;;;2BAGT,CAAA;YACrB,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,KAAK,SAAS,EAAE,CAAC;YACpD,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAA;YACjG,UAAU,IAAI,MAAM,IAAA,mBAAQ,EAAC,cAAc,EAAE,MAAM,CAAC,CAAA;QACtD,CAAC;QAED,UAAU,IAAI,eAAe,CAAA;QAC7B,OAAO,UAAU,CAAA;IACnB,CAAC;CACF;AA1SD,6BA0SC;AAED,gLAAgL;AAChL,SAAS,mBAAmB,CAAC,aAA+C;IAC1E,IAAI,aAAa,IAAI,IAAI,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACxD,aAAa,GAAG,CAAC,qBAAqB,CAAC,CAAA;IACzC,CAAC;IACD,OAAO,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,uBAAuB,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AAC/F,CAAC;AAED,SAAS,aAAa,CAAC,UAAyB;IAC9C,IAAI,sBAAsB,CAAC,UAAU,EAAE,eAAe,CAAC,EAAE,CAAC;QACxD,OAAO,sFAAsF,CAAA;IAC/F,CAAC;SAAM,CAAC;QACN,OAAO,EAAE,CAAA;IACX,CAAC;AACH,CAAC;AAED,SAAS,cAAc,CAAC,UAAyB,EAAE,eAAwB;IACzE,MAAM,YAAY,GAAkB,CAAC,kBAAkB,EAAE,+CAA+C,CAAC,CAAA;IAEzG,IAAI,sBAAsB,CAAC,UAAU,EAAE,eAAe,CAAC,EAAE,CAAC;QACxD,YAAY,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAA;IAChE,CAAC;IACD,IAAI,sBAAsB,CAAC,UAAU,EAAE,eAAe,CAAC,EAAE,CAAC;QACxD,YAAY,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAA;IAC9D,CAAC;IAED,IAAI,eAAe,EAAE,CAAC;QACpB,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACtB,YAAY,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAA;QAC1C,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,wBAAwB,EAAE,IAAI,CAAC,CAAA;QAChE,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,0BAA0B,EAAE,IAAI,CAAC,CAAA;QAClE,YAAY,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAA;QAC3C,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAA;IACzC,CAAC;SAAM,CAAC;QACN,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACzB,CAAC;IACD,OAAO,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AAC/B,CAAC;AAED,SAAS,eAAe,CAAC,UAAyB;IAChD,IAAI,sBAAsB,CAAC,UAAU,EAAE,kBAAkB,CAAC,EAAE,CAAC;QAC3D,OAAO,uDAAuD,CAAA;IAChE,CAAC;SAAM,CAAC;QACN,OAAO,EAAE,CAAA;IACX,CAAC;AACH,CAAC;AAED,SAAS,sBAAsB,CAAC,UAAyB,EAAE,YAAoB;IAC7E,MAAM,gBAAgB,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAA;IAC7E,OAAO,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAA;AAC7D,CAAC;AAED,SAAS,sBAAsB,CAAC,UAAyB;IACvD,OAAO,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAA;AACrF,CAAC", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { Arch, asArray, deepAssign, InvalidConfigurationError, log } from \"builder-util\"\nimport { copyOrLinkFile, walk } from \"builder-util/out/fs\"\nimport { emptyDir, readdir, readFile, writeFile } from \"fs-extra\"\nimport * as path from \"path\"\nimport { AppXOptions } from \"../\"\nimport { getSignVendorPath, isOldWin6 } from \"../codeSign/windowsCodeSign\"\nimport { Target } from \"../core\"\nimport { getTemplatePath } from \"../util/pathManager\"\nimport { VmManager } from \"../vm/vm\"\nimport { WinPackager } from \"../winPackager\"\nimport { createStageDir } from \"./targetUtil\"\n\nconst APPX_ASSETS_DIR_NAME = \"appx\"\n\nconst vendorAssetsForDefaultAssets: { [key: string]: string } = {\n  \"StoreLogo.png\": \"SampleAppx.50x50.png\",\n  \"Square150x150Logo.png\": \"SampleAppx.150x150.png\",\n  \"Square44x44Logo.png\": \"SampleAppx.44x44.png\",\n  \"Wide310x150Logo.png\": \"SampleAppx.310x150.png\",\n}\n\nconst DEFAULT_RESOURCE_LANG = \"en-US\"\n\nexport default class AppXTarget extends Target {\n  readonly options: AppXOptions = deepAssign({}, this.packager.platformSpecificBuildOptions, this.packager.config.appx)\n\n  constructor(\n    private readonly packager: WinPackager,\n    readonly outDir: string\n  ) {\n    super(\"appx\")\n\n    if (process.platform !== \"darwin\" && (process.platform !== \"win32\" || isOldWin6())) {\n      throw new Error(\"AppX is supported only on Windows 10 or Windows Server 2012 R2 (version number 6.3+)\")\n    }\n  }\n\n  // https://docs.microsoft.com/en-us/windows/uwp/packaging/create-app-package-with-makeappx-tool#mapping-files\n  async build(appOutDir: string, arch: Arch): Promise<any> {\n    const packager = this.packager\n    const artifactName = packager.expandArtifactBeautyNamePattern(this.options, \"appx\", arch)\n    const artifactPath = path.join(this.outDir, artifactName)\n    await packager.info.callArtifactBuildStarted({\n      targetPresentableName: \"AppX\",\n      file: artifactPath,\n      arch,\n    })\n\n    const vendorPath = await getSignVendorPath()\n    const vm = await packager.vm.value\n\n    const stageDir = await createStageDir(this, packager, arch)\n\n    const mappingFile = stageDir.getTempFile(\"mapping.txt\")\n    const makeAppXArgs = [\"pack\", \"/o\" /* overwrite the output file if it exists */, \"/f\", vm.toVmFile(mappingFile), \"/p\", vm.toVmFile(artifactPath)]\n    if (packager.compression === \"store\") {\n      makeAppXArgs.push(\"/nc\")\n    }\n\n    const mappingList: Array<Array<string>> = []\n    mappingList.push(\n      await BluebirdPromise.map(walk(appOutDir), file => {\n        let appxPath = file.substring(appOutDir.length + 1)\n        if (path.sep !== \"\\\\\") {\n          appxPath = appxPath.replace(/\\//g, \"\\\\\")\n        }\n        return `\"${vm.toVmFile(file)}\" \"app\\\\${appxPath}\"`\n      })\n    )\n\n    const userAssetDir = await this.packager.getResource(undefined, APPX_ASSETS_DIR_NAME)\n    const assetInfo = await AppXTarget.computeUserAssets(vm, vendorPath, userAssetDir)\n    const userAssets = assetInfo.userAssets\n\n    const manifestFile = stageDir.getTempFile(\"AppxManifest.xml\")\n    await this.writeManifest(manifestFile, arch, await this.computePublisherName(), userAssets)\n    await packager.info.callAppxManifestCreated(manifestFile)\n    mappingList.push(assetInfo.mappings)\n    mappingList.push([`\"${vm.toVmFile(manifestFile)}\" \"AppxManifest.xml\"`])\n    const signToolArch = arch === Arch.arm64 ? \"x64\" : Arch[arch]\n\n    if (isScaledAssetsProvided(userAssets)) {\n      const outFile = vm.toVmFile(stageDir.getTempFile(\"resources.pri\"))\n      const makePriPath = vm.toVmFile(path.join(vendorPath, \"windows-10\", signToolArch, \"makepri.exe\"))\n\n      const assetRoot = stageDir.getTempFile(\"appx/assets\")\n      await emptyDir(assetRoot)\n      await BluebirdPromise.map(assetInfo.allAssets, it => copyOrLinkFile(it, path.join(assetRoot, path.basename(it))))\n\n      await vm.exec(makePriPath, [\n        \"new\",\n        \"/Overwrite\",\n        \"/Manifest\",\n        vm.toVmFile(manifestFile),\n        \"/ProjectRoot\",\n        vm.toVmFile(path.dirname(assetRoot)),\n        \"/ConfigXml\",\n        vm.toVmFile(path.join(getTemplatePath(\"appx\"), \"priconfig.xml\")),\n        \"/OutputFile\",\n        outFile,\n      ])\n\n      // in addition to resources.pri, resources.scale-140.pri and other such files will be generated\n      for (const resourceFile of (await readdir(stageDir.dir)).filter(it => it.startsWith(\"resources.\")).sort()) {\n        mappingList.push([`\"${vm.toVmFile(stageDir.getTempFile(resourceFile))}\" \"${resourceFile}\"`])\n      }\n      makeAppXArgs.push(\"/l\")\n    }\n\n    let mapping = \"[Files]\"\n    for (const list of mappingList) {\n      mapping += \"\\r\\n\" + list.join(\"\\r\\n\")\n    }\n    await writeFile(mappingFile, mapping)\n    packager.debugLogger.add(\"appx.mapping\", mapping)\n\n    if (this.options.makeappxArgs != null) {\n      makeAppXArgs.push(...this.options.makeappxArgs)\n    }\n    await vm.exec(vm.toVmFile(path.join(vendorPath, \"windows-10\", signToolArch, \"makeappx.exe\")), makeAppXArgs)\n    await packager.sign(artifactPath)\n\n    await stageDir.cleanup()\n\n    await packager.info.callArtifactBuildCompleted({\n      file: artifactPath,\n      packager,\n      arch,\n      safeArtifactName: packager.computeSafeArtifactName(artifactName, \"appx\"),\n      target: this,\n      isWriteUpdateInfo: this.options.electronUpdaterAware,\n    })\n  }\n\n  private static async computeUserAssets(vm: VmManager, vendorPath: string, userAssetDir: string | null) {\n    const mappings: Array<string> = []\n    let userAssets: Array<string>\n    const allAssets: Array<string> = []\n    if (userAssetDir == null) {\n      userAssets = []\n    } else {\n      userAssets = (await readdir(userAssetDir)).filter(it => !it.startsWith(\".\") && !it.endsWith(\".db\") && it.includes(\".\"))\n      for (const name of userAssets) {\n        mappings.push(`\"${vm.toVmFile(userAssetDir)}${vm.pathSep}${name}\" \"assets\\\\${name}\"`)\n        allAssets.push(path.join(userAssetDir, name))\n      }\n    }\n\n    for (const defaultAsset of Object.keys(vendorAssetsForDefaultAssets)) {\n      if (userAssets.length === 0 || !isDefaultAssetIncluded(userAssets, defaultAsset)) {\n        const file = path.join(vendorPath, \"appxAssets\", vendorAssetsForDefaultAssets[defaultAsset])\n        mappings.push(`\"${vm.toVmFile(file)}\" \"assets\\\\${defaultAsset}\"`)\n        allAssets.push(file)\n      }\n    }\n\n    // we do not use process.arch to build path to tools, because even if you are on x64, ia32 appx tool must be used if you build appx for ia32\n    return { userAssets, mappings, allAssets }\n  }\n\n  // https://github.com/electron-userland/electron-builder/issues/2108#issuecomment-333200711\n  private async computePublisherName() {\n    if ((await this.packager.cscInfo.value) == null) {\n      log.info({ reason: \"Windows Store only build\" }, \"AppX is not signed\")\n      return this.options.publisher || \"CN=ms\"\n    }\n\n    const certInfo = await this.packager.lazyCertInfo.value\n    const publisher = this.options.publisher || (certInfo == null ? null : certInfo.bloodyMicrosoftSubjectDn)\n    if (publisher == null) {\n      throw new Error(\"Internal error: cannot compute subject using certificate info\")\n    }\n    return publisher\n  }\n\n  private async writeManifest(outFile: string, arch: Arch, publisher: string, userAssets: Array<string>) {\n    const appInfo = this.packager.appInfo\n    const options = this.options\n    const executable = `app\\\\${appInfo.productFilename}.exe`\n    const displayName = options.displayName || appInfo.productName\n    const extensions = await this.getExtensions(executable, displayName)\n\n    const manifest = (await readFile(path.join(getTemplatePath(\"appx\"), \"appxmanifest.xml\"), \"utf8\")).replace(/\\${([a-zA-Z0-9]+)}/g, (match, p1): string => {\n      switch (p1) {\n        case \"publisher\":\n          return publisher\n\n        case \"publisherDisplayName\": {\n          const name = options.publisherDisplayName || appInfo.companyName\n          if (name == null) {\n            throw new InvalidConfigurationError(`Please specify \"author\" in the application package.json — it is required because \"appx.publisherDisplayName\" is not set.`)\n          }\n          return name\n        }\n\n        case \"version\":\n          return appInfo.getVersionInWeirdWindowsForm(options.setBuildNumber === true)\n\n        case \"applicationId\": {\n          const result = options.applicationId || options.identityName || appInfo.name\n          if (!isNaN(parseInt(result[0], 10))) {\n            let message = `AppX Application.Id can’t start with numbers: \"${result}\"`\n            if (options.applicationId == null) {\n              message += `\\nPlease set appx.applicationId (or correct appx.identityName or name)`\n            }\n            throw new InvalidConfigurationError(message)\n          }\n          return result\n        }\n\n        case \"identityName\":\n          return options.identityName || appInfo.name\n\n        case \"executable\":\n          return executable\n\n        case \"displayName\":\n          return displayName\n\n        case \"description\":\n          return appInfo.description || appInfo.productName\n\n        case \"backgroundColor\":\n          return options.backgroundColor || \"#464646\"\n\n        case \"logo\":\n          return \"assets\\\\StoreLogo.png\"\n\n        case \"square150x150Logo\":\n          return \"assets\\\\Square150x150Logo.png\"\n\n        case \"square44x44Logo\":\n          return \"assets\\\\Square44x44Logo.png\"\n\n        case \"lockScreen\":\n          return lockScreenTag(userAssets)\n\n        case \"defaultTile\":\n          return defaultTileTag(userAssets, options.showNameOnTiles || false)\n\n        case \"splashScreen\":\n          return splashScreenTag(userAssets)\n\n        case \"arch\":\n          return arch === Arch.ia32 ? \"x86\" : arch === Arch.arm64 ? \"arm64\" : \"x64\"\n\n        case \"resourceLanguages\":\n          return resourceLanguageTag(asArray(options.languages))\n\n        case \"extensions\":\n          return extensions\n\n        case \"minVersion\":\n          return arch === Arch.arm64 ? \"10.0.16299.0\" : \"10.0.14316.0\"\n\n        case \"maxVersionTested\":\n          return arch === Arch.arm64 ? \"10.0.16299.0\" : \"10.0.14316.0\"\n\n        default:\n          throw new Error(`Macro ${p1} is not defined`)\n      }\n    })\n    await writeFile(outFile, manifest)\n  }\n\n  private async getExtensions(executable: string, displayName: string): Promise<string> {\n    const uriSchemes = asArray(this.packager.config.protocols).concat(asArray(this.packager.platformSpecificBuildOptions.protocols))\n\n    const fileAssociations = asArray(this.packager.config.fileAssociations).concat(asArray(this.packager.platformSpecificBuildOptions.fileAssociations))\n\n    let isAddAutoLaunchExtension = this.options.addAutoLaunchExtension\n    if (isAddAutoLaunchExtension === undefined) {\n      const deps = this.packager.info.metadata.dependencies\n      isAddAutoLaunchExtension = deps != null && deps[\"electron-winstore-auto-launch\"] != null\n    }\n\n    if (!isAddAutoLaunchExtension && uriSchemes.length === 0 && fileAssociations.length === 0 && this.options.customExtensionsPath === undefined) {\n      return \"\"\n    }\n\n    let extensions = \"<Extensions>\"\n\n    if (isAddAutoLaunchExtension) {\n      extensions += `\n        <desktop:Extension Category=\"windows.startupTask\" Executable=\"${executable}\" EntryPoint=\"Windows.FullTrustApplication\">\n          <desktop:StartupTask TaskId=\"SlackStartup\" Enabled=\"true\" DisplayName=\"${displayName}\" />\n        </desktop:Extension>`\n    }\n\n    for (const protocol of uriSchemes) {\n      for (const scheme of asArray(protocol.schemes)) {\n        extensions += `\n          <uap:Extension Category=\"windows.protocol\">\n            <uap:Protocol Name=\"${scheme}\">\n               <uap:DisplayName>${protocol.name}</uap:DisplayName>\n             </uap:Protocol>\n          </uap:Extension>`\n      }\n    }\n\n    for (const fileAssociation of fileAssociations) {\n      for (const ext of asArray(fileAssociation.ext)) {\n        extensions += `\n          <uap:Extension Category=\"windows.fileTypeAssociation\">\n            <uap:FileTypeAssociation Name=\"${ext}\">\n              <uap:SupportedFileTypes>\n                <uap:FileType>.${ext}</uap:FileType>\n              </uap:SupportedFileTypes>\n            </uap:FileTypeAssociation>\n          </uap:Extension>`\n      }\n    }\n\n    if (this.options.customExtensionsPath !== undefined) {\n      const extensionsPath = path.resolve(this.packager.info.appDir, this.options.customExtensionsPath)\n      extensions += await readFile(extensionsPath, \"utf8\")\n    }\n\n    extensions += \"</Extensions>\"\n    return extensions\n  }\n}\n\n// get the resource - language tag, see https://docs.microsoft.com/en-us/windows/uwp/globalizing/manage-language-and-region#specify-the-supported-languages-in-the-apps-manifest\nfunction resourceLanguageTag(userLanguages: Array<string> | null | undefined): string {\n  if (userLanguages == null || userLanguages.length === 0) {\n    userLanguages = [DEFAULT_RESOURCE_LANG]\n  }\n  return userLanguages.map(it => `<Resource Language=\"${it.replace(/_/g, \"-\")}\" />`).join(\"\\n\")\n}\n\nfunction lockScreenTag(userAssets: Array<string>): string {\n  if (isDefaultAssetIncluded(userAssets, \"BadgeLogo.png\")) {\n    return '<uap:LockScreen Notification=\"badgeAndTileText\" BadgeLogo=\"assets\\\\BadgeLogo.png\" />'\n  } else {\n    return \"\"\n  }\n}\n\nfunction defaultTileTag(userAssets: Array<string>, showNameOnTiles: boolean): string {\n  const defaultTiles: Array<string> = [\"<uap:DefaultTile\", 'Wide310x150Logo=\"assets\\\\Wide310x150Logo.png\"']\n\n  if (isDefaultAssetIncluded(userAssets, \"LargeTile.png\")) {\n    defaultTiles.push('Square310x310Logo=\"assets\\\\LargeTile.png\"')\n  }\n  if (isDefaultAssetIncluded(userAssets, \"SmallTile.png\")) {\n    defaultTiles.push('Square71x71Logo=\"assets\\\\SmallTile.png\"')\n  }\n\n  if (showNameOnTiles) {\n    defaultTiles.push(\">\")\n    defaultTiles.push(\"<uap:ShowNameOnTiles>\")\n    defaultTiles.push(\"<uap:ShowOn\", 'Tile=\"wide310x150Logo\"', \"/>\")\n    defaultTiles.push(\"<uap:ShowOn\", 'Tile=\"square150x150Logo\"', \"/>\")\n    defaultTiles.push(\"</uap:ShowNameOnTiles>\")\n    defaultTiles.push(\"</uap:DefaultTile>\")\n  } else {\n    defaultTiles.push(\"/>\")\n  }\n  return defaultTiles.join(\" \")\n}\n\nfunction splashScreenTag(userAssets: Array<string>): string {\n  if (isDefaultAssetIncluded(userAssets, \"SplashScreen.png\")) {\n    return '<uap:SplashScreen Image=\"assets\\\\SplashScreen.png\" />'\n  } else {\n    return \"\"\n  }\n}\n\nfunction isDefaultAssetIncluded(userAssets: Array<string>, defaultAsset: string) {\n  const defaultAssetName = defaultAsset.substring(0, defaultAsset.indexOf(\".\"))\n  return userAssets.some(it => it.includes(defaultAssetName))\n}\n\nfunction isScaledAssetsProvided(userAssets: Array<string>) {\n  return userAssets.some(it => it.includes(\".scale-\") || it.includes(\".targetsize-\"))\n}\n"]}