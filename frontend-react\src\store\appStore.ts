// MizzyStar 新星计划 - 全局状态神经中枢
// 单一、安全的 Zustand Store，避免循环依赖

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

// ============================================================================
// 状态类型定义
// ============================================================================

interface AppState {
  // ========================================
  // 面板可见性状态
  // ========================================
  isCatalogVisible: boolean;
  isInfoPanelVisible: boolean;
  isWorkbenchVisible: boolean;
  isFullscreenGallery: boolean;

  // ========================================
  // 面板尺寸状态
  // ========================================
  catalogPanelWidth: number;
  infoPanelWidth: number;
  workbenchHeight: number;

  // ========================================
  // 数据选择状态
  // ========================================
  selectedCaseId: number | null;
  activeTagId: string | null;
  selectedFileIds: number[];
  
  // ========================================
  // 搜索和筛选状态
  // ========================================
  searchQuery: string;
  activeFilters: Record<string, string>;

  // ========================================
  // 画廊视图状态
  // ========================================
  galleryLayout: 'grid' | 'list' | 'masonry';
  galleryZoomLevel: number;
  gallerySortBy: 'name' | 'date' | 'size' | 'type';
  gallerySortOrder: 'asc' | 'desc';
  showFileName: boolean;
  showFileInfo: boolean;

  // ========================================
  // 工作台状态
  // ========================================
  activeWorkbench: 'clipboard' | 'batch' | 'export';

  // ========================================
  // Actions (操作方法)
  // ========================================
  
  // 面板控制
  toggleCatalog: () => void;
  toggleInfoPanel: () => void;
  toggleWorkbench: () => void;
  toggleFullscreenGallery: () => void;
  
  // 面板尺寸控制
  setCatalogPanelWidth: (width: number) => void;
  setInfoPanelWidth: (width: number) => void;
  setWorkbenchHeight: (height: number) => void;

  // 数据选择控制
  setSelectedCase: (caseId: number | null) => void;
  setActiveTag: (tagId: string | null) => void;
  setSelectedFile: (fileId: number) => void;
  toggleFileSelection: (fileId: number) => void;
  clearFileSelection: () => void;
  selectAllFiles: (fileIds: number[]) => void;

  // 搜索和筛选控制
  setSearchQuery: (query: string) => void;
  setActiveFilters: (filters: Record<string, string>) => void;
  clearFilters: () => void;

  // 画廊视图控制
  setGalleryLayout: (layout: 'grid' | 'list' | 'masonry') => void;
  setGalleryZoomLevel: (level: number) => void;
  setGallerySortBy: (sortBy: 'name' | 'date' | 'size' | 'type') => void;
  setGallerySortOrder: (order: 'asc' | 'desc') => void;
  toggleShowFileName: () => void;
  toggleShowFileInfo: () => void;

  // 工作台控制
  setActiveWorkbench: (workbench: 'clipboard' | 'batch' | 'export') => void;

  // 重置方法
  resetPanelStates: () => void;
  resetViewStates: () => void;
  resetAllStates: () => void;
}

// ============================================================================
// 初始状态定义
// ============================================================================

const initialState = {
  // 面板可见性
  isCatalogVisible: true,
  isInfoPanelVisible: true,
  isWorkbenchVisible: false,
  isFullscreenGallery: false,

  // 面板尺寸
  catalogPanelWidth: 280,
  infoPanelWidth: 320,
  workbenchHeight: 200,

  // 数据选择
  selectedCaseId: null,
  activeTagId: null,
  selectedFileIds: [],

  // 搜索和筛选
  searchQuery: '',
  activeFilters: {},

  // 画廊视图
  galleryLayout: 'grid' as const,
  galleryZoomLevel: 50,
  gallerySortBy: 'name' as const,
  gallerySortOrder: 'asc' as const,
  showFileName: true,
  showFileInfo: false,

  // 工作台
  activeWorkbench: 'clipboard' as const,
};

// ============================================================================
// 创建 Store
// ============================================================================

export const useAppStore = create<AppState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      ...initialState,

      // ========================================
      // 面板控制 Actions
      // ========================================
      toggleCatalog: () => 
        set((state) => ({ isCatalogVisible: !state.isCatalogVisible }), false, 'toggleCatalog'),

      toggleInfoPanel: () => 
        set((state) => ({ isInfoPanelVisible: !state.isInfoPanelVisible }), false, 'toggleInfoPanel'),

      toggleWorkbench: () => 
        set((state) => ({ isWorkbenchVisible: !state.isWorkbenchVisible }), false, 'toggleWorkbench'),

      toggleFullscreenGallery: () => 
        set((state) => ({ isFullscreenGallery: !state.isFullscreenGallery }), false, 'toggleFullscreenGallery'),

      // ========================================
      // 面板尺寸控制 Actions
      // ========================================
      setCatalogPanelWidth: (width) => 
        set({ catalogPanelWidth: Math.max(200, Math.min(500, width)) }, false, 'setCatalogPanelWidth'),

      setInfoPanelWidth: (width) => 
        set({ infoPanelWidth: Math.max(250, Math.min(600, width)) }, false, 'setInfoPanelWidth'),

      setWorkbenchHeight: (height) => 
        set({ workbenchHeight: Math.max(150, Math.min(400, height)) }, false, 'setWorkbenchHeight'),

      // ========================================
      // 数据选择控制 Actions
      // ========================================
      setSelectedCase: (caseId) => 
        set({ selectedCaseId: caseId }, false, 'setSelectedCase'),

      setActiveTag: (tagId) => 
        set({ activeTagId: tagId }, false, 'setActiveTag'),

      setSelectedFile: (fileId) => 
        set({ selectedFileIds: [fileId] }, false, 'setSelectedFile'),

      toggleFileSelection: (fileId) => 
        set((state) => {
          const isSelected = state.selectedFileIds.includes(fileId);
          return {
            selectedFileIds: isSelected
              ? state.selectedFileIds.filter(id => id !== fileId)
              : [...state.selectedFileIds, fileId]
          };
        }, false, 'toggleFileSelection'),

      clearFileSelection: () => 
        set({ selectedFileIds: [] }, false, 'clearFileSelection'),

      selectAllFiles: (fileIds) => 
        set({ selectedFileIds: [...fileIds] }, false, 'selectAllFiles'),

      // ========================================
      // 搜索和筛选控制 Actions
      // ========================================
      setSearchQuery: (query) => 
        set({ searchQuery: query }, false, 'setSearchQuery'),

      setActiveFilters: (filters) => 
        set({ activeFilters: filters }, false, 'setActiveFilters'),

      clearFilters: () => 
        set({ activeFilters: {}, searchQuery: '' }, false, 'clearFilters'),

      // ========================================
      // 画廊视图控制 Actions
      // ========================================
      setGalleryLayout: (layout) => 
        set({ galleryLayout: layout }, false, 'setGalleryLayout'),

      setGalleryZoomLevel: (level) => 
        set({ galleryZoomLevel: Math.max(10, Math.min(100, level)) }, false, 'setGalleryZoomLevel'),

      setGallerySortBy: (sortBy) => 
        set({ gallerySortBy: sortBy }, false, 'setGallerySortBy'),

      setGallerySortOrder: (order) => 
        set({ gallerySortOrder: order }, false, 'setGallerySortOrder'),

      toggleShowFileName: () => 
        set((state) => ({ showFileName: !state.showFileName }), false, 'toggleShowFileName'),

      toggleShowFileInfo: () => 
        set((state) => ({ showFileInfo: !state.showFileInfo }), false, 'toggleShowFileInfo'),

      // ========================================
      // 工作台控制 Actions
      // ========================================
      setActiveWorkbench: (workbench) => 
        set({ activeWorkbench: workbench }, false, 'setActiveWorkbench'),

      // ========================================
      // 重置方法 Actions
      // ========================================
      resetPanelStates: () => 
        set({
          isCatalogVisible: initialState.isCatalogVisible,
          isInfoPanelVisible: initialState.isInfoPanelVisible,
          isWorkbenchVisible: initialState.isWorkbenchVisible,
          isFullscreenGallery: initialState.isFullscreenGallery,
        }, false, 'resetPanelStates'),

      resetViewStates: () => 
        set({
          galleryLayout: initialState.galleryLayout,
          galleryZoomLevel: initialState.galleryZoomLevel,
          gallerySortBy: initialState.gallerySortBy,
          gallerySortOrder: initialState.gallerySortOrder,
          showFileName: initialState.showFileName,
          showFileInfo: initialState.showFileInfo,
        }, false, 'resetViewStates'),

      resetAllStates: () => 
        set({ ...initialState }, false, 'resetAllStates'),
    }),
    {
      name: 'mizzy-star-store', // DevTools 中的名称
    }
  )
);

// ============================================================================
// 选择器 Hooks (可选的便利方法)
// ============================================================================

// 面板状态选择器
export const usePanelStates = () => useAppStore((state) => ({
  isCatalogVisible: state.isCatalogVisible,
  isInfoPanelVisible: state.isInfoPanelVisible,
  isWorkbenchVisible: state.isWorkbenchVisible,
  isFullscreenGallery: state.isFullscreenGallery,
}));

// 选择状态选择器
export const useSelectionStates = () => useAppStore((state) => ({
  selectedCaseId: state.selectedCaseId,
  activeTagId: state.activeTagId,
  selectedFileIds: state.selectedFileIds,
}));

// 画廊状态选择器
export const useGalleryStates = () => useAppStore((state) => ({
  galleryLayout: state.galleryLayout,
  galleryZoomLevel: state.galleryZoomLevel,
  gallerySortBy: state.gallerySortBy,
  gallerySortOrder: state.gallerySortOrder,
  showFileName: state.showFileName,
  showFileInfo: state.showFileInfo,
}));

// 搜索状态选择器
export const useSearchStates = () => useAppStore((state) => ({
  searchQuery: state.searchQuery,
  activeFilters: state.activeFilters,
}));
