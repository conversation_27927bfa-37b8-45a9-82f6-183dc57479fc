openapi: 3.0.3
info:
  title: 迷星 Mizzy Star 标签管理 API
  description: |
    标签管理系统API规范文档
    
    提供完整的标签管理功能，包括：
    - 标签树结构获取
    - 标签搜索和筛选
    - 自定义标签管理
    - 文件标签关联
    - 批量标签操作
    - 标签缓存管理
    
    ## 标签数据结构
    
    系统支持多种类型的标签：
    - **properties**: 高基数属性标签（如文件名、质量分数等）
    - **metadata**: 元数据标签（如相机、镜头、项目等）
    - **cv**: 计算机视觉标签（如物体、场景等）
    - **user**: 用户标签
    - **ai**: AI生成标签
    - **custom**: 自定义标签
    
  version: 1.0.0
  contact:
    name: Mizzy Star API Support
    email: <EMAIL>

servers:
  - url: http://localhost:8001/api/v1
    description: 开发服务器

paths:
  /cases/{case_id}/tags/tree:
    get:
      summary: 获取案例标签树结构
      description: |
        返回指定案例的完整标签树结构，包含所有标签类别的层级组织。
        
        标签树结构包括：
        - properties: 高基数属性标签
        - tags: 按类别组织的标签（metadata, cv, user, ai）
        - custom: 自定义标签
      tags:
        - Tags
      parameters:
        - name: case_id
          in: path
          required: true
          schema:
            type: integer
          description: 案例ID
      responses:
        '200':
          description: 成功返回标签树
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TagTreeResponse'
        '404':
          description: 案例不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /cases/{case_id}/tags/search:
    get:
      summary: 搜索案例标签
      description: |
        根据关键词搜索标签，支持按标签名称和值进行模糊搜索。
        可选择特定类别进行筛选。
      tags:
        - Tags
      parameters:
        - name: case_id
          in: path
          required: true
          schema:
            type: integer
          description: 案例ID
        - name: q
          in: query
          required: true
          schema:
            type: string
          description: 搜索关键词
        - name: category
          in: query
          required: false
          schema:
            type: string
            enum: [properties, metadata, cv, user, ai, custom]
          description: 标签类别筛选
        - name: limit
          in: query
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 200
            default: 50
          description: 返回结果数量限制
      responses:
        '200':
          description: 成功返回搜索结果
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TagSearchResponse'
        '404':
          description: 案例不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /cases/{case_id}/tags/stats:
    get:
      summary: 获取案例标签统计信息
      description: |
        返回标签总数、各类别数量和最常用标签等统计信息。
      tags:
        - Tags
      parameters:
        - name: case_id
          in: path
          required: true
          schema:
            type: integer
          description: 案例ID
      responses:
        '200':
          description: 成功返回统计信息
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TagStatsResponse'
        '404':
          description: 案例不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /cases/{case_id}/tags/cache/refresh:
    post:
      summary: 刷新案例标签缓存
      description: |
        重新扫描所有文件的标签数据并重建缓存。
        当标签数据不一致时使用此接口。
      tags:
        - Tags
      parameters:
        - name: case_id
          in: path
          required: true
          schema:
            type: integer
          description: 案例ID
      responses:
        '200':
          description: 缓存刷新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TagCacheRefreshResponse'
        '404':
          description: 案例不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /cases/{case_id}/tags/custom:
    get:
      summary: 获取案例的所有自定义标签
      description: |
        返回指定案例的所有自定义标签，包含使用统计信息。
      tags:
        - Custom Tags
      parameters:
        - name: case_id
          in: path
          required: true
          schema:
            type: integer
          description: 案例ID
      responses:
        '200':
          description: 成功返回自定义标签列表
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CustomTag'
        '404':
          description: 案例不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    post:
      summary: 创建自定义标签
      description: |
        为指定案例创建新的自定义标签。
      tags:
        - Custom Tags
      parameters:
        - name: case_id
          in: path
          required: true
          schema:
            type: integer
          description: 案例ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomTagCreate'
      responses:
        '201':
          description: 自定义标签创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomTag'
        '400':
          description: 请求参数错误或标签名已存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: 案例不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /cases/{case_id}/tags/custom/{tag_id}:
    get:
      summary: 获取单个自定义标签
      description: |
        获取指定自定义标签的详细信息。
      tags:
        - Custom Tags
      parameters:
        - name: case_id
          in: path
          required: true
          schema:
            type: integer
          description: 案例ID
        - name: tag_id
          in: path
          required: true
          schema:
            type: integer
          description: 自定义标签ID
      responses:
        '200':
          description: 成功返回自定义标签
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomTag'
        '404':
          description: 案例或标签不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    put:
      summary: 更新自定义标签
      description: |
        更新指定自定义标签的信息。
      tags:
        - Custom Tags
      parameters:
        - name: case_id
          in: path
          required: true
          schema:
            type: integer
          description: 案例ID
        - name: tag_id
          in: path
          required: true
          schema:
            type: integer
          description: 自定义标签ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomTagUpdate'
      responses:
        '200':
          description: 自定义标签更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomTag'
        '400':
          description: 请求参数错误或标签名冲突
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: 案例或标签不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    delete:
      summary: 删除自定义标签
      description: |
        删除指定的自定义标签，同时移除所有文件关联。
      tags:
        - Custom Tags
      parameters:
        - name: case_id
          in: path
          required: true
          schema:
            type: integer
          description: 案例ID
        - name: tag_id
          in: path
          required: true
          schema:
            type: integer
          description: 自定义标签ID
      responses:
        '204':
          description: 自定义标签删除成功
        '404':
          description: 案例或标签不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /cases/{case_id}/files/{file_id}/tags/custom:
    get:
      summary: 获取文件的自定义标签
      description: |
        获取指定文件关联的所有自定义标签。
      tags:
        - File Tags
      parameters:
        - name: case_id
          in: path
          required: true
          schema:
            type: integer
          description: 案例ID
        - name: file_id
          in: path
          required: true
          schema:
            type: integer
          description: 文件ID
      responses:
        '200':
          description: 成功返回文件的自定义标签
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CustomTag'
        '404':
          description: 案例不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    post:
      summary: 为文件添加自定义标签
      description: |
        为指定文件添加一个或多个自定义标签。
      tags:
        - File Tags
      parameters:
        - name: case_id
          in: path
          required: true
          schema:
            type: integer
          description: 案例ID
        - name: file_id
          in: path
          required: true
          schema:
            type: integer
          description: 文件ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddCustomTagsRequest'
      responses:
        '200':
          description: 标签添加操作完成
          content:
            application/json:
              schema:
                type: object
                properties:
                  added_tags:
                    type: array
                    items:
                      type: integer
                    description: 成功添加的标签ID列表
                  skipped_tags:
                    type: array
                    items:
                      type: object
                      properties:
                        tag_id:
                          type: integer
                        reason:
                          type: string
                    description: 跳过的标签及原因
                  success:
                    type: boolean
                    description: 是否有标签成功添加
        '404':
          description: 案例不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /cases/{case_id}/files/{file_id}/tags/custom/{tag_id}:
    delete:
      summary: 从文件移除自定义标签
      description: |
        从指定文件移除特定的自定义标签关联。
      tags:
        - File Tags
      parameters:
        - name: case_id
          in: path
          required: true
          schema:
            type: integer
          description: 案例ID
        - name: file_id
          in: path
          required: true
          schema:
            type: integer
          description: 文件ID
        - name: tag_id
          in: path
          required: true
          schema:
            type: integer
          description: 自定义标签ID
      responses:
        '204':
          description: 标签关联移除成功
        '404':
          description: 案例不存在或标签关联不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /cases/{case_id}/tags/batch:
    post:
      summary: 批量标签操作
      description: |
        对多个文件执行批量标签操作，支持批量添加或移除自定义标签。
      tags:
        - Batch Operations
      parameters:
        - name: case_id
          in: path
          required: true
          schema:
            type: integer
          description: 案例ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BatchTagOperationRequest'
      responses:
        '200':
          description: 批量操作完成
          content:
            application/json:
              schema:
                type: object
                properties:
                  action:
                    type: string
                    description: 执行的操作类型
                  tag_id:
                    type: integer
                    description: 操作的标签ID
                  success_files:
                    type: array
                    items:
                      type: integer
                    description: 操作成功的文件ID列表
                  failed_files:
                    type: array
                    items:
                      type: object
                      properties:
                        file_id:
                          type: integer
                        reason:
                          type: string
                    description: 操作失败的文件及原因
                  success_count:
                    type: integer
                    description: 成功操作的文件数量
                  failed_count:
                    type: integer
                    description: 失败操作的文件数量
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: 案例不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    TagValue:
      type: object
      properties:
        value:
          type: string
          description: 标签值
        count:
          type: integer
          description: 包含此标签值的文件数量
        file_ids:
          type: array
          items:
            type: integer
          description: 包含此标签值的文件ID列表
      required:
        - value
        - count
        - file_ids

    TagCategory:
      type: object
      properties:
        name:
          type: string
          description: 标签名称
        values:
          type: array
          items:
            $ref: '#/components/schemas/TagValue'
          description: 标签值列表
      required:
        - name
        - values

    CustomTag:
      type: object
      properties:
        id:
          type: integer
          description: 自定义标签ID
        name:
          type: string
          description: 标签名称
        color:
          type: string
          pattern: '^#[0-9A-Fa-f]{6}$'
          description: 标签颜色（十六进制）
        display_order:
          type: integer
          description: 显示顺序
        count:
          type: integer
          description: 使用此标签的文件数量
        file_ids:
          type: array
          items:
            type: integer
          description: 使用此标签的文件ID列表
        created_at:
          type: string
          format: date-time
          description: 创建时间
        updated_at:
          type: string
          format: date-time
          description: 更新时间
      required:
        - id
        - name
        - color
        - display_order
        - count
        - file_ids
        - created_at
        - updated_at

    TagTreeResponse:
      type: object
      properties:
        properties:
          type: object
          additionalProperties:
            type: array
            items:
              $ref: '#/components/schemas/TagValue'
          description: 高基数属性标签
        tags:
          type: object
          properties:
            metadata:
              type: array
              items:
                $ref: '#/components/schemas/TagCategory'
              description: 元数据标签
            cv:
              type: array
              items:
                $ref: '#/components/schemas/TagCategory'
              description: 计算机视觉标签
            user:
              type: array
              items:
                $ref: '#/components/schemas/TagCategory'
              description: 用户标签
            ai:
              type: array
              items:
                $ref: '#/components/schemas/TagCategory'
              description: AI标签
          description: 按类别组织的标签
        custom:
          type: array
          items:
            $ref: '#/components/schemas/CustomTag'
          description: 自定义标签
      required:
        - properties
        - tags
        - custom

    TagSearchResult:
      type: object
      properties:
        category:
          type: string
          description: 标签类别
        name:
          type: string
          description: 标签名称
        value:
          type: string
          description: 标签值
        count:
          type: integer
          description: 文件数量
        file_ids:
          type: array
          items:
            type: integer
          description: 文件ID列表
      required:
        - category
        - name
        - value
        - count
        - file_ids

    TagSearchResponse:
      type: object
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/TagSearchResult'
          description: 搜索结果列表
        total:
          type: integer
          description: 总结果数量
      required:
        - results
        - total

    TagStatsResponse:
      type: object
      properties:
        total_tags:
          type: integer
          description: 标签总数
        categories:
          type: object
          additionalProperties:
            type: integer
          description: 各类别标签数量
        most_used:
          type: array
          items:
            type: object
            properties:
              category:
                type: string
              name:
                type: string
              value:
                type: string
              count:
                type: integer
          description: 最常用标签列表
      required:
        - total_tags
        - categories
        - most_used

    TagCacheRefreshResponse:
      type: object
      properties:
        success:
          type: boolean
          description: 操作是否成功
        message:
          type: string
          description: 操作结果消息
        processed_files:
          type: integer
          description: 处理的文件数量
        cache_entries:
          type: integer
          description: 生成的缓存条目数量
      required:
        - success
        - message
        - processed_files
        - cache_entries

    CustomTagCreate:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 100
          description: 标签名称
        color:
          type: string
          pattern: '^#[0-9A-Fa-f]{6}$'
          default: '#3B82F6'
          description: 标签颜色（十六进制）
      required:
        - name

    CustomTagUpdate:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 100
          description: 标签名称
        color:
          type: string
          pattern: '^#[0-9A-Fa-f]{6}$'
          description: 标签颜色（十六进制）
        display_order:
          type: integer
          description: 显示顺序

    AddCustomTagsRequest:
      type: object
      properties:
        tag_ids:
          type: array
          items:
            type: integer
          minItems: 1
          description: 要添加的标签ID列表
      required:
        - tag_ids

    BatchTagOperationRequest:
      type: object
      properties:
        action:
          type: string
          enum: [add, remove]
          description: 操作类型
        file_ids:
          type: array
          items:
            type: integer
          minItems: 1
          description: 目标文件ID列表
        tag_id:
          type: integer
          description: 操作的标签ID
      required:
        - action
        - file_ids
        - tag_id

    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          description: 错误类型
        message:
          type: string
          description: 错误消息
        details:
          type: object
          description: 错误详情
        timestamp:
          type: string
          format: date-time
          description: 错误时间戳
      required:
        - error
        - message

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []

tags:
  - name: Tags
    description: 标签树和搜索相关操作
  - name: Custom Tags
    description: 自定义标签管理操作
  - name: File Tags
    description: 文件标签关联操作
  - name: Batch Operations
    description: 批量标签操作