-- =============================================================================
-- 高性能标签检索索引优化方案
-- 针对10,000+文件的实时标签检索优化
-- =============================================================================

-- 1. 复合JSONB索引 - 支持多标签组合查询
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_files_tags_composite 
ON files USING GIN (
    (tags->'tags'->'user'), 
    (tags->'tags'->'ai'), 
    (tags->'tags'->'custom')
);

-- 2. 标签存在性索引 - 快速筛选有标签的文件
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_files_has_user_tags 
ON files ((tags->'tags'->'user' IS NOT NULL AND jsonb_array_length(tags->'tags'->'user') > 0))
WHERE tags->'tags'->'user' IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_files_has_custom_tags 
ON files ((tags->'tags'->'custom' IS NOT NULL AND jsonb_typeof(tags->'tags'->'custom') = 'object'))
WHERE tags->'tags'->'custom' IS NOT NULL;

-- 3. 标签计数索引 - 支持按标签数量排序
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_files_tag_count 
ON files ((
    COALESCE(jsonb_array_length(tags->'tags'->'user'), 0) + 
    COALESCE(jsonb_array_length(tags->'tags'->'ai'), 0) +
    COALESCE((SELECT COUNT(*) FROM jsonb_object_keys(tags->'tags'->'custom')), 0)
));

-- 4. 案例+标签复合索引 - 优化案例内标签查询
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_files_case_tags 
ON files (case_id, (tags->'tags'->'user'), (tags->'tags'->'custom'));

-- 5. 时间+标签索引 - 支持时间范围内的标签查询
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_files_time_tags 
ON files (created_at DESC, (tags->'tags'->'user'))
WHERE tags->'tags'->'user' IS NOT NULL;

-- 6. 质量+标签索引 - 支持高质量文件的标签查询
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_files_quality_tags 
ON files (quality_score DESC, (tags->'tags'->'user'))
WHERE quality_score IS NOT NULL AND tags->'tags'->'user' IS NOT NULL;

-- =============================================================================
-- 标签缓存表优化索引
-- =============================================================================

-- 1. 标签查找复合索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tag_cache_lookup 
ON tag_cache (case_id, tag_category, tag_name, tag_value);

-- 2. 文件数量排序索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tag_cache_popularity 
ON tag_cache (case_id, file_count DESC, tag_category);

-- 3. 标签名称模糊搜索索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tag_cache_name_search 
ON tag_cache USING GIN (to_tsvector('simple', tag_name || ' ' || tag_value));

-- 4. 文件ID数组索引 - 支持文件ID查询
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tag_cache_file_ids 
ON tag_cache USING GIN (file_ids);

-- =============================================================================
-- 性能监控视图
-- =============================================================================

-- 创建标签查询性能监控视图
CREATE OR REPLACE VIEW tag_query_performance AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched,
    ROUND(idx_tup_read::numeric / NULLIF(idx_scan, 0), 2) as avg_tuples_per_scan
FROM pg_stat_user_indexes 
WHERE tablename IN ('files', 'tag_cache')
ORDER BY idx_scan DESC;

-- 创建标签使用统计视图
CREATE OR REPLACE VIEW tag_usage_stats AS
SELECT 
    tc.tag_category,
    tc.tag_name,
    tc.tag_value,
    tc.file_count,
    COUNT(*) OVER (PARTITION BY tc.tag_category) as category_total_tags,
    ROUND(tc.file_count * 100.0 / SUM(tc.file_count) OVER (PARTITION BY tc.case_id), 2) as usage_percentage
FROM tag_cache tc
ORDER BY tc.file_count DESC;

-- =============================================================================
-- 查询优化函数
-- =============================================================================

-- 高性能标签搜索函数
CREATE OR REPLACE FUNCTION search_files_by_tags(
    p_case_id INTEGER,
    p_user_tags TEXT[] DEFAULT NULL,
    p_custom_tags JSONB DEFAULT NULL,
    p_operator TEXT DEFAULT 'AND',
    p_limit INTEGER DEFAULT 100,
    p_offset INTEGER DEFAULT 0
) RETURNS TABLE(
    file_id INTEGER,
    file_name VARCHAR,
    file_path TEXT,
    thumbnail_path TEXT,
    tag_match_count INTEGER,
    relevance_score NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    WITH tag_matches AS (
        SELECT 
            f.id,
            f.file_name,
            f.file_path,
            f.thumbnail_small_path,
            -- 计算标签匹配数量
            (
                CASE WHEN p_user_tags IS NOT NULL THEN
                    (SELECT COUNT(*) FROM unnest(p_user_tags) AS ut(tag) 
                     WHERE f.tags->'tags'->'user' ? ut.tag)
                ELSE 0 END +
                CASE WHEN p_custom_tags IS NOT NULL THEN
                    (SELECT COUNT(*) FROM jsonb_object_keys(p_custom_tags) AS ck(key)
                     WHERE f.tags->'tags'->'custom' ? ck.key 
                     AND f.tags->'tags'->'custom'->ck.key = p_custom_tags->ck.key)
                ELSE 0 END
            ) AS match_count,
            -- 计算相关性分数
            (
                COALESCE(f.quality_score, 0) * 0.3 +
                (EXTRACT(EPOCH FROM NOW() - f.created_at) / 86400) * -0.1 +
                (
                    CASE WHEN p_user_tags IS NOT NULL THEN
                        (SELECT COUNT(*) FROM unnest(p_user_tags) AS ut(tag) 
                         WHERE f.tags->'tags'->'user' ? ut.tag) * 10
                    ELSE 0 END
                ) +
                (
                    CASE WHEN p_custom_tags IS NOT NULL THEN
                        (SELECT COUNT(*) FROM jsonb_object_keys(p_custom_tags) AS ck(key)
                         WHERE f.tags->'tags'->'custom' ? ck.key) * 15
                    ELSE 0 END
                )
            ) AS relevance
        FROM files f
        WHERE f.case_id = p_case_id
        AND (
            CASE p_operator
                WHEN 'AND' THEN (
                    (p_user_tags IS NULL OR 
                     (SELECT COUNT(*) FROM unnest(p_user_tags) AS ut(tag) 
                      WHERE f.tags->'tags'->'user' ? ut.tag) = array_length(p_user_tags, 1)) AND
                    (p_custom_tags IS NULL OR 
                     (SELECT COUNT(*) FROM jsonb_object_keys(p_custom_tags) AS ck(key)
                      WHERE f.tags->'tags'->'custom' ? ck.key 
                      AND f.tags->'tags'->'custom'->ck.key = p_custom_tags->ck.key) = jsonb_object_keys(p_custom_tags))
                )
                WHEN 'OR' THEN (
                    (p_user_tags IS NOT NULL AND 
                     EXISTS(SELECT 1 FROM unnest(p_user_tags) AS ut(tag) 
                            WHERE f.tags->'tags'->'user' ? ut.tag)) OR
                    (p_custom_tags IS NOT NULL AND 
                     EXISTS(SELECT 1 FROM jsonb_object_keys(p_custom_tags) AS ck(key)
                            WHERE f.tags->'tags'->'custom' ? ck.key))
                )
                ELSE TRUE
            END
        )
    )
    SELECT 
        tm.id,
        tm.file_name,
        tm.file_path,
        tm.thumbnail_small_path,
        tm.match_count,
        tm.relevance
    FROM tag_matches tm
    WHERE tm.match_count > 0
    ORDER BY tm.relevance DESC, tm.match_count DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- 标签自动完成函数
CREATE OR REPLACE FUNCTION get_tag_suggestions(
    p_case_id INTEGER,
    p_query TEXT,
    p_category TEXT DEFAULT NULL,
    p_limit INTEGER DEFAULT 20
) RETURNS TABLE(
    tag_name TEXT,
    tag_value TEXT,
    tag_category TEXT,
    file_count INTEGER,
    relevance_score NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        tc.tag_name,
        tc.tag_value,
        tc.tag_category,
        tc.file_count,
        (
            CASE 
                WHEN tc.tag_name ILIKE p_query || '%' THEN 100
                WHEN tc.tag_value ILIKE p_query || '%' THEN 90
                WHEN tc.tag_name ILIKE '%' || p_query || '%' THEN 70
                WHEN tc.tag_value ILIKE '%' || p_query || '%' THEN 60
                ELSE 0
            END + 
            LOG(tc.file_count + 1) * 10
        ) AS relevance
    FROM tag_cache tc
    WHERE tc.case_id = p_case_id
    AND (p_category IS NULL OR tc.tag_category = p_category)
    AND (
        tc.tag_name ILIKE '%' || p_query || '%' OR 
        tc.tag_value ILIKE '%' || p_query || '%'
    )
    ORDER BY relevance DESC, tc.file_count DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;
