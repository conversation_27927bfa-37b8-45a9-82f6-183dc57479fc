{"version": 3, "file": "mosaicUpdates.js", "sourceRoot": "", "sources": ["../../src/util/mosaicUpdates.ts"], "names": [], "mappings": ";;;;;;AAAA,4EAAyC;AACzC,qDAA+B;AAC/B,+DAAyC;AACzC,2DAAqC;AACrC,qDAA+B;AAC/B,mDAA6B;AAC7B,qDAA+B;AAC/B,kDAA4D;AAW5D,qDAAiF;AAKjF;;;;GAIG;AACH,SAAgB,mBAAmB,CAAsB,YAA6B;IACpF,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;QAChC,OAAO,IAAA,aAAG,EAAC,EAAE,EAAE,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;KACtD;SAAM;QACL,OAAO,YAAY,CAAC,IAAI,CAAC;KAC1B;AACH,CAAC;AAND,kDAMC;AAED;;;;;GAKG;AACH,SAAgB,UAAU,CAAsB,IAAmB,EAAE,OAA0B;IAC7F,IAAI,WAAW,GAAG,IAAI,CAAC;IACvB,OAAO,CAAC,OAAO,CAAC,UAAC,OAAwB;QACvC,WAAW,GAAG,IAAA,6BAAM,EAAC,WAA8B,EAAE,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC;IACrF,CAAC,CAAC,CAAC;IAEH,OAAO,WAAW,CAAC;AACrB,CAAC;AAPD,gCAOC;AAED;;;;;GAKG;AACH,SAAgB,kBAAkB,CAAsB,IAA0B,EAAE,IAAgB;IAClG,IAAM,UAAU,GAAG,IAAA,mBAAS,EAAC,IAAI,CAAC,CAAC;IACnC,IAAM,YAAY,GAAG,IAAA,cAAI,EAAC,IAAI,CAAC,CAAC;IAChC,IAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,IAAA,gCAAc,EAAC,YAAa,CAAC,CAAC,CAAC;IACrE,IAAM,OAAO,GAAG,IAAA,8CAA4B,EAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IAEhE,OAAO;QACL,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE;YACJ,IAAI,EAAE,OAAO;SACd;KACF,CAAC;AACJ,CAAC;AAZD,gDAYC;AAED,SAAS,iBAAiB,CAAC,CAAa,EAAE,CAAa,EAAE,MAAc;IACrE,OAAO,IAAA,iBAAO,EAAC,IAAA,cAAI,EAAC,CAAC,EAAE,MAAM,CAAC,EAAE,IAAA,cAAI,EAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;AACnD,CAAC;AAED;;;;;;;;GAQG;AACH,SAAgB,mBAAmB,CACjC,IAAmB,EACnB,UAAsB,EACtB,eAA2B,EAC3B,QAAkC;IAElC,IAAI,eAAe,GAAG,IAAA,8CAA4B,EAAC,IAAI,EAAE,eAAe,CAAC,CAAC;IAC1E,IAAM,OAAO,GAAsB,EAAE,CAAC;IAEtC,IAAM,2BAA2B,GAAG,iBAAiB,CAAC,UAAU,EAAE,eAAe,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;IAC3G,IAAI,2BAA2B,EAAE;QAC/B,0DAA0D;QAC1D,eAAe,GAAG,UAAU,CAAC,eAAe,EAAE;YAC5C,kBAAkB,CAAC,eAAe,EAAE,IAAA,cAAI,EAAC,UAAU,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;SAC9E,CAAC,CAAC;KACJ;SAAM;QACL,6BAA6B;QAC7B,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;QAEnD,4EAA4E;QAC5E,IAAM,yBAAyB,GAAG,iBAAiB,CAAC,UAAU,EAAE,eAAe,EAAE,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACxG,IAAI,yBAAyB,EAAE;YAC7B,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;SAClD;KACF;IAED,IAAM,UAAU,GAAG,IAAA,8CAA4B,EAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IAClE,IAAI,KAAoB,CAAC;IACzB,IAAI,MAAqB,CAAC;IAC1B,IAAI,QAAQ,KAAK,wCAAwB,CAAC,IAAI,IAAI,QAAQ,KAAK,wCAAwB,CAAC,GAAG,EAAE;QAC3F,KAAK,GAAG,UAAU,CAAC;QACnB,MAAM,GAAG,eAAe,CAAC;KAC1B;SAAM;QACL,KAAK,GAAG,eAAe,CAAC;QACxB,MAAM,GAAG,UAAU,CAAC;KACrB;IAED,IAAI,SAAS,GAAoB,QAAQ,CAAC;IAC1C,IAAI,QAAQ,KAAK,wCAAwB,CAAC,IAAI,IAAI,QAAQ,KAAK,wCAAwB,CAAC,KAAK,EAAE;QAC7F,SAAS,GAAG,KAAK,CAAC;KACnB;IAED,OAAO,CAAC,IAAI,CAAC;QACX,IAAI,EAAE,eAAe;QACrB,IAAI,EAAE;YACJ,IAAI,EAAE,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,SAAS,WAAA,EAAE;SACnC;KACF,CAAC,CAAC;IAEH,OAAO,OAAO,CAAC;AACjB,CAAC;AAlDD,kDAkDC;AAED;;;;GAIG;AACH,SAAgB,gBAAgB,CAAsB,IAAgB;IACpE,IAAM,UAAU,GAAG,IAAA,mBAAS,EAAC,IAAI,CAAC,CAAC;IACnC,IAAM,UAAU,GAAG,IAAA,cAAI,EAAC,IAAI,CAAC,CAAC;IAE9B,IAAI,eAAuB,CAAC;IAC5B,IAAI,UAAU,KAAK,OAAO,EAAE;QAC1B,eAAe,GAAG,CAAC,CAAC;KACrB;SAAM;QACL,eAAe,GAAG,GAAG,CAAC;KACvB;IAED,OAAO;QACL,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE;YACJ,eAAe,EAAE;gBACf,IAAI,EAAE,eAAe;aACtB;SACF;KACF,CAAC;AACJ,CAAC;AAnBD,4CAmBC;AAED;;;;;GAKG;AACH,SAAgB,kBAAkB,CAAsB,IAAgB,EAAE,UAAkB;;IAC1F,IAAI,IAAI,GAAwB,EAAE,CAAC;IACnC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;QACzC,IAAM,MAAM,GAAiB,IAAI,CAAC,CAAC,CAAC,CAAC;QACrC,IAAM,eAAe,GAAG,MAAM,KAAK,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,UAAU,CAAC;QAC3E,IAAI;gBACF,eAAe,EAAE;oBACf,IAAI,EAAE,eAAe;iBACtB;;YACD,GAAC,MAAM,IAAG,IAAI;eACf,CAAC;KACH;IAED,OAAO;QACL,IAAI,MAAA;QACJ,IAAI,EAAE,EAAE;KACT,CAAC;AACJ,CAAC;AAjBD,gDAiBC"}