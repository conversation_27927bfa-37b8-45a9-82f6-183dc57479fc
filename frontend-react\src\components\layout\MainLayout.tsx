import * as React from 'react';
import { cn } from '@/utils/cn';

// ============================================================================
// MainLayout Component Interface
// ============================================================================

export interface MainLayoutProps {
  /**
   * 目录栏内容
   */
  catalogPanel: React.ReactNode;
  
  /**
   * 画廊内容
   */
  galleryPanel: React.ReactNode;
  
  /**
   * 工作台内容
   */
  workbenchPanel?: React.ReactNode;
  
  /**
   * 信息栏内容
   */
  infoPanel: React.ReactNode;
  
  /**
   * 是否显示目录栏
   */
  showCatalogPanel?: boolean;
  
  /**
   * 是否显示工作台
   */
  showWorkbench?: boolean;
  
  /**
   * 是否显示信息栏
   */
  showInfoPanel?: boolean;
  
  /**
   * 是否全屏画廊模式
   */
  isFullscreenGallery?: boolean;
  
  /**
   * 目录栏宽度 (px)
   */
  catalogPanelWidth?: number;
  
  /**
   * 信息栏宽度 (px)
   */
  infoPanelWidth?: number;
  
  /**
   * 工作台高度 (px)
   */
  workbenchHeight?: number;
  
  /**
   * 布局变化回调
   */
  onLayoutChange?: (layout: {
    catalogPanelWidth: number;
    infoPanelWidth: number;
    workbenchHeight: number;
  }) => void;
  
  className?: string;
}

// ============================================================================
// MainLayout Component Implementation
// ============================================================================

/**
 * MainLayout 组件 - Project Novak 主布局
 * 
 * 实现 Mizzy Star 的四象限布局系统：
 * ┌─────────────┬─────────────────────┬─────────────┐
 * │   目录栏    │        画廊         │   信息栏    │
 * │  (Catalog)  │      (Gallery)      │   (Info)    │
 * │             ├─────────────────────┤             │
 * │             │       工作台        │             │
 * │             │    (Workbench)      │             │
 * └─────────────┴─────────────────────┴─────────────┘
 * 
 * 设计特点：
 * 1. 响应式布局：支持面板显隐切换
 * 2. 可调整尺寸：支持拖拽调整面板大小
 * 3. 全屏模式：支持画廊全屏显示
 * 4. 性能优化：使用CSS Grid实现高效布局
 */
const MainLayout = React.forwardRef<HTMLDivElement, MainLayoutProps>(
  ({
    catalogPanel,
    galleryPanel,
    workbenchPanel,
    infoPanel,
    showCatalogPanel = true,
    showWorkbench = false,
    showInfoPanel = true,
    isFullscreenGallery = false,
    catalogPanelWidth = 280,
    infoPanelWidth = 320,
    workbenchHeight = 300,
    onLayoutChange,
    className,
    ...props
  }, ref) => {
    
    // 全屏画廊模式
    if (isFullscreenGallery) {
      return (
        <div
          ref={ref}
          className={cn('h-screen w-screen bg-background', className)}
          {...props}
        >
          {galleryPanel}
        </div>
      );
    }

    // 计算网格模板
    const gridTemplateColumns = [
      showCatalogPanel ? `${catalogPanelWidth}px` : '0px',
      '1fr', // 画廊占据剩余空间
      showInfoPanel ? `${infoPanelWidth}px` : '0px',
    ].join(' ');

    const gridTemplateRows = showWorkbench 
      ? `1fr ${workbenchHeight}px`
      : '1fr';

    return (
      <div
        ref={ref}
        className={cn(
          'h-screen w-screen bg-background overflow-hidden',
          'grid gap-0',
          className
        )}
        style={{
          gridTemplateColumns,
          gridTemplateRows,
          gridTemplateAreas: showWorkbench
            ? `"catalog gallery info" "catalog workbench info"`
            : `"catalog gallery info"`,
        }}
        {...props}
      >
        {/* 目录栏 */}
        {showCatalogPanel && (
          <div
            className="panel-border panel-shadow bg-card overflow-hidden"
            style={{ gridArea: 'catalog' }}
          >
            {catalogPanel}
          </div>
        )}

        {/* 画廊 */}
        <div
          className="bg-background overflow-hidden relative"
          style={{ gridArea: 'gallery' }}
        >
          {galleryPanel}
        </div>

        {/* 工作台 */}
        {showWorkbench && workbenchPanel && (
          <div
            className="panel-border panel-shadow bg-card overflow-hidden border-t"
            style={{ gridArea: 'workbench' }}
          >
            {workbenchPanel}
          </div>
        )}

        {/* 信息栏 */}
        {showInfoPanel && (
          <div
            className="panel-border panel-shadow bg-card overflow-hidden"
            style={{ gridArea: 'info' }}
          >
            {infoPanel}
          </div>
        )}

        {/* 调整手柄 - 目录栏 */}
        {showCatalogPanel && (
          <div
            className="absolute top-0 bottom-0 w-1 bg-border hover:bg-primary cursor-col-resize z-10 transition-colors"
            style={{ left: `${catalogPanelWidth}px` }}
            onMouseDown={(e) => {
              // TODO: 实现拖拽调整逻辑
              console.log('Resize catalog panel', e);
            }}
          />
        )}

        {/* 调整手柄 - 信息栏 */}
        {showInfoPanel && (
          <div
            className="absolute top-0 bottom-0 w-1 bg-border hover:bg-primary cursor-col-resize z-10 transition-colors"
            style={{ right: `${infoPanelWidth}px` }}
            onMouseDown={(e) => {
              // TODO: 实现拖拽调整逻辑
              console.log('Resize info panel', e);
            }}
          />
        )}

        {/* 调整手柄 - 工作台 */}
        {showWorkbench && (
          <div
            className="absolute left-0 right-0 h-1 bg-border hover:bg-primary cursor-row-resize z-10 transition-colors"
            style={{ bottom: `${workbenchHeight}px` }}
            onMouseDown={(e) => {
              // TODO: 实现拖拽调整逻辑
              console.log('Resize workbench', e);
            }}
          />
        )}
      </div>
    );
  }
);

MainLayout.displayName = 'MainLayout';

export { MainLayout };
