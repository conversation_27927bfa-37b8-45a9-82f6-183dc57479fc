// Project Novak - API Hooks
// 基于 TanStack Query 的服务器状态管理 Hooks

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getCases,
  getCase,
  createCase,
  getFiles,
  getFile,
  uploadFiles,
  deleteFile,
  getTags,
  updateFileTag,
  batchUpdateTags,
  searchFiles,
  searchTags,
} from '@/services/api';
import type { FileFilters } from '@/types';

// ============================================================================
// Query Keys (查询键管理)
// ============================================================================

export const queryKeys = {
  // Cases
  cases: ['cases'] as const,
  case: (id: number) => ['cases', id] as const,

  // Files
  files: ['files'] as const,
  filesList: (filters: Partial<FileFilters>) => ['files', 'list', filters] as const,
  file: (id: number) => ['files', id] as const,

  // Tags
  tags: ['tags'] as const,
  tagsList: (caseId?: number) => ['tags', 'list', caseId] as const,

  // Search
  searchFiles: (query: string, caseId?: number) => ['search', 'files', query, caseId] as const,
  searchTags: (query: string, caseId?: number) => ['search', 'tags', query, caseId] as const,
} as const;

// ============================================================================
// Cases Hooks
// ============================================================================

/**
 * 获取所有案例列表
 */
export function useCases() {
  return useQuery({
    queryKey: queryKeys.cases,
    queryFn: getCases,
    staleTime: 5 * 60 * 1000, // 5分钟内认为数据是新鲜的
  });
}

/**
 * 获取单个案例详情
 */
export function useCase(caseId: number | null) {
  return useQuery({
    queryKey: queryKeys.case(caseId!),
    queryFn: () => getCase(caseId!),
    enabled: caseId !== null, // 只有当 caseId 存在时才执行查询
    staleTime: 5 * 60 * 1000,
  });
}

/**
 * 创建新案例
 */
export function useCreateCase() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createCase,
    onSuccess: () => {
      // 创建成功后，使案例列表缓存失效，触发重新获取
      queryClient.invalidateQueries({ queryKey: queryKeys.cases });
    },
  });
}

// ============================================================================
// Files Hooks
// ============================================================================

/**
 * 获取文件列表（支持筛选）
 */
export function useFiles(filters: Partial<FileFilters> = {}) {
  return useQuery({
    queryKey: queryKeys.filesList(filters),
    queryFn: () => getFiles(filters),
    staleTime: 2 * 60 * 1000, // 2分钟内认为数据是新鲜的
  });
}

/**
 * 获取单个文件详情
 */
export function useFile(fileId: number | null) {
  return useQuery({
    queryKey: queryKeys.file(fileId!),
    queryFn: () => getFile(fileId!),
    enabled: fileId !== null,
    staleTime: 5 * 60 * 1000,
  });
}

/**
 * 上传文件
 */
export function useUploadFiles() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ caseId, files }: { caseId: number; files: FileList }) =>
      uploadFiles(caseId, files),
    onSuccess: () => {
      // 上传成功后，使文件列表缓存失效
      queryClient.invalidateQueries({ queryKey: queryKeys.files });
    },
  });
}

/**
 * 删除文件
 * 支持乐观更新，提供更好的用户体验
 */
export function useDeleteFile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ caseId, fileId }: { caseId: number; fileId: number }) =>
      deleteFile(caseId, fileId),

    // 乐观更新：立即从UI中移除文件，不等待服务器响应
    onMutate: async ({ caseId, fileId }) => {
      // 取消任何正在进行的查询，避免冲突
      await queryClient.cancelQueries({ queryKey: queryKeys.files });

      // 获取当前的文件列表缓存
      const previousFiles = queryClient.getQueriesData({ queryKey: queryKeys.files });

      // 乐观更新：从所有相关的文件列表中移除该文件
      queryClient.setQueriesData(
        { queryKey: queryKeys.files },
        (oldData: any) => {
          if (!oldData?.data) return oldData;

          return {
            ...oldData,
            data: oldData.data.filter((file: any) => file.id !== fileId),
            total: Math.max(0, (oldData.total || 0) - 1),
          };
        }
      );

      // 返回上下文，用于错误回滚
      return { previousFiles, caseId, fileId };
    },

    // 成功时的处理
    onSuccess: (data, variables, context) => {
      console.log(`✅ File ${variables.fileId} deleted successfully from case ${variables.caseId}`);

      // 可以在这里添加成功通知
      // showNotification('文件已删除', 'success');
    },

    // 错误时回滚乐观更新
    onError: (error, variables, context) => {
      console.error('❌ File deletion failed:', error);

      // 回滚所有乐观更新
      if (context?.previousFiles) {
        context.previousFiles.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }

      // 可以在这里添加错误通知
      // showNotification('删除文件失败', 'error');
    },

    // 无论成功还是失败，都重新获取数据确保一致性
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.files });
    },
  });
}

// ============================================================================
// Tags Hooks
// ============================================================================

/**
 * 获取标签列表
 */
export function useTags(caseId?: number) {
  return useQuery({
    queryKey: queryKeys.tagsList(caseId),
    queryFn: () => getTags(caseId),
    staleTime: 5 * 60 * 1000,
  });
}

/**
 * 更新文件标签
 */
export function useUpdateFileTag() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateFileTag,
    onSuccess: () => {
      // 标签更新成功后，使相关缓存失效
      queryClient.invalidateQueries({ queryKey: queryKeys.tags });
      queryClient.invalidateQueries({ queryKey: queryKeys.files });
    },
  });
}

/**
 * 批量更新标签
 */
export function useBatchUpdateTags() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: batchUpdateTags,
    onSuccess: () => {
      // 批量更新成功后，使所有相关缓存失效
      queryClient.invalidateQueries({ queryKey: queryKeys.tags });
      queryClient.invalidateQueries({ queryKey: queryKeys.files });
    },
  });
}

// ============================================================================
// Search Hooks
// ============================================================================

/**
 * 搜索文件
 */
export function useSearchFiles(query: string, caseId?: number) {
  return useQuery({
    queryKey: queryKeys.searchFiles(query, caseId),
    queryFn: () => searchFiles(query, caseId),
    enabled: query.length > 0, // 只有当查询不为空时才执行
    staleTime: 1 * 60 * 1000, // 搜索结果1分钟内有效
  });
}

/**
 * 搜索标签
 */
export function useSearchTags(query: string, caseId?: number) {
  return useQuery({
    queryKey: queryKeys.searchTags(query, caseId),
    queryFn: () => searchTags(query, caseId),
    enabled: query.length > 0,
    staleTime: 1 * 60 * 1000,
  });
}

// ============================================================================
// Utility Hooks
// ============================================================================

/**
 * 预取文件详情（用于优化用户体验）
 */
export function usePrefetchFile() {
  const queryClient = useQueryClient();

  return (fileId: number) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.file(fileId),
      queryFn: () => getFile(fileId),
      staleTime: 5 * 60 * 1000,
    });
  };
}

/**
 * 手动刷新数据
 */
export function useRefreshData() {
  const queryClient = useQueryClient();

  return {
    refreshFiles: () => queryClient.invalidateQueries({ queryKey: queryKeys.files }),
    refreshTags: () => queryClient.invalidateQueries({ queryKey: queryKeys.tags }),
    refreshCases: () => queryClient.invalidateQueries({ queryKey: queryKeys.cases }),
    refreshAll: () => queryClient.invalidateQueries(),
  };
}

// ============================================================================
// Error Handling Hook
// ============================================================================

/**
 * 统一的错误处理
 */
export function useApiError() {
  return {
    handleError: (error: unknown) => {
      console.error('API Error:', error);
      // 这里可以添加全局错误处理逻辑
      // 比如显示错误通知、记录错误日志等
    },
  };
}
