# src/schemas.py
from pydantic import BaseModel, Field, field_serializer
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum

# 导入模型中的枚举
from .models import RuleType
import json
from . import models # 确保你已经导入了 models
# 导入案例状态枚举
class CaseStatus(str, Enum):
    ACTIVE = "active"
    DELETED = "deleted"
    PERMANENTLY_DELETED = "permanently_deleted"

# 封面类型枚举
class CoverType(str, Enum):
    MANUAL = "manual"
    AUTOMATIC = "automatic"
    PLACEHOLDER = "placeholder"

# 规则类型枚举
class RuleType(str, Enum):
    FILENAME_PARSING = "FILENAME_PARSING"
    DATE_TAGGING_FORMAT = "DATE_TAGGING_FORMAT"

# --- 标签系统 Schemas ---

# 文件标签结构
class FileTagsProperties(BaseModel):
    """存储高基数属性，如文件名、质量分数、哈希值等"""
    filename: Optional[str] = None
    qualityScore: Optional[float] = None
    hash: Optional[str] = None
    fileSize: Optional[int] = None

class FileTagsMetadata(BaseModel):
    """元数据标签"""
    camera: Optional[str] = None
    lens: Optional[str] = None
    project: Optional[str] = None
    photographer: Optional[str] = None
    captureDate: Optional[str] = None

class FileTagsCV(BaseModel):
    """计算机视觉标签"""
    objects: Optional[List[str]] = None
    faces: Optional[int] = None
    scene: Optional[str] = None

class FileTagsCategories(BaseModel):
    """存储低基数标签，按类别组织"""
    metadata: Optional[FileTagsMetadata] = None
    cv: Optional[FileTagsCV] = None
    user: Optional[List[str]] = None  # 用户自定义标签
    ai: Optional[List[str]] = None    # AI生成的标签

class FileTags(BaseModel):
    """文件标签的复杂对象结构，包含properties和tags两个固定的key"""
    properties: Optional[FileTagsProperties] = None
    tags: Optional[FileTagsCategories] = None

# --- File Schemas ---
# 文件不再需要 case_id，因为其归属由它所在的数据库文件决定

class FileBase(BaseModel):
    file_name: str
    file_type: Optional[str] = None
    width: Optional[int] = None
    height: Optional[int] = None

class FileCreate(FileBase):
    file_path: str # 在创建时，路径是必须的
    thumbnail_small_path: Optional[str] = None
    taken_at: Optional[datetime] = None
    case_id: Optional[int] = None  # PostgreSQL模式下需要case_id

class File(FileBase):
    id: int
    file_path: str
    thumbnail_small_path: Optional[str] = None
    created_at: datetime
    taken_at: Optional[datetime] = None
    case_id: Optional[int] = None  # PostgreSQL模式下的case_id

    # 图像质量分析字段
    quality_score: Optional[float] = None
    sharpness: Optional[float] = None
    brightness: Optional[float] = None
    dynamic_range: Optional[float] = None
    num_faces: Optional[int] = None
    face_sharpness: Optional[float] = None
    face_quality: Optional[float] = None
    cluster_id: Optional[int] = None
    phash: Optional[str] = None
    group_id: Optional[str] = None
    frame_number: Optional[int] = None
    isCurrentCoverSource: Optional[bool] = False  # 是否是当前封面的源文件

    # 标签系统字段
    tags: Optional[Union[FileTags, str, dict]] = None

    # 向量字段 - 用于AI搜索和相似性匹配
    vector_image: Optional[List[float]] = None
    vector_text: Optional[List[float]] = None

    @field_serializer('tags')
    def serialize_tags(self, value):
        """序列化tags字段，处理JSON字符串"""
        if value is None:
            return None

        # 如果是字符串，尝试解析为JSON
        if isinstance(value, str):
            try:
                parsed = json.loads(value)
                return parsed
            except (json.JSONDecodeError, TypeError):
                return None

        # 如果是FileTags对象，转换为字典
        if hasattr(value, 'dict'):
            return value.dict()

        # 如果已经是字典，直接返回
        if isinstance(value, dict):
            return value

        return None

    class Config:
        from_attributes = True # 允许从ORM对象转换

# --- Case Schemas ---
# Case 的关键变化是：返回时需要包含 db_path 和 files 列表

class CaseBase(BaseModel):
    case_name: str
    description: Optional[str] = None

class CaseCreate(CaseBase):
    pass

class CaseUpdate(BaseModel):
    case_name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[CaseStatus] = None

class CaseBasic(CaseBase):
    """案例基本信息（不包含文件列表，用于优化性能）"""
    id: int
    created_at: datetime
    status: CaseStatus = CaseStatus.ACTIVE  # 案例状态
    deleted_at: Optional[datetime] = None   # 删除时间

    # 封面相关字段（直接使用数据库字段名）
    cover_image_url: Optional[str] = None
    cover_type: CoverType = CoverType.PLACEHOLDER
    cover_source_file_id: Optional[int] = None
    cover_needs_attention: bool = False
    cover_updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Case(CaseBase):
    id: int
    created_at: datetime
    status: CaseStatus = CaseStatus.ACTIVE  # 案例状态
    deleted_at: Optional[datetime] = None   # 删除时间
    files: List[File] = []       # PostgreSQL单一数据库架构 - 直接关联文件列表

    # 封面相关字段（直接使用数据库字段名）
    cover_image_url: Optional[str] = None
    cover_type: CoverType = CoverType.PLACEHOLDER
    cover_source_file_id: Optional[int] = None
    cover_needs_attention: bool = False
    cover_updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# --- 回收站专用的数据模式 ---
class TrashCase(CaseBase):
    """回收站中的案例数据模式"""
    id: int
    created_at: datetime
    deleted_at: datetime  # 回收站中必须有删除时间
    db_path: Optional[str] = None
    files: List[File] = []

    class Config:
        from_attributes = True

# --- 操作响应模式 ---
# --- 图像质量分析模式 ---
class QualityAnalysisRequest(BaseModel):
    """质量分析请求模式"""
    file_ids: Optional[List[int]] = None  # 指定要分析的文件ID列表，如果为空则分析全部
    analyze_similarity: Optional[bool] = True
    export_excel: Optional[bool] = True
    min_cluster_size: Optional[int] = 2
    weights: Optional[Dict[str, float]] = None
    phash_threshold: Optional[int] = 18

class QualityAnalysisResponse(BaseModel):
    """质量分析响应模式"""
    success: bool
    message: str
    task_id: Optional[str] = None
    total_files: Optional[int] = None
    clusters_count: Optional[int] = None
    report_path: Optional[str] = None
    coverUpdated: Optional[bool] = False
    newCoverUrl: Optional[str] = None

class ClusterSummary(BaseModel):
    """聚类摘要信息"""
    cluster_id: int
    file_count: int
    representative_file: Optional[str] = None
    avg_quality_score: Optional[float] = None

# --- 操作响应模式 ---
class OperationResponse(BaseModel):
    """通用操作响应模式"""
    success: bool
    message: str
    data: Optional[dict] = None

# --- 封面管理相关模式 ---
class SetCoverRequest(BaseModel):
    """设置封面请求模式"""
    fileId: int

class CoverReselectionResult(BaseModel):
    """封面重选结果模式"""
    coverReselected: bool
    reselectionStatus: str  # NOT_NEEDED, SUCCESS, FAILED_NO_IMAGES, FAILED_ERROR
    requiresManualSelection: bool
    newCoverUrl: Optional[str] = None

class FileDeleteResponse(BaseModel):
    """文件删除响应模式"""
    success: bool
    message: str
    coverReselected: bool
    reselectionStatus: str
    requiresManualSelection: bool
    newCoverUrl: Optional[str] = None

class RemoveCoverResponse(BaseModel):
    """移除封面响应模式"""
    case: Case
    revertedTo: str  # automatic, placeholder

# --- 案例处理规则 Schemas ---

class CaseProcessingRuleBase(BaseModel):
    """案例处理规则基础模式"""
    rule_type: RuleType
    rule_config: Dict[str, Any]  # 自由形式的对象
    is_active: bool = True

class CreateRuleRequest(CaseProcessingRuleBase):
    """创建规则请求模式"""
    pass

class UpdateRuleRequest(BaseModel):
    """更新规则请求模式"""
    rule_config: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None

class CaseProcessingRule(CaseProcessingRuleBase):
    """案例处理规则完整模式"""
    id: int
    case_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# --- 文件筛选相关 Schemas ---

class FileListResponse(BaseModel):
    """文件列表响应模式"""
    files: List[File]
    total: int
    limit: int
    offset: int
    filters: Optional[Dict[str, str]] = None

# --- 标签管理相关 Schemas ---

class TagValue(BaseModel):
    """标签值模式"""
    value: str
    count: int
    file_ids: List[int]

class TagCategory(BaseModel):
    """标签类别模式"""
    name: str
    values: List[TagValue]

class TagTreeResponse(BaseModel):
    """标签树响应模式"""
    properties: Optional[Dict[str, Any]] = None
    tags: Dict[str, List[TagCategory]]
    custom: List['CustomTag'] = []

# --- 自定义标签相关 Schemas ---

class CustomTagBase(BaseModel):
    """自定义标签基础模式"""
    name: str
    color: str = "#3B82F6"
    display_order: int = 0

class CustomTagCreate(CustomTagBase):
    """创建自定义标签请求模式"""
    pass

class CustomTagUpdate(BaseModel):
    """更新自定义标签请求模式"""
    name: Optional[str] = None
    color: Optional[str] = None
    display_order: Optional[int] = None

class CustomTag(CustomTagBase):
    """自定义标签完整模式"""
    id: int
    case_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# --- 规则管理相关 Schemas ---

class CreateRuleRequest(BaseModel):
    """创建规则请求模式"""
    rule_type: RuleType
    rule_config: Dict[str, Any]
    is_active: bool = True

class UpdateRuleRequest(BaseModel):
    """更新规则请求模式"""
    rule_config: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None

class CaseProcessingRule(BaseModel):
    """案例处理规则模式"""
    id: int
    case_id: int
    rule_type: RuleType
    rule_config: Dict[str, Any]
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class OperationResponse(BaseModel):
    """操作响应模式"""
    success: bool
    message: str

class TagSearchResult(BaseModel):
    """标签搜索结果模式"""
    category: str
    name: str
    value: str
    count: int
    file_ids: List[int]

class TagSearchResponse(BaseModel):
    """标签搜索响应模式"""
    results: List[TagSearchResult]

class CustomTagCreate(BaseModel):
    """创建自定义标签请求模式"""
    name: str
    color: Optional[str] = "#3B82F6"

class CustomTagUpdate(BaseModel):
    """更新自定义标签请求模式"""
    name: Optional[str] = None
    color: Optional[str] = None
    display_order: Optional[int] = None

class CustomTag(BaseModel):
    """自定义标签模式"""
    id: int
    name: str
    color: str
    display_order: int
    count: Optional[int] = 0
    file_ids: Optional[List[int]] = []
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class FileCustomTagsAdd(BaseModel):
    """为文件添加自定义标签请求模式"""
    tag_names: List[str]  # 改为使用标签名称而不是ID

class BatchTagOperation(BaseModel):
    """批量标签操作请求模式"""
    action: str  # "add" or "remove"
    file_ids: List[int]

class TagStatsResponse(BaseModel):
    """标签统计响应模式"""
    total_tags: int
    categories: Dict[str, int]
    most_used: List[Dict[str, Any]]

class TagCacheRefreshResponse(BaseModel):
    """标签缓存刷新响应模式"""
    success: bool
    message: str
    processed_files: int
    cache_entries: int

# --- 错误响应 Schema ---

class ErrorResponse(BaseModel):
    """错误响应模式"""
    error: str
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: Optional[datetime] = None

# --- 文件名提取功能 Schemas ---

class FilenameExtractionRule(BaseModel):
    """文件名提取规则"""
    id: str = Field(..., description="规则唯一标识")
    start: int = Field(..., ge=0, description="起始字符索引")
    end: int = Field(..., gt=0, description="结束字符索引")
    tag_name: str = Field(..., min_length=1, max_length=50, description="标签名称")
    description: Optional[str] = Field(None, max_length=200, description="规则描述")

class FilenameExtractionOptions(BaseModel):
    """文件名提取选项"""
    overwrite_existing: bool = Field(False, description="是否覆盖已存在的同名自定义标签")
    skip_on_error: bool = Field(True, description="遇到错误时是否跳过该文件")

class FilenameExtractionRequest(BaseModel):
    """文件名提取请求"""
    file_ids: List[int] = Field(..., min_items=1, description="要处理的文件ID列表")
    extraction_rules: List[FilenameExtractionRule] = Field(..., min_items=1, description="提取规则列表")
    options: FilenameExtractionOptions = Field(default_factory=FilenameExtractionOptions, description="提取选项")

class FilenameExtractionResponse(BaseModel):
    """文件名提取响应"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    message: str = Field(..., description="响应消息")
    estimated_time: str = Field(..., description="预估完成时间")
    files_count: int = Field(..., description="文件数量")
    rules_count: int = Field(..., description="规则数量")

class FilenameExtractionResult(BaseModel):
    """文件名提取结果"""
    task_id: str = Field(..., description="任务ID")
    success_count: int = Field(..., description="成功处理的文件数量")
    error_count: int = Field(..., description="处理失败的文件数量")
    generated_tags: List[str] = Field(..., description="生成的标签名称列表")
    errors: List[Dict[str, Any]] = Field(default_factory=list, description="错误详情列表")
