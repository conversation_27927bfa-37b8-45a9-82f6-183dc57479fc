import * as React from 'react';
import { But<PERSON>, FileCard } from '@/components/ui';
import { cn } from '@/utils/cn';

// ============================================================================
// WorkbenchPanel Component Interface
// ============================================================================

export interface WorkbenchPanelProps {
  /**
   * 当前活动的工作台类型
   */
  activeWorkbench?: 'clipboard' | 'cluster' | null;

  /**
   * 剪贴板中的文件
   */
  clipboardFiles?: Array<{
    id: number;
    fileName: string;
    filePath: string;
    fileType: string;
    fileSize: number;
    thumbnailPath?: string;
    position?: { x: number; y: number };
  }>;

  /**
   * 图像簇数据
   */
  clusterData?: {
    mainImage?: {
      id: number;
      fileName: string;
      filePath: string;
      fileType: string;
      fileSize: number;
      thumbnailPath?: string;
    };
    similarImages?: Array<{
      id: number;
      fileName: string;
      filePath: string;
      fileType: string;
      fileSize: number;
      thumbnailPath?: string;
    }>;
    versionImages?: Array<{
      id: number;
      fileName: string;
      filePath: string;
      fileType: string;
      fileSize: number;
      thumbnailPath?: string;
    }>;
  };

  /**
   * 工作台切换回调
   */
  onWorkbenchChange?: (type: 'clipboard' | 'cluster' | null) => void;

  /**
   * 剪贴板文件操作回调
   */
  onClipboardFileMove?: (fileId: number, position: { x: number; y: number }) => void;

  /**
   * 图像簇操作回调
   */
  onClusterOperation?: (operation: 'setMain' | 'addSimilar' | 'addVersion' | 'remove', fileId: number) => void;

  className?: string;
}

// ============================================================================
// WorkbenchPanel Component Implementation
// ============================================================================

/**
 * WorkbenchPanel 组件 - 工作台面板
 *
 * 实现 Mizzy Star 的灵活工作台功能：
 * 1. 剪贴板 - 自由排布和组织图片
 * 2. 图像簇整理 - 相似图片分组和版本管理
 * 3. 可扩展的工作台组件系统
 *
 * 对应原始需求中的【工作台】功能
 */
const WorkbenchPanel = React.forwardRef<HTMLDivElement, WorkbenchPanelProps>(
  ({
    activeWorkbench = null,
    clipboardFiles = [],
    clusterData,
    onWorkbenchChange,
    onClipboardFileMove,
    onClusterOperation,
    className,
    ...props
  }, ref) => {

    return (
      <div
        ref={ref}
        className={cn(
          'h-full flex flex-col bg-card',
          className
        )}
        {...props}
      >
        {/* 工作台标签栏 */}
        <div className="flex-shrink-0 border-b border-border">
          <div className="flex items-center">
            <Button
              variant={activeWorkbench === 'clipboard' ? 'primary' : 'ghost'}
              size="sm"
              className="rounded-none border-r"
              onClick={() => onWorkbenchChange?.('clipboard')}
            >
              📋 剪贴板
            </Button>
            <Button
              variant={activeWorkbench === 'cluster' ? 'primary' : 'ghost'}
              size="sm"
              className="rounded-none border-r"
              onClick={() => onWorkbenchChange?.('cluster')}
            >
              🔗 图像簇整理
            </Button>
            <div className="flex-1" />
            <Button
              variant="ghost"
              size="sm"
              className="rounded-none"
              onClick={() => onWorkbenchChange?.(null)}
            >
              ✕
            </Button>
          </div>
        </div>

        {/* 工作台内容区域 */}
        <div className="flex-1 overflow-hidden">
          {activeWorkbench === 'clipboard' && (
            <ClipboardWorkbench
              files={clipboardFiles}
              onFileMove={onClipboardFileMove}
            />
          )}

          {activeWorkbench === 'cluster' && (
            <ClusterWorkbench
              clusterData={clusterData}
              onClusterOperation={onClusterOperation}
            />
          )}

          {!activeWorkbench && (
            <div className="h-full flex items-center justify-center">
              <div className="text-center space-y-4">
                <div className="text-4xl">🛠️</div>
                <h3 className="text-lg font-medium">选择工作台</h3>
                <p className="text-muted-foreground max-w-md">
                  选择一个工作台组件开始工作，或从画廊拖拽图片到此处
                </p>
                <div className="flex gap-2 justify-center">
                  <Button
                    variant="outline"
                    onClick={() => onWorkbenchChange?.('clipboard')}
                  >
                    📋 剪贴板
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => onWorkbenchChange?.('cluster')}
                  >
                    🔗 图像簇整理
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }
);

// ============================================================================
// 剪贴板工作台子组件
// ============================================================================

interface ClipboardWorkbenchProps {
  files: WorkbenchPanelProps['clipboardFiles'];
  onFileMove?: WorkbenchPanelProps['onClipboardFileMove'];
}

const ClipboardWorkbench: React.FC<ClipboardWorkbenchProps> = ({
  files = [],
  onFileMove: _onFileMove,
}) => {
  return (
    <div className="h-full relative bg-background overflow-auto">
      {files.length === 0 ? (
        <div className="h-full flex items-center justify-center">
          <div className="text-center space-y-4">
            <div className="text-4xl">📋</div>
            <h3 className="text-lg font-medium">剪贴板为空</h3>
            <p className="text-muted-foreground">
              从画廊拖拽图片到此处开始整理
            </p>
          </div>
        </div>
      ) : (
        <div className="p-4 min-h-full">
          {files.map((file) => (
            <div
              key={file.id}
              className="absolute"
              style={{
                left: file.position?.x || 0,
                top: file.position?.y || 0,
              }}
            >
              <FileCard
                file={file}
                showFileName
                className="cursor-move"
                onMouseDown={(e) => {
                  // TODO: 实现拖拽移动逻辑
                  console.log('Start dragging file', file.id, e);
                }}
              />
            </div>
          ))}
        </div>
      )}

      {/* 工具栏 */}
      <div className="absolute top-4 right-4 flex gap-2">
        <Button variant="outline" size="sm">
          🔄 自动排列
        </Button>
        <Button variant="outline" size="sm">
          🗑️ 清空
        </Button>
      </div>
    </div>
  );
};

// ============================================================================
// 图像簇工作台子组件
// ============================================================================

interface ClusterWorkbenchProps {
  clusterData?: WorkbenchPanelProps['clusterData'];
  onClusterOperation?: WorkbenchPanelProps['onClusterOperation'];
}

const ClusterWorkbench: React.FC<ClusterWorkbenchProps> = ({
  clusterData,
  onClusterOperation: _onClusterOperation,
}) => {
  if (!clusterData) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="text-4xl">🔗</div>
          <h3 className="text-lg font-medium">未选择图像簇</h3>
          <p className="text-muted-foreground">
            在画廊中双击图像簇卡片开始整理
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full p-4 space-y-6 overflow-auto">
      {/* 主图区域 */}
      <div className="space-y-2">
        <h3 className="text-sm font-medium text-foreground">主图</h3>
        <div className="border-2 border-dashed border-primary/50 rounded-lg p-4 min-h-[200px] flex items-center justify-center">
          {clusterData.mainImage ? (
            <FileCard
              file={clusterData.mainImage}
              showFileName
              showFileInfo
              size="lg"
            />
          ) : (
            <div className="text-center text-muted-foreground">
              <div className="text-2xl mb-2">🖼️</div>
              <p>拖拽图片到此处设为主图</p>
            </div>
          )}
        </div>
      </div>

      {/* 相似图区域 */}
      <div className="space-y-2">
        <h3 className="text-sm font-medium text-foreground">
          相似图 ({clusterData.similarImages?.length || 0})
        </h3>
        <div className="border border-border rounded-lg p-4 min-h-[120px]">
          {clusterData.similarImages && clusterData.similarImages.length > 0 ? (
            <div className="grid grid-cols-4 gap-2">
              {clusterData.similarImages.map((image) => (
                <FileCard
                  key={image.id}
                  file={image}
                  showFileName
                  size="sm"
                />
              ))}
            </div>
          ) : (
            <div className="h-full flex items-center justify-center text-muted-foreground">
              <div className="text-center">
                <div className="text-xl mb-1">📸</div>
                <p className="text-sm">拖拽相似图片到此处</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 版本图区域 */}
      <div className="space-y-2">
        <h3 className="text-sm font-medium text-foreground">
          版本图 ({clusterData.versionImages?.length || 0})
        </h3>
        <div className="border border-border rounded-lg p-4 min-h-[120px]">
          {clusterData.versionImages && clusterData.versionImages.length > 0 ? (
            <div className="grid grid-cols-4 gap-2">
              {clusterData.versionImages.map((image) => (
                <FileCard
                  key={image.id}
                  file={image}
                  showFileName
                  size="sm"
                />
              ))}
            </div>
          ) : (
            <div className="h-full flex items-center justify-center text-muted-foreground">
              <div className="text-center">
                <div className="text-xl mb-1">🔄</div>
                <p className="text-sm">拖拽版本图片到此处</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex gap-2 pt-4 border-t border-border">
        <Button variant="destructive" size="sm">
          🗑️ 解散图像簇
        </Button>
        <Button variant="outline" size="sm">
          💾 保存更改
        </Button>
      </div>
    </div>
  );
};

WorkbenchPanel.displayName = 'WorkbenchPanel';

export { WorkbenchPanel };
