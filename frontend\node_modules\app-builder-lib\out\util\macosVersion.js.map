{"version": 3, "file": "macosVersion.js", "sourceRoot": "", "sources": ["../../src/util/macosVersion.ts"], "names": [], "mappings": ";;;AAAA,uCAAmC;AACnC,uCAA+B;AAC/B,iCAAgC;AAChC,8CAA0C;AAC1C,2BAAyC;AAEzC,MAAM,YAAY,GAAG,IAAI,eAAI,CAAS,KAAK,IAAI,EAAE;IAC/C,MAAM,IAAI,GAAG,MAAM,IAAA,mBAAQ,EAAC,kDAAkD,EAAE,MAAM,CAAC,CAAA;IACvF,MAAM,OAAO,GAAG,6DAA6D,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACxF,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAA;IACpD,CAAC;IACD,SAAG,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,eAAe,CAAC,CAAA;IACnD,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;AAC1B,CAAC,CAAC,CAAA;AAEF,SAAS,KAAK,CAAC,OAAe;IAC5B,OAAO,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC,CAAC,OAAO,CAAA;AACnE,CAAC;AAED,KAAK,UAAU,+BAA+B,CAAC,KAAa;IAC1D,OAAO,MAAM,CAAC,GAAG,CAAC,MAAM,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA;AAC3D,CAAC;AAED,SAAgB,iBAAiB;IAC/B,qBAAqB;IACrB,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAI,MAAM,CAAC,GAAG,CAAC,IAAA,YAAS,GAAE,EAAE,QAAQ,CAAC,CAAA;AAC3E,CAAC;AAHD,8CAGC;AAEM,KAAK,UAAU,aAAa;IACjC,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAC,MAAM,+BAA+B,CAAC,SAAS,CAAC,CAAC,CAAA;AAC5F,CAAC;AAFD,sCAEC;AAED,SAAgB,eAAe;IAC7B,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAI,MAAM,CAAC,GAAG,CAAC,IAAA,YAAS,GAAE,EAAE,QAAQ,CAAC,CAAA;AAC3E,CAAC;AAFD,0CAEC", "sourcesContent": ["import { readFile } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport * as semver from \"semver\"\nimport { log } from \"builder-util/out/log\"\nimport { release as osRelease } from \"os\"\n\nconst macOsVersion = new Lazy<string>(async () => {\n  const file = await readFile(\"/System/Library/CoreServices/SystemVersion.plist\", \"utf8\")\n  const matches = /<key>ProductVersion<\\/key>[\\s\\S]*<string>([\\d.]+)<\\/string>/.exec(file)\n  if (!matches) {\n    throw new Error(\"Couldn't find the macOS version\")\n  }\n  log.debug({ version: matches[1] }, \"macOS version\")\n  return clean(matches[1])\n})\n\nfunction clean(version: string) {\n  return version.split(\".\").length === 2 ? `${version}.0` : version\n}\n\nasync function isOsVersionGreaterThanOrEqualTo(input: string) {\n  return semver.gte(await macOsVersion.value, clean(input))\n}\n\nexport function isMacOsHighSierra(): boolean {\n  // 17.7.0 === 10.13.6\n  return process.platform === \"darwin\" && semver.gte(osRelease(), \"17.7.0\")\n}\n\nexport async function isMacOsSierra() {\n  return process.platform === \"darwin\" && (await isOsVersionGreaterThanOrEqualTo(\"10.12.0\"))\n}\n\nexport function isMacOsCatalina() {\n  return process.platform === \"darwin\" && semver.gte(osRelease(), \"19.0.0\")\n}\n"]}