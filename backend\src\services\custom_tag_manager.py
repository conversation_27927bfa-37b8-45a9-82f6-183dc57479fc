# src/services/custom_tag_manager.py
"""
自定义标签管理器（PostgreSQL版本）
负责管理自定义标签的创建、更新、删除和关联
使用PostgreSQL的JSONB字段存储自定义标签
"""
import logging
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import text, and_
from datetime import datetime

from ..database_manager import get_master_db
from ..models import File
from .. import schemas

logger = logging.getLogger(__name__)


class CustomTagManager:
    """自定义标签管理器（PostgreSQL版本）"""

    def __init__(self, case_id: int):
        self.case_id = case_id
        logger.info(f"初始化自定义标签管理器，案例ID: {case_id}")

    def _get_session(self) -> Session:
        """获取数据库会话"""
        return next(get_master_db())
    
    def _ensure_custom_tags_table(self, session: Session):
        """确保自定义标签表存在（PostgreSQL版本）"""
        try:
            # 检查表是否存在
            table_exists = session.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = 'custom_tags'
                )
            """)).scalar()

            if not table_exists:
                # 创建自定义标签表
                session.execute(text("""
                    CREATE TABLE custom_tags (
                        id SERIAL PRIMARY KEY,
                        case_id INTEGER NOT NULL,
                        tag_name VARCHAR(100) NOT NULL,
                        tag_color VARCHAR(7) DEFAULT '#3B82F6',
                        display_order INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(case_id, tag_name)
                    )
                """))

                # 创建索引
                session.execute(text("CREATE INDEX idx_custom_tags_case_id ON custom_tags(case_id)"))
                session.execute(text("CREATE INDEX idx_custom_tags_display_order ON custom_tags(case_id, display_order)"))

                logger.info("自定义标签表创建成功")
            else:
                # 检查是否有case_id字段
                has_case_id = session.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.columns
                        WHERE table_schema = 'public'
                        AND table_name = 'custom_tags'
                        AND column_name = 'case_id'
                    )
                """)).scalar()

                if not has_case_id:
                    # 添加case_id字段
                    session.execute(text("ALTER TABLE custom_tags ADD COLUMN case_id INTEGER NOT NULL DEFAULT 0"))
                    session.execute(text("CREATE INDEX IF NOT EXISTS idx_custom_tags_case_id ON custom_tags(case_id)"))
                    logger.info("为自定义标签表添加case_id字段")

            session.commit()

        except Exception as e:
            logger.error(f"确保自定义标签表存在失败: {e}")
            session.rollback()
            raise
    
    def create_custom_tag(self, tag_data: schemas.CustomTagCreate) -> schemas.CustomTag:
        """创建自定义标签"""
        session = self._get_session()
        try:
            self._ensure_custom_tags_table(session)

            # 检查标签名是否已存在
            existing = session.execute(text("""
                SELECT id FROM custom_tags WHERE case_id = :case_id AND tag_name = :tag_name
            """), {"case_id": self.case_id, "tag_name": tag_data.name}).fetchone()

            if existing:
                raise ValueError(f"标签名 '{tag_data.name}' 已存在")

            # 获取下一个显示顺序
            max_order = session.execute(text("""
                SELECT COALESCE(MAX(display_order), 0) FROM custom_tags WHERE case_id = :case_id
            """), {"case_id": self.case_id}).scalar()

            # 插入新标签
            result = session.execute(text("""
                INSERT INTO custom_tags (case_id, tag_name, tag_color, display_order)
                VALUES (:case_id, :tag_name, :tag_color, :display_order)
                RETURNING id, created_at, updated_at
            """), {
                "case_id": self.case_id,
                "tag_name": tag_data.name,
                "tag_color": tag_data.color or '#3B82F6',
                "display_order": max_order + 1
            })

            row = result.fetchone()
            tag_id, created_at, updated_at = row
            session.commit()

            logger.info(f"创建自定义标签成功: {tag_data.name} (ID: {tag_id})")

            return schemas.CustomTag(
                id=tag_id,
                name=tag_data.name,
                color=tag_data.color or '#3B82F6',
                display_order=max_order + 1,
                count=0,
                file_ids=[],
                created_at=created_at,
                updated_at=updated_at
            )

        except Exception as e:
            logger.error(f"创建自定义标签失败: {e}")
            session.rollback()
            raise
        finally:
            session.close()
    
    def get_custom_tags(self) -> List[schemas.CustomTag]:
        """获取所有自定义标签"""
        session = self._get_session()
        try:
            self._ensure_custom_tags_table(session)

            # 获取自定义标签及其关联的文件数量
            result = session.execute(text("""
                SELECT ct.id, ct.tag_name, ct.tag_color, ct.display_order,
                       ct.created_at, ct.updated_at,
                       COUNT(CASE WHEN f.tags->'tags'->'user' ? ct.tag_name THEN 1 END) as file_count,
                       ARRAY_AGG(CASE WHEN f.tags->'tags'->'user' ? ct.tag_name THEN f.id END)
                       FILTER (WHERE f.tags->'tags'->'user' ? ct.tag_name) as file_ids
                FROM custom_tags ct
                LEFT JOIN files f ON f.case_id = ct.case_id
                WHERE ct.case_id = :case_id
                GROUP BY ct.id, ct.tag_name, ct.tag_color, ct.display_order,
                         ct.created_at, ct.updated_at
                ORDER BY ct.display_order, ct.created_at
            """), {"case_id": self.case_id})

            custom_tags = []
            for row in result.fetchall():
                tag_id, name, color, display_order, created_at, updated_at, file_count, file_ids_array = row

                # 处理文件ID列表
                file_ids = []
                if file_ids_array:
                    try:
                        file_ids = [int(fid) for fid in file_ids_array if fid is not None]
                    except Exception as e:
                        logger.warning(f"处理文件ID列表失败: {e}")

                custom_tags.append(schemas.CustomTag(
                    id=tag_id,
                    name=name,
                    color=color,
                    display_order=display_order,
                    count=file_count or 0,
                    file_ids=file_ids,
                    created_at=created_at,
                    updated_at=updated_at
                ))

            return custom_tags

        except Exception as e:
            logger.error(f"获取自定义标签失败: {e}")
            raise
        finally:
            session.close()

    def get_custom_tag(self, tag_id: int) -> Optional[schemas.CustomTag]:
        """获取单个自定义标签"""
        session = self._get_session()
        try:
            self._ensure_custom_tags_table(session)

            result = session.execute(text("""
                SELECT ct.id, ct.tag_name, ct.tag_color, ct.display_order,
                       ct.created_at, ct.updated_at,
                       COUNT(CASE WHEN f.tags->'tags'->'user' ? ct.tag_name THEN 1 END) as file_count,
                       ARRAY_AGG(CASE WHEN f.tags->'tags'->'user' ? ct.tag_name THEN f.id END)
                       FILTER (WHERE f.tags->'tags'->'user' ? ct.tag_name) as file_ids
                FROM custom_tags ct
                LEFT JOIN files f ON f.case_id = ct.case_id
                WHERE ct.id = :tag_id AND ct.case_id = :case_id
                GROUP BY ct.id, ct.tag_name, ct.tag_color, ct.display_order,
                         ct.created_at, ct.updated_at
            """), {"tag_id": tag_id, "case_id": self.case_id}).fetchone()

            if not result:
                return None

            tag_id, name, color, display_order, created_at, updated_at, file_count, file_ids_array = result

            # 处理文件ID列表
            file_ids = []
            if file_ids_array:
                try:
                    file_ids = [int(fid) for fid in file_ids_array if fid is not None]
                except Exception as e:
                    logger.warning(f"处理文件ID列表失败: {e}")

            return schemas.CustomTag(
                id=tag_id,
                name=name,
                color=color,
                display_order=display_order,
                count=file_count or 0,
                file_ids=file_ids,
                created_at=created_at,
                updated_at=updated_at
            )

        except Exception as e:
            logger.error(f"获取自定义标签失败: {e}")
            raise
        finally:
            session.close()

    def update_custom_tag(self, tag_id: int, tag_data: schemas.CustomTagUpdate) -> Optional[schemas.CustomTag]:
        """更新自定义标签"""
        session = self._get_session()
        try:
            self._ensure_custom_tags_table(session)

            # 检查标签是否存在
            existing = session.execute(text("""
                SELECT id, tag_name FROM custom_tags
                WHERE id = :tag_id AND case_id = :case_id
            """), {"tag_id": tag_id, "case_id": self.case_id}).fetchone()

            if not existing:
                return None

            old_tag_name = existing[1]

            # 构建更新语句
            update_fields = []
            params = {"tag_id": tag_id, "case_id": self.case_id}

            if tag_data.name is not None:
                # 检查新名称是否与其他标签冲突
                name_conflict = session.execute(text("""
                    SELECT id FROM custom_tags
                    WHERE tag_name = :tag_name AND case_id = :case_id AND id != :tag_id
                """), {"tag_name": tag_data.name, "case_id": self.case_id, "tag_id": tag_id}).fetchone()

                if name_conflict:
                    raise ValueError(f"标签名 '{tag_data.name}' 已存在")

                update_fields.append("tag_name = :tag_name")
                params["tag_name"] = tag_data.name

            if tag_data.color is not None:
                update_fields.append("tag_color = :tag_color")
                params["tag_color"] = tag_data.color

            if tag_data.display_order is not None:
                update_fields.append("display_order = :display_order")
                params["display_order"] = tag_data.display_order

            if update_fields:
                update_fields.append("updated_at = CURRENT_TIMESTAMP")

                session.execute(text(f"""
                    UPDATE custom_tags
                    SET {', '.join(update_fields)}
                    WHERE id = :tag_id AND case_id = :case_id
                """), params)

                # 如果标签名称发生变化，需要更新所有文件中的标签引用
                if tag_data.name is not None and tag_data.name != old_tag_name:
                    new_tag_json = f'["{tag_data.name}"]'
                    session.execute(text("""
                        UPDATE files
                        SET tags = jsonb_set(
                            tags,
                            '{tags,user}',
                            (
                                COALESCE(tags->'tags'->'user', '[]'::jsonb) - :old_tag_name::text
                            ) || :new_tag_json::jsonb
                        )
                        WHERE case_id = :case_id
                        AND tags->'tags'->'user' ? :old_tag_name
                    """), {
                        "old_tag_name": old_tag_name,
                        "new_tag_json": new_tag_json,
                        "case_id": self.case_id
                    })

                session.commit()

            # 返回更新后的标签
            return self.get_custom_tag(tag_id)

        except Exception as e:
            logger.error(f"更新自定义标签失败: {e}")
            session.rollback()
            raise
        finally:
            session.close()
    
    def get_custom_tag(self, tag_id: int) -> Optional[schemas.CustomTag]:
        """获取单个自定义标签"""
        session = self._get_session()
        try:
            self._ensure_tables_exist(session)
            
            result = session.execute(text("""
                SELECT ct.id, ct.tag_name, ct.tag_color, ct.display_order, 
                       ct.created_at, ct.updated_at,
                       COUNT(fct.file_id) as file_count,
                       GROUP_CONCAT(fct.file_id) as file_ids
                FROM custom_tags ct
                LEFT JOIN file_custom_tags fct ON ct.id = fct.custom_tag_id
                WHERE ct.id = ?
                GROUP BY ct.id, ct.tag_name, ct.tag_color, ct.display_order, 
                         ct.created_at, ct.updated_at
            """), (tag_id,)).fetchone()
            
            if not result:
                return None
            
            tag_id, name, color, display_order, created_at, updated_at, file_count, file_ids_str = result
            
            # 处理文件ID列表
            file_ids = []
            if file_ids_str:
                try:
                    file_ids = [int(fid) for fid in file_ids_str.split(',') if fid]
                except Exception as e:
                    logger.warning(f"处理文件ID列表失败: {e}")
            
            return schemas.CustomTag(
                id=tag_id,
                name=name,
                color=color,
                display_order=display_order,
                count=file_count or 0,
                file_ids=file_ids,
                created_at=created_at,
                updated_at=updated_at
            )
            
        except Exception as e:
            logger.error(f"获取自定义标签失败: {e}")
            raise
        finally:
            session.close()
    
    def update_custom_tag(self, tag_id: int, tag_data: schemas.CustomTagUpdate) -> Optional[schemas.CustomTag]:
        """更新自定义标签"""
        session = self._get_session()
        try:
            self._ensure_tables_exist(session)
            
            # 检查标签是否存在
            existing = session.execute(text("""
                SELECT id FROM custom_tags WHERE id = ?
            """), (tag_id,)).fetchone()
            
            if not existing:
                return None
            
            # 构建更新语句
            update_fields = []
            params = []
            
            if tag_data.name is not None:
                # 检查新名称是否与其他标签冲突
                name_conflict = session.execute(text("""
                    SELECT id FROM custom_tags WHERE tag_name = ? AND id != ?
                """), (tag_data.name, tag_id)).fetchone()
                
                if name_conflict:
                    raise ValueError(f"标签名 '{tag_data.name}' 已存在")
                
                update_fields.append("tag_name = ?")
                params.append(tag_data.name)
            
            if tag_data.color is not None:
                update_fields.append("tag_color = ?")
                params.append(tag_data.color)
            
            if tag_data.display_order is not None:
                update_fields.append("display_order = ?")
                params.append(tag_data.display_order)
            
            if update_fields:
                update_fields.append("updated_at = CURRENT_TIMESTAMP")
                params.append(tag_id)
                
                session.execute(text(f"""
                    UPDATE custom_tags 
                    SET {', '.join(update_fields)}
                    WHERE id = ?
                """), params)
                session.commit()
            
            # 返回更新后的标签
            return self.get_custom_tag(tag_id)
            
        except Exception as e:
            logger.error(f"更新自定义标签失败: {e}")
            session.rollback()
            raise
        finally:
            session.close()
    
    def delete_custom_tag(self, tag_id: int) -> bool:
        """删除自定义标签并从所有文件中移除"""
        session = self._get_session()
        try:
            self._ensure_custom_tags_table(session)

            # 获取标签信息
            tag_info = session.execute(text("""
                SELECT tag_name FROM custom_tags
                WHERE id = :tag_id AND case_id = :case_id
            """), {"tag_id": tag_id, "case_id": self.case_id}).fetchone()

            if not tag_info:
                return False

            tag_name = tag_info[0]

            # 从所有文件的JSONB中移除该标签
            session.execute(text("""
                UPDATE files
                SET tags = jsonb_set(
                    tags,
                    '{tags,user}',
                    COALESCE(tags->'tags'->'user', '[]'::jsonb) - :tag_name
                )
                WHERE case_id = :case_id
                AND tags->'tags'->'user' ? :tag_name
            """), {"tag_name": tag_name, "case_id": self.case_id})

            # 删除标签定义
            result = session.execute(text("""
                DELETE FROM custom_tags
                WHERE id = :tag_id AND case_id = :case_id
            """), {"tag_id": tag_id, "case_id": self.case_id})

            session.commit()

            if result.rowcount > 0:
                logger.info(f"自定义标签 '{tag_name}' (ID: {tag_id}) 已删除，并从所有文件中移除")
                return True
            else:
                return False

        except Exception as e:
            logger.error(f"删除自定义标签失败: {e}")
            session.rollback()
            raise
        finally:
            session.close()
    
    def add_tags_to_file(self, file_id: int, tag_names: List[str]) -> Dict[str, Any]:
        """为文件添加自定义标签"""
        session = self._get_session()
        try:
            self._ensure_custom_tags_table(session)

            # 检查文件是否存在
            file_exists = session.execute(text("""
                SELECT id FROM files WHERE id = :file_id AND case_id = :case_id
            """), {"file_id": file_id, "case_id": self.case_id}).fetchone()

            if not file_exists:
                raise ValueError(f"文件 {file_id} 不存在")

            added_tags = []
            skipped_tags = []

            for tag_name in tag_names:
                # 检查标签是否存在
                tag_exists = session.execute(text("""
                    SELECT id FROM custom_tags
                    WHERE tag_name = :tag_name AND case_id = :case_id
                """), {"tag_name": tag_name, "case_id": self.case_id}).fetchone()

                if not tag_exists:
                    skipped_tags.append({"tag_name": tag_name, "reason": "标签不存在"})
                    continue

                # 检查文件是否已经有该标签 - 统一转换为JSONB处理
                has_tag = session.execute(text("""
                    SELECT 1 FROM files
                    WHERE id = :file_id
                    AND (tags::jsonb)->'tags'->'user' ? :tag_name
                """), {"file_id": file_id, "tag_name": tag_name}).fetchone()

                if has_tag:
                    skipped_tags.append({"tag_name": tag_name, "reason": "已经关联"})
                    continue

                # 添加标签到文件的JSONB字段 - 统一转换为JSONB处理
                session.execute(text("""
                    UPDATE files
                    SET tags = jsonb_set(
                        tags::jsonb,
                        '{tags,user}',
                        COALESCE((tags::jsonb)->'tags'->'user', '[]'::jsonb) || jsonb_build_array(:tag_name)
                    )
                    WHERE id = :file_id
                """), {"file_id": file_id, "tag_name": tag_name})

                added_tags.append(tag_name)

            session.commit()

            return {
                "added_tags": added_tags,
                "skipped_tags": skipped_tags,
                "success": len(added_tags) > 0
            }

        except Exception as e:
            logger.error(f"为文件添加自定义标签失败: {e}")
            session.rollback()
            raise
        finally:
            session.close()
    
    def remove_tag_from_file(self, file_id: int, tag_name: str) -> bool:
        """从文件移除自定义标签"""
        session = self._get_session()
        try:
            # 从文件的JSONB字段中移除标签 - 统一转换为JSONB处理
            result = session.execute(text("""
                UPDATE files
                SET tags = jsonb_set(
                    tags::jsonb,
                    '{tags,user}',
                    COALESCE((tags::jsonb)->'tags'->'user', '[]'::jsonb) - :tag_name
                )
                WHERE id = :file_id
                AND case_id = :case_id
                AND (tags::jsonb)->'tags'->'user' ? :tag_name
            """), {"file_id": file_id, "tag_name": tag_name, "case_id": self.case_id})

            session.commit()

            return result.rowcount > 0

        except Exception as e:
            logger.error(f"从文件移除自定义标签失败: {e}")
            session.rollback()
            raise
        finally:
            session.close()
    
    def batch_tag_operation(self, file_ids: List[int], tag_name: str, action: str) -> Dict[str, Any]:
        """批量标签操作"""
        session = self._get_session()
        try:
            self._ensure_custom_tags_table(session)

            # 检查标签是否存在
            tag_exists = session.execute(text("""
                SELECT id FROM custom_tags
                WHERE tag_name = :tag_name AND case_id = :case_id
            """), {"tag_name": tag_name, "case_id": self.case_id}).fetchone()

            if not tag_exists:
                raise ValueError(f"标签 '{tag_name}' 不存在")

            success_files = []
            failed_files = []

            for file_id in file_ids:
                try:
                    # 检查文件是否存在
                    file_exists = session.execute(text("""
                        SELECT id FROM files
                        WHERE id = :file_id AND case_id = :case_id
                    """), {"file_id": file_id, "case_id": self.case_id}).fetchone()

                    if not file_exists:
                        failed_files.append({"file_id": file_id, "reason": "文件不存在"})
                        continue

                    if action == "add":
                        # 检查是否已经有该标签 - 统一转换为JSONB处理
                        has_tag = session.execute(text("""
                            SELECT 1 FROM files
                            WHERE id = :file_id
                            AND (tags::jsonb)->'tags'->'user' ? :tag_name
                        """), {"file_id": file_id, "tag_name": tag_name}).fetchone()

                        if not has_tag:
                            session.execute(text(f"""
                                UPDATE files
                                SET tags = jsonb_set(
                                    COALESCE(tags, '{{"properties": {{}}, "tags": {{"metadata": {{}}, "cv": {{}}, "user": [], "ai": []}}}}'::jsonb),
                                    '{{tags,user}}',
                                    COALESCE(tags->'tags'->'user', '[]'::jsonb) || '["{tag_name}"]'::jsonb
                                )
                                WHERE id = :file_id
                            """), {"file_id": file_id})
                            success_files.append(file_id)
                        else:
                            failed_files.append({"file_id": file_id, "reason": "已经关联"})

                    elif action == "remove":
                        result = session.execute(text("""
                            UPDATE files
                            SET tags = jsonb_set(
                                tags,
                                '{tags,user}',
                                COALESCE(tags->'tags'->'user', '[]'::jsonb) - :tag_name
                            )
                            WHERE id = :file_id
                            AND tags->'tags'->'user' ? :tag_name
                        """), {"file_id": file_id, "tag_name": tag_name})

                        if result.rowcount > 0:
                            success_files.append(file_id)
                        else:
                            failed_files.append({"file_id": file_id, "reason": "未关联"})

                    else:
                        failed_files.append({"file_id": file_id, "reason": "无效操作"})

                except Exception as e:
                    failed_files.append({"file_id": file_id, "reason": str(e)})

            session.commit()

            return {
                "action": action,
                "tag_name": tag_name,
                "success_files": success_files,
                "failed_files": failed_files,
                "success_count": len(success_files),
                "failed_count": len(failed_files)
            }

        except Exception as e:
            logger.error(f"批量标签操作失败: {e}")
            session.rollback()
            raise
        finally:
            session.close()
    
    def get_file_custom_tags(self, file_id: int) -> List[schemas.CustomTag]:
        """获取文件的自定义标签"""
        session = self._get_session()
        try:
            self._ensure_custom_tags_table(session)

            # 获取文件的用户标签 - 统一转换为JSONB处理
            file_tags = session.execute(text("""
                SELECT (tags::jsonb)->'tags'->'user' as user_tags
                FROM files
                WHERE id = :file_id AND case_id = :case_id
            """), {"file_id": file_id, "case_id": self.case_id}).fetchone()

            if not file_tags or not file_tags[0]:
                return []

            user_tags = file_tags[0]  # 这是一个JSONB数组

            # 获取对应的自定义标签定义
            if isinstance(user_tags, list) and len(user_tags) > 0:
                # 如果user_tags是Python列表，直接使用ANY操作符
                result = session.execute(text("""
                    SELECT ct.id, ct.tag_name, ct.tag_color, ct.display_order,
                           ct.created_at, ct.updated_at
                    FROM custom_tags ct
                    WHERE ct.case_id = :case_id
                    AND ct.tag_name = ANY(:user_tags)
                    ORDER BY ct.display_order, ct.created_at
                """), {"case_id": self.case_id, "user_tags": user_tags})
            else:
                # 如果user_tags是JSONB，使用jsonb_array_elements_text
                result = session.execute(text("""
                    SELECT ct.id, ct.tag_name, ct.tag_color, ct.display_order,
                           ct.created_at, ct.updated_at
                    FROM custom_tags ct
                    WHERE ct.case_id = :case_id
                    AND ct.tag_name = ANY(
                        SELECT jsonb_array_elements_text(:user_tags)
                    )
                    ORDER BY ct.display_order, ct.created_at
                """), {"case_id": self.case_id, "user_tags": user_tags})

            custom_tags = []
            for row in result.fetchall():
                tag_id, name, color, display_order, created_at, updated_at = row

                custom_tags.append(schemas.CustomTag(
                    id=tag_id,
                    name=name,
                    color=color,
                    display_order=display_order,
                    count=0,  # 在这个上下文中不需要计算count
                    file_ids=[],
                    created_at=created_at,
                    updated_at=updated_at
                ))

            return custom_tags

        except Exception as e:
            logger.error(f"获取文件自定义标签失败: {e}")
            raise
        finally:
            session.close()