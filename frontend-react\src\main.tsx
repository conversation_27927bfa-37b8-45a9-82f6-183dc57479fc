import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import './index.css'
import App from './App.tsx'
import MizzyStarApp from './MizzyStarApp.tsx'
import TestApp from './TestApp.tsx'
import MinimalMizzyStarApp from './MinimalMizzyStarApp.tsx'
import CSSTestApp from './CSSTestApp.tsx'

// 启动 Mock Service Worker
import { startMSW } from './mocks/browser'

// 创建 QueryClient 实例
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // 默认查询配置
      staleTime: 5 * 60 * 1000, // 5分钟
      gcTime: 10 * 60 * 1000, // 10分钟 (原 cacheTime)
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      // 默认变更配置
      retry: 1,
    },
  },
})

// 启动 MSW 并渲染应用
async function enableMocking() {
  // 只在开发环境启动 MSW
  if (import.meta.env.DEV) {
    await startMSW();
  }

  return;
}

enableMocking().then(() => {
  createRoot(document.getElementById('root')!).render(
    <StrictMode>
      <QueryClientProvider client={queryClient}>
        <MinimalMizzyStarApp />
        <ReactQueryDevtools initialIsOpen={false} />
      </QueryClientProvider>
    </StrictMode>,
  );
});
