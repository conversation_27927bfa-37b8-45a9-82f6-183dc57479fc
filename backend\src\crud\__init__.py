# src/crud/__init__.py
"""
CRUD 模块：按功能分离的数据库操作
"""

from .case_crud import (
    create_case,
    get_cases,
    get_case,
    get_case_basic,
    delete_case,
    update_case
)

from .trash_crud import (
    get_trash_cases,
    get_trash_case,
    restore_case,
    permanently_delete_case
)

from .file_crud import (
    create_file_for_case,
    get_files_for_case,
    get_file,
    delete_file
)

__all__ = [
    # 案例操作
    "create_case",
    "get_cases",
    "get_case",
    "get_case_basic",
    "delete_case",

    # 回收站操作
    "get_trash_cases",
    "get_trash_case",
    "restore_case",
    "permanently_delete_case",

    # 文件操作
    "create_file_for_case",
    "get_files_for_case",
    "get_file",
    "delete_file"
]