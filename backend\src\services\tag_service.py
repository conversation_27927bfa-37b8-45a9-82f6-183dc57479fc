# src/services/tag_service.py
"""
标签服务层 - 统一标签系统API
提供标签相关的业务逻辑和数据访问功能
"""

import logging
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import text, and_, func
from datetime import datetime

from ..database import get_master_db
from ..crud.case_crud import get_case
from .. import models, schemas

logger = logging.getLogger(__name__)


class TagService:
    """
    标签服务 - 统一处理标签相关的业务逻辑
    
    提供标签树获取、标签搜索、文件标签管理等功能
    整合了原有的tags.py和custom_tags.py中的功能
    """
    
    def __init__(self, case_id: int, db: Session = None):
        """
        初始化标签服务
        
        Args:
            case_id: 案例ID
            db: 数据库会话，如果为None则自动创建
        """
        self.case_id = case_id
        self._db = db
        logger.info(f"初始化标签服务，案例ID: {case_id}")
    
    @property
    def db(self) -> Session:
        """
        获取数据库会话
        
        如果初始化时没有提供db，则自动创建一个新的会话
        """
        if self._db is None:
            self._db = next(get_master_db())
        return self._db
    
    def get_tag_tree(self, include_empty: bool = False) -> Dict[str, Any]:
        """
        获取优化的标签树结构
        
        使用数据库聚合查询直接获取标签统计信息，避免客户端计算
        
        Args:
            include_empty: 是否包含空标签
            
        Returns:
            按类别组织的标签树结构
        """
        try:
            # 验证案例存在
            case = get_case(self.db, self.case_id)
            if not case:
                raise ValueError("案例不存在")
                
            logger.info(f"🚀 开始获取案例 {self.case_id} 的优化标签树")
            
            # 使用SQL聚合查询获取标签统计信息
            tag_tree = {
                "tags": {
                    "metadata": {},
                    "cv": {},
                    "user": {},
                    "ai": {}
                },
                "properties": {},
                "custom": []  # 自定义标签将在后面单独处理
            }
            
            # 1. 获取元数据标签统计
            metadata_stats = self._get_metadata_tag_stats()
            tag_tree["tags"]["metadata"] = metadata_stats
            
            # 2. 获取CV标签统计
            cv_stats = self._get_cv_tag_stats()
            tag_tree["tags"]["cv"] = cv_stats
            
            # 3. 获取质量分数统计
            quality_stats = self._get_quality_stats()
            tag_tree["properties"] = quality_stats
            
            # 4. 获取用户标签统计
            user_stats = self._get_user_tag_stats()
            tag_tree["tags"]["user"] = user_stats
            
            # 5. 获取AI标签统计
            ai_stats = self._get_ai_tag_stats()
            tag_tree["tags"]["ai"] = ai_stats
            
            # 6. 获取自定义标签
            try:
                custom_tags = self.get_custom_tags()
                tag_tree["custom"] = custom_tags
            except Exception as e:
                logger.warning(f"获取自定义标签失败: {e}")
                tag_tree["custom"] = []
            
            logger.info(f"✅ 优化标签树获取完成，案例 {self.case_id}")
            return tag_tree
            
        except Exception as e:
            logger.error(f"获取优化标签树失败: {e}")
            raise
    
    def _get_metadata_tag_stats(self) -> Dict[str, Dict[str, int]]:
        """
        获取元数据标签统计
        
        Returns:
            元数据标签统计信息
        """
        try:
            from sqlalchemy import text

            # 使用SQL聚合查询获取元数据标签统计
            result = self.db.execute(text("""
                WITH metadata_tags AS (
                    SELECT
                        f.id as file_id,
                        jsonb_each_text(f.tags->'tags'->'metadata') as tag_pair
                    FROM files f
                    WHERE f.case_id = :case_id
                    AND f.tags->'tags'->'metadata' IS NOT NULL
                    AND jsonb_typeof(f.tags->'tags'->'metadata') = 'object'
                )
                SELECT
                    (tag_pair).key as tag_name,
                    (tag_pair).value as tag_value,
                    COUNT(*) as file_count,
                    ARRAY_AGG(file_id) as file_ids
                FROM metadata_tags
                GROUP BY (tag_pair).key, (tag_pair).value
                ORDER BY tag_name, file_count DESC
            """), {"case_id": self.case_id})

            metadata_stats = {}
            for row in result:
                tag_name = row.tag_name
                tag_value = row.tag_value
                file_count = row.file_count
                file_ids = row.file_ids

                if tag_name not in metadata_stats:
                    metadata_stats[tag_name] = {}

                metadata_stats[tag_name][tag_value] = {
                    "count": file_count,
                    "file_ids": file_ids
                }

            return metadata_stats

        except Exception as e:
            logger.warning(f"获取元数据标签统计失败: {e}")
            return {}
    
    def _get_cv_tag_stats(self) -> Dict[str, Dict[str, int]]:
        """
        获取CV标签统计
        
        Returns:
            CV标签统计信息
        """
        try:
            from sqlalchemy import text

            # 获取CV标签统计
            result = self.db.execute(text("""
                WITH cv_tags AS (
                    SELECT
                        f.id as file_id,
                        jsonb_each_text(f.tags->'tags'->'cv') as tag_pair
                    FROM files f
                    WHERE f.case_id = :case_id
                    AND f.tags->'tags'->'cv' IS NOT NULL
                    AND jsonb_typeof(f.tags->'tags'->'cv') = 'object'
                )
                SELECT
                    (tag_pair).key as tag_name,
                    (tag_pair).value as tag_value,
                    COUNT(*) as file_count,
                    ARRAY_AGG(file_id) as file_ids
                FROM cv_tags
                GROUP BY (tag_pair).key, (tag_pair).value
                ORDER BY tag_name, file_count DESC
            """), {"case_id": self.case_id})

            cv_stats = {}
            for row in result:
                tag_name = row.tag_name
                tag_value = row.tag_value
                file_count = row.file_count
                file_ids = row.file_ids

                if tag_name not in cv_stats:
                    cv_stats[tag_name] = {}

                cv_stats[tag_name][tag_value] = {
                    "count": file_count,
                    "file_ids": file_ids
                }

            return cv_stats

        except Exception as e:
            logger.warning(f"获取CV标签统计失败: {e}")
            return {}
    
    def _get_quality_stats(self) -> Dict[str, Dict[str, int]]:
        """
        获取质量分数统计
        
        Returns:
            质量分数统计信息
        """
        try:
            from sqlalchemy import text

            # 获取质量分数统计
            result = self.db.execute(text("""
                WITH quality_scores AS (
                    SELECT
                        f.id as file_id,
                        (f.tags->'properties'->>'qualityScore')::int as quality_score
                    FROM files f
                    WHERE f.case_id = :case_id
                    AND f.tags->'properties'->>'qualityScore' IS NOT NULL
                    AND f.tags->'properties'->>'qualityScore' ~ '^[0-9]+$'
                ),
                quality_ranges AS (
                    SELECT
                        file_id,
                        quality_score,
                        CASE
                            WHEN quality_score >= 90 THEN '优秀 (90-100)'
                            WHEN quality_score >= 80 THEN '良好 (80-89)'
                            WHEN quality_score >= 70 THEN '一般 (70-79)'
                            WHEN quality_score >= 60 THEN '较差 (60-69)'
                            ELSE '很差 (<60)'
                        END as quality_range
                    FROM quality_scores
                )
                SELECT
                    quality_range,
                    COUNT(*) as file_count,
                    ARRAY_AGG(file_id) as file_ids,
                    MIN(quality_score) as min_score,
                    MAX(quality_score) as max_score,
                    AVG(quality_score)::int as avg_score
                FROM quality_ranges
                GROUP BY quality_range
                ORDER BY min_score DESC
            """), {"case_id": self.case_id})

            quality_stats = {}
            for row in result:
                quality_range = row.quality_range
                file_count = row.file_count
                file_ids = row.file_ids

                quality_stats[quality_range] = {
                    "count": file_count,
                    "file_ids": file_ids,
                    "min_score": row.min_score,
                    "max_score": row.max_score,
                    "avg_score": row.avg_score
                }

            return quality_stats

        except Exception as e:
            logger.warning(f"获取质量统计失败: {e}")
            return {}
    
    def _get_user_tag_stats(self) -> Dict[str, Dict[str, int]]:
        """
        获取用户标签统计
        
        Returns:
            用户标签统计信息
        """
        try:
            from sqlalchemy import text

            # 获取用户标签统计
            result = self.db.execute(text("""
                WITH user_tags AS (
                    SELECT
                        f.id as file_id,
                        jsonb_array_elements_text(f.tags->'tags'->'user') as tag_name
                    FROM files f
                    WHERE f.case_id = :case_id
                    AND f.tags->'tags'->'user' IS NOT NULL
                    AND jsonb_typeof(f.tags->'tags'->'user') = 'array'
                )
                SELECT
                    tag_name,
                    COUNT(*) as file_count,
                    ARRAY_AGG(file_id) as file_ids
                FROM user_tags
                GROUP BY tag_name
                ORDER BY file_count DESC
            """), {"case_id": self.case_id})

            user_stats = {}
            for row in result:
                tag_name = row.tag_name
                file_count = row.file_count
                file_ids = row.file_ids

                user_stats[tag_name] = {
                    "count": file_count,
                    "file_ids": file_ids
                }

            return user_stats

        except Exception as e:
            logger.warning(f"获取用户标签统计失败: {e}")
            return {}
    
    def _get_ai_tag_stats(self) -> Dict[str, Dict[str, int]]:
        """
        获取AI标签统计
        
        Returns:
            AI标签统计信息
        """
        try:
            from sqlalchemy import text

            # 获取AI标签统计
            result = self.db.execute(text("""
                WITH ai_tags AS (
                    SELECT
                        f.id as file_id,
                        jsonb_array_elements_text(f.tags->'tags'->'ai') as tag_name
                    FROM files f
                    WHERE f.case_id = :case_id
                    AND f.tags->'tags'->'ai' IS NOT NULL
                    AND jsonb_typeof(f.tags->'tags'->'ai') = 'array'
                )
                SELECT
                    tag_name,
                    COUNT(*) as file_count,
                    ARRAY_AGG(file_id) as file_ids
                FROM ai_tags
                GROUP BY tag_name
                ORDER BY file_count DESC
            """), {"case_id": self.case_id})

            ai_stats = {}
            for row in result:
                tag_name = row.tag_name
                file_count = row.file_count
                file_ids = row.file_ids

                ai_stats[tag_name] = {
                    "count": file_count,
                    "file_ids": file_ids
                }

            return ai_stats

        except Exception as e:
            logger.warning(f"获取AI标签统计失败: {e}")
            return {}
    
    # ==================== 自定义标签相关方法 ====================
    
    def get_custom_tags(self) -> List[Dict[str, Any]]:
        """
        获取案例的所有自定义标签
        
        Returns:
            自定义标签列表
        """
        try:
            from ..services.custom_tag_manager import CustomTagManager
            
            # 使用CustomTagManager获取自定义标签
            custom_tag_manager = CustomTagManager(self.case_id, self.db)
            custom_tags = custom_tag_manager.get_custom_tags()
            
            # 转换为字典列表返回
            return [tag.to_dict() for tag in custom_tags]
        except Exception as e:
            logger.warning(f"获取自定义标签失败: {e}")
            return []
    
    def create_custom_tag(self, tag_data: schemas.CustomTagCreate) -> Dict[str, Any]:
        """
        创建自定义标签
        
        Args:
            tag_data: 标签数据
            
        Returns:
            创建的标签信息
        """
        try:
            from ..services.custom_tag_manager import CustomTagManager
            
            # 使用CustomTagManager创建自定义标签
            custom_tag_manager = CustomTagManager(self.case_id, self.db)
            custom_tag = custom_tag_manager.create_custom_tag(
                name=tag_data.name,
                color=tag_data.color,
                display_order=tag_data.display_order if hasattr(tag_data, 'display_order') else None
            )
            
            # 转换为字典返回
            return custom_tag.to_dict() if custom_tag else {}
        except Exception as e:
            logger.warning(f"创建自定义标签失败: {e}")
            return {}
    
    def update_custom_tag(self, tag_id: int, tag_data: schemas.CustomTagUpdate) -> Dict[str, Any]:
        """
        更新自定义标签
        
        Args:
            tag_id: 标签ID
            tag_data: 标签更新数据
            
        Returns:
            更新后的标签信息
        """
        try:
            from ..services.custom_tag_manager import CustomTagManager
            
            # 使用CustomTagManager更新自定义标签
            custom_tag_manager = CustomTagManager(self.case_id, self.db)
            custom_tag = custom_tag_manager.update_custom_tag(
                tag_id=tag_id,
                name=tag_data.name,
                color=tag_data.color,
                display_order=tag_data.display_order if hasattr(tag_data, 'display_order') else None
            )
            
            # 转换为字典返回
            return custom_tag.to_dict() if custom_tag else {}
        except Exception as e:
            logger.warning(f"更新自定义标签失败: {e}")
            return {}
    
    def delete_custom_tag(self, tag_id: int) -> bool:
        """
        删除自定义标签
        
        Args:
            tag_id: 标签ID
            
        Returns:
            是否删除成功
        """
        try:
            from ..services.custom_tag_manager import CustomTagManager
            
            # 使用CustomTagManager删除自定义标签
            custom_tag_manager = CustomTagManager(self.case_id, self.db)
            result = custom_tag_manager.delete_custom_tag(tag_id=tag_id)
            
            return result
        except Exception as e:
            logger.warning(f"删除自定义标签失败: {e}")
            return False
    
    def add_tags_to_file(self, file_id: int, tag_names: List[str]) -> Dict[str, Any]:
        """
        为文件添加标签
        
        Args:
            file_id: 文件ID
            tag_names: 标签名称列表
            
        Returns:
            添加结果
        """
        try:
            from ..services.custom_tag_manager import CustomTagManager
            
            # 使用CustomTagManager为文件添加标签
            custom_tag_manager = CustomTagManager(self.case_id, self.db)
            result = custom_tag_manager.add_tags_to_file(file_id=file_id, tag_names=tag_names)
            
            return {
                "success": True,
                "added": result.get("added", []),
                "skipped": result.get("skipped", [])
            }
        except Exception as e:
            logger.warning(f"为文件添加标签失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "added": [],
                "skipped": []
            }
    
    def remove_tag_from_file(self, file_id: int, tag_name: str) -> bool:
        """
        从文件中移除标签 (按标签名称)

        Args:
            file_id: 文件ID
            tag_name: 标签名称

        Returns:
            是否移除成功
        """
        try:
            from ..services.custom_tag_manager import CustomTagManager

            # 使用CustomTagManager从文件中移除标签
            custom_tag_manager = CustomTagManager(self.case_id, self.db)
            result = custom_tag_manager.remove_tag_from_file(file_id=file_id, tag_name=tag_name)

            return result
        except Exception as e:
            logger.warning(f"从文件中移除标签失败: {e}")
            return False

    def remove_tag_from_file_by_id(self, file_id: int, tag_id: int) -> bool:
        """
        从文件中移除标签 (按标签ID)

        Args:
            file_id: 文件ID
            tag_id: 标签ID

        Returns:
            是否移除成功
        """
        try:
            # 直接操作file_custom_tags表
            from .. import models

            # 查找并删除关联记录
            association = self.db.query(models.FileCustomTags).filter(
                models.FileCustomTags.file_id == file_id,
                models.FileCustomTags.custom_tag_id == tag_id
            ).first()

            if association:
                self.db.delete(association)
                self.db.commit()
                logger.info(f"成功从文件 {file_id} 移除标签 {tag_id}")
                return True
            else:
                logger.warning(f"未找到文件 {file_id} 和标签 {tag_id} 的关联")
                return False

        except Exception as e:
            logger.warning(f"从文件中移除标签失败: {e}")
            self.db.rollback()
            return False
    
    def batch_tag_operation(self, file_ids: List[int], tag_name: str, action: str) -> Dict[str, Any]:
        """
        批量标签操作
        
        Args:
            file_ids: 文件ID列表
            tag_name: 标签名称
            action: 操作类型 (add/remove)
            
        Returns:
            操作结果
        """
        try:
            from ..services.custom_tag_manager import CustomTagManager
            
            # 使用CustomTagManager进行批量标签操作
            custom_tag_manager = CustomTagManager(self.case_id, self.db)
            result = custom_tag_manager.batch_tag_operation(
                file_ids=file_ids,
                tag_name=tag_name,
                action=action
            )
            
            return {
                "success": True,
                "successful_files": result.get("successful_files", []),
                "failed_files": result.get("failed_files", [])
            }
        except Exception as e:
            logger.warning(f"批量标签操作失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "successful_files": [],
                "failed_files": file_ids
            }
    
    def get_file_custom_tags(self, file_id: int) -> List[Dict[str, Any]]:
        """
        获取文件的自定义标签
        
        Args:
            file_id: 文件ID
            
        Returns:
            自定义标签列表
        """
        try:
            from ..services.custom_tag_manager import CustomTagManager
            
            # 使用CustomTagManager获取文件的自定义标签
            custom_tag_manager = CustomTagManager(self.case_id, self.db)
            custom_tags = custom_tag_manager.get_file_custom_tags(file_id=file_id)
            
            # 转换为字典列表返回
            return [tag.to_dict() for tag in custom_tags]
        except Exception as e:
            logger.warning(f"获取文件自定义标签失败: {e}")
            return []
    
    # ==================== 标签搜索相关方法 ====================
    
    def search_tags(self, query: str, limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索标签
        
        Args:
            query: 搜索关键词
            limit: 返回结果数量限制，默认100
            
        Returns:
            匹配的标签列表
        """
        try:
            from sqlalchemy import text
            import re
            
            # 清理查询字符串，移除特殊字符
            clean_query = re.sub(r'[^\w\s]', '', query).strip().lower()
            if not clean_query:
                return []
            
            # 构建模糊匹配模式
            pattern = f"%{clean_query}%"
            
            # 搜索自定义标签
            custom_tags_result = self.db.execute(text("""
                SELECT id, name, color, display_order, created_at, updated_at
                FROM custom_tags
                WHERE case_id = :case_id AND LOWER(name) LIKE :pattern
                ORDER BY display_order
                LIMIT :limit
            """), {"case_id": self.case_id, "pattern": pattern, "limit": limit})
            
            # 处理结果
            results = []
            for row in custom_tags_result:
                results.append({
                    "id": row.id,
                    "name": row.name,
                    "color": row.color,
                    "display_order": row.display_order,
                    "type": "custom",
                    "created_at": row.created_at.isoformat() if row.created_at else None,
                    "updated_at": row.updated_at.isoformat() if row.updated_at else None
                })
            
            # 搜索元数据标签
            metadata_tags = self._get_metadata_tag_stats()
            for tag_name, values in metadata_tags.items():
                if clean_query in tag_name.lower():
                    for value, stats in values.items():
                        results.append({
                            "id": None,
                            "name": f"{tag_name}: {value}",
                            "color": "#808080",  # 默认灰色
                            "display_order": 9999,
                            "type": "metadata",
                            "file_count": stats.get("count", 0),
                            "file_ids": stats.get("file_ids", [])
                        })
            
            # 搜索CV标签
            cv_tags = self._get_cv_tag_stats()
            for tag_name, values in cv_tags.items():
                if clean_query in tag_name.lower():
                    for value, stats in values.items():
                        results.append({
                            "id": None,
                            "name": f"{tag_name}: {value}",
                            "color": "#4A90E2",  # 默认蓝色
                            "display_order": 9999,
                            "type": "cv",
                            "file_count": stats.get("count", 0),
                            "file_ids": stats.get("file_ids", [])
                        })
            
            # 搜索AI标签
            ai_tags = self._get_ai_tag_stats()
            for tag_name, stats in ai_tags.items():
                if clean_query in tag_name.lower():
                    results.append({
                        "id": None,
                        "name": tag_name,
                        "color": "#9B59B6",  # 默认紫色
                        "display_order": 9999,
                        "type": "ai",
                        "file_count": stats.get("count", 0),
                        "file_ids": stats.get("file_ids", [])
                    })
            
            # 搜索用户标签
            user_tags = self._get_user_tag_stats()
            for tag_name, stats in user_tags.items():
                if clean_query in tag_name.lower():
                    results.append({
                        "id": None,
                        "name": tag_name,
                        "color": "#2ECC71",  # 默认绿色
                        "display_order": 9999,
                        "type": "user",
                        "file_count": stats.get("count", 0),
                        "file_ids": stats.get("file_ids", [])
                    })
            
            # 按标签名称排序并限制结果数量
            results.sort(key=lambda x: x["name"])
            return results[:limit]
            
        except Exception as e:
            logger.warning(f"搜索标签失败: {e}")
            return []
    
    def get_files_by_tag(self, category: str, name: str, value: str = None) -> List[Dict[str, Any]]:
        """
        根据标签获取文件
        
        Args:
            category: 标签类别 (metadata/cv/user/ai/custom)
            name: 标签名称
            value: 标签值 (对于metadata和cv标签)
            
        Returns:
            文件列表
        """
        try:
            from sqlalchemy import text
            
            file_ids = []
            
            if category == "metadata":
                # 获取带有指定元数据标签的文件
                if not value:
                    return []
                
                result = self.db.execute(text("""
                    SELECT f.id
                    FROM files f
                    WHERE f.case_id = :case_id
                    AND f.tags->'tags'->'metadata'->:name = :value::jsonb
                """), {"case_id": self.case_id, "name": name, "value": f'"{value}"'})
                
                file_ids = [row.id for row in result]
                
            elif category == "cv":
                # 获取带有指定CV标签的文件
                if not value:
                    return []
                
                result = self.db.execute(text("""
                    SELECT f.id
                    FROM files f
                    WHERE f.case_id = :case_id
                    AND f.tags->'tags'->'cv'->:name = :value::jsonb
                """), {"case_id": self.case_id, "name": name, "value": f'"{value}"'})
                
                file_ids = [row.id for row in result]
                
            elif category == "user":
                # 获取带有指定用户标签的文件
                result = self.db.execute(text("""
                    SELECT f.id
                    FROM files f
                    WHERE f.case_id = :case_id
                    AND f.tags->'tags'->'user' ? :name
                """), {"case_id": self.case_id, "name": name})
                
                file_ids = [row.id for row in result]
                
            elif category == "ai":
                # 获取带有指定AI标签的文件
                result = self.db.execute(text("""
                    SELECT f.id
                    FROM files f
                    WHERE f.case_id = :case_id
                    AND f.tags->'tags'->'ai' ? :name
                """), {"case_id": self.case_id, "name": name})
                
                file_ids = [row.id for row in result]
                
            elif category == "custom":
                # 获取带有指定自定义标签的文件
                result = self.db.execute(text("""
                    SELECT f.id
                    FROM files f
                    WHERE f.case_id = :case_id
                    AND f.tags->'tags'->'user' ? :name
                """), {"case_id": self.case_id, "name": name})
                
                file_ids = [row.id for row in result]
            
            # 如果没有找到文件，返回空列表
            if not file_ids:
                return []
            
            # 获取文件详细信息
            files_result = self.db.execute(text("""
                SELECT 
                    f.id, 
                    f.filename, 
                    f.filepath, 
                    f.filesize, 
                    f.filetype, 
                    f.thumbnail_path,
                    f.created_at,
                    f.updated_at,
                    f.tags
                FROM files f
                WHERE f.id IN :file_ids
                ORDER BY f.filename
            """), {"file_ids": tuple(file_ids) if len(file_ids) > 1 else f"({file_ids[0]})"})  # 处理单个ID的情况
            
            files = []
            for row in files_result:
                files.append({
                    "id": row.id,
                    "filename": row.filename,
                    "filepath": row.filepath,
                    "filesize": row.filesize,
                    "filetype": row.filetype,
                    "thumbnail_path": row.thumbnail_path,
                    "created_at": row.created_at.isoformat() if row.created_at else None,
                    "updated_at": row.updated_at.isoformat() if row.updated_at else None,
                    "tags": row.tags
                })
            
            return files
            
        except Exception as e:
            logger.warning(f"根据标签获取文件失败: {e}")
            return []