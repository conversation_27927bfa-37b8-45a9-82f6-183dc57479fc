{"version": 3, "file": "metadata.js", "sourceRoot": "", "sources": ["../../src/options/metadata.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Configuration } from \"../configuration\"\n\nexport interface Metadata {\n  /**\n   * The application name.\n   * @required\n   */\n  readonly name?: string\n\n  /**\n   * The application description.\n   */\n  readonly description?: string\n\n  /**\n   * The url to the project [homepage](https://docs.npmjs.com/files/package.json#homepage) (NuGet Package `projectUrl` (optional) or Linux Package URL (required)).\n   *\n   * If not specified and your project repository is public on GitHub, it will be `https://github.com/${user}/${project}` by default.\n   */\n  readonly homepage?: string | null\n\n  /**\n   * *linux-only.* The [license](https://docs.npmjs.com/files/package.json#license) name.\n   */\n  readonly license?: string | null\n\n  readonly author?: AuthorMetadata | null\n\n  /**\n   * The [repository](https://docs.npmjs.com/files/package.json#repository).\n   */\n  readonly repository?: string | RepositoryInfo | null\n\n  /**\n   * The electron-builder configuration.\n   */\n  readonly build?: Configuration\n\n  /** @private */\n  readonly dependencies?: { [key: string]: string }\n  /** @private */\n  readonly version?: string\n  /** @private */\n  readonly type?: string\n  /** @private */\n  readonly shortVersion?: string | null\n  /** @private */\n  readonly shortVersionWindows?: string | null\n  /** @private */\n  readonly productName?: string | null\n  /** @private */\n  readonly main?: string | null\n}\n\nexport interface AuthorMetadata {\n  readonly name: string\n  readonly email?: string\n}\n\nexport interface RepositoryInfo {\n  readonly url: string\n}\n"]}