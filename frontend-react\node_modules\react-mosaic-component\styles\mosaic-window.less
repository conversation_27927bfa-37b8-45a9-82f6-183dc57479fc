@import (reference) 'mixins';

.mosaic-window,
.mosaic-preview {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 0 1px fade(black, 20%);

  @toolbar-height: 30px;
  .mosaic-window-toolbar {
    z-index: 4;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    height: @toolbar-height;
    background: white;
    box-shadow: 0 1px 1px fade(black, 20%);

    &.draggable {
      cursor: move;
    }
  }

  .mosaic-window-title {
    display: flex;
    align-items: center;
    height: 100%;
    padding-left: 15px;
    flex: 1;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    min-height: 18px;
  }

  .mosaic-window-controls {
    display: flex;
    height: 100%;

    .separator {
      @separator-height: 20px;
      height: @separator-height;
      border-left: 1px solid black;
      margin: (@toolbar-height - @separator-height)/2 4px;
    }
  }

  .mosaic-window-body {
    position: relative;
    flex: 1;
    height: 0;
    background: white;
    z-index: 1;
    overflow: hidden;
  }

  .mosaic-window-additional-actions-bar {
    .absolute-fill(@top: @toolbar-height; @bottom: initial);
    height: 0;
    overflow: hidden;
    background: white;
    justify-content: flex-end;
    display: flex;
    z-index: 3;

    .@{ns}-button {
      margin: 0;

      &:after {
        display: none;
      }
    }
  }

  .mosaic-window-body-overlay {
    .absolute-fill();
    opacity: 0;
    background: white;
    display: none;
    z-index: 2;
  }

  &.additional-controls-open {
    .mosaic-window-additional-actions-bar {
      height: @toolbar-height;
    }
    .mosaic-window-body-overlay {
      display: block;
    }
  }

  .mosaic-preview {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 0;
    border: 1px solid black;
    max-height: 400px;

    .mosaic-window-body {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    h4 {
      margin-bottom: 10px;
    }
  }
}

.mosaic:not(.mosaic-blueprint-theme) {
  .mosaic-default-control {
    &.close-button:before {
      content: 'Close';
    }
    &.split-button:before {
      content: 'Split';
    }
    &.replace-button:before {
      content: 'Replace';
    }
    &.expand-button:before {
      content: 'Expand';
    }
  }
}
