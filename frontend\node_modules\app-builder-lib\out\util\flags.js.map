{"version": 3, "file": "flags.js", "sourceRoot": "", "sources": ["../../src/util/flags.ts"], "names": [], "mappings": ";;;AAAA,+CAAwC;AAExC,SAAgB,mBAAmB;IACjC,OAAO,IAAA,wBAAS,EAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAA;AACnD,CAAC;AAFD,kDAEC;AAED,SAAgB,mBAAmB;IACjC,OAAO,CAAC,IAAA,wBAAS,EAAC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAA;AACrE,CAAC;AAFD,kDAEC;AAED,SAAgB,+BAA+B;IAC7C,OAAO,OAAO,CAAC,GAAG,CAAC,2BAA2B,KAAK,OAAO,CAAA;AAC5D,CAAC;AAFD,0EAEC", "sourcesContent": ["import { isEnvTrue } from \"builder-util\"\n\nexport function isUseSystemSigncode() {\n  return isEnvTrue(process.env.USE_SYSTEM_SIGNCODE)\n}\n\nexport function isBuildCacheEnabled() {\n  return !isEnvTrue(process.env.ELECTRON_BUILDER_DISABLE_BUILD_CACHE)\n}\n\nexport function isAutoDiscoveryCodeSignIdentity() {\n  return process.env.CSC_IDENTITY_AUTO_DISCOVERY !== \"false\"\n}\n"]}