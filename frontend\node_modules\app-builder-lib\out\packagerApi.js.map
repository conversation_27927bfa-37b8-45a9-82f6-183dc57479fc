{"version": 3, "file": "packagerApi.js", "sourceRoot": "", "sources": ["../src/packagerApi.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Arch } from \"builder-util\"\nimport { PublishConfiguration } from \"builder-util-runtime\"\nimport { Configuration } from \"./configuration\"\nimport { Platform, Target } from \"./core\"\nimport { Packager } from \"./packager\"\nimport { PlatformPackager } from \"./platformPackager\"\nimport { UploadTask } from \"electron-publish\"\n\nexport interface PackagerOptions {\n  targets?: Map<Platform, Map<Arch, Array<string>>>\n\n  mac?: Array<string>\n  linux?: Array<string>\n  win?: Array<string>\n\n  projectDir?: string | null\n\n  platformPackagerFactory?: ((info: Packager, platform: Platform) => PlatformPackager<any>) | null\n\n  readonly config?: Configuration | string | null\n\n  readonly effectiveOptionComputed?: (options: any) => Promise<boolean>\n\n  readonly prepackaged?: string | null\n}\n\nexport interface ArtifactCreated extends UploadTask {\n  readonly packager: PlatformPackager<any>\n  readonly target: Target | null\n\n  updateInfo?: any\n\n  readonly safeArtifactName?: string | null\n\n  readonly publishConfig?: PublishConfiguration | null\n\n  readonly isWriteUpdateInfo?: boolean\n}\n\nexport interface ArtifactBuildStarted {\n  readonly targetPresentableName: string\n\n  readonly file: string\n  // null for NSIS\n  readonly arch: Arch | null\n}\n"]}