{"version": 3, "sources": ["../../src/utils/js_utils.ts"], "sourcesContent": ["// cheap lodash replacements\n\nexport function memoize<T>(fn: () => T): () => T {\n\tlet result: T | null = null\n\tconst memoized = () => {\n\t\tif (result == null) {\n\t\t\tresult = fn()\n\t\t}\n\t\treturn result\n\t}\n\treturn memoized\n}\n\n/**\n * drop-in replacement for _.without\n */\nexport function without<T>(items: T[], item: T) {\n\treturn items.filter((i) => i !== item)\n}\n\nexport function union<T extends string | number>(itemsA: T[], itemsB: T[]) {\n\tconst set = new Set<T>()\n\tconst insertItem = (item: T) => set.add(item)\n\titemsA.forEach(insertItem)\n\titemsB.forEach(insertItem)\n\n\tconst result: T[] = []\n\tset.forEach((key) => result.push(key))\n\treturn result\n}\n"], "names": ["memoize", "fn", "result", "memoized", "without", "items", "item", "filter", "i", "union", "itemsA", "itemsB", "set", "Set", "insertItem", "add", "for<PERSON>ach", "key", "push"], "mappings": "AAAA,4BAA4B;AAE5B,OAAO,SAASA,OAAO,CAAIC,EAAW,EAAW;IAChD,IAAIC,MAAM,GAAa,IAAI;IAC3B,MAAMC,QAAQ,GAAG,IAAM;QACtB,IAAID,MAAM,IAAI,IAAI,EAAE;YACnBA,MAAM,GAAGD,EAAE,EAAE;SACb;QACD,OAAOC,MAAM,CAAA;KACb;IACD,OAAOC,QAAQ,CAAA;CACf;AAED;;GAEG,CACH,OAAO,SAASC,OAAO,CAAIC,KAAU,EAAEC,IAAO,EAAE;IAC/C,OAAOD,KAAK,CAACE,MAAM,CAAC,CAACC,CAAC,GAAKA,CAAC,KAAKF,IAAI;IAAA,CAAC,CAAA;CACtC;AAED,OAAO,SAASG,KAAK,CAA4BC,MAAW,EAAEC,MAAW,EAAE;IAC1E,MAAMC,GAAG,GAAG,IAAIC,GAAG,EAAK;IACxB,MAAMC,UAAU,GAAG,CAACR,IAAO,GAAKM,GAAG,CAACG,GAAG,CAACT,IAAI,CAAC;IAAA;IAC7CI,MAAM,CAACM,OAAO,CAACF,UAAU,CAAC;IAC1BH,MAAM,CAACK,OAAO,CAACF,UAAU,CAAC;IAE1B,MAAMZ,MAAM,GAAQ,EAAE;IACtBU,GAAG,CAACI,OAAO,CAAC,CAACC,GAAG,GAAKf,MAAM,CAACgB,IAAI,CAACD,GAAG,CAAC;IAAA,CAAC;IACtC,OAAOf,MAAM,CAAA;CACb"}