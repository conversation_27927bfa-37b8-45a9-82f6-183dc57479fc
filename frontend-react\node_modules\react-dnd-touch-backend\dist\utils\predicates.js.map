{"version": 3, "sources": ["../../src/utils/predicates.ts"], "sourcesContent": ["// Used for MouseEvent.buttons (note the s on the end).\nconst MouseButtons = {\n\tLeft: 1,\n\tRight: 2,\n\tCenter: 4,\n}\n\n// Used for e.button (note the lack of an s on the end).\nconst MouseButton = {\n\tLeft: 0,\n\tCenter: 1,\n\tRight: 2,\n}\n\n/**\n * Only touch events and mouse events where the left button is pressed should initiate a drag.\n * @param {MouseEvent | TouchEvent} e The event\n */\nexport function eventShouldStartDrag(e: MouseEvent): boolean {\n\t// For touch events, button will be undefined. If e.button is defined,\n\t// then it should be MouseButton.Left.\n\treturn e.button === undefined || e.button === MouseButton.Left\n}\n\n/**\n * Only touch events and mouse events where the left mouse button is no longer held should end a drag.\n * It's possible the user mouse downs with the left mouse button, then mouse down and ups with the right mouse button.\n * We don't want releasing the right mouse button to end the drag.\n * @param {MouseEvent | TouchEvent} e The event\n */\nexport function eventShouldEndDrag(e: MouseEvent): boolean {\n\t// Touch events will have buttons be undefined, while mouse events will have e.buttons's left button\n\t// bit field unset if the left mouse button has been released\n\treturn e.buttons === undefined || (e.buttons & MouseButtons.Left) === 0\n}\n\nexport function isTouchEvent(\n\te: Touch | TouchEvent | MouseEvent,\n): e is TouchEvent {\n\treturn !!(e as TouchEvent).targetTouches\n}\n"], "names": ["MouseButtons", "Left", "Right", "Center", "MouseB<PERSON>on", "eventShouldStartDrag", "e", "button", "undefined", "eventShouldEndDrag", "buttons", "isTouchEvent", "targetTouches"], "mappings": "AAAA,uDAAuD;AACvD,MAAMA,YAAY,GAAG;IACpBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;CACT;AAED,wDAAwD;AACxD,MAAMC,WAAW,GAAG;IACnBH,IAAI,EAAE,CAAC;IACPE,MAAM,EAAE,CAAC;IACTD,KAAK,EAAE,CAAC;CACR;AAED;;;GAGG,CACH,OAAO,SAASG,oBAAoB,CAACC,CAAa,EAAW;IAC5D,sEAAsE;IACtE,sCAAsC;IACtC,OAAOA,CAAC,CAACC,MAAM,KAAKC,SAAS,IAAIF,CAAC,CAACC,MAAM,KAAKH,WAAW,CAACH,IAAI,CAAA;CAC9D;AAED;;;;;GAKG,CACH,OAAO,SAASQ,kBAAkB,CAACH,CAAa,EAAW;IAC1D,oGAAoG;IACpG,6DAA6D;IAC7D,OAAOA,CAAC,CAACI,OAAO,KAAKF,SAAS,IAAI,CAACF,CAAC,CAACI,OAAO,GAAGV,YAAY,CAACC,IAAI,CAAC,KAAK,CAAC,CAAA;CACvE;AAED,OAAO,SAASU,YAAY,CAC3BL,CAAkC,EAChB;IAClB,OAAO,CAAC,CAAC,AAACA,CAAC,CAAgBM,aAAa,CAAA;CACxC"}