import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/utils/cn';

// ============================================================================
// TagItem Variants Definition
// ============================================================================

const tagVariants = cva(
  // Base styles
  'inline-flex items-center gap-1.5 rounded-full border px-2.5 py-0.5 text-xs font-medium transition-smooth focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  {
    variants: {
      variant: {
        default: 'border-border bg-background text-foreground hover:bg-accent',
        primary: 'border-primary/20 bg-primary/10 text-primary hover:bg-primary/20',
        secondary: 'border-secondary bg-secondary text-secondary-foreground hover:bg-secondary/80',
        success: 'border-green-200 bg-green-50 text-green-700 hover:bg-green-100',
        warning: 'border-yellow-200 bg-yellow-50 text-yellow-700 hover:bg-yellow-100',
        error: 'border-red-200 bg-red-50 text-red-700 hover:bg-red-100',
        outline: 'border-border bg-transparent hover:bg-accent',
      },
      size: {
        sm: 'text-xs px-2 py-0.5',
        md: 'text-sm px-2.5 py-0.5',
        lg: 'text-sm px-3 py-1',
      },
      clickable: {
        true: 'cursor-pointer hover:shadow-sm',
        false: 'cursor-default',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
      clickable: false,
    },
  }
);

// ============================================================================
// TagItem Component Interface
// ============================================================================

export interface TagItemProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof tagVariants> {
  /**
   * 标签文本内容
   */
  children: React.ReactNode;

  /**
   * 是否可移除 - 显示删除按钮
   */
  removable?: boolean;

  /**
   * 移除回调函数
   */
  onRemove?: (event: React.MouseEvent) => void;

  /**
   * 左侧图标
   */
  icon?: React.ReactNode;

  /**
   * 数量徽章 - 显示该标签关联的项目数量
   */
  count?: number;

  /**
   * 是否选中状态
   */
  selected?: boolean;

  /**
   * 是否禁用
   */
  disabled?: boolean;
}

// ============================================================================
// TagItem Component Implementation
// ============================================================================

/**
 * TagItem 组件 - Project Novak 原子组件
 *
 * 设计原则：
 * 1. 愚蠢原则：只负责标签展示，不包含业务逻辑
 * 2. 组合优于配置：支持图标、计数、删除等组合功能
 * 3. 无障碍优先：完整的键盘导航和屏幕阅读器支持
 * 4. 灵活变体：支持多种样式和状态
 *
 * @example
 * ```tsx
 * // 基础标签
 * <TagItem>Photography</TagItem>
 *
 * // 可点击标签
 * <TagItem clickable onClick={handleClick}>
 *   Nature
 * </TagItem>
 *
 * // 带图标和计数
 * <TagItem
 *   icon={<CameraIcon />}
 *   count={42}
 *   variant="primary"
 * >
 *   Camera
 * </TagItem>
 *
 * // 可移除标签
 * <TagItem
 *   removable
 *   onRemove={handleRemove}
 *   variant="secondary"
 * >
 *   Temporary Tag
 * </TagItem>
 *
 * // 选中状态
 * <TagItem selected variant="primary">
 *   Selected Tag
 * </TagItem>
 * ```
 */
const TagItem = React.forwardRef<HTMLDivElement, TagItemProps>(
  ({
    className,
    variant,
    size,
    clickable,
    children,
    removable = false,
    onRemove,
    icon,
    count,
    selected = false,
    disabled = false,
    onClick,
    onKeyDown,
    ...props
  }, ref) => {
    // 处理键盘事件
    const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
      if (disabled) return;

      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        onClick?.(event as any);
      }

      if (event.key === 'Delete' || event.key === 'Backspace') {
        if (removable && onRemove) {
          event.preventDefault();
          onRemove(event as any);
        }
      }

      onKeyDown?.(event);
    };

    // 处理删除按钮点击
    const handleRemoveClick = (event: React.MouseEvent) => {
      event.stopPropagation(); // 防止触发标签的 onClick
      onRemove?.(event);
    };

    const isInteractive = clickable || !!onClick;
    const isSelected = selected;

    return (
      <div
        ref={ref}
        className={cn(
          tagVariants({
            variant: isSelected ? 'primary' : variant,
            size,
            clickable: isInteractive,
            className
          }),
          disabled && 'opacity-50 cursor-not-allowed',
          isSelected && 'ring-2 ring-primary ring-offset-1'
        )}
        onClick={disabled ? undefined : onClick}
        onKeyDown={handleKeyDown}
        tabIndex={isInteractive && !disabled ? 0 : undefined}
        role={isInteractive ? 'button' : undefined}
        aria-pressed={isSelected ? 'true' : undefined}
        aria-disabled={disabled}
        {...props}
      >
        {/* Left Icon */}
        {icon && (
          <span className="flex-shrink-0" aria-hidden="true">
            {icon}
          </span>
        )}

        {/* Content */}
        <span className="truncate">
          {children}
        </span>

        {/* Count Badge */}
        {count !== undefined && count > 0 && (
          <span
            className="flex-shrink-0 ml-1 px-1.5 py-0.5 text-xs bg-background/50 rounded-full"
            aria-label={`${count} items`}
          >
            {count}
          </span>
        )}

        {/* Remove Button */}
        {removable && (
          <button
            type="button"
            className="flex-shrink-0 ml-1 p-0.5 rounded-full hover:bg-background/50 focus:outline-none focus:ring-1 focus:ring-ring transition-colors"
            onClick={handleRemoveClick}
            aria-label="Remove tag"
            tabIndex={-1} // 使用父元素的 tab 导航
          >
            <svg
              className="w-3 h-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        )}
      </div>
    );
  }
);

TagItem.displayName = 'TagItem';

export { TagItem, tagVariants };
