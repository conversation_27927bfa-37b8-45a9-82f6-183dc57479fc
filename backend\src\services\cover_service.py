# src/services/cover_service.py
"""
封面图管理服务
"""

import os
import shutil
import logging
from typing import Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import text

from .. import models, schemas
from ..crud.case_crud import get_case

logger = logging.getLogger(__name__)


class CoverImageError(Exception):
    """封面图相关错误基类"""
    def __init__(self, error_code: str, message: str, details: Optional[Dict] = None):
        self.error_code = error_code
        self.message = message
        self.details = details or {}
        super().__init__(message)


class CoverImageService:
    """封面图管理服务"""
    
    def __init__(self):
        self.base_data_dir = "data"
        self.manual_cover_filename = "ChooseFront.jpg"
        self.automatic_cover_filename = "Front.jpg"
        self.placeholder_filename = "placeholder-cover.jpg"
        
    def get_case_cover_dir(self, case_id: int) -> str:
        """获取案例封面目录路径"""
        return os.path.join(self.base_data_dir, f"case_{case_id}")
    
    def get_placeholder_url(self) -> str:
        """获取占位图URL"""
        # 使用指定的占位图片路径
        placeholder_path = r"C:\Users\<USER>\mizzy_star_v0.3\data\placeholder-cover.jpg"
        if os.path.exists(placeholder_path):
            return f"file://{os.path.abspath(placeholder_path)}"

        # 备用：使用相对路径
        fallback_path = os.path.join(self.base_data_dir, self.placeholder_filename)
        if os.path.exists(fallback_path):
            return f"file://{os.path.abspath(fallback_path)}"

        # 如果文件不存在，返回内嵌的SVG
        return "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPHJlY3Qgd2lkdGg9IjQwMCIgaGVpZ2h0PSIzMDAiIGZpbGw9IiNGM0Y0RjYiLz4KICA8cmVjdCB4PSI1MCIgeT0iNTAiIHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRTVFN0VCIiBzdHJva2U9IiNEMUQ1REIiIHN0cm9rZS13aWR0aD0iMiIgcng9IjgiLz4KICA8Y2lyY2xlIGN4PSIxMjAiIGN5PSIxMjAiIHI9IjIwIiBmaWxsPSIjOUNBM0FGIi8+CiAgPHBhdGggZD0iTTUwIDIwMEwxMjAgMTMwTDE4MDE5MEwyNTAgMTQwTDM1MCAyNDBWMjUwSDUwVjIwMFoiIGZpbGw9IiNEMUQ1REIiLz4KICA8dGV4dCB4PSIyMDAiIHk9IjI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzZCNzI4MCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE2Ij7mmoLml6Dlsrrmnalic3ZnPgo="
    
    def get_cover_url(self, case_id: int, cover_type: str) -> str:
        """根据封面类型获取封面URL"""
        if cover_type == models.CoverType.PLACEHOLDER.value:
            return self.get_placeholder_url()
        
        cover_dir = self.get_case_cover_dir(case_id)
        
        if cover_type == models.CoverType.MANUAL.value:
            filename = self.manual_cover_filename
        else:  # automatic
            filename = self.automatic_cover_filename
        
        cover_path = os.path.join(cover_dir, filename)
        if os.path.exists(cover_path):
            return f"file://{os.path.abspath(cover_path)}"
        else:
            return self.get_placeholder_url()
    
    def _get_master_db_session(self, case_id: int) -> Session:
        """获取主数据库会话（PostgreSQL模式）"""
        # PostgreSQL模式：使用主数据库会话
        from ..database import get_master_db
        return next(get_master_db())
    
    def _get_best_quality_file(self, db: Session, case_id: int) -> Optional[models.File]:
        """获取质量分数最高的图片文件"""
        try:
            # 获取案例信息
            case = get_case(db, case_id)
            if not case:
                return None

            # PostgreSQL模式：从主数据库查询文件
            file_obj = db.query(models.File).filter(
                models.File.case_id == case_id,
                models.File.file_type.like('image/%'),
                models.File.quality_score.isnot(None)
            ).order_by(models.File.quality_score.desc()).first()

            return file_obj

        except Exception as e:
            logger.error(f"获取最佳质量文件失败: {e}")
            return None
    
    def _create_cover_file(self, source_path: str, target_path: str) -> bool:
        """创建封面文件（复制原图到案例目录作为封面显示）"""
        try:
            # 确保目标目录存在
            os.makedirs(os.path.dirname(target_path), exist_ok=True)

            # 复制文件作为封面显示文件
            shutil.copy2(source_path, target_path)
            logger.info(f"封面显示文件创建成功: {source_path} -> {target_path}")
            return True

        except Exception as e:
            logger.error(f"封面显示文件创建失败: {e}")
            return False

    def _copy_file_as_cover(self, source_path: str, target_path: str) -> bool:
        """复制文件作为封面（保持向后兼容）"""
        return self._create_cover_file(source_path, target_path)
    
    def _update_case_cover_info(self, db: Session, case_id: int, cover_url: str, 
                               cover_type: str, source_file_id: Optional[int] = None,
                               needs_attention: bool = False) -> bool:
        """更新案例的封面信息"""
        try:
            # 更新案例封面信息
            update_data = {
                "cover_image_url": cover_url,
                "cover_type": cover_type,
                "cover_source_file_id": source_file_id,
                "cover_needs_attention": needs_attention,
                "cover_updated_at": datetime.utcnow()
            }
            
            db.execute(
                text("""UPDATE cases SET 
                        cover_image_url = :cover_image_url,
                        cover_type = :cover_type,
                        cover_source_file_id = :cover_source_file_id,
                        cover_needs_attention = :cover_needs_attention,
                        cover_updated_at = :cover_updated_at
                        WHERE id = :case_id"""),
                {**update_data, "case_id": case_id}
            )
            db.commit()
            
            logger.info(f"案例 {case_id} 封面信息更新成功")
            return True
            
        except Exception as e:
            logger.error(f"更新案例封面信息失败: {e}")
            db.rollback()
            return False
    
    def set_placeholder_cover(self, db: Session, case_id: int) -> str:
        """设置占位图封面"""
        try:
            logger.info(f"为案例 {case_id} 设置占位图封面")
            
            placeholder_url = self.get_placeholder_url()
            success = self._update_case_cover_info(
                db, case_id, placeholder_url,
                models.CoverType.PLACEHOLDER.value
            )
            
            if success:
                logger.info(f"占位图封面设置成功: {placeholder_url}")
                return placeholder_url
            else:
                raise CoverImageError("SET_PLACEHOLDER_FAILED", "设置占位图封面失败")
                
        except Exception as e:
            logger.error(f"设置占位图封面失败: {e}")
            raise CoverImageError("SET_PLACEHOLDER_FAILED", f"设置占位图封面失败: {str(e)}")
    
    def auto_select_cover(self, db: Session, case_id: int) -> Optional[str]:
        """自动选择封面图"""
        try:
            logger.info(f"开始为案例 {case_id} 自动选择封面")
            
            # 1. 检查是否已有手动封面
            case = get_case(db, case_id)
            if case and case.cover_type == models.CoverType.MANUAL.value:
                logger.info(f"案例 {case_id} 已有手动封面，跳过自动选择")
                return None
            
            # 2. 获取质量分数最高的图片
            best_file = self._get_best_quality_file(db, case_id)
            if not best_file:
                logger.warning(f"案例 {case_id} 没有找到合适的图片文件")
                # 设置占位图
                return self.set_placeholder_cover(db, case_id)
            
            # 3. 创建封面显示文件（用于前端显示）
            cover_dir = self.get_case_cover_dir(case_id)
            auto_cover_path = os.path.join(cover_dir, self.automatic_cover_filename)

            if not self._create_cover_file(best_file.file_path, auto_cover_path):
                logger.error(f"创建自动封面显示文件失败")
                return self.set_placeholder_cover(db, case_id)

            # 4. 更新数据库记录（记录原图路径，不是封面文件路径）
            # 封面URL指向案例目录中的Front.jpg文件用于显示
            cover_url = self.get_cover_url(case_id, models.CoverType.AUTOMATIC.value)
            success = self._update_case_cover_info(
                db, case_id, cover_url,
                models.CoverType.AUTOMATIC.value,
                source_file_id=best_file.id
            )
            
            if success:
                logger.info(f"自动封面选择成功: {cover_url}")
                return cover_url
            else:
                return self.set_placeholder_cover(db, case_id)
                
        except Exception as e:
            logger.error(f"自动选择封面失败: {e}")
            try:
                return self.set_placeholder_cover(db, case_id)
            except:
                return None

    def _validate_cover_file(self, db: Session, case_id: int, file_id: int) -> models.File:
        """验证封面文件的有效性"""
        try:
            # 获取案例信息
            case = get_case(db, case_id)
            if not case:
                raise CoverImageError("CASE_NOT_FOUND", "案例不存在")

            # PostgreSQL模式：从主数据库查询文件
            file_obj = db.query(models.File).filter(
                models.File.id == file_id,
                models.File.case_id == case_id
            ).first()

            if not file_obj:
                raise CoverImageError("FILE_NOT_FOUND", "文件不存在")

            # 检查是否是图片文件
            if not file_obj.file_type or not file_obj.file_type.startswith('image/'):
                raise CoverImageError("INVALID_FILE_TYPE", "只能选择图片文件作为封面")

            # 检查文件是否存在
            if not file_obj.file_path or not os.path.exists(file_obj.file_path):
                raise CoverImageError("FILE_NOT_FOUND", "源文件不存在")

            return file_obj

        except CoverImageError:
            raise
        except Exception as e:
            logger.error(f"验证封面文件失败: {e}")
            raise CoverImageError("VALIDATION_FAILED", f"文件验证失败: {str(e)}")

    def set_manual_cover(self, db: Session, case_id: int, file_id: int) -> str:
        """手动设置案例封面"""
        try:
            logger.info(f"开始设置案例 {case_id} 的手动封面，文件ID: {file_id}")

            # 1. 验证文件存在且为图片
            file_obj = self._validate_cover_file(db, case_id, file_id)

            # 2. 创建封面目录
            cover_dir = self.get_case_cover_dir(case_id)
            os.makedirs(cover_dir, exist_ok=True)

            # 3. 创建封面显示文件到案例目录
            manual_cover_path = os.path.join(cover_dir, self.manual_cover_filename)

            if not self._create_cover_file(file_obj.file_path, manual_cover_path):
                raise CoverImageError("COPY_FAILED", "创建封面显示文件失败")

            # 4. 更新数据库记录（记录原图路径）
            cover_url = self.get_cover_url(case_id, models.CoverType.MANUAL.value)
            success = self._update_case_cover_info(
                db, case_id, cover_url,
                models.CoverType.MANUAL.value,
                source_file_id=file_id
            )

            if success:
                logger.info(f"手动封面设置成功: {cover_url}")
                return cover_url
            else:
                raise CoverImageError("DATABASE_UPDATE_FAILED", "数据库更新失败")

        except CoverImageError:
            raise
        except Exception as e:
            logger.error(f"设置手动封面失败: {e}")
            raise CoverImageError("SET_MANUAL_COVER_FAILED", f"设置手动封面失败: {str(e)}")

    def remove_manual_cover(self, db: Session, case_id: int) -> Dict[str, Any]:
        """移除手动封面选择，回退到自动选择"""
        try:
            logger.info(f"开始移除案例 {case_id} 的手动封面")

            # 1. 删除手动封面文件
            cover_dir = self.get_case_cover_dir(case_id)
            manual_cover_path = os.path.join(cover_dir, self.manual_cover_filename)

            if os.path.exists(manual_cover_path):
                os.remove(manual_cover_path)
                logger.info(f"删除手动封面文件: {manual_cover_path}")

            # 2. 尝试自动选择封面
            new_cover_url = self.auto_select_cover(db, case_id)

            if new_cover_url:
                if new_cover_url == self.get_placeholder_url():
                    reverted_to = "placeholder"
                else:
                    reverted_to = "automatic"
            else:
                # 如果自动选择失败，设置占位图
                new_cover_url = self.set_placeholder_cover(db, case_id)
                reverted_to = "placeholder"

            return {
                "revertedTo": reverted_to,
                "newCoverUrl": new_cover_url
            }

        except Exception as e:
            logger.error(f"移除手动封面失败: {e}")
            # 确保至少有占位图
            try:
                placeholder_url = self.set_placeholder_cover(db, case_id)
                return {
                    "revertedTo": "placeholder",
                    "newCoverUrl": placeholder_url
                }
            except:
                raise CoverImageError("REMOVE_MANUAL_COVER_FAILED", f"移除手动封面失败: {str(e)}")

    def handle_file_deletion_cover_impact(self, db: Session, case_id: int, deleted_file_id: int) -> Dict[str, Any]:
        """处理文件删除对封面的影响"""
        try:
            logger.info(f"处理案例 {case_id} 文件 {deleted_file_id} 删除对封面的影响")

            case = get_case(db, case_id)
            if not case:
                return {
                    "coverReselected": False,
                    "reselectionStatus": "NOT_NEEDED",
                    "requiresManualSelection": False,
                    "newCoverUrl": None
                }

            # 检查删除的文件是否是当前封面的源文件
            if case.cover_source_file_id != deleted_file_id:
                logger.info(f"删除的文件 {deleted_file_id} 不是封面源文件，无需重选")
                return {
                    "coverReselected": False,
                    "reselectionStatus": "NOT_NEEDED",
                    "requiresManualSelection": False,
                    "newCoverUrl": None
                }

            logger.info(f"删除的文件 {deleted_file_id} 是当前封面源文件，开始重选封面")

            # 如果是手动封面，删除手动封面文件
            if case.cover_type == models.CoverType.MANUAL.value:
                cover_dir = self.get_case_cover_dir(case_id)
                manual_cover_path = os.path.join(cover_dir, self.manual_cover_filename)
                if os.path.exists(manual_cover_path):
                    os.remove(manual_cover_path)
                    logger.info(f"删除手动封面文件: {manual_cover_path}")

            # 尝试自动重新选择封面
            new_cover_url = self.auto_select_cover(db, case_id)

            if new_cover_url and new_cover_url != self.get_placeholder_url():
                return {
                    "coverReselected": True,
                    "reselectionStatus": "SUCCESS",
                    "requiresManualSelection": False,
                    "newCoverUrl": new_cover_url
                }
            else:
                # 自动重选失败，需要用户手动选择
                placeholder_url = self.set_placeholder_cover(db, case_id)
                self._update_case_cover_info(
                    db, case_id, placeholder_url,
                    models.CoverType.PLACEHOLDER.value,
                    needs_attention=True
                )

                return {
                    "coverReselected": True,
                    "reselectionStatus": "FAILED_NO_IMAGES",
                    "requiresManualSelection": True,
                    "newCoverUrl": placeholder_url
                }

        except Exception as e:
            logger.error(f"处理文件删除封面影响失败: {e}")
            try:
                placeholder_url = self.set_placeholder_cover(db, case_id)
                self._update_case_cover_info(
                    db, case_id, placeholder_url,
                    models.CoverType.PLACEHOLDER.value,
                    needs_attention=True
                )
                return {
                    "coverReselected": True,
                    "reselectionStatus": "FAILED_ERROR",
                    "requiresManualSelection": True,
                    "newCoverUrl": placeholder_url
                }
            except:
                return {
                    "coverReselected": True,
                    "reselectionStatus": "FAILED_ERROR",
                    "requiresManualSelection": True,
                    "newCoverUrl": None
                }


# 全局服务实例
_cover_service_instance = None


def get_cover_service() -> CoverImageService:
    """获取封面图服务实例（依赖注入）"""
    global _cover_service_instance
    if _cover_service_instance is None:
        _cover_service_instance = CoverImageService()
    return _cover_service_instance
