// Project Novak - 工作流验证脚本
// 验证 Vite + Electron + MSW 是否正常工作

const http = require('http');

console.log('🔍 验证 Project Novak 工作流...\n');

// 检查 Vite 服务器
function checkVite() {
    return new Promise((resolve) => {
        const req = http.get('http://localhost:5174', (res) => {
            console.log('✅ Vite 服务器: 正常运行 (http://localhost:5174)');
            console.log(`   状态码: ${res.statusCode}`);
            resolve(true);
        });
        
        req.on('error', () => {
            console.log('❌ Vite 服务器: 未运行');
            resolve(false);
        });
        
        req.setTimeout(5000, () => {
            console.log('❌ Vite 服务器: 连接超时');
            req.destroy();
            resolve(false);
        });
    });
}

// 检查 MSW API
function checkMSW() {
    return new Promise((resolve) => {
        const req = http.get('http://localhost:5174/api/cases', (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const json = JSON.parse(data);
                    if (json.data && Array.isArray(json.data)) {
                        console.log('✅ MSW API: 正常工作');
                        console.log(`   返回 ${json.data.length} 个案例`);
                        resolve(true);
                    } else {
                        console.log('❌ MSW API: 数据格式错误');
                        resolve(false);
                    }
                } catch (e) {
                    console.log('❌ MSW API: JSON 解析失败');
                    resolve(false);
                }
            });
        });
        
        req.on('error', () => {
            console.log('❌ MSW API: 请求失败');
            resolve(false);
        });
        
        req.setTimeout(5000, () => {
            console.log('❌ MSW API: 连接超时');
            req.destroy();
            resolve(false);
        });
    });
}

// 检查进程
function checkProcesses() {
    const { exec } = require('child_process');
    
    return new Promise((resolve) => {
        exec('ps aux | grep -E "(vite|electron)" | grep -v grep', (error, stdout) => {
            if (stdout) {
                const lines = stdout.trim().split('\n');
                const viteProcess = lines.find(line => line.includes('vite') && !line.includes('electron'));
                const electronProcess = lines.find(line => line.includes('electron'));
                
                if (viteProcess) {
                    console.log('✅ Vite 进程: 正在运行');
                } else {
                    console.log('❌ Vite 进程: 未找到');
                }
                
                if (electronProcess) {
                    console.log('✅ Electron 进程: 正在运行');
                } else {
                    console.log('❌ Electron 进程: 未找到');
                }
                
                resolve(viteProcess && electronProcess);
            } else {
                console.log('❌ 进程检查: 未找到相关进程');
                resolve(false);
            }
        });
    });
}

// 主验证函数
async function verifyWorkflow() {
    console.log('📋 检查清单:\n');
    
    const viteOk = await checkVite();
    const mswOk = await checkMSW();
    const processOk = await checkProcesses();
    
    console.log('\n📊 验证结果:');
    console.log('='.repeat(40));
    console.log(`Vite 服务器:     ${viteOk ? '✅ 正常' : '❌ 异常'}`);
    console.log(`MSW 数据层:      ${mswOk ? '✅ 正常' : '❌ 异常'}`);
    console.log(`进程状态:        ${processOk ? '✅ 正常' : '❌ 异常'}`);
    console.log('='.repeat(40));
    
    const allOk = viteOk && mswOk && processOk;
    
    if (allOk) {
        console.log('\n🎉 Project Novak 工作流验证成功！');
        console.log('🚀 应用已准备就绪，可以开始开发！');
        console.log('\n📱 访问地址:');
        console.log('   浏览器: http://localhost:5174');
        console.log('   Electron: 应该已自动打开窗口');
    } else {
        console.log('\n⚠️ 工作流存在问题，请检查上述失败项');
    }
    
    return allOk;
}

// 运行验证
verifyWorkflow().catch(console.error);
