// Project Novak - MSW 验证脚本
// 验证 MSW 是否正常工作

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 验证 MSW 数据流...');

const mswFiles = [
  'src/mocks/data.ts',
  'src/mocks/handlers.ts',
  'src/mocks/browser.ts',
  'src/mocks/index.ts',
  'public/mockServiceWorker.js'
];

console.log('\n📁 检查 MSW 文件:');
mswFiles.forEach(file => {
  const exists = fs.existsSync(path.join(__dirname, file));
  console.log(`${exists ? '✅' : '❌'} ${file}`);
});

// 检查 package.json 中的 MSW 配置
console.log('\n📦 检查 package.json 配置:');
const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));

const hasMSW = packageJson.devDependencies && packageJson.devDependencies.msw;
const hasMSWConfig = packageJson.msw && packageJson.msw.workerDirectory;

console.log(`${hasMSW ? '✅' : '❌'} MSW 依赖: ${hasMSW ? packageJson.devDependencies.msw : '未安装'}`);
console.log(`${hasMSWConfig ? '✅' : '❌'} MSW 配置: ${hasMSWConfig ? JSON.stringify(packageJson.msw) : '未配置'}`);

// 检查 main.tsx 中的 MSW 集成
console.log('\n🔧 检查 main.tsx 集成:');
const mainTsx = fs.readFileSync(path.join(__dirname, 'src/main.tsx'), 'utf8');

const hasImport = mainTsx.includes('startMSW');
const hasEnableMocking = mainTsx.includes('enableMocking');

console.log(`${hasImport ? '✅' : '❌'} MSW 导入`);
console.log(`${hasEnableMocking ? '✅' : '❌'} MSW 启动逻辑`);

console.log('\n🎯 MSW 验证完成!');
console.log('\n📋 下一步:');
console.log('1. 启动开发服务器: npm run dev:web');
console.log('2. 打开浏览器: http://localhost:5175');
console.log('3. 检查控制台是否显示 MSW 启动信息');
console.log('4. 点击 "🧪 MSW 测试" 按钮验证 API 调用');
