"""
智能导出系统
支持从基于标签的虚拟画廊导出文件
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import json
import os
import shutil
from pathlib import Path
import zipfile
from PIL import Image

from ..database import get_independent_db
from ..models import File
from ..services.high_performance_search import HighPerformanceSearchService, SearchQuery

logger = logging.getLogger(__name__)

class ExportFormat(Enum):
    """导出格式"""
    ORIGINAL = "original"          # 原始格式
    JPEG = "jpeg"                 # JPEG格式
    PNG = "png"                   # PNG格式
    WEBP = "webp"                 # WebP格式
    PDF = "pdf"                   # PDF格式

class ExportStructure(Enum):
    """导出结构"""
    FLAT = "flat"                 # 平铺结构
    BY_TAG = "by_tag"            # 按标签分组
    BY_DATE = "by_date"          # 按日期分组
    BY_QUALITY = "by_quality"    # 按质量分组
    CUSTOM = "custom"            # 自定义结构

class NamingPattern(Enum):
    """命名模式"""
    ORIGINAL = "original"         # 保持原名
    SEQUENTIAL = "sequential"     # 序号命名
    TIMESTAMP = "timestamp"       # 时间戳命名
    TAG_BASED = "tag_based"      # 基于标签命名
    CUSTOM = "custom"            # 自定义模式

@dataclass
class ExportPreset:
    """导出预设"""
    id: str
    name: str
    description: str
    format: ExportFormat
    structure: ExportStructure
    naming_pattern: NamingPattern
    quality: int = 95  # JPEG质量
    max_size: Optional[tuple] = None  # 最大尺寸 (width, height)
    watermark: Optional[str] = None  # 水印文本
    metadata_export: bool = True  # 是否导出元数据
    custom_naming_template: Optional[str] = None  # 自定义命名模板
    custom_structure_template: Optional[str] = None  # 自定义结构模板
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

@dataclass
class ExportTask:
    """导出任务"""
    id: str
    case_id: int
    preset_id: str
    search_query: SearchQuery
    output_path: str
    status: str = "pending"  # pending, running, completed, failed
    progress: float = 0.0
    total_files: int = 0
    processed_files: int = 0
    error_message: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

class BaseExporter(ABC):
    """导出器基类"""

    def __init__(self, preset: ExportPreset):
        self.preset = preset

    @abstractmethod
    async def export_file(self, file_path: str, output_path: str, metadata: Dict[str, Any]) -> bool:
        """导出单个文件"""
        pass

    @abstractmethod
    def get_output_filename(self, original_filename: str, index: int, metadata: Dict[str, Any]) -> str:
        """生成输出文件名"""
        pass

class ImageExporter(BaseExporter):
    """图像导出器"""

    async def export_file(self, file_path: str, output_path: str, metadata: Dict[str, Any]) -> bool:
        """导出图像文件"""
        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            if self.preset.format == ExportFormat.ORIGINAL:
                # 直接复制原文件
                shutil.copy2(file_path, output_path)
                return True

            # 使用PIL处理图像
            with Image.open(file_path) as img:
                # 转换颜色模式
                if self.preset.format == ExportFormat.JPEG and img.mode in ('RGBA', 'P'):
                    img = img.convert('RGB')
                elif self.preset.format == ExportFormat.PNG and img.mode == 'P':
                    img = img.convert('RGBA')

                # 调整尺寸
                if self.preset.max_size:
                    img.thumbnail(self.preset.max_size, Image.Resampling.LANCZOS)

                # 添加水印
                if self.preset.watermark:
                    img = self._add_watermark(img, self.preset.watermark)

                # 保存文件
                save_kwargs = {}
                if self.preset.format == ExportFormat.JPEG:
                    save_kwargs['quality'] = self.preset.quality
                    save_kwargs['optimize'] = True
                elif self.preset.format == ExportFormat.WEBP:
                    save_kwargs['quality'] = self.preset.quality
                    save_kwargs['method'] = 6

                img.save(output_path, format=self.preset.format.value.upper(), **save_kwargs)

                return True

        except Exception as e:
            logger.error(f"导出图像文件失败 {file_path}: {e}")
            return False

    def _add_watermark(self, img: Image.Image, watermark_text: str) -> Image.Image:
        """添加水印"""
        from PIL import ImageDraw, ImageFont

        # 创建副本
        watermarked = img.copy()
        draw = ImageDraw.Draw(watermarked)

        # 计算水印位置和大小
        width, height = watermarked.size
        font_size = max(width, height) // 50

        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            # 使用默认字体
            font = ImageFont.load_default()

        # 获取文本尺寸
        bbox = draw.textbbox((0, 0), watermark_text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        # 计算位置（右下角）
        x = width - text_width - 20
        y = height - text_height - 20

        # 绘制半透明背景
        draw.rectangle([x-10, y-5, x+text_width+10, y+text_height+5],
                      fill=(0, 0, 0, 128))

        # 绘制文本
        draw.text((x, y), watermark_text, font=font, fill=(255, 255, 255, 255))

        return watermarked

    def get_output_filename(self, original_filename: str, index: int, metadata: Dict[str, Any]) -> str:
        """生成输出文件名"""
        base_name = Path(original_filename).stem

        if self.preset.naming_pattern == NamingPattern.ORIGINAL:
            filename = base_name
        elif self.preset.naming_pattern == NamingPattern.SEQUENTIAL:
            filename = f"{index:06d}"
        elif self.preset.naming_pattern == NamingPattern.TIMESTAMP:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            filename = f"{timestamp}_{index:03d}"
        elif self.preset.naming_pattern == NamingPattern.TAG_BASED:
            # 基于标签生成文件名
            tags = metadata.get('tags', {}).get('tags', {})
            custom_tags = tags.get('custom', {})

            if custom_tags:
                # 使用自定义标签
                tag_parts = []
                for key, value in list(custom_tags.items())[:3]:  # 最多使用3个标签
                    tag_parts.append(f"{key}_{value}")
                filename = "_".join(tag_parts) if tag_parts else base_name
            else:
                filename = base_name
        elif self.preset.naming_pattern == NamingPattern.CUSTOM and self.preset.custom_naming_template:
            # 使用自定义模板
            filename = self._apply_naming_template(
                self.preset.custom_naming_template,
                original_filename,
                index,
                metadata
            )
        else:
            filename = base_name

        # 添加扩展名
        if self.preset.format == ExportFormat.ORIGINAL:
            ext = Path(original_filename).suffix
        else:
            ext = f".{self.preset.format.value}"

        # 清理文件名
        filename = self._sanitize_filename(filename)

        return f"{filename}{ext}"

    def _apply_naming_template(self, template: str, original_filename: str,
                             index: int, metadata: Dict[str, Any]) -> str:
        """应用自定义命名模板"""
        # 可用的变量
        variables = {
            'original_name': Path(original_filename).stem,
            'index': f"{index:06d}",
            'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
            'date': datetime.now().strftime("%Y%m%d"),
            'time': datetime.now().strftime("%H%M%S"),
        }

        # 添加标签变量
        tags = metadata.get('tags', {}).get('tags', {})
        custom_tags = tags.get('custom', {})
        for key, value in custom_tags.items():
            variables[f"tag_{key}"] = str(value)

        # 替换模板变量
        try:
            return template.format(**variables)
        except KeyError as e:
            logger.warning(f"模板变量不存在: {e}, 使用原始文件名")
            return variables['original_name']

    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除非法字符"""
        import re
        # 移除或替换非法字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 限制长度
        if len(filename) > 200:
            filename = filename[:200]
        return filename

class ExportPresetManager:
    """导出预设管理器"""

    def __init__(self):
        self.presets: Dict[str, ExportPreset] = {}
        self._load_default_presets()

    def _load_default_presets(self):
        """加载默认预设"""
        default_presets = [
            ExportPreset(
                id="high_quality_jpeg",
                name="高质量JPEG",
                description="高质量JPEG格式，适合打印和存档",
                format=ExportFormat.JPEG,
                structure=ExportStructure.BY_TAG,
                naming_pattern=NamingPattern.TAG_BASED,
                quality=95
            ),
            ExportPreset(
                id="web_optimized",
                name="Web优化",
                description="Web优化格式，适合网络分享",
                format=ExportFormat.WEBP,
                structure=ExportStructure.FLAT,
                naming_pattern=NamingPattern.SEQUENTIAL,
                quality=85,
                max_size=(1920, 1080)
            ),
            ExportPreset(
                id="thumbnail_collection",
                name="缩略图集合",
                description="小尺寸缩略图，适合快速预览",
                format=ExportFormat.JPEG,
                structure=ExportStructure.BY_DATE,
                naming_pattern=NamingPattern.TIMESTAMP,
                quality=80,
                max_size=(400, 400)
            ),
            ExportPreset(
                id="original_archive",
                name="原始存档",
                description="保持原始格式和质量",
                format=ExportFormat.ORIGINAL,
                structure=ExportStructure.BY_TAG,
                naming_pattern=NamingPattern.ORIGINAL
            )
        ]

        for preset in default_presets:
            self.presets[preset.id] = preset

    def get_preset(self, preset_id: str) -> Optional[ExportPreset]:
        """获取预设"""
        return self.presets.get(preset_id)

    def list_presets(self) -> List[ExportPreset]:
        """列出所有预设"""
        return list(self.presets.values())

    def create_preset(self, preset: ExportPreset) -> bool:
        """创建预设"""
        try:
            self.presets[preset.id] = preset
            return True
        except Exception as e:
            logger.error(f"创建预设失败: {e}")
            return False

    def update_preset(self, preset: ExportPreset) -> bool:
        """更新预设"""
        try:
            if preset.id in self.presets:
                preset.updated_at = datetime.now()
                self.presets[preset.id] = preset
                return True
            return False
        except Exception as e:
            logger.error(f"更新预设失败: {e}")
            return False

    def delete_preset(self, preset_id: str) -> bool:
        """删除预设"""
        try:
            if preset_id in self.presets:
                del self.presets[preset_id]
                return True
            return False
        except Exception as e:
            logger.error(f"删除预设失败: {e}")
            return False


class ExportTaskManager:
    """导出任务管理器"""

    def __init__(self):
        self.tasks: Dict[str, ExportTask] = {}
        self.preset_manager = ExportPresetManager()
        self.search_service = HighPerformanceSearchService()
        self.task_queue = asyncio.Queue()
        self.is_running = False

    async def create_export_task(
        self,
        case_id: int,
        preset_id: str,
        search_query: SearchQuery,
        output_path: str
    ) -> Optional[str]:
        """创建导出任务"""

        # 验证预设
        preset = self.preset_manager.get_preset(preset_id)
        if not preset:
            logger.error(f"预设 {preset_id} 不存在")
            return None

        # 生成任务ID
        import uuid
        task_id = f"export_{uuid.uuid4().hex[:8]}"

        # 创建任务
        task = ExportTask(
            id=task_id,
            case_id=case_id,
            preset_id=preset_id,
            search_query=search_query,
            output_path=output_path
        )

        self.tasks[task_id] = task

        # 将任务加入队列
        await self.task_queue.put(task_id)

        logger.info(f"已创建导出任务: {task_id}")
        return task_id

    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        task = self.tasks.get(task_id)
        if not task:
            return None

        return {
            'id': task.id,
            'status': task.status,
            'progress': task.progress,
            'total_files': task.total_files,
            'processed_files': task.processed_files,
            'error_message': task.error_message,
            'created_at': task.created_at.isoformat(),
            'started_at': task.started_at.isoformat() if task.started_at else None,
            'completed_at': task.completed_at.isoformat() if task.completed_at else None
        }

    def list_tasks(self, case_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """列出任务"""
        tasks = []
        for task in self.tasks.values():
            if case_id is None or task.case_id == case_id:
                tasks.append(self.get_task_status(task.id))

        return sorted(tasks, key=lambda x: x['created_at'], reverse=True)

    async def execute_export_task(self, task_id: str) -> bool:
        """执行导出任务"""

        task = self.tasks.get(task_id)
        if not task:
            logger.error(f"任务 {task_id} 不存在")
            return False

        try:
            # 更新任务状态
            task.status = "running"
            task.started_at = datetime.now()

            # 获取预设
            preset = self.preset_manager.get_preset(task.preset_id)
            if not preset:
                raise Exception(f"预设 {task.preset_id} 不存在")

            # 搜索文件
            logger.info(f"开始搜索文件，任务: {task_id}")
            search_results, total_count = await self.search_service.search_files(task.search_query)

            task.total_files = total_count
            task.processed_files = 0

            if total_count == 0:
                task.status = "completed"
                task.completed_at = datetime.now()
                logger.info(f"任务 {task_id} 完成，没有找到匹配的文件")
                return True

            # 创建导出器
            exporter = self._create_exporter(preset)

            # 创建输出目录结构
            await self._create_output_structure(task, preset, search_results)

            # 导出文件
            logger.info(f"开始导出 {total_count} 个文件，任务: {task_id}")

            for index, result in enumerate(search_results):
                try:
                    # 生成输出路径
                    output_file_path = self._get_output_file_path(
                        task, preset, result, index, exporter
                    )

                    # 导出文件
                    success = await exporter.export_file(
                        result.file_path,
                        output_file_path,
                        result.tags
                    )

                    if success:
                        task.processed_files += 1

                        # 导出元数据
                        if preset.metadata_export:
                            await self._export_metadata(result, output_file_path)

                    # 更新进度
                    task.progress = (task.processed_files / task.total_files) * 100

                    # 记录进度
                    if (index + 1) % 10 == 0:
                        logger.info(f"任务 {task_id} 进度: {task.processed_files}/{task.total_files}")

                except Exception as e:
                    logger.error(f"导出文件失败 {result.file_path}: {e}")
                    continue

            # 创建压缩包（如果需要）
            if task.output_path.endswith('.zip'):
                await self._create_zip_archive(task)

            # 完成任务
            task.status = "completed"
            task.completed_at = datetime.now()
            task.progress = 100.0

            logger.info(f"任务 {task_id} 完成，成功导出 {task.processed_files}/{task.total_files} 个文件")
            return True

        except Exception as e:
            # 任务失败
            task.status = "failed"
            task.error_message = str(e)
            task.completed_at = datetime.now()

            logger.error(f"任务 {task_id} 失败: {e}")
            return False

    def _create_exporter(self, preset: ExportPreset) -> BaseExporter:
        """创建导出器"""
        # 目前只支持图像导出器
        return ImageExporter(preset)

    async def _create_output_structure(
        self,
        task: ExportTask,
        preset: ExportPreset,
        search_results: List[Any]
    ):
        """创建输出目录结构"""

        base_path = Path(task.output_path)
        if task.output_path.endswith('.zip'):
            base_path = base_path.with_suffix('')

        # 创建基础目录
        base_path.mkdir(parents=True, exist_ok=True)

        if preset.structure == ExportStructure.BY_TAG:
            # 按标签创建目录
            tag_dirs = set()
            for result in search_results:
                tags = result.tags.get('tags', {}).get('custom', {})
                for tag_name, tag_value in tags.items():
                    tag_dir = base_path / f"{tag_name}_{tag_value}"
                    tag_dirs.add(tag_dir)

            for tag_dir in tag_dirs:
                tag_dir.mkdir(parents=True, exist_ok=True)

        elif preset.structure == ExportStructure.BY_DATE:
            # 按日期创建目录
            date_dirs = set()
            for result in search_results:
                date_str = result.created_at.strftime("%Y-%m-%d")
                date_dir = base_path / date_str
                date_dirs.add(date_dir)

            for date_dir in date_dirs:
                date_dir.mkdir(parents=True, exist_ok=True)

        elif preset.structure == ExportStructure.BY_QUALITY:
            # 按质量创建目录
            quality_dirs = {
                base_path / "high_quality",
                base_path / "medium_quality",
                base_path / "low_quality"
            }

            for quality_dir in quality_dirs:
                quality_dir.mkdir(parents=True, exist_ok=True)

    def _get_output_file_path(
        self,
        task: ExportTask,
        preset: ExportPreset,
        result: Any,
        index: int,
        exporter: BaseExporter
    ) -> str:
        """获取输出文件路径"""

        base_path = Path(task.output_path)
        if task.output_path.endswith('.zip'):
            base_path = base_path.with_suffix('')

        # 生成文件名
        filename = exporter.get_output_filename(result.file_name, index, result.tags)

        # 根据结构确定子目录
        if preset.structure == ExportStructure.FLAT:
            return str(base_path / filename)

        elif preset.structure == ExportStructure.BY_TAG:
            # 使用第一个自定义标签作为目录
            tags = result.tags.get('tags', {}).get('custom', {})
            if tags:
                tag_name, tag_value = next(iter(tags.items()))
                subdir = f"{tag_name}_{tag_value}"
            else:
                subdir = "untagged"
            return str(base_path / subdir / filename)

        elif preset.structure == ExportStructure.BY_DATE:
            date_str = result.created_at.strftime("%Y-%m-%d")
            return str(base_path / date_str / filename)

        elif preset.structure == ExportStructure.BY_QUALITY:
            if result.quality_score and result.quality_score >= 0.8:
                subdir = "high_quality"
            elif result.quality_score and result.quality_score >= 0.5:
                subdir = "medium_quality"
            else:
                subdir = "low_quality"
            return str(base_path / subdir / filename)

        else:
            return str(base_path / filename)

    async def _export_metadata(self, result: Any, output_file_path: str):
        """导出元数据"""
        metadata_path = Path(output_file_path).with_suffix('.json')

        metadata = {
            'file_id': result.file_id,
            'original_name': result.file_name,
            'original_path': result.file_path,
            'tags': result.tags,
            'quality_score': result.quality_score,
            'created_at': result.created_at.isoformat() if result.created_at else None,
            'export_timestamp': datetime.now().isoformat()
        }

        try:
            with open(metadata_path, 'w', encoding='utf-8') as f:
                import json
                json.dump(metadata, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"导出元数据失败 {metadata_path}: {e}")

    async def _create_zip_archive(self, task: ExportTask):
        """创建ZIP压缩包"""
        source_dir = Path(task.output_path).with_suffix('')
        zip_path = task.output_path

        try:
            import zipfile
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in source_dir.rglob('*'):
                    if file_path.is_file():
                        arcname = file_path.relative_to(source_dir)
                        zipf.write(file_path, arcname)

            # 删除临时目录
            import shutil
            shutil.rmtree(source_dir)

            logger.info(f"已创建ZIP压缩包: {zip_path}")

        except Exception as e:
            logger.error(f"创建ZIP压缩包失败: {e}")

# 全局导出任务管理器实例
export_manager = ExportTaskManager()
