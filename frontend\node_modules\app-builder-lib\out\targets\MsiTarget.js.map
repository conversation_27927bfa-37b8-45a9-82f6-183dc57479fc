{"version": 3, "file": "MsiTarget.js", "sourceRoot": "", "sources": ["../../src/targets/MsiTarget.ts"], "names": [], "mappings": ";;AAAA,+CAA0C;AAC1C,+CAA6D;AAC7D,+DAA2C;AAC3C,gDAA8C;AAC9C,4CAA0C;AAC1C,mCAAmC;AACnC,2BAA0B;AAC1B,0CAAiD;AACjD,uCAA+B;AAC/B,6BAA4B;AAE5B,kCAAgC;AAChC,wGAAuJ;AACvJ,0DAAkD;AAClD,qDAAqD;AACrD,iCAAoC;AACpC,yCAA4C;AAE5C,6CAA4E;AAE5E,MAAM,qCAAqC,GAAG,2BAAI,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAA;AAChG,MAAM,WAAW,GAAG,mBAAmB,CAAA;AAEvC,mHAAmH;AACnH,MAAqB,SAAU,SAAQ,aAAM;IAK3C,YACqB,QAAqB,EAC/B,MAAc,EACvB,IAAI,GAAG,KAAK,EACZ,gBAAgB,GAAG,IAAI;QAEvB,KAAK,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAA;QALV,aAAQ,GAAR,QAAQ,CAAa;QAC/B,WAAM,GAAN,MAAM,CAAQ;QANN,OAAE,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,cAAS,EAAE,CAAC,CAAC,CAAC,IAAI,sBAAa,EAAE,CAAA;QAEnF,YAAO,GAAe,IAAA,yBAAU,EAAC,IAAI,CAAC,QAAQ,CAAC,4BAA4B,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;QAWrG,oBAAe,GAAG,IAAI,eAAI,CAAwB,KAAK,IAAI,EAAE;YACrE,MAAM,QAAQ,GAAG,CAAC,MAAM,IAAA,mBAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,IAAA,6BAAe,EAAC,IAAI,CAAC,IAAI,CAAC,EAAE,cAAc,CAAC,EAAE,MAAM,CAAC,CAAC;iBAC7F,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;iBACpB,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;iBACpB,OAAO,CAAC,cAAc,EAAE,SAAS,CAAC,CAAA;YACrC,OAAO,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QAC9B,CAAC,CAAC,CAAA;IARF,CAAC;IAUD;;OAEG;IACH,IAAY,kBAAkB;QAC5B,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAA;QAC5G,OAAO,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;IAC1F,CAAC;IAED,IAAc,MAAM;QAClB,OAAO,GAAG,IAAI,CAAC,kBAAkB,UAAU,CAAA;IAC7C,CAAC;IAED,IAAc,WAAW;QACvB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,2BAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,EAAE,qCAAqC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAA;IAC7H,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,SAAiB,EAAE,IAAU;QACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,YAAY,GAAG,QAAQ,CAAC,+BAA+B,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;QACxF,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;QACzD,MAAM,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC;YAC3C,qBAAqB,EAAE,KAAK;YAC5B,IAAI,EAAE,YAAY;YAClB,IAAI;SACL,CAAC,CAAA;QAEF,MAAM,QAAQ,GAAG,MAAM,IAAA,2BAAc,EAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QAC3D,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAA;QAElB,MAAM,aAAa,GAAG,IAAA,yDAAmB,EAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;QAEtE,qGAAqG;QACrG,wGAAwG;QACxG,wGAAwG;QACxG,oEAAoE;QACpE,MAAM,OAAO,GAAG,IAAI,IAAI,mBAAI,CAAC,KAAK,CAAC,CAAC,CAAC,mBAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAA;QAEpD,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC,aAAa,CAAC,CAAA;QACvD,MAAM,WAAW,GAAG,CAAC,gBAAgB,CAAC,CAAA;QACtC,MAAM,IAAA,oBAAS,EAAC,WAAW,EAAE,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC,CAAA;QAEzF,MAAM,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAA;QAEtD,uCAAuC;QACvC,MAAM,UAAU,GAAG,MAAM,IAAA,2BAAa,EAAC,KAAK,EAAE,cAAc,EAAE,0FAA0F,CAAC,CAAA;QAEzJ,uCAAuC;QACvC,MAAM,UAAU,GAAG,CAAC,OAAO,EAAE,OAAO,KAAK,mBAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,YAAY,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAA;QACzI,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;QAC9B,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,EAAE,UAAU,EAAE;YAC1E,GAAG,EAAE,QAAQ,CAAC,GAAG;SAClB,CAAC,CAAA;QAEF,MAAM,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAA;QAEpF,MAAM,QAAQ,CAAC,OAAO,EAAE,CAAA;QAExB,MAAM,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QAEjC,MAAM,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC;YAC7C,IAAI,EAAE,YAAY;YAClB,QAAQ;YACR,IAAI;YACJ,gBAAgB,EAAE,QAAQ,CAAC,uBAAuB,CAAC,YAAY,EAAE,KAAK,CAAC;YACvE,MAAM,EAAE,IAAI;YACZ,iBAAiB,EAAE,KAAK;SACzB,CAAC,CAAA;IACJ,CAAC;IAEO,KAAK,CAAC,KAAK,CAAC,WAA0B,EAAE,EAAa,EAAE,YAAoB,EAAE,SAAiB,EAAE,UAAkB,EAAE,OAAe;QACzI,uCAAuC;QACvC,MAAM,SAAS,GAAG;YAChB,MAAM;YACN,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC;YACzB,IAAI;YACJ,mDAAmD;YACnD,OAAO;YACP,2CAA2C;YAC3C,kKAAkK;YAClK,SAAS;YACT,YAAY,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;YACpC,eAAe;SAChB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAA;QAEjC,qLAAqL;QACrL,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YACjC,uCAAuC;YACvC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QACzB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;YACpC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAA;QAC1C,CAAC;QAED,+EAA+E;QAC/E,SAAS,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAA;QAC9B,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,EAAE,SAAS,EAAE;YACxE,GAAG,EAAE,OAAO;SACb,CAAC,CAAA;IACJ,CAAC;IAEO,gBAAgB;QACtB,MAAM,IAAI,GAAkB,CAAC,WAAW,CAAC,CAAA;QACzC,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,KAAK,KAAK,EAAE,CAAC;YAC5C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAClB,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAAI,IAAI,EAAE,CAAC;YAC3C,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;QAC9C,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAES,KAAK,CAAC,aAAa,CAAC,SAAiB,EAAE,OAAa,EAAE,aAAiD;QAC/G,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAA;QACrC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAA;QACpE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAE5B,OAAO,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACxC,GAAG,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC7C,uBAAuB,EAAE,aAAa,CAAC,uBAAuB,KAAK,mEAA6B,CAAC,KAAK;YACtG,gBAAgB,EAAE,OAAO,CAAC,cAAc,KAAK,KAAK;YAClD,6GAA6G;YAC7G,cAAc,EAAE,OAAO,KAAK,mBAAI,CAAC,GAAG,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,oBAAoB;YACpF,qEAAqE;YACrE,4BAA4B,EAAE,IAAA,0CAA6B,EAAC,OAAO,EAAE,aAAa,CAAC,UAAU,IAAI,aAAa,CAAC,YAAY,KAAK,IAAI,CAAC;YACrI,IAAI;YACJ,KAAK;SACN,CAAC,CAAA;IACJ,CAAC;IAES,KAAK,CAAC,cAAc,CAAC,aAAiD;QAC9E,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAA;QACrC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAA;QAClD,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAA;QAE7C,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAA;QACvC,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,kBAAG,CAAC,IAAI,CAAC,2EAA2E,CAAC,CAAA;QACvF,CAAC;QAED,OAAO;YACL,GAAG,aAAa;YAChB,QAAQ,EAAE,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC9D,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,gBAAgB,EAAE,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;YAC3D,OAAO,EAAE,OAAO,CAAC,4BAA4B,EAAE;YAC/C,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,YAAY,EAAE,WAAW,IAAI,OAAO,CAAC,WAAW;YAChD,cAAc,EAAE,OAAO,CAAC,WAAW;SACpC,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,SAAiB;QACpD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAA;QACrC,IAAI,2BAA2B,GAAG,KAAK,CAAA;QACvC,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAU,CAAA;QAClC,MAAM,IAAI,GAAkB,EAAE,CAAA;QAC9B,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QAC/B,MAAM,aAAa,GAAG,IAAA,yDAAmB,EAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;QACtE,MAAM,KAAK,GAAG,MAAM,sBAAe,CAAC,GAAG,CAAC,IAAA,SAAI,EAAC,SAAS,CAAC,EAAE,IAAI,CAAC,EAAE;YAC9D,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;YAExD,MAAM,SAAS,GAAG,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACnD,MAAM,QAAQ,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAA;YACnF,IAAI,WAAW,GAAkB,IAAI,CAAA;YACrC,IAAI,OAAO,GAAG,EAAE,CAAA;YAChB,gHAAgH;YAChH,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;gBAClB,8FAA8F;gBAC9F,+OAA+O;gBAC/O,2FAA2F;gBAC3F,OAAO,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAA;gBAC7C,oEAAoE;gBACpE,WAAW,GAAG,GAAG,GAAG,IAAA,mBAAU,EAAC,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;gBACjI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC3B,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;oBACrB,IAAI,CAAC,IAAI,CAAC,kBAAkB,WAAW,WAAW,WAAW,MAAM,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;gBACzG,CAAC;YACH,CAAC;iBAAM,IAAI,CAAC,2BAA2B,EAAE,CAAC;gBACxC,2BAA2B,GAAG,IAAI,CAAA;YACpC,CAAC;YAED,kHAAkH;YAClH,8EAA8E;YAC9E,IAAI,MAAM,GAAG,aAAa,WAAW,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe,WAAW,GAAG,GAAG,CAAA;YACtF,MAAM,IAAI,KAAK,SAAS,iBAAiB,OAAO,CAAC,QAAQ,CAAC,0BAA0B,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC,gCAAgC,CAAA;YACnJ,MAAM,gBAAgB,GAAG,WAAW,KAAK,GAAG,OAAO,CAAC,eAAe,MAAM,CAAA;YACzE,IAAI,gBAAgB,EAAE,CAAC;gBACrB,MAAM,IAAI,sBAAsB,CAAA;YAClC,CAAC;iBAAM,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;gBAChC,MAAM,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAA;YACnD,CAAC;YAED,MAAM,uBAAuB,GAAG,aAAa,CAAC,uBAAuB,KAAK,mEAA6B,CAAC,KAAK,CAAA;YAC7G,IAAI,gBAAgB,IAAI,CAAC,uBAAuB,IAAI,aAAa,CAAC,yBAAyB,CAAC,EAAE,CAAC;gBAC7F,MAAM,IAAI,KAAK,CAAA;gBACf,MAAM,YAAY,GAAG,aAAa,CAAC,YAAY,CAAA;gBAC/C,IAAI,uBAAuB,EAAE,CAAC;oBAC5B,MAAM,IAAI,GAAG,SAAS,oEAAoE,OAAO,CAC/F,YAAY,CACb,gEAAgE,IAAI,CAAC,MAAM,OAAO,CAAA;gBACrF,CAAC;gBAED,MAAM,eAAe,GAAG,aAAa,CAAC,YAAY,IAAI,IAAI,CAAA;gBAC1D,MAAM,4BAA4B,GAAG,eAAe,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,mBAAmB,CAAA;gBAChG,IAAI,aAAa,CAAC,yBAAyB,EAAE,CAAC;oBAC5C,IAAI,eAAe,EAAE,CAAC;wBACpB,IAAI,CAAC,IAAI,CAAC,kBAAkB,4BAA4B,+BAA+B,aAAa,CAAC,YAAY,OAAO,CAAC,CAAA;oBAC3H,CAAC;oBACD,MAAM,IAAI,GAAG,SAAS,iDAAiD,4BAA4B,WAAW,OAAO,CACnH,YAAY,CACb,gEAAgE,IAAI,CAAC,MAAM,MAAM,CAAA;oBAClF,MAAM,IAAI,GAAG,SAAS,6DAA6D,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAA;oBAC3H,MAAM,IAAI,GAAG,SAAS,iBAAiB,CAAA;gBACzC,CAAC;gBACD,MAAM,IAAI,GAAG,SAAS,SAAS,CAAA;gBAE/B,IAAI,eAAe,EAAE,CAAC;oBACpB,MAAM,IAAI,qBAAqB,4BAA4B,gBAAgB,4BAA4B,sBAAsB,CAAA;gBAC/H,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,IAAI,CAAA;YAChB,CAAC;YAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAA;YACvD,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtD,KAAK,MAAM,IAAI,IAAI,gBAAgB,EAAE,CAAC;oBACpC,MAAM,UAAU,GAAG,IAAA,sBAAO,EAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,+BAAY,CAAC,CAAA;oBACtD,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;wBAC7B,MAAM,IAAI,GAAG,SAAS,iBAAiB,IAAI,CAAC,kBAAkB,IAAI,GAAG,2BAA2B,IAAI,CAAC,MAAM,KACzG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,gBAAgB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,EAC3D,KAAK,CAAA;wBACL,MAAM,IAAI,GAAG,SAAS,sBAAsB,GAAG,sBAAsB,CAAA;wBACrE,MAAM,IAAI,GAAG,SAAS,4CAA4C,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,iCAAiC,CAAA;wBAC7I,MAAM,IAAI,GAAG,SAAS,oBAAoB,CAAA;wBAC1C,MAAM,IAAI,GAAG,SAAS,eAAe,CAAA;oBACvC,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,GAAG,MAAM,KAAK,SAAS,cAAc,CAAA;QAC9C,CAAC,CAAC,CAAA;QAEF,OAAO,EAAE,IAAI,EAAE,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAA;IACvE,CAAC;CACF;AA5QD,4BA4QC;AAED,SAAS,YAAY,CAAC,IAAmB,EAAE,WAAmB;IAC5D,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,CAAA;IACzC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE,CAAC,CAAA;AAChC,CAAC;AAED,SAAS,OAAO,CAAC,GAAW;IAC1B,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;AAC/H,CAAC", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { Arch, asArray, log, deepAssign } from \"builder-util\"\nimport { UUID } from \"builder-util-runtime\"\nimport { getBinFromUrl } from \"../binDownload\"\nimport { walk } from \"builder-util/out/fs\"\nimport { createHash } from \"crypto\"\nimport * as ejs from \"ejs\"\nimport { readFile, writeFile } from \"fs/promises\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { MsiOptions } from \"../\"\nimport { Target } from \"../core\"\nimport { DesktopShortcutCreationPolicy, FinalCommonWindowsInstallerOptions, getEffectiveOptions } from \"../options/CommonWindowsInstallerConfiguration\"\nimport { normalizeExt } from \"../platformPackager\"\nimport { getTemplatePath } from \"../util/pathManager\"\nimport { VmManager } from \"../vm/vm\"\nimport { WineVmManager } from \"../vm/WineVm\"\nimport { WinPackager } from \"../winPackager\"\nimport { createStageDir, getWindowsInstallationDirName } from \"./targetUtil\"\n\nconst ELECTRON_BUILDER_UPGRADE_CODE_NS_UUID = UUID.parse(\"d752fe43-5d44-44d5-9fc9-6dd1bf19d5cc\")\nconst ROOT_DIR_ID = \"APPLICATIONFOLDER\"\n\n// WiX doesn't support Mono, so, dontnet462 is required to be installed for wine (preinstalled in our bundled wine)\nexport default class MsiTarget extends Target {\n  protected readonly vm = process.platform === \"win32\" ? new VmManager() : new WineVmManager()\n\n  readonly options: MsiOptions = deepAssign(this.packager.platformSpecificBuildOptions, this.packager.config.msi)\n\n  constructor(\n    protected readonly packager: WinPackager,\n    readonly outDir: string,\n    name = \"msi\",\n    isAsyncSupported = true\n  ) {\n    super(name, isAsyncSupported)\n  }\n\n  protected projectTemplate = new Lazy<(data: any) => string>(async () => {\n    const template = (await readFile(path.join(getTemplatePath(this.name), \"template.xml\"), \"utf8\"))\n      .replace(/{{/g, \"<%\")\n      .replace(/}}/g, \"%>\")\n      .replace(/\\${([^}]+)}/g, \"<%=$1%>\")\n    return ejs.compile(template)\n  })\n\n  /**\n   * A product-specific string that can be used in an [MSI Identifier](https://docs.microsoft.com/en-us/windows/win32/msi/identifier).\n   */\n  private get productMsiIdPrefix() {\n    const sanitizedId = this.packager.appInfo.productFilename.replace(/[^\\w.]/g, \"\").replace(/^[^A-Za-z_]+/, \"\")\n    return sanitizedId.length > 0 ? sanitizedId : \"App\" + this.upgradeCode.replace(/-/g, \"\")\n  }\n\n  protected get iconId() {\n    return `${this.productMsiIdPrefix}Icon.exe`\n  }\n\n  protected get upgradeCode(): string {\n    return (this.options.upgradeCode || UUID.v5(this.packager.appInfo.id, ELECTRON_BUILDER_UPGRADE_CODE_NS_UUID)).toUpperCase()\n  }\n\n  async build(appOutDir: string, arch: Arch) {\n    const packager = this.packager\n    const artifactName = packager.expandArtifactBeautyNamePattern(this.options, \"msi\", arch)\n    const artifactPath = path.join(this.outDir, artifactName)\n    await packager.info.callArtifactBuildStarted({\n      targetPresentableName: \"MSI\",\n      file: artifactPath,\n      arch,\n    })\n\n    const stageDir = await createStageDir(this, packager, arch)\n    const vm = this.vm\n\n    const commonOptions = getEffectiveOptions(this.options, this.packager)\n\n    // wix 4.0.0.5512.2 doesn't support the arm64 architecture so default to x64 when building for arm64.\n    // This will result in an x64 MSI installer that installs an arm64 version of the application. This is a\n    // stopgap until the electron-builder-binaries wix version is upgraded to a version that supports arm64:\n    // https://github.com/electron-userland/electron-builder/issues/6077\n    const wixArch = arch == Arch.arm64 ? Arch.x64 : arch\n\n    const projectFile = stageDir.getTempFile(\"project.wxs\")\n    const objectFiles = [\"project.wixobj\"]\n    await writeFile(projectFile, await this.writeManifest(appOutDir, wixArch, commonOptions))\n\n    await packager.info.callMsiProjectCreated(projectFile)\n\n    // noinspection SpellCheckingInspection\n    const vendorPath = await getBinFromUrl(\"wix\", \"4.0.0.5512.2\", \"/X5poahdCc3199Vt6AP7gluTlT1nxi9cbbHhZhCMEu+ngyP1LiBMn+oZX7QAZVaKeBMc2SjVp7fJqNLqsUnPNQ==\")\n\n    // noinspection SpellCheckingInspection\n    const candleArgs = [\"-arch\", wixArch === Arch.ia32 ? \"x86\" : \"x64\", `-dappDir=${vm.toVmFile(appOutDir)}`].concat(this.getCommonWixArgs())\n    candleArgs.push(\"project.wxs\")\n    await vm.exec(vm.toVmFile(path.join(vendorPath, \"candle.exe\")), candleArgs, {\n      cwd: stageDir.dir,\n    })\n\n    await this.light(objectFiles, vm, artifactPath, appOutDir, vendorPath, stageDir.dir)\n\n    await stageDir.cleanup()\n\n    await packager.sign(artifactPath)\n\n    await packager.info.callArtifactBuildCompleted({\n      file: artifactPath,\n      packager,\n      arch,\n      safeArtifactName: packager.computeSafeArtifactName(artifactName, \"msi\"),\n      target: this,\n      isWriteUpdateInfo: false,\n    })\n  }\n\n  private async light(objectFiles: Array<string>, vm: VmManager, artifactPath: string, appOutDir: string, vendorPath: string, tempDir: string) {\n    // noinspection SpellCheckingInspection\n    const lightArgs = [\n      \"-out\",\n      vm.toVmFile(artifactPath),\n      \"-v\",\n      // https://github.com/wixtoolset/issues/issues/5169\n      \"-spdb\",\n      // https://sourceforge.net/p/wix/bugs/2405/\n      // error LGHT1076 : ICE61: This product should remove only older versions of itself. The Maximum version is not less than the current product. (1.1.0.42 1.1.0.42)\n      \"-sw1076\",\n      `-dappDir=${vm.toVmFile(appOutDir)}`,\n      // \"-dcl:high\",\n    ].concat(this.getCommonWixArgs())\n\n    // http://windows-installer-xml-wix-toolset.687559.n2.nabble.com/Build-3-5-2229-0-give-me-the-following-error-error-LGHT0216-An-unexpected-Win32-exception-with-errorn-td5707443.html\n    if (process.platform !== \"win32\") {\n      // noinspection SpellCheckingInspection\n      lightArgs.push(\"-sval\")\n    }\n\n    if (this.options.oneClick === false) {\n      lightArgs.push(\"-ext\", \"WixUIExtension\")\n    }\n\n    // objectFiles - only filenames, we set current directory to our temp stage dir\n    lightArgs.push(...objectFiles)\n    await vm.exec(vm.toVmFile(path.join(vendorPath, \"light.exe\")), lightArgs, {\n      cwd: tempDir,\n    })\n  }\n\n  private getCommonWixArgs() {\n    const args: Array<string> = [\"-pedantic\"]\n    if (this.options.warningsAsErrors !== false) {\n      args.push(\"-wx\")\n    }\n    if (this.options.additionalWixArgs != null) {\n      args.push(...this.options.additionalWixArgs)\n    }\n    return args\n  }\n\n  protected async writeManifest(appOutDir: string, wixArch: Arch, commonOptions: FinalCommonWindowsInstallerOptions) {\n    const appInfo = this.packager.appInfo\n    const { files, dirs } = await this.computeFileDeclaration(appOutDir)\n    const options = this.options\n\n    return (await this.projectTemplate.value)({\n      ...(await this.getBaseOptions(commonOptions)),\n      isCreateDesktopShortcut: commonOptions.isCreateDesktopShortcut !== DesktopShortcutCreationPolicy.NEVER,\n      isRunAfterFinish: options.runAfterFinish !== false,\n      // https://stackoverflow.com/questions/1929038/compilation-error-ice80-the-64bitcomponent-uses-32bitdirectory\n      programFilesId: wixArch === Arch.x64 ? \"ProgramFiles64Folder\" : \"ProgramFilesFolder\",\n      // wix in the name because special wix format can be used in the name\n      installationDirectoryWixName: getWindowsInstallationDirName(appInfo, commonOptions.isAssisted || commonOptions.isPerMachine === true),\n      dirs,\n      files,\n    })\n  }\n\n  protected async getBaseOptions(commonOptions: FinalCommonWindowsInstallerOptions): Promise<any> {\n    const appInfo = this.packager.appInfo\n    const iconPath = await this.packager.getIconPath()\n    const compression = this.packager.compression\n\n    const companyName = appInfo.companyName\n    if (!companyName) {\n      log.warn(`Manufacturer is not set for MSI — please set \"author\" in the package.json`)\n    }\n\n    return {\n      ...commonOptions,\n      iconPath: iconPath == null ? null : this.vm.toVmFile(iconPath),\n      iconId: this.iconId,\n      compressionLevel: compression === \"store\" ? \"none\" : \"high\",\n      version: appInfo.getVersionInWeirdWindowsForm(),\n      productName: appInfo.productName,\n      upgradeCode: this.upgradeCode,\n      manufacturer: companyName || appInfo.productName,\n      appDescription: appInfo.description,\n    }\n  }\n\n  private async computeFileDeclaration(appOutDir: string) {\n    const appInfo = this.packager.appInfo\n    let isRootDirAddedToRemoveTable = false\n    const dirNames = new Set<string>()\n    const dirs: Array<string> = []\n    const fileSpace = \" \".repeat(6)\n    const commonOptions = getEffectiveOptions(this.options, this.packager)\n    const files = await BluebirdPromise.map(walk(appOutDir), file => {\n      const packagePath = file.substring(appOutDir.length + 1)\n\n      const lastSlash = packagePath.lastIndexOf(path.sep)\n      const fileName = lastSlash > 0 ? packagePath.substring(lastSlash + 1) : packagePath\n      let directoryId: string | null = null\n      let dirName = \"\"\n      // Wix Directory.FileSource doesn't work - https://stackoverflow.com/questions/21519388/wix-filesource-confusion\n      if (lastSlash > 0) {\n        // This Name attribute may also define multiple directories using the inline directory syntax.\n        // For example, \"ProgramFilesFolder:\\My Company\\My Product\\bin\" would create a reference to a Directory element with Id=\"ProgramFilesFolder\" then create directories named \"My Company\" then \"My Product\" then \"bin\" nested beneath each other.\n        // This syntax is a shortcut to defining each directory in an individual Directory element.\n        dirName = packagePath.substring(0, lastSlash)\n        // https://github.com/electron-userland/electron-builder/issues/3027\n        directoryId = \"d\" + createHash(\"md5\").update(dirName).digest(\"base64\").replace(/\\//g, \"_\").replace(/\\+/g, \".\").replace(/=+$/, \"\")\n        if (!dirNames.has(dirName)) {\n          dirNames.add(dirName)\n          dirs.push(`<Directory Id=\"${directoryId}\" Name=\"${ROOT_DIR_ID}:\\\\${dirName.replace(/\\//g, \"\\\\\")}\\\\\"/>`)\n        }\n      } else if (!isRootDirAddedToRemoveTable) {\n        isRootDirAddedToRemoveTable = true\n      }\n\n      // since RegistryValue can be part of Component, *** *** *** *** *** *** *** *** *** wix cannot auto generate guid\n      // https://stackoverflow.com/questions/1405100/change-my-component-guid-in-wix\n      let result = `<Component${directoryId === null ? \"\" : ` Directory=\"${directoryId}\"`}>`\n      result += `\\n${fileSpace}  <File Name=\"${xmlAttr(fileName)}\" Source=\"$(var.appDir)${path.sep}${xmlAttr(packagePath)}\" ReadOnly=\"yes\" KeyPath=\"yes\"`\n      const isMainExecutable = packagePath === `${appInfo.productFilename}.exe`\n      if (isMainExecutable) {\n        result += ' Id=\"mainExecutable\"'\n      } else if (directoryId === null) {\n        result += ` Id=\"${path.basename(packagePath)}_f\"`\n      }\n\n      const isCreateDesktopShortcut = commonOptions.isCreateDesktopShortcut !== DesktopShortcutCreationPolicy.NEVER\n      if (isMainExecutable && (isCreateDesktopShortcut || commonOptions.isCreateStartMenuShortcut)) {\n        result += `>\\n`\n        const shortcutName = commonOptions.shortcutName\n        if (isCreateDesktopShortcut) {\n          result += `${fileSpace}  <Shortcut Id=\"desktopShortcut\" Directory=\"DesktopFolder\" Name=\"${xmlAttr(\n            shortcutName\n          )}\" WorkingDirectory=\"APPLICATIONFOLDER\" Advertise=\"yes\" Icon=\"${this.iconId}\"/>\\n`\n        }\n\n        const hasMenuCategory = commonOptions.menuCategory != null\n        const startMenuShortcutDirectoryId = hasMenuCategory ? \"AppProgramMenuDir\" : \"ProgramMenuFolder\"\n        if (commonOptions.isCreateStartMenuShortcut) {\n          if (hasMenuCategory) {\n            dirs.push(`<Directory Id=\"${startMenuShortcutDirectoryId}\" Name=\"ProgramMenuFolder:\\\\${commonOptions.menuCategory}\\\\\"/>`)\n          }\n          result += `${fileSpace}  <Shortcut Id=\"startMenuShortcut\" Directory=\"${startMenuShortcutDirectoryId}\" Name=\"${xmlAttr(\n            shortcutName\n          )}\" WorkingDirectory=\"APPLICATIONFOLDER\" Advertise=\"yes\" Icon=\"${this.iconId}\">\\n`\n          result += `${fileSpace}    <ShortcutProperty Key=\"System.AppUserModel.ID\" Value=\"${xmlAttr(this.packager.appInfo.id)}\"/>\\n`\n          result += `${fileSpace}  </Shortcut>\\n`\n        }\n        result += `${fileSpace}</File>`\n\n        if (hasMenuCategory) {\n          result += `<RemoveFolder Id=\"${startMenuShortcutDirectoryId}\" Directory=\"${startMenuShortcutDirectoryId}\" On=\"uninstall\"/>\\n`\n        }\n      } else {\n        result += `/>`\n      }\n\n      const fileAssociations = this.packager.fileAssociations\n      if (isMainExecutable && fileAssociations.length !== 0) {\n        for (const item of fileAssociations) {\n          const extensions = asArray(item.ext).map(normalizeExt)\n          for (const ext of extensions) {\n            result += `${fileSpace}  <ProgId Id=\"${this.productMsiIdPrefix}.${ext}\" Advertise=\"yes\" Icon=\"${this.iconId}\" ${\n              item.description ? `Description=\"${item.description}\"` : \"\"\n            }>\\n`\n            result += `${fileSpace}    <Extension Id=\"${ext}\" Advertise=\"yes\">\\n`\n            result += `${fileSpace}      <Verb Id=\"open\" Command=\"Open with ${xmlAttr(this.packager.appInfo.productName)}\" Argument=\"&quot;%1&quot;\"/>\\n`\n            result += `${fileSpace}    </Extension>\\n`\n            result += `${fileSpace}  </ProgId>\\n`\n          }\n        }\n      }\n\n      return `${result}\\n${fileSpace}</Component>`\n    })\n\n    return { dirs: listToString(dirs, 2), files: listToString(files, 3) }\n  }\n}\n\nfunction listToString(list: Array<string>, indentLevel: number) {\n  const space = \" \".repeat(indentLevel * 2)\n  return list.join(`\\n${space}`)\n}\n\nfunction xmlAttr(str: string) {\n  return str.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&apos;\")\n}\n"]}