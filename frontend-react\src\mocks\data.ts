// Project Novak - Mock Data
// 真实可用的模拟数据，完全符合 API 类型定义

import type { Case, FileItem, TagItem } from '@/types';

// 简单的 1x1 像素透明 PNG 作为占位符
const PLACEHOLDER_IMAGE = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

// ============================================================================
// Mock Cases Data
// ============================================================================

export const mockCases: Case[] = [
  {
    id: 1,
    case_name: "城市摄影项目",
    description: "记录城市变迁的摄影项目",
    created_at: "2024-01-15T10:30:00Z",
    status: "active",
    deleted_at: null,
    db_path: "/data/cases/city_photography.db"
  },
  {
    id: 2,
    case_name: "自然风光收集",
    description: "收集各地自然风光照片",
    created_at: "2024-01-20T14:20:00Z",
    status: "active",
    deleted_at: null,
    db_path: "/data/cases/nature_collection.db"
  },
  {
    id: 3,
    case_name: "人像摄影作品",
    description: "专业人像摄影作品集",
    created_at: "2024-01-25T09:15:00Z",
    status: "active",
    deleted_at: null,
    db_path: "/data/cases/portrait_works.db"
  }
];

// ============================================================================
// Mock Files Data
// ============================================================================

export const mockFiles: FileItem[] = [
  {
    id: 1,
    case_id: 1,
    file_name: "sunset_cityscape.jpg",
    file_path: PLACEHOLDER_IMAGE,
    file_type: "image/jpeg",
    file_size: 2048000,
    width: 1920,
    height: 1080,
    created_at: "2024-01-15T10:30:00Z",
    taken_at: "2024-01-14T18:45:00Z",
    thumbnail_small_path: PLACEHOLDER_IMAGE,
    tags: {
      properties: {
        filename: "sunset_cityscape.jpg",
        qualityScore: 85,
        fileSize: 2048000
      },
      tags: {
        metadata: {
          camera: "Canon EOS R5",
          lens: "24-70mm f/2.8",
          iso: "400",
          aperture: "f/8",
          shutter: "1/125s"
        },
        cv: {
          sharpness: 85,
          brightness: 78,
          contrast: 82
        },
        user: ["城市", "日落", "天际线"],
        ai: ["建筑", "天空", "城市景观", "黄昏"],
        custom: {
          location: "北京CBD",
          weather: "晴朗"
        }
      }
    },
    quality_score: 85,
    sharpness: 85,
    brightness: 78,
    dynamic_range: 82,
    num_faces: 0,
    face_sharpness: null,
    face_quality: null,
    cluster_id: "cluster_001",
    phash: "a1b2c3d4e5f6"
  },
  {
    id: 2,
    case_id: 1,
    file_name: "night_street.jpg",
    file_path: PLACEHOLDER_IMAGE,
    file_type: "image/jpeg",
    file_size: 1536000,
    width: 1920,
    height: 1080,
    created_at: "2024-01-16T20:15:00Z",
    taken_at: "2024-01-16T19:30:00Z",
    thumbnail_small_path: PLACEHOLDER_IMAGE,
    tags: {
      properties: {
        filename: "night_street.jpg",
        qualityScore: 92,
        fileSize: 1536000
      },
      tags: {
        metadata: {
          camera: "Sony A7R4",
          lens: "85mm f/1.4",
          iso: "1600",
          aperture: "f/2.8",
          shutter: "1/60s"
        },
        cv: {
          sharpness: 92,
          brightness: 65,
          contrast: 88
        },
        user: ["夜景", "街道", "灯光"],
        ai: ["城市", "夜晚", "街灯", "建筑"],
        custom: {
          location: "上海外滩",
          weather: "多云"
        }
      }
    },
    quality_score: 92,
    sharpness: 92,
    brightness: 65,
    dynamic_range: 88,
    num_faces: 0,
    face_sharpness: null,
    face_quality: null,
    cluster_id: "cluster_002",
    phash: "b2c3d4e5f6a1"
  },
  {
    id: 3,
    case_id: 2,
    file_name: "mountain_lake.jpg",
    file_path: PLACEHOLDER_IMAGE,
    file_type: "image/jpeg",
    file_size: 3072000,
    width: 2400,
    height: 1600,
    created_at: "2024-01-17T14:45:00Z",
    taken_at: "2024-01-17T08:20:00Z",
    thumbnail_small_path: PLACEHOLDER_IMAGE,
    tags: {
      properties: {
        filename: "mountain_lake.jpg",
        qualityScore: 95,
        fileSize: 3072000
      },
      tags: {
        metadata: {
          camera: "Nikon D850",
          lens: "14-24mm f/2.8",
          iso: "100",
          aperture: "f/11",
          shutter: "1/250s"
        },
        cv: {
          sharpness: 95,
          brightness: 82,
          contrast: 90
        },
        user: ["自然", "山脉", "湖泊", "风景"],
        ai: ["山", "水", "自然风光", "倒影"],
        custom: {
          location: "青海湖",
          weather: "晴朗",
          season: "夏季"
        }
      }
    },
    quality_score: 95,
    sharpness: 95,
    brightness: 82,
    dynamic_range: 90,
    num_faces: 0,
    face_sharpness: null,
    face_quality: null,
    cluster_id: "cluster_003",
    phash: "c3d4e5f6a1b2"
  },
  {
    id: 4,
    case_id: 3,
    file_name: "portrait_studio.jpg",
    file_path: PLACEHOLDER_IMAGE,
    file_type: "image/jpeg",
    file_size: 2560000,
    width: 1200,
    height: 1600,
    created_at: "2024-01-18T16:30:00Z",
    taken_at: "2024-01-18T15:45:00Z",
    thumbnail_small_path: PLACEHOLDER_IMAGE,
    tags: {
      properties: {
        filename: "portrait_studio.jpg",
        qualityScore: 88,
        fileSize: 2560000
      },
      tags: {
        metadata: {
          camera: "Canon EOS R6",
          lens: "85mm f/1.2",
          iso: "200",
          aperture: "f/2.8",
          shutter: "1/160s"
        },
        cv: {
          sharpness: 88,
          brightness: 75,
          contrast: 85
        },
        user: ["人像", "工作室", "专业"],
        ai: ["人物", "肖像", "专业摄影"],
        custom: {
          location: "摄影工作室",
          lighting: "专业灯光"
        }
      }
    },
    quality_score: 88,
    sharpness: 88,
    brightness: 75,
    dynamic_range: 85,
    num_faces: 1,
    face_sharpness: 90,
    face_quality: 87,
    cluster_id: "cluster_004",
    phash: "d4e5f6a1b2c3"
  },
  {
    id: 5,
    case_id: 1,
    file_name: "urban_architecture.jpg",
    file_path: PLACEHOLDER_IMAGE,
    file_type: "image/jpeg",
    file_size: 1800000,
    width: 1920,
    height: 1080,
    created_at: "2024-01-19T11:20:00Z",
    taken_at: "2024-01-19T10:15:00Z",
    thumbnail_small_path: PLACEHOLDER_IMAGE,
    tags: {
      properties: {
        filename: "urban_architecture.jpg",
        qualityScore: 80,
        fileSize: 1800000
      },
      tags: {
        metadata: {
          camera: "Fujifilm X-T4",
          lens: "16-55mm f/2.8",
          iso: "200",
          aperture: "f/5.6",
          shutter: "1/200s"
        },
        cv: {
          sharpness: 80,
          brightness: 70,
          contrast: 78
        },
        user: ["建筑", "现代", "几何"],
        ai: ["建筑", "现代建筑", "几何图案"],
        custom: {
          location: "深圳",
          style: "现代主义"
        }
      }
    },
    quality_score: 80,
    sharpness: 80,
    brightness: 70,
    dynamic_range: 78,
    num_faces: 0,
    face_sharpness: null,
    face_quality: null,
    cluster_id: "cluster_005",
    phash: "e5f6a1b2c3d4"
  }
];

// ============================================================================
// Mock Tags Data
// ============================================================================

export const mockTags: TagItem[] = [
  { key: "camera", value: "Canon EOS R5", count: 2, fileIds: [1], category: "metadata" },
  { key: "camera", value: "Sony A7R4", count: 1, fileIds: [2], category: "metadata" },
  { key: "camera", value: "Nikon D850", count: 1, fileIds: [3], category: "metadata" },
  { key: "user_tag", value: "城市", count: 2, fileIds: [1, 2], category: "user" },
  { key: "user_tag", value: "夜景", count: 1, fileIds: [2], category: "user" },
  { key: "user_tag", value: "自然", count: 1, fileIds: [3], category: "user" },
  { key: "user_tag", value: "人像", count: 1, fileIds: [4], category: "user" },
  { key: "ai_tag", value: "建筑", count: 3, fileIds: [1, 2, 5], category: "ai" },
  { key: "ai_tag", value: "天空", count: 2, fileIds: [1, 3], category: "ai" },
  { key: "ai_tag", value: "自然风光", count: 1, fileIds: [3], category: "ai" }
];

// ============================================================================
// Helper Functions
// ============================================================================

/**
 * 根据案例ID筛选文件
 */
export function getFilesByCase(caseId: number): FileItem[] {
  return mockFiles.filter(file => file.case_id === caseId);
}

/**
 * 根据搜索查询筛选文件
 */
export function searchFiles(query: string, caseId?: number): FileItem[] {
  let files = caseId ? getFilesByCase(caseId) : mockFiles;

  if (!query) return files;

  const lowerQuery = query.toLowerCase();
  return files.filter(file =>
    file.file_name.toLowerCase().includes(lowerQuery) ||
    file.tags?.tags.user.some(tag => tag.toLowerCase().includes(lowerQuery)) ||
    file.tags?.tags.ai.some(tag => tag.toLowerCase().includes(lowerQuery))
  );
}

/**
 * 根据案例ID获取标签
 */
export function getTagsByCase(caseId?: number): TagItem[] {
  if (!caseId) return mockTags;

  const caseFiles = getFilesByCase(caseId);
  const caseFileIds = caseFiles.map(f => f.id);

  return mockTags.filter(tag =>
    tag.fileIds.some(id => caseFileIds.includes(id))
  );
}
