# 迷星 Mizzy Star 标签管理界面设计文档

## 概述

本设计文档描述了基于用户设计草图的标签管理界面系统的完整技术实现方案。系统将提供直观的标签组织、管理和应用功能，支持拖拽交互和可调整的分栏布局。

## 架构设计

### 系统架构图

```
标签管理系统架构
├── 后端API层
│   ├── 标签管理API
│   ├── 标签缓存管理
│   ├── 标签统计API
│   └── 标签搜索API
├── 数据存储层
│   ├── 标签数据表
│   ├── 标签缓存文件
│   ├── 删除记录表
│   └── 标签关系索引
├── 前端界面层
│   ├── 标签管理面板
│   ├── 标签画廊面板
│   ├── 拖拽交互组件
│   └── 分栏布局组件
└── 交互逻辑层
    ├── 标签拖拽逻辑
    ├── 分栏调整逻辑
    ├── 搜索过滤逻辑
    └── 实时同步逻辑
```

## 后端设计

### 1. 数据模型扩展

#### 标签缓存表 (TagCache)
```python
class TagCache(Base):
    __tablename__ = "tag_cache"
    
    id = Column(Integer, primary_key=True)
    case_id = Column(Integer, ForeignKey("cases.id"))
    tag_category = Column(String(50))  # metadata, cv, user, ai
    tag_name = Column(String(100))
    tag_value = Column(String(500))
    file_ids = Column(JSON)  # 包含该标签的文件ID列表
    file_count = Column(Integer, default=0)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
```

#### 删除文件记录表 (DeletedFiles)
```python
class DeletedFiles(Base):
    __tablename__ = "deleted_files"
    
    id = Column(Integer, primary_key=True)
    case_id = Column(Integer, ForeignKey("cases.id"))
    file_id = Column(Integer)
    file_name = Column(String(255))
    deleted_at = Column(DateTime, server_default=func.now())
    is_permanent = Column(Boolean, default=False)
```

#### 自定义标签表 (CustomTags)
```python
class CustomTags(Base):
    __tablename__ = "custom_tags"
    
    id = Column(Integer, primary_key=True)
    case_id = Column(Integer, ForeignKey("cases.id"))
    tag_name = Column(String(100))
    tag_color = Column(String(7), default="#3B82F6")  # 十六进制颜色
    display_order = Column(Integer, default=0)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
```

### 2. API接口设计

#### 标签管理API
```python
# 获取案例标签树结构
GET /api/v1/cases/{case_id}/tags/tree
Response: {
    "properties": {...},
    "tags": {
        "metadata": [
            {
                "name": "project",
                "values": [
                    {"value": "春季拍摄", "count": 5, "file_ids": [1,2,3,4,5]},
                    {"value": "夏季拍摄", "count": 3, "file_ids": [6,7,8]}
                ]
            }
        ],
        "cv": [...],
        "user": [...],
        "ai": [...]
    },
    "custom": [
        {"id": 1, "name": "重要", "color": "#EF4444", "count": 2, "file_ids": [1,3]}
    ]
}

# 搜索标签
GET /api/v1/cases/{case_id}/tags/search?q={query}&type={category}
Response: {
    "results": [
        {
            "category": "metadata",
            "name": "project",
            "value": "春季拍摄",
            "count": 5,
            "file_ids": [1,2,3,4,5]
        }
    ]
}

# 创建自定义标签
POST /api/v1/cases/{case_id}/tags/custom
Body: {
    "name": "重要",
    "color": "#EF4444"
}

# 为文件添加自定义标签
POST /api/v1/cases/{case_id}/files/{file_id}/tags/custom
Body: {
    "tag_ids": [1, 2, 3]
}

# 移除文件的自定义标签
DELETE /api/v1/cases/{case_id}/files/{file_id}/tags/custom/{tag_id}

# 批量标签操作
POST /api/v1/cases/{case_id}/tags/batch
Body: {
    "action": "add|remove",
    "file_ids": [1, 2, 3],
    "tag_id": 1
}
```

#### 标签缓存管理API
```python
# 刷新标签缓存
POST /api/v1/cases/{case_id}/tags/cache/refresh

# 获取标签统计
GET /api/v1/cases/{case_id}/tags/stats
Response: {
    "total_tags": 25,
    "categories": {
        "metadata": 8,
        "cv": 5,
        "user": 2,
        "ai": 3,
        "custom": 7
    },
    "most_used": [
        {"name": "project", "category": "metadata", "count": 15},
        {"name": "重要", "category": "custom", "count": 8}
    ]
}
```

### 3. 标签缓存系统

#### 缓存文件结构
```
data/case_{id}/
├── Label_Cache/
│   ├── metadata_cache.json
│   ├── cv_cache.json
│   ├── user_cache.json
│   ├── ai_cache.json
│   ├── custom_cache.json
│   └── tag_index.json
└── deleted_files.json
```

#### 缓存更新逻辑
```python
class TagCacheManager:
    def update_cache(self, case_id: int, file_id: int, tags_data: dict):
        """更新标签缓存"""
        # 1. 检查文件是否在删除列表中
        # 2. 更新各类别的标签缓存
        # 3. 重建标签索引
        # 4. 更新统计信息
        
    def rebuild_cache(self, case_id: int):
        """重建整个案例的标签缓存"""
        # 1. 扫描所有文件的标签数据
        # 2. 排除已删除的文件
        # 3. 重新生成所有缓存文件
        
    def get_files_by_tag(self, case_id: int, category: str, name: str, value: str):
        """根据标签获取文件列表"""
        # 1. 检查缓存是否存在
        # 2. 过滤已删除的文件
        # 3. 返回文件ID列表
```

## 前端设计

### 1. 组件架构

#### 主要组件结构
```
TagManagementPage/
├── TagManagementPanel/
│   ├── TagSearchBox
│   ├── TagTreeView
│   ├── CustomTagManager
│   └── TagStatistics
├── TagGalleryPanel/
│   ├── GalleryHeader
│   ├── ImageGrid
│   ├── ImageModal
│   └── BatchActions
├── ResizablePanels/
│   ├── PanelDivider
│   └── PanelContainer
└── DragDropProvider/
    ├── DragPreview
    └── DropZone
```

#### 状态管理
```javascript
// 标签管理状态
const tagManagementState = {
    // 标签数据
    tagTree: {},
    customTags: [],
    selectedTags: [],
    searchQuery: '',
    
    // 画廊状态
    filteredFiles: [],
    selectedFiles: [],
    currentFile: null,
    
    // UI状态
    leftPanelWidth: 400,
    isLoading: false,
    dragState: null
};
```

### 2. 拖拽交互设计

#### 拖拽类型定义
```javascript
const DragTypes = {
    TAG: 'tag',
    FILE: 'file',
    CUSTOM_TAG: 'custom_tag'
};

// 拖拽数据结构
const dragData = {
    type: DragTypes.FILE,
    data: {
        fileIds: [1, 2, 3],
        sourceTag: 'metadata.project.春季拍摄'
    }
};
```

#### 拖拽交互逻辑
```javascript
class DragDropManager {
    // 文件拖拽到标签
    handleFilesToTag(fileIds, targetTag) {
        // 1. 验证目标标签类型
        // 2. 批量添加标签
        // 3. 更新UI状态
    }
    
    // 标签层级重组
    handleTagReorganize(sourceTag, targetParent) {
        // 1. 验证层级关系
        // 2. 更新标签结构
        // 3. 刷新标签树
    }
}
```

### 3. 响应式布局设计

#### 分栏布局
```css
.tag-management-layout {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

.tag-panel {
    min-width: 300px;
    max-width: 600px;
    flex-shrink: 0;
    border-right: 1px solid #e5e7eb;
}

.gallery-panel {
    flex: 1;
    min-width: 400px;
    overflow: hidden;
}

.panel-divider {
    width: 4px;
    cursor: col-resize;
    background: #f3f4f6;
    transition: background-color 0.2s;
}

.panel-divider:hover {
    background: #3b82f6;
}
```

#### 响应式断点
```css
/* 平板模式 */
@media (max-width: 1024px) {
    .tag-management-layout {
        flex-direction: column;
    }
    
    .tag-panel {
        max-height: 40vh;
        min-width: unset;
        max-width: unset;
    }
}

/* 移动模式 */
@media (max-width: 768px) {
    .tag-panel {
        position: fixed;
        top: 0;
        left: -100%;
        width: 80vw;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .tag-panel.open {
        left: 0;
    }
}
```

## 数据流设计

### 1. 标签数据流
```
文件上传 → 规则引擎 → 标签生成 → 缓存更新 → UI刷新
     ↓
质量分析 → 标签补充 → 缓存更新 → UI刷新
     ↓
用户操作 → 标签修改 → 缓存更新 → UI刷新
```

### 2. 缓存同步机制
```python
class TagSyncManager:
    async def sync_tag_changes(self, case_id: int, changes: list):
        """同步标签变更"""
        # 1. 批量处理标签变更
        # 2. 更新数据库
        # 3. 刷新缓存
        # 4. 通知前端更新
        
    async def handle_file_deletion(self, case_id: int, file_ids: list):
        """处理文件删除"""
        # 1. 添加到删除记录表
        # 2. 更新标签缓存（排除删除的文件）
        # 3. 清理空标签
        
    async def handle_file_restoration(self, case_id: int, file_ids: list):
        """处理文件恢复"""
        # 1. 从删除记录表移除
        # 2. 重新加入标签缓存
        # 3. 更新统计信息
```

## 错误处理设计

### 1. 后端错误处理
```python
class TagManagementError(Exception):
    """标签管理相关错误"""
    pass

class TagCacheError(TagManagementError):
    """标签缓存错误"""
    pass

class TagValidationError(TagManagementError):
    """标签验证错误"""
    pass

# 错误处理中间件
@app.exception_handler(TagManagementError)
async def tag_error_handler(request: Request, exc: TagManagementError):
    return JSONResponse(
        status_code=400,
        content={
            "error": "TAG_MANAGEMENT_ERROR",
            "message": str(exc),
            "timestamp": datetime.utcnow().isoformat()
        }
    )
```

### 2. 前端错误处理
```javascript
class TagErrorHandler {
    static handleApiError(error) {
        const errorMap = {
            'TAG_CACHE_ERROR': '标签缓存错误，请刷新页面重试',
            'TAG_VALIDATION_ERROR': '标签格式不正确',
            'NETWORK_ERROR': '网络连接失败，请检查网络'
        };
        
        const message = errorMap[error.code] || '操作失败，请重试';
        showNotification(message, 'error');
    }
    
    static handleDragError(error) {
        showNotification('拖拽操作失败：' + error.message, 'warning');
    }
}
```

## 性能优化设计

### 1. 后端性能优化
- 标签缓存预计算
- 数据库查询优化
- 批量操作支持
- 异步处理机制

### 2. 前端性能优化
- 虚拟滚动（大量图片）
- 图片懒加载
- 标签树虚拟化
- 防抖搜索

### 3. 缓存策略
- 标签数据本地缓存
- 图片缩略图缓存
- API响应缓存
- 增量更新机制

## 测试策略

### 1. 后端测试
- 标签API单元测试
- 缓存系统集成测试
- 并发操作测试
- 性能压力测试

### 2. 前端测试
- 组件单元测试
- 拖拽交互测试
- 响应式布局测试
- 用户体验测试

### 3. 端到端测试
- 完整标签管理流程测试
- 多用户并发测试
- 数据一致性测试
- 错误恢复测试