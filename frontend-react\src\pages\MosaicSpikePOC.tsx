// Project Novak - Spike A: react-mosaic POC
// 验证 react-mosaic 是否能替代自研布局系统

import React, { useState, useCallback } from 'react';
import {
  Mosaic,
  MosaicWindow,
  createBalancedTreeFromLeaves,
  updateTree,
  getNodeAtPath,
  getOtherDirection,
  getPathToCorner,
} from 'react-mosaic-component';
import type {
  MosaicNode,
  CreateNode,
  MosaicDirection,
} from 'react-mosaic-component';
import 'react-mosaic-component/react-mosaic-component.css';
import '../styles/mosaic-theme.css';

// ============================================================================
// Panel Definitions
// ============================================================================

type PanelId = 'catalog' | 'gallery' | 'workbench' | 'info';

const PANEL_CONFIGS = {
  catalog: {
    title: '📚 目录栏 (Catalog)',
    color: 'bg-blue-900',
    description: '标签管理、搜索筛选、档案库选择'
  },
  gallery: {
    title: '🖼️ 画廊 (Gallery)',
    color: 'bg-green-900',
    description: '图片展示、网格/列表视图、缩放控制'
  },
  workbench: {
    title: '🛠️ 工作台 (Workbench)',
    color: 'bg-purple-900',
    description: '剪贴板、图像簇整理、自由排布'
  },
  info: {
    title: 'ℹ️ 信息栏 (Info)',
    color: 'bg-orange-900',
    description: '文件详情、元数据、标签编辑'
  },
};

// ============================================================================
// Panel Components
// ============================================================================

interface PanelViewProps {
  panelId: PanelId;
  onTogglePanel?: (panelId: PanelId) => void;
}

const PanelView: React.FC<PanelViewProps> = ({ panelId, onTogglePanel }) => {
  const config = PANEL_CONFIGS[panelId];

  return (
    <div className={`w-full h-full flex flex-col ${config.color} text-white p-4`}>
      <div className="flex-1 flex flex-col items-center justify-center space-y-4">
        <h2 className="text-2xl font-bold text-center">{config.title}</h2>
        <p className="text-center text-gray-300 max-w-md">
          {config.description}
        </p>

        {/* 模拟内容 */}
        <div className="w-full max-w-md space-y-2">
          {panelId === 'catalog' && (
            <>
              <div className="bg-blue-800 p-2 rounded text-sm">🏷️ 城市摄影 (24)</div>
              <div className="bg-blue-800 p-2 rounded text-sm">🏷️ 自然风光 (18)</div>
              <div className="bg-blue-800 p-2 rounded text-sm">🏷️ 人像作品 (12)</div>
            </>
          )}

          {panelId === 'gallery' && (
            <div className="grid grid-cols-3 gap-2">
              {[1,2,3,4,5,6].map(i => (
                <div key={i} className="bg-green-800 aspect-square rounded flex items-center justify-center text-xs">
                  IMG {i}
                </div>
              ))}
            </div>
          )}

          {panelId === 'workbench' && (
            <>
              <div className="bg-purple-800 p-2 rounded text-sm">📋 剪贴板 (3 项)</div>
              <div className="bg-purple-800 p-2 rounded text-sm">🔗 图像簇 A</div>
              <div className="bg-purple-800 p-2 rounded text-sm">🔗 图像簇 B</div>
            </>
          )}

          {panelId === 'info' && (
            <>
              <div className="bg-orange-800 p-2 rounded text-sm">📄 sunset_city.jpg</div>
              <div className="bg-orange-800 p-2 rounded text-sm">📊 1920×1080, 2.1MB</div>
              <div className="bg-orange-800 p-2 rounded text-sm">🏷️ 城市, 日落, 建筑</div>
            </>
          )}
        </div>
      </div>

      {/* 面板控制 */}
      <div className="mt-4 flex justify-center space-x-2">
        <button
          className="px-3 py-1 bg-white bg-opacity-20 rounded text-xs hover:bg-opacity-30"
          onClick={() => onTogglePanel?.(panelId)}
        >
          切换显示
        </button>
      </div>
    </div>
  );
};

// ============================================================================
// Layout Configurations
// ============================================================================

// 四象限布局 (类似我们的 MainLayout)
const FOUR_QUADRANT_LAYOUT: MosaicNode<PanelId> = {
  direction: 'row',
  first: {
    direction: 'column',
    first: 'catalog',
    second: 'workbench',
    splitPercentage: 70, // 目录栏占上方70%
  },
  second: {
    direction: 'column',
    first: 'gallery',
    second: 'info',
    splitPercentage: 75, // 画廊占上方75%
  },
  splitPercentage: 25, // 左侧占25%
};

// 三面板布局 (隐藏工作台)
const THREE_PANEL_LAYOUT: MosaicNode<PanelId> = {
  direction: 'row',
  first: 'catalog',
  second: {
    direction: 'column',
    first: 'gallery',
    second: 'info',
    splitPercentage: 75,
  },
  splitPercentage: 25,
};

// 全屏画廊
const FULLSCREEN_GALLERY: MosaicNode<PanelId> = 'gallery';

// ============================================================================
// Main POC Component
// ============================================================================

export const MosaicSpikePOC: React.FC = () => {
  const [currentLayout, setCurrentLayout] = useState<MosaicNode<PanelId> | null>(FOUR_QUADRANT_LAYOUT);
  const [hiddenPanels, setHiddenPanels] = useState<Set<PanelId>>(new Set());
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});

  // 切换面板显示/隐藏
  const togglePanel = (panelId: PanelId) => {
    const newHidden = new Set(hiddenPanels);
    if (newHidden.has(panelId)) {
      newHidden.delete(panelId);
    } else {
      newHidden.add(panelId);
    }
    setHiddenPanels(newHidden);

    // 重新计算布局
    const visiblePanels = Object.keys(PANEL_CONFIGS).filter(
      id => !newHidden.has(id as PanelId)
    ) as PanelId[];

    if (visiblePanels.length === 0) {
      setCurrentLayout(null);
    } else if (visiblePanels.length === 1) {
      setCurrentLayout(visiblePanels[0]);
    } else {
      // 使用 react-mosaic 的自动平衡布局
      setCurrentLayout(createBalancedTreeFromLeaves(visiblePanels));
    }
  };

  // 预设布局切换
  const switchToLayout = (layout: MosaicNode<PanelId> | null) => {
    setCurrentLayout(layout);
    setHiddenPanels(new Set());
  };

  // 高级功能测试
  const runAdvancedTests = () => {
    const results: Record<string, boolean> = {};

    // 测试 1: 动态添加面板
    try {
      if (currentLayout) {
        const newLayout = updateTree(currentLayout, [{
          path: [],
          spec: {
            $set: {
              direction: 'row' as MosaicDirection,
              first: currentLayout,
              second: 'catalog',
              splitPercentage: 75,
            }
          }
        }]);
        results['动态添加面板'] = newLayout !== null;
      } else {
        results['动态添加面板'] = false;
      }
    } catch {
      results['动态添加面板'] = false;
    }

    // 测试 2: 路径查找
    try {
      if (currentLayout) {
        const path = getPathToCorner(currentLayout, 1); // 使用数字表示角落
        results['路径查找'] = path !== undefined;
      } else {
        results['路径查找'] = false;
      }
    } catch {
      results['路径查找'] = false;
    }

    // 测试 3: 节点操作
    try {
      if (currentLayout) {
        const node = getNodeAtPath(currentLayout, []);
        results['节点操作'] = node !== undefined;
      } else {
        results['节点操作'] = false;
      }
    } catch {
      results['节点操作'] = false;
    }

    // 测试 4: 方向切换
    try {
      const direction = getOtherDirection('row');
      results['方向切换'] = direction === 'column';
    } catch {
      results['方向切换'] = false;
    }

    // 测试 5: 平衡树创建
    try {
      const balancedTree = createBalancedTreeFromLeaves(['catalog', 'gallery', 'workbench']);
      results['平衡树创建'] = balancedTree !== null;
    } catch {
      results['平衡树创建'] = false;
    }

    setTestResults(results);
  };

  // 布局变化处理
  const handleLayoutChange = useCallback((newLayout: MosaicNode<PanelId> | null) => {
    setCurrentLayout(newLayout);
  }, []);

  // 创建新节点
  const createNode: CreateNode<PanelId> = useCallback(() => 'gallery', []);

  return (
    <div className="h-screen w-screen flex flex-col bg-gray-900">
      {/* 控制面板 */}
      <div className="flex-shrink-0 bg-gray-800 p-4 border-b border-gray-700 space-y-4">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-bold text-white">
            🔬 Spike A: react-mosaic 验证
          </h1>

          <div className="flex space-x-2">
            {/* 预设布局 */}
            <button
              className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              onClick={() => switchToLayout(FOUR_QUADRANT_LAYOUT)}
            >
              四象限
            </button>
            <button
              className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
              onClick={() => switchToLayout(THREE_PANEL_LAYOUT)}
            >
              三面板
            </button>
            <button
              className="px-3 py-1 bg-purple-600 text-white rounded text-sm hover:bg-purple-700"
              onClick={() => switchToLayout(FULLSCREEN_GALLERY)}
            >
              全屏画廊
            </button>

            {/* 高级测试 */}
            <button
              className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
              onClick={runAdvancedTests}
            >
              🧪 高级测试
            </button>

            {/* 面板切换 */}
            {Object.entries(PANEL_CONFIGS).map(([id, config]) => (
              <button
                key={id}
                className={`px-2 py-1 rounded text-xs ${
                  hiddenPanels.has(id as PanelId)
                    ? 'bg-gray-600 text-gray-400'
                    : 'bg-gray-700 text-white'
                }`}
                onClick={() => togglePanel(id as PanelId)}
              >
                {config.title.split(' ')[0]}
              </button>
            ))}
          </div>
        </div>

        {/* 测试结果显示 */}
        {Object.keys(testResults).length > 0 && (
          <div className="bg-gray-700 p-3 rounded">
            <h3 className="text-white font-medium mb-2">🧪 高级功能测试结果:</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {Object.entries(testResults).map(([test, passed]) => (
                <div key={test} className={`text-xs p-2 rounded ${passed ? 'bg-green-600' : 'bg-red-600'} text-white`}>
                  {passed ? '✅' : '❌'} {test}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Mosaic 布局区域 */}
      <div className="flex-1 overflow-hidden">
        {currentLayout ? (
          <Mosaic<PanelId>
            renderTile={(id, path) => (
              <MosaicWindow<PanelId>
                path={path}
                title={PANEL_CONFIGS[id].title}
                createNode={createNode}
                toolbarControls={[
                  // 自定义工具栏按钮
                  <button
                    key="toggle"
                    className="mosaic-default-control"
                    onClick={() => togglePanel(id)}
                    title={`隐藏 ${PANEL_CONFIGS[id].title}`}
                  >
                    ✕
                  </button>,
                  <button
                    key="info"
                    className="mosaic-default-control"
                    title={`${PANEL_CONFIGS[id].title} 信息`}
                  >
                    ℹ️
                  </button>
                ]}
              >
                <PanelView panelId={id} onTogglePanel={togglePanel} />
              </MosaicWindow>
            )}
            value={currentLayout}
            onChange={handleLayoutChange}
            className="mosaic-blueprint-theme"
          />
        ) : (
          <div className="h-full flex items-center justify-center bg-gray-900 text-white">
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-4">所有面板已隐藏</h2>
              <p className="text-gray-400">点击上方按钮显示面板</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
