# Mizzy Star PostgreSQL升级 - 生产部署指南

## 🎯 部署概述

本指南提供Mizzy Star PostgreSQL升级项目的完整生产部署流程，确保安全、高效地将系统部署到生产环境。

## 📋 部署前检查清单

### 环境要求
- [ ] **操作系统**: Linux (Ubuntu 20.04+ 推荐) 或 Windows Server
- [ ] **Docker**: 20.10+ 版本
- [ ] **Docker Compose**: 2.0+ 版本
- [ ] **内存**: 最少4GB，推荐8GB+
- [ ] **存储**: 最少20GB可用空间，推荐SSD
- [ ] **网络**: 稳定的网络连接，端口5432和8000可用

### 依赖检查
- [ ] **Python**: 3.8+ 版本
- [ ] **Git**: 用于代码部署
- [ ] **备份工具**: 确保有数据备份方案
- [ ] **监控工具**: 系统和应用监控

## 🚀 快速部署流程

### 第一步：代码部署

```bash
# 1. 克隆项目代码
git clone <repository-url> mizzy_star_v0.3
cd mizzy_star_v0.3

# 2. 切换到PostgreSQL分支
git checkout feature/postgresql-upgrade

# 3. 验证代码完整性
ls -la postgresql-upgrade/
ls -la backend/
ls -la tests/
```

### 第二步：PostgreSQL环境部署

```bash
# 1. 进入Docker配置目录
cd postgresql-upgrade/docker

# 2. 配置环境变量
cp .env.example .env
# 编辑.env文件，设置安全的密码

# 3. 启动PostgreSQL服务
docker-compose up -d

# 4. 验证服务状态
docker-compose ps
docker-compose logs postgres
```

### 第三步：数据库初始化

```bash
# 1. 进入工具目录
cd ../tools

# 2. 初始化数据库Schema
python db_initializer.py \
  --password "your_secure_password" \
  --host "localhost" \
  --create-schema

# 3. 验证部署
python db_initializer.py \
  --password "your_secure_password" \
  --host "localhost" \
  --verify-only
```

### 第四步：应用服务配置

```bash
# 1. 进入后端目录
cd ../../backend

# 2. 配置环境变量
cp .env.example .env

# 编辑.env文件：
# MASTER_DB_TYPE=postgresql
# CASE_DB_TYPE=postgresql
# POSTGRES_HOST=localhost
# POSTGRES_PORT=5432
# POSTGRES_USER=mizzy_user
# POSTGRES_PASSWORD=your_secure_password
# POSTGRES_DB=mizzy_main

# 3. 安装Python依赖
pip install -r requirements.txt

# 4. 启动应用服务
python -m uvicorn src.main:app --host 0.0.0.0 --port 8000
```

### 第五步：验证部署

```bash
# 1. 运行完整测试套件
cd ../tests
python run_all_tests.py --base-url http://localhost:8000

# 2. 运行性能验证
python simple_postgresql_test.py

# 3. 检查服务健康状态
curl http://localhost:8000/health
```

## 🔧 详细配置说明

### PostgreSQL配置优化

#### docker-compose.yml关键配置
```yaml
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: mizzy_main
      POSTGRES_USER: mizzy_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c work_mem=4MB
      -c maintenance_work_mem=64MB
```

#### 生产环境PostgreSQL调优
```sql
-- 性能优化配置
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;

-- 重启PostgreSQL使配置生效
SELECT pg_reload_conf();
```

### 应用服务配置

#### 环境变量配置
```bash
# 数据库配置
MASTER_DB_TYPE=postgresql
CASE_DB_TYPE=postgresql
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=mizzy_user
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_DB=mizzy_main

# 应用配置
DEBUG=false
DB_ECHO_SQL=false
LOG_LEVEL=INFO

# 缓存配置
CACHE_ENABLED=true
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# 性能配置
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
```

#### 系统服务配置 (systemd)
```ini
# /etc/systemd/system/mizzy-star.service
[Unit]
Description=Mizzy Star Application
After=network.target postgresql.service

[Service]
Type=simple
User=mizzy
WorkingDirectory=/opt/mizzy_star/backend
Environment=PATH=/opt/mizzy_star/venv/bin
ExecStart=/opt/mizzy_star/venv/bin/python -m uvicorn src.main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

## 📊 监控和维护

### 性能监控

#### 数据库监控
```sql
-- 查询性能监控
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 10;

-- 索引使用情况
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read
FROM pg_stat_user_indexes 
ORDER BY idx_scan DESC;

-- 缓存命中率
SELECT 
    datname,
    blks_read,
    blks_hit,
    round(blks_hit::float/(blks_read+blks_hit)*100, 2) as cache_hit_ratio
FROM pg_stat_database 
WHERE datname = 'mizzy_main';
```

#### 应用监控
```python
# 健康检查端点
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "database": "connected",
        "timestamp": datetime.now().isoformat()
    }

# 性能指标端点
@app.get("/metrics")
async def metrics():
    return {
        "query_count": get_query_count(),
        "avg_response_time": get_avg_response_time(),
        "cache_hit_rate": get_cache_hit_rate()
    }
```

### 日志管理

#### 应用日志配置
```python
import logging
from logging.handlers import RotatingFileHandler

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        RotatingFileHandler('logs/mizzy_star.log', maxBytes=10485760, backupCount=5),
        logging.StreamHandler()
    ]
)
```

#### PostgreSQL日志配置
```sql
-- 启用慢查询日志
ALTER SYSTEM SET log_min_duration_statement = 1000;  -- 记录超过1秒的查询
ALTER SYSTEM SET log_statement = 'mod';  -- 记录修改语句
ALTER SYSTEM SET log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h ';
SELECT pg_reload_conf();
```

## 🔒 安全配置

### 数据库安全

#### 用户权限配置
```sql
-- 创建只读用户
CREATE USER mizzy_readonly WITH PASSWORD 'readonly_password';
GRANT CONNECT ON DATABASE mizzy_main TO mizzy_readonly;
GRANT USAGE ON SCHEMA public TO mizzy_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO mizzy_readonly;

-- 创建备份用户
CREATE USER mizzy_backup WITH PASSWORD 'backup_password';
GRANT CONNECT ON DATABASE mizzy_main TO mizzy_backup;
GRANT USAGE ON SCHEMA public TO mizzy_backup;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO mizzy_backup;
```

#### 网络安全
```bash
# 防火墙配置
sudo ufw allow 22/tcp      # SSH
sudo ufw allow 8000/tcp    # 应用服务
sudo ufw allow from 10.0.0.0/8 to any port 5432  # 内网PostgreSQL访问
sudo ufw enable
```

### 应用安全

#### HTTPS配置 (使用Nginx)
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 💾 备份和恢复

### 数据库备份

#### 自动备份脚本
```bash
#!/bin/bash
# backup_database.sh

BACKUP_DIR="/opt/backups/mizzy_star"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="mizzy_main_backup_$DATE.sql"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
docker exec mizzy_postgres pg_dump -U mizzy_user mizzy_main > $BACKUP_DIR/$BACKUP_FILE

# 压缩备份文件
gzip $BACKUP_DIR/$BACKUP_FILE

# 清理7天前的备份
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete

echo "Backup completed: $BACKUP_FILE.gz"
```

#### 定时备份 (crontab)
```bash
# 每天凌晨2点执行备份
0 2 * * * /opt/scripts/backup_database.sh >> /var/log/mizzy_backup.log 2>&1
```

### 数据恢复

#### 恢复流程
```bash
# 1. 停止应用服务
sudo systemctl stop mizzy-star

# 2. 恢复数据库
gunzip -c /opt/backups/mizzy_star/mizzy_main_backup_YYYYMMDD_HHMMSS.sql.gz | \
docker exec -i mizzy_postgres psql -U mizzy_user mizzy_main

# 3. 验证数据完整性
python postgresql-upgrade/tools/db_initializer.py --verify-only

# 4. 重启应用服务
sudo systemctl start mizzy-star
```

## 🚨 故障排除

### 常见问题

#### 1. PostgreSQL连接失败
```bash
# 检查服务状态
docker-compose ps
docker-compose logs postgres

# 检查网络连接
telnet localhost 5432

# 检查配置文件
cat postgresql-upgrade/docker/.env
```

#### 2. 应用启动失败
```bash
# 检查环境变量
cat backend/.env

# 检查Python依赖
pip list | grep -E "(psycopg2|sqlalchemy|fastapi)"

# 查看详细错误
python -m uvicorn src.main:app --reload --log-level debug
```

#### 3. 性能问题
```sql
-- 检查慢查询
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 5;

-- 检查索引使用
SELECT schemaname, tablename, indexname, idx_scan 
FROM pg_stat_user_indexes 
WHERE idx_scan = 0;
```

### 紧急联系信息
- **技术支持**: [技术团队联系方式]
- **运维团队**: [运维团队联系方式]
- **项目负责人**: [项目负责人联系方式]

## ✅ 部署验证清单

部署完成后，请逐项验证以下内容：

- [ ] PostgreSQL服务正常运行
- [ ] 数据库Schema完整部署
- [ ] 所有索引创建成功
- [ ] 应用服务正常启动
- [ ] API接口响应正常
- [ ] 性能测试通过
- [ ] 监控系统正常
- [ ] 备份机制工作
- [ ] 日志记录正常
- [ ] 安全配置生效

**部署完成标志**: 所有验证项目通过 ✅

---

**文档版本**: v1.0  
**最后更新**: 2024年7月21日  
**适用版本**: Mizzy Star PostgreSQL升级 v1.0
