# src/crud/file_crud.py
"""
文件相关的CRUD操作 - PostgreSQL单一数据库架构
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_
from sqlalchemy.orm.attributes import flag_modified
import logging
from copy import deepcopy

from .. import models, schemas
from ..database_config import db_config, DatabaseType

logger = logging.getLogger(__name__)


def create_file_for_case(db: Session, case_id: int, file: schemas.FileCreate) -> Optional[models.File]:
    """
    为指定的活跃案例创建文件记录 - PostgreSQL单一数据库架构
    """
    try:
        # 验证案例存在且为活跃状态
        db_case = db.query(models.Case).filter(
            models.Case.id == case_id,
            models.Case.status == models.CaseStatus.ACTIVE
        ).first()

        if not db_case:
            logger.error(f"案例 {case_id} 不存在或不是活跃状态")
            return None

        # 创建文件记录
        file_data = file.model_dump()
        file_data['case_id'] = case_id  # 设置case_id外键

        db_file = models.File(**file_data)
        db.add(db_file)
        db.commit()
        db.refresh(db_file)

        logger.info(f"✅ 文件已创建: {db_file.file_name}, ID={db_file.id}, Case={case_id}")
        return db_file

    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"❌ 创建文件记录失败: {e}")
        return None


def get_files_for_case(db: Session, case_id: int) -> List[models.File]:
    """
    获取指定案例的所有文件 - PostgreSQL单一数据库架构
    """
    try:
        # 验证案例存在且为活跃状态
        db_case = db.query(models.Case).filter(
            models.Case.id == case_id,
            models.Case.status == models.CaseStatus.ACTIVE
        ).first()

        if not db_case:
            logger.warning(f"⚠️ 案例不存在或不活跃: ID={case_id}")
            return []

        # 查询案例的所有文件
        files = db.query(models.File)\
                 .filter(models.File.case_id == case_id)\
                 .order_by(models.File.created_at.desc())\
                 .all()

        logger.info(f"✅ 获取案例文件: Case={case_id}, 文件数={len(files)}")
        return files

    except SQLAlchemyError as e:
        logger.error(f"❌ 获取案例文件失败: Case={case_id}, 错误={e}")
        return []

# 为了兼容性，添加别名
get_files_by_case = get_files_for_case


def get_file(db: Session, file_id: int, case_id: int) -> Optional[models.File]:
    """
    获取指定案例中的单个文件 - PostgreSQL单一数据库架构
    """
    try:
        file = db.query(models.File)\
                .filter(models.File.id == file_id)\
                .filter(models.File.case_id == case_id)\
                .first()

        if file:
            logger.info(f"✅ 获取文件: ID={file_id}, Case={case_id}")
        else:
            logger.warning(f"⚠️ 文件不存在: ID={file_id}, Case={case_id}")

        return file

    except SQLAlchemyError as e:
        logger.error(f"❌ 获取文件失败: ID={file_id}, Case={case_id}, 错误={e}")
        return None


def update_file(db: Session, file_id: int, case_id: int, file_update: dict) -> Optional[models.File]:
    """
    更新文件信息 - PostgreSQL单一数据库架构
    """
    try:
        file = get_file(db, file_id, case_id)
        if not file:
            return None

        # 更新字段
        for field, value in file_update.items():
            if hasattr(file, field):
                setattr(file, field, value)

        db.commit()
        db.refresh(file)

        logger.info(f"✅ 文件已更新: ID={file_id}, Case={case_id}")
        return file

    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"❌ 更新文件失败: ID={file_id}, Case={case_id}, 错误={e}")
        return None


def delete_file(db: Session, file_id: int, case_id: int) -> bool:
    """
    删除指定案例的特定文件 - PostgreSQL单一数据库架构
    """
    try:
        file = get_file(db, file_id, case_id)
        if not file:
            logger.warning(f"⚠️ 要删除的文件不存在: ID={file_id}, Case={case_id}")
            return False

        # 删除文件记录
        db.delete(file)
        db.commit()

        logger.info(f"✅ 文件已删除: ID={file_id}, Case={case_id}")
        return True

    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"❌ 删除文件失败: ID={file_id}, Case={case_id}, 错误={e}")
        return False


def batch_delete_files(db: Session, case_id: int, file_ids: List[int]) -> dict:
    """
    批量删除文件 - PostgreSQL单一数据库架构
    """
    result = {
        'deleted': 0,
        'failed': 0,
        'errors': []
    }

    try:
        for file_id in file_ids:
            if delete_file(db, file_id, case_id):
                result['deleted'] += 1
            else:
                result['failed'] += 1
                result['errors'].append(f"删除文件 {file_id} 失败")

        logger.info(f"✅ 批量删除完成: Case={case_id}, 成功={result['deleted']}, 失败={result['failed']}")
        return result

    except Exception as e:
        logger.error(f"❌ 批量删除失败: Case={case_id}, 错误={e}")
        result['failed'] = len(file_ids)
        result['errors'].append(str(e))
        return result


def soft_delete_file(db: Session, case_id: int, file_id: int) -> bool:
    """
    软删除文件：保留缩略图到回收站，删除数据库记录 - PostgreSQL单一数据库架构
    """
    import os
    import shutil
    import json
    from pathlib import Path
    from datetime import datetime

    try:
        logger.info(f"开始软删除文件: ID={file_id}, Case={case_id}")

        # 获取文件信息
        file = get_file(db, file_id, case_id)
        if not file:
            logger.warning(f"⚠️ 要软删除的文件不存在: ID={file_id}, Case={case_id}")
            return False

        logger.info(f"找到文件: {file.file_name}, 路径: {file.file_path}")

        # 创建案例目录下的trash文件夹
        from ..database import DATA_DIR
        case_dir = DATA_DIR / f"case_{case_id}"
        case_dir.mkdir(exist_ok=True)
        trash_dir = case_dir / "trash"
        trash_dir.mkdir(exist_ok=True)

        logger.info(f"创建回收站目录: {trash_dir}")

        # 移动缩略图到trash文件夹（保留原始图片不动）
        if file.thumbnail_small_path and os.path.exists(file.thumbnail_small_path):
            thumbnail_name = Path(file.thumbnail_small_path).name
            trash_thumbnail_path = trash_dir / thumbnail_name

            try:
                shutil.copy2(file.thumbnail_small_path, trash_thumbnail_path)
                logger.info(f"缩略图已复制到回收站: {trash_thumbnail_path}")
            except Exception as e:
                logger.warning(f"复制缩略图到回收站失败: {e}")

        # 创建删除记录文件（记录删除的文件信息）
        delete_record = {
            "file_id": file.id,
            "file_name": file.file_name,
            "file_type": file.file_type,
            "file_path": file.file_path,
            "thumbnail_small_path": file.thumbnail_small_path,
            "width": file.width,
            "height": file.height,
            "created_at": file.created_at.isoformat() if file.created_at else None,
            "taken_at": file.taken_at.isoformat() if file.taken_at else None,
            "deleted_at": datetime.utcnow().isoformat(),
            "quality_score": file.quality_score,
            "tags": file.tags,
            "case_id": file.case_id
        }

        # 保存删除记录到JSON文件
        record_file = trash_dir / f"deleted_file_{file_id}.json"
        with open(record_file, 'w', encoding='utf-8') as f:
            json.dump(delete_record, f, ensure_ascii=False, indent=2)

        logger.info(f"删除记录已保存: {record_file}")

        # 从数据库中删除文件记录
        db.delete(file)
        db.commit()

        logger.info(f"✅ 文件软删除完成: ID={file_id}, 缩略图保存在回收站")
        return True

    except Exception as e:
        db.rollback()
        logger.error(f"❌ 软删除文件失败: ID={file_id}, Case={case_id}, 错误={e}")
        return False


def update_file_custom_tags(
    db: Session,
    file_id: int,
    case_id: int,
    new_tags: Dict[str, str],
    overwrite_existing: bool = False
) -> bool:
    """
    专用的、健壮的标签更新函数

    这个函数专门用于安全地更新文件的自定义标签，避免SQLAlchemy的变更检测盲点。

    Args:
        db: 数据库会话
        file_id: 文件ID
        case_id: 案例ID
        new_tags: 要添加的新标签字典，格式: {"标签名": "标签值"}
        overwrite_existing: 是否覆盖已存在的标签

    Returns:
        bool: 是否成功更新
    """
    try:
        logger.info(f"🏷️  开始更新文件 {file_id} 的自定义标签: {new_tags}")

        # 1. 获取文件对象
        file_record = db.query(models.File).filter(
            models.File.id == file_id,
            models.File.case_id == case_id
        ).first()

        if not file_record:
            logger.error(f"❌ 文件 {file_id} 不存在或不属于案例 {case_id}")
            return False

        logger.info(f"✅ 找到文件: {file_record.file_name}")
        logger.info(f"📋 当前标签结构: {file_record.tags}")

        # 2. 创建标签的深拷贝，避免原地修改
        updated_tags = deepcopy(file_record.tags or {})

        # 3. 确保基本结构存在
        if 'tags' not in updated_tags:
            updated_tags['tags'] = {}
        if 'user' not in updated_tags['tags']:
            updated_tags['tags']['user'] = []
        if 'custom' not in updated_tags['tags']:
            updated_tags['tags']['custom'] = {}

        logger.info(f"🏗️  初始化后的标签结构: {updated_tags}")

        # 4. 安全地添加新标签到custom字段
        custom_tags = updated_tags['tags']['custom']
        added_tags = []

        for tag_name, tag_value in new_tags.items():
            if tag_name in custom_tags and not overwrite_existing:
                logger.info(f"⏭️  跳过已存在的标签: {tag_name} = {custom_tags[tag_name]}")
                continue

            custom_tags[tag_name] = tag_value
            added_tags.append(f"{tag_name}:{tag_value}")
            logger.info(f"✅ 添加标签到custom: {tag_name} = {tag_value}")

            # 同时添加到user数组中（如果不存在）
            tag_entry = f"{tag_name}:{tag_value}"
            if tag_entry not in updated_tags['tags']['user']:
                updated_tags['tags']['user'].append(tag_entry)
                logger.info(f"✅ 添加标签到user: {tag_entry}")

        if not added_tags:
            logger.info(f"ℹ️  没有新标签需要添加")
            return True

        logger.info(f"🎯 最终标签结构: {updated_tags}")

        # 5. 关键步骤：正确触发SQLAlchemy的变更检测
        file_record.tags = updated_tags
        flag_modified(file_record, 'tags')

        logger.info(f"🔄 已标记tags字段为已修改，准备提交到数据库")

        # 6. 提交更改
        db.commit()
        db.refresh(file_record)

        logger.info(f"💾 成功更新文件 {file_id} 的标签，添加了 {len(added_tags)} 个新标签: {added_tags}")
        logger.info(f"📋 更新后的标签: {file_record.tags}")

        return True

    except Exception as e:
        db.rollback()
        logger.error(f"❌ 更新文件 {file_id} 标签时出错: {e}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return False