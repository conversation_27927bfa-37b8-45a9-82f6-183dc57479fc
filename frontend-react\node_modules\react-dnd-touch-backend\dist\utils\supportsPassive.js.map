{"version": 3, "sources": ["../../src/utils/supportsPassive.ts"], "sourcesContent": ["export const supportsPassive = ((): boolean => {\n\t// simular to <PERSON><PERSON><PERSON><PERSON>'s test\n\tlet supported = false\n\ttry {\n\t\taddEventListener(\n\t\t\t'test',\n\t\t\t() => {\n\t\t\t\t// do nothing\n\t\t\t},\n\t\t\tObject.defineProperty({}, 'passive', {\n\t\t\t\tget() {\n\t\t\t\t\tsupported = true\n\t\t\t\t\treturn true\n\t\t\t\t},\n\t\t\t}),\n\t\t)\n\t} catch (e) {\n\t\t// do nothing\n\t}\n\treturn supported\n})()\n"], "names": ["supportsPassive", "supported", "addEventListener", "Object", "defineProperty", "get", "e"], "mappings": "AAAA,OAAO,MAAMA,eAAe,GAAG,CAAC,IAAe;IAC9C,2BAA2B;IAC3B,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAI;QACHC,gBAAgB,CACf,MAAM,EACN,IAAM;QACL,aAAa;SACb,EACDC,MAAM,CAACC,cAAc,CAAC,EAAE,EAAE,SAAS,EAAE;YACpCC,GAAG,IAAG;gBACLJ,SAAS,GAAG,IAAI;gBAChB,OAAO,IAAI,CAAA;aACX;SACD,CAAC,CACF;KACD,CAAC,OAAOK,CAAC,EAAE;IACX,aAAa;KACb;IACD,OAAOL,SAAS,CAAA;CAChB,CAAC,EAAE,CAAA"}