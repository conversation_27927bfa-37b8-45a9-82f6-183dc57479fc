{"name": "react-mosaic-component", "version": "6.1.1", "description": "A React Tiling Window Manager", "license": "Apache-2.0", "main": "lib/index.js", "style": "lib/react-mosaic.css", "type": "commonjs", "typings": "lib/index.d.ts", "repository": {"type": "git", "url": "https://github.com/nomcopter/react-mosaic.git"}, "funding": "https://github.com/nomcopter/react-mosaic?sponsor=1", "keywords": ["ui", "react", "component", "typescript", "tiling-window-manager", "window-manager"], "sideEffects": ["*.css"], "scripts": {"build": "npm-run-all clean -lp build:**", "build:ts": "tsc -p tsconfig-build.json", "build:less": "lessc --autoprefix=defaults styles/index.less react-mosaic-component.css", "bundle": "rm -rf docs/ && webpack --config webpack/bundle.ts", "clean": "rm -rf lib/", "start": "webpack-dev-server --config webpack/hot.ts", "prettier:run": "prettier 'styles/*.less' '*.md' '{,.}*.yml' '.circleci/*.yml'", "test": "npm-run-all build -lp test:**", "test:lint": "tslint -c tslint.yml -p tsconfig.json -e test", "test:unit": "mocha", "test:format": "yarn run prettier:run --list-different", "fix": "npm-run-all -lp fix:**", "fix:format": "yarn run prettier:run --write", "fix:lint": "yarn run test:lint --fix", "version": "npm-run-all test bundle && git add -A docs/"}, "dependencies": {"classnames": "^2.3.2", "immutability-helper": "^3.1.1", "lodash": "^4.17.21", "prop-types": "^15.8.1", "rdndmb-html5-to-touch": "^8.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-multi-backend": "^8.0.0", "react-dnd-touch-backend": "^16.0.1", "uuid": "^9.0.0"}, "devDependencies": {"@blueprintjs/core": "^4.15.1", "@blueprintjs/icons": "^4.13.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.10", "@types/chai": "^4.3.0", "@types/classnames": "^2.3.1", "@types/dom4": "^2.0.2", "@types/lodash": "^4.14.191", "@types/mocha": "^7.0.2", "@types/prop-types": "^15.7.5", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.10", "@types/uuid": "^9.0.0", "@types/webpack": "^5.28.0", "chai": "^4.3.6", "css-loader": "^6.7.3", "dnd-core": "16.0.1", "html-loader": "^4.2.0", "html-webpack-plugin": "^5.5.0", "jsdom": "^15.2.1", "jsdom-global": "^3.0.2", "less": "^3.13.1", "less-loader": "^11.1.0", "less-plugin-autoprefix": "^2.0.0", "mocha": "^6.2.3", "mocha-junit-reporter": "^1.23.3", "mock-require": "^3.0.3", "npm-run-all": "^4.1.5", "prettier": "^2.8.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-refresh": "^0.14.0", "source-map-loader": "^4.0.1", "style-loader": "^3.3.1", "ts-loader": "^9.4.2", "ts-node": "^10.9.1", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "tslint-plugin-prettier": "^2.3.0", "tslint-react": "^5.0.0", "typescript": "^4.9.5", "webpack": "^5.75.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.11.1", "yarn-deduplicate": "^6.0.1"}, "peerDependencies": {"react": ">=16"}}