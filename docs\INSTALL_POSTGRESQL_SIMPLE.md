# 🐘 PostgreSQL简易安装指南

## 🎯 目标
为mizzy_star项目安装和配置PostgreSQL数据库

## 📥 步骤1: 下载PostgreSQL

1. **打开浏览器**，访问: https://www.postgresql.org/download/windows/
2. **点击** "Download the installer" 按钮
3. **选择版本**: PostgreSQL 16.x (推荐最新稳定版)
4. **选择架构**: Windows x86-64
5. **下载文件**: 大约350MB，文件名类似 `postgresql-16.1-1-windows-x64.exe`

## 🔧 步骤2: 安装PostgreSQL

### 2.1 运行安装程序
1. **右键点击**下载的安装程序
2. **选择**"以管理员身份运行"
3. 如果出现UAC提示，点击"是"

### 2.2 安装向导设置

| 步骤 | 设置 | 推荐值 |
|------|------|--------|
| 安装目录 | Installation Directory | `C:\Program Files\PostgreSQL\16` (默认) |
| 组件选择 | Select Components | **全部勾选** ✅ |
| 数据目录 | Data Directory | `C:\Program Files\PostgreSQL\16\data` (默认) |
| 超级用户密码 | Superuser Password | `mizzy_star_2025` |
| 端口 | Port | `5432` (默认) |
| 区域设置 | Advanced Options | `[Default locale]` (默认) |

### 2.3 重要组件说明
确保以下组件被勾选：
- ✅ **PostgreSQL Server** - 数据库服务器
- ✅ **pgAdmin 4** - 图形管理工具
- ✅ **Stack Builder** - 扩展管理器
- ✅ **Command Line Tools** - 命令行工具

## ✅ 步骤3: 验证安装

### 3.1 检查服务状态
1. 按 `Win + R`，输入 `services.msc`，回车
2. 查找 `postgresql-x64-16` 服务
3. 确认状态为 **"正在运行"**

### 3.2 测试命令行工具
打开**命令提示符**或**PowerShell**：

```bash
# 检查PostgreSQL版本
psql --version

# 如果提示"命令不存在"，需要添加到PATH环境变量
```

### 3.3 添加到PATH环境变量 (如果需要)
1. 右键"此电脑" → "属性" → "高级系统设置"
2. 点击"环境变量"
3. 在"系统变量"中找到"Path"，点击"编辑"
4. 点击"新建"，添加: `C:\Program Files\PostgreSQL\16\bin`
5. 点击"确定"保存
6. **重新打开**命令提示符

## 🗄️ 步骤4: 创建mizzy_star数据库

### 4.1 连接到PostgreSQL
```bash
psql -U postgres -h localhost
# 输入密码: mizzy_star_2025
```

### 4.2 创建数据库和用户
在PostgreSQL命令行中执行：

```sql
-- 创建数据库
CREATE DATABASE mizzy_star_db;

-- 创建专用用户 (可选)
CREATE USER mizzy_user WITH PASSWORD 'mizzy_star_2025';

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE mizzy_star_db TO mizzy_user;

-- 连接到新数据库
\c mizzy_star_db

-- 安装向量扩展 (AI功能支持)
CREATE EXTENSION IF NOT EXISTS vector;

-- 查看已安装的扩展
\dx

-- 退出
\q
```

## 🐍 步骤5: 安装Python依赖

### 5.1 进入backend目录
```bash
cd C:\Users\<USER>\mizzy_star_v0.3\backend
```

### 5.2 安装PostgreSQL Python包
```bash
# 安装PostgreSQL适配器
pip install psycopg2-binary

# 安装向量支持
pip install pgvector

# 安装SQLAlchemy PostgreSQL支持
pip install sqlalchemy[postgresql]
```

### 5.3 运行自动配置脚本
```bash
python install_postgresql_deps.py
```

## ⚙️ 步骤6: 配置mizzy_star项目

### 6.1 创建环境变量文件
在项目根目录 `C:\Users\<USER>\mizzy_star_v0.3\` 创建 `.env` 文件：

```bash
# PostgreSQL配置
USE_POSTGRESQL=true
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=mizzy_star_2025
POSTGRES_DB=mizzy_star_db
```

### 6.2 初始化数据库
```bash
cd backend
python init_database.py --reset
```

### 6.3 运行测试
```bash
python test_postgresql_migration.py
```

### 6.4 启动服务
```bash
python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
```

## 🎉 完成验证

如果看到以下输出，说明安装成功：

```
✅ 数据库连接测试成功
✅ 案例管理功能正常
✅ 文件管理功能正常
✅ API服务正常运行 (http://localhost:8000)
🎉 所有测试通过！PostgreSQL架构迁移成功！
```

## 🛠️ 常见问题

### Q1: psql命令不存在
**A**: 需要将PostgreSQL bin目录添加到PATH环境变量

### Q2: 连接被拒绝
**A**: 检查PostgreSQL服务是否运行，端口是否正确

### Q3: 密码认证失败
**A**: 确认密码正确，检查用户是否存在

### Q4: 扩展安装失败
**A**: 确认PostgreSQL版本支持，或跳过向量扩展

## 📞 获取帮助

如果遇到问题：
1. 检查PostgreSQL日志文件
2. 查看Windows事件查看器
3. 确认防火墙设置
4. 重启PostgreSQL服务

## 🚀 下一步

PostgreSQL安装完成后，您可以：
1. 使用pgAdmin 4图形界面管理数据库
2. 开发基于PostgreSQL的高性能应用
3. 利用向量搜索功能实现AI图像检索
4. 享受企业级数据库的稳定性和性能

**安装完成！开始使用PostgreSQL驱动的mizzy_star项目吧！** 🎊
