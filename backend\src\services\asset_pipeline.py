"""
可扩展的资产处理流水线
支持图像预处理功能的动态扩展
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable, Type, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import json
from pathlib import Path

from PIL import Image, ImageEnhance, ImageOps
import cv2
import numpy as np

from ..database import get_independent_db
from ..models import File
from ..services.search_cache import SearchCache

logger = logging.getLogger(__name__)

class ProcessingStage(Enum):
    """处理阶段"""
    PRE_IMPORT = "pre_import"      # 导入前处理
    POST_IMPORT = "post_import"    # 导入后处理
    ON_DEMAND = "on_demand"        # 按需处理
    BATCH = "batch"                # 批量处理

class ProcessingPriority(Enum):
    """处理优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class ProcessingContext:
    """处理上下文"""
    file_id: int
    file_path: str
    case_id: int
    stage: ProcessingStage
    priority: ProcessingPriority = ProcessingPriority.NORMAL
    metadata: Dict[str, Any] = field(default_factory=dict)
    cache_key: Optional[str] = None

class ProcessingResult:
    """处理结果"""
    def __init__(self, success: bool, data: Any = None, error: str = None,
                 cache_invalidation: List[str] = None):
        self.success = success
        self.data = data
        self.error = error
        self.cache_invalidation = cache_invalidation or []
        self.timestamp = datetime.now()

class BaseProcessor(ABC):
    """处理器基类"""

    def __init__(self, name: str, version: str = "1.0.0"):
        self.name = name
        self.version = version
        self.enabled = True
        self.cache_manager = SearchCache()

    @abstractmethod
    async def process(self, context: ProcessingContext) -> ProcessingResult:
        """处理文件"""
        pass

    @abstractmethod
    def get_supported_stages(self) -> List[ProcessingStage]:
        """获取支持的处理阶段"""
        pass

    @abstractmethod
    def get_supported_formats(self) -> List[str]:
        """获取支持的文件格式"""
        pass

    def can_process(self, context: ProcessingContext) -> bool:
        """检查是否可以处理该文件"""
        if not self.enabled:
            return False

        # 检查阶段
        if context.stage not in self.get_supported_stages():
            return False

        # 检查格式
        file_ext = Path(context.file_path).suffix.lower()
        if file_ext not in self.get_supported_formats():
            return False

        return True

    async def invalidate_cache(self, context: ProcessingContext, patterns: List[str]):
        """使缓存失效"""
        # 清除相关的缓存类型
        self.cache_manager.invalidate_pattern("file_search")
        self.cache_manager.invalidate_pattern("thumbnail_cache")

class ImageInversionProcessor(BaseProcessor):
    """图像反相处理器"""

    def __init__(self):
        super().__init__("image_inversion", "1.0.0")

    async def process(self, context: ProcessingContext) -> ProcessingResult:
        """执行图像反相"""
        try:
            logger.info(f"开始图像反相处理: {context.file_path}")

            # 生成输出路径
            input_path = Path(context.file_path)
            output_path = input_path.parent / f"{input_path.stem}_inverted{input_path.suffix}"

            # 执行反相处理
            with Image.open(context.file_path) as img:
                # 转换为RGB模式
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # 执行反相
                inverted_img = ImageOps.invert(img)

                # 保存结果
                inverted_img.save(output_path, quality=95)

            # 更新数据库记录
            with get_independent_db() as db:
                file_record = db.query(File).filter(File.id == context.file_id).first()
                if file_record:
                    # 更新处理历史
                    processing_history = file_record.tags.get('processing_history', []) if file_record.tags else []
                    processing_history.append({
                        'processor': self.name,
                        'version': self.version,
                        'timestamp': datetime.now().isoformat(),
                        'operation': 'inversion',
                        'output_path': str(output_path)
                    })

                    # 更新tags字段
                    if not file_record.tags:
                        file_record.tags = {}
                    file_record.tags['processing_history'] = processing_history

                    db.commit()

            cache_patterns = [f"file_search_*_{context.case_id}_*", f"thumbnail_{context.file_id}_*"]

            return ProcessingResult(
                success=True,
                data={'output_path': str(output_path)},
                cache_invalidation=cache_patterns
            )

        except Exception as e:
            logger.error(f"图像反相处理失败: {e}")
            return ProcessingResult(success=False, error=str(e))

    def get_supported_stages(self) -> List[ProcessingStage]:
        return [ProcessingStage.POST_IMPORT, ProcessingStage.ON_DEMAND, ProcessingStage.BATCH]

    def get_supported_formats(self) -> List[str]:
        return ['.jpg', '.jpeg', '.png', '.tiff', '.bmp']

class ColorCorrectionProcessor(BaseProcessor):
    """颜色校正处理器"""

    def __init__(self):
        super().__init__("color_correction", "1.0.0")

    async def process(self, context: ProcessingContext) -> ProcessingResult:
        """执行颜色校正"""
        try:
            logger.info(f"开始颜色校正处理: {context.file_path}")

            # 获取校正参数
            brightness = context.metadata.get('brightness', 1.0)
            contrast = context.metadata.get('contrast', 1.0)
            saturation = context.metadata.get('saturation', 1.0)

            # 生成输出路径
            input_path = Path(context.file_path)
            output_path = input_path.parent / f"{input_path.stem}_corrected{input_path.suffix}"

            # 执行颜色校正
            with Image.open(context.file_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # 亮度调整
                if brightness != 1.0:
                    enhancer = ImageEnhance.Brightness(img)
                    img = enhancer.enhance(brightness)

                # 对比度调整
                if contrast != 1.0:
                    enhancer = ImageEnhance.Contrast(img)
                    img = enhancer.enhance(contrast)

                # 饱和度调整
                if saturation != 1.0:
                    enhancer = ImageEnhance.Color(img)
                    img = enhancer.enhance(saturation)

                # 保存结果
                img.save(output_path, quality=95)

            # 更新数据库记录
            with get_independent_db() as db:
                file_record = db.query(File).filter(File.id == context.file_id).first()
                if file_record:
                    processing_history = file_record.tags.get('processing_history', []) if file_record.tags else []
                    processing_history.append({
                        'processor': self.name,
                        'version': self.version,
                        'timestamp': datetime.now().isoformat(),
                        'operation': 'color_correction',
                        'parameters': {
                            'brightness': brightness,
                            'contrast': contrast,
                            'saturation': saturation
                        },
                        'output_path': str(output_path)
                    })

                    if not file_record.tags:
                        file_record.tags = {}
                    file_record.tags['processing_history'] = processing_history

                    db.commit()

            cache_patterns = [f"file_search_*_{context.case_id}_*", f"thumbnail_{context.file_id}_*"]

            return ProcessingResult(
                success=True,
                data={
                    'output_path': str(output_path),
                    'parameters': {
                        'brightness': brightness,
                        'contrast': contrast,
                        'saturation': saturation
                    }
                },
                cache_invalidation=cache_patterns
            )

        except Exception as e:
            logger.error(f"颜色校正处理失败: {e}")
            return ProcessingResult(success=False, error=str(e))

    def get_supported_stages(self) -> List[ProcessingStage]:
        return [ProcessingStage.POST_IMPORT, ProcessingStage.ON_DEMAND, ProcessingStage.BATCH]

    def get_supported_formats(self) -> List[str]:
        return ['.jpg', '.jpeg', '.png', '.tiff', '.bmp']

class AutoCropProcessor(BaseProcessor):
    """自动裁剪处理器"""

    def __init__(self):
        super().__init__("auto_crop", "1.0.0")

    async def process(self, context: ProcessingContext) -> ProcessingResult:
        """执行自动裁剪"""
        try:
            logger.info(f"开始自动裁剪处理: {context.file_path}")

            # 读取图像
            img_cv = cv2.imread(context.file_path)
            if img_cv is None:
                raise ValueError("无法读取图像文件")

            # 转换为灰度图
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

            # 边缘检测
            edges = cv2.Canny(gray, 50, 150)

            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                return ProcessingResult(success=False, error="未找到可裁剪的区域")

            # 找到最大轮廓
            largest_contour = max(contours, key=cv2.contourArea)

            # 获取边界矩形
            x, y, w, h = cv2.boundingRect(largest_contour)

            # 添加边距
            margin = context.metadata.get('margin', 10)
            x = max(0, x - margin)
            y = max(0, y - margin)
            w = min(img_cv.shape[1] - x, w + 2 * margin)
            h = min(img_cv.shape[0] - y, h + 2 * margin)

            # 裁剪图像
            cropped = img_cv[y:y+h, x:x+w]

            # 生成输出路径
            input_path = Path(context.file_path)
            output_path = input_path.parent / f"{input_path.stem}_cropped{input_path.suffix}"

            # 保存结果
            cv2.imwrite(str(output_path), cropped)

            # 更新数据库记录
            with get_independent_db() as db:
                file_record = db.query(File).filter(File.id == context.file_id).first()
                if file_record:
                    processing_history = file_record.tags.get('processing_history', []) if file_record.tags else []
                    processing_history.append({
                        'processor': self.name,
                        'version': self.version,
                        'timestamp': datetime.now().isoformat(),
                        'operation': 'auto_crop',
                        'parameters': {
                            'crop_region': {'x': x, 'y': y, 'width': w, 'height': h},
                            'margin': margin
                        },
                        'output_path': str(output_path)
                    })

                    if not file_record.tags:
                        file_record.tags = {}
                    file_record.tags['processing_history'] = processing_history

                    db.commit()

            cache_patterns = [f"file_search_*_{context.case_id}_*", f"thumbnail_{context.file_id}_*"]

            return ProcessingResult(
                success=True,
                data={
                    'output_path': str(output_path),
                    'crop_region': {'x': x, 'y': y, 'width': w, 'height': h}
                },
                cache_invalidation=cache_patterns
            )

        except Exception as e:
            logger.error(f"自动裁剪处理失败: {e}")
            return ProcessingResult(success=False, error=str(e))

    def get_supported_stages(self) -> List[ProcessingStage]:
        return [ProcessingStage.POST_IMPORT, ProcessingStage.ON_DEMAND, ProcessingStage.BATCH]

    def get_supported_formats(self) -> List[str]:
        return ['.jpg', '.jpeg', '.png', '.tiff', '.bmp']


class AssetPipelineManager:
    """资产处理流水线管理器"""

    def __init__(self):
        self.processors: Dict[str, BaseProcessor] = {}
        self.processing_queue = asyncio.Queue()
        self.cache_manager = SearchCache()
        self.is_running = False

        # 注册默认处理器
        self.register_processor(ImageInversionProcessor())
        self.register_processor(ColorCorrectionProcessor())
        self.register_processor(AutoCropProcessor())

    def register_processor(self, processor: BaseProcessor):
        """注册处理器"""
        self.processors[processor.name] = processor
        logger.info(f"已注册处理器: {processor.name} v{processor.version}")

    def unregister_processor(self, name: str):
        """注销处理器"""
        if name in self.processors:
            del self.processors[name]
            logger.info(f"已注销处理器: {name}")

    def get_processor(self, name: str) -> Optional[BaseProcessor]:
        """获取处理器"""
        return self.processors.get(name)

    def list_processors(self) -> List[Dict[str, Any]]:
        """列出所有处理器"""
        return [
            {
                'name': processor.name,
                'version': processor.version,
                'enabled': processor.enabled,
                'supported_stages': [stage.value for stage in processor.get_supported_stages()],
                'supported_formats': processor.get_supported_formats()
            }
            for processor in self.processors.values()
        ]

    async def process_file(
        self,
        file_id: int,
        processor_name: str,
        stage: ProcessingStage = ProcessingStage.ON_DEMAND,
        priority: ProcessingPriority = ProcessingPriority.NORMAL,
        metadata: Dict[str, Any] = None
    ) -> ProcessingResult:
        """处理单个文件"""

        # 获取处理器
        processor = self.get_processor(processor_name)
        if not processor:
            return ProcessingResult(success=False, error=f"处理器 {processor_name} 不存在")

        # 获取文件信息
        with get_independent_db() as db:
            file_record = db.query(File).filter(File.id == file_id).first()
            if not file_record:
                return ProcessingResult(success=False, error=f"文件 {file_id} 不存在")

        # 创建处理上下文
        context = ProcessingContext(
            file_id=file_id,
            file_path=file_record.file_path,
            case_id=file_record.case_id,
            stage=stage,
            priority=priority,
            metadata=metadata or {}
        )

        # 检查处理器是否支持
        if not processor.can_process(context):
            return ProcessingResult(
                success=False,
                error=f"处理器 {processor_name} 不支持处理该文件"
            )

        # 执行处理
        try:
            result = await processor.process(context)

            # 处理缓存失效
            if result.success and result.cache_invalidation:
                await processor.invalidate_cache(context, result.cache_invalidation)

            return result

        except Exception as e:
            logger.error(f"处理文件 {file_id} 时出错: {e}")
            return ProcessingResult(success=False, error=str(e))

    async def batch_process_files(
        self,
        file_ids: List[int],
        processor_name: str,
        stage: ProcessingStage = ProcessingStage.BATCH,
        priority: ProcessingPriority = ProcessingPriority.NORMAL,
        metadata: Dict[str, Any] = None,
        max_concurrent: int = 4
    ) -> Dict[str, Any]:
        """批量处理文件"""

        logger.info(f"开始批量处理 {len(file_ids)} 个文件，处理器: {processor_name}")

        # 创建信号量限制并发数
        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_single_file(file_id: int) -> Tuple[int, ProcessingResult]:
            async with semaphore:
                result = await self.process_file(
                    file_id, processor_name, stage, priority, metadata
                )
                return file_id, result

        # 并发处理所有文件
        tasks = [process_single_file(file_id) for file_id in file_ids]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 统计结果
        success_count = 0
        failed_count = 0
        errors = []

        for result in results:
            if isinstance(result, Exception):
                failed_count += 1
                errors.append(str(result))
            else:
                file_id, processing_result = result
                if processing_result.success:
                    success_count += 1
                else:
                    failed_count += 1
                    errors.append(f"文件 {file_id}: {processing_result.error}")

        return {
            'total': len(file_ids),
            'success': success_count,
            'failed': failed_count,
            'errors': errors,
            'processor': processor_name,
            'stage': stage.value
        }

    async def queue_processing_task(self, task: Dict[str, Any]):
        """将处理任务加入队列"""
        await self.processing_queue.put(task)
        logger.info(f"已将任务加入处理队列: {task.get('type')}")

# 全局流水线管理器实例
pipeline_manager = AssetPipelineManager()