# 标签管理系统 API 规范文档

## 概述

本文档描述了迷星 Mizzy Star 标签管理系统的完整 API 规范。该系统提供了强大的标签组织、管理和应用功能，支持多种标签类型和灵活的查询方式。

## 基础信息

- **API 版本**: v1
- **基础路径**: `/api/v1/cases/{case_id}/tags`
- **认证方式**: 无需认证（内部系统）
- **数据格式**: JSON
- **字符编码**: UTF-8

## 数据模型

### 标签树结构 (TagTreeResponse)

```json
{
  "properties": {
    "filename": {
      "IMG_001.jpg": {
        "count": 1,
        "file_ids": [1]
      }
    },
    "qualityScore": {
      "0.85": {
        "count": 3,
        "file_ids": [1, 2, 3]
      }
    }
  },
  "tags": {
    "metadata": {
      "camera": {
        "Nikon D850": {
          "count": 5,
          "file_ids": [1, 2, 3, 4, 5]
        }
      },
      "project": {
        "春季拍摄": {
          "count": 3,
          "file_ids": [1, 2, 3]
        }
      }
    },
    "cv": {
      "objects": {
        "person": {
          "count": 8,
          "file_ids": [1, 3, 5, 7, 9, 11, 13, 15]
        },
        "car": {
          "count": 2,
          "file_ids": [2, 4]
        }
      }
    },
    "user": {
      "重要": {
        "count": 4,
        "file_ids": [1, 5, 8, 12]
      }
    },
    "ai": {
      "风景": {
        "count": 6,
        "file_ids": [2, 4, 6, 8, 10, 12]
      }
    }
  },
  "custom": [
    {
      "id": 1,
      "name": "重要",
      "color": "#EF4444",
      "count": 3,
      "file_ids": [1, 5, 8]
    },
    {
      "id": 2,
      "name": "待处理",
      "color": "#F59E0B",
      "count": 2,
      "file_ids": [3, 7]
    }
  ]
}
```

### 自定义标签 (CustomTag)

```json
{
  "id": 1,
  "name": "重要",
  "color": "#EF4444",
  "display_order": 1,
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### 标签搜索结果 (TagSearchResult)

```json
{
  "category": "metadata",
  "name": "camera",
  "value": "Nikon D850",
  "count": 5,
  "file_ids": [1, 2, 3, 4, 5]
}
```

### 标签统计 (TagStatsResponse)

```json
{
  "total_tags": 45,
  "categories": {
    "metadata": 12,
    "cv": 8,
    "user": 5,
    "ai": 10,
    "properties": 6,
    "custom": 4
  },
  "most_used": [
    {
      "name": "camera",
      "category": "metadata",
      "count": 15
    },
    {
      "name": "重要",
      "category": "custom",
      "count": 8
    }
  ]
}
```

## API 端点

### 1. 获取标签树结构

获取案例的完整标签层级结构，包括所有类别的标签和文件数量统计。

**请求**
```http
GET /api/v1/cases/{case_id}/tags/tree?include_empty=false
```

**参数**
- `case_id` (path, required): 案例ID
- `include_empty` (query, optional): 是否包含空标签，默认 false

**响应**
```json
{
  "properties": { ... },
  "tags": { ... },
  "custom": [ ... ]
}
```

**状态码**
- `200`: 成功
- `404`: 案例不存在
- `500`: 服务器错误

### 2. 搜索标签

根据关键词和类别搜索标签。

**请求**
```http
GET /api/v1/cases/{case_id}/tags/search?q=camera&category=metadata&limit=50
```

**参数**
- `case_id` (path, required): 案例ID
- `q` (query, required): 搜索关键词
- `category` (query, optional): 标签类别筛选
- `limit` (query, optional): 返回结果数量限制，默认 50

**响应**
```json
{
  "results": [
    {
      "category": "metadata",
      "name": "camera",
      "value": "Nikon D850",
      "count": 5,
      "file_ids": [1, 2, 3, 4, 5]
    }
  ]
}
```

### 3. 创建自定义标签

创建新的自定义标签。

**请求**
```http
POST /api/v1/cases/{case_id}/tags/custom
Content-Type: application/json

{
  "name": "重要",
  "color": "#EF4444"
}
```

**参数**
- `case_id` (path, required): 案例ID
- `name` (body, required): 标签名称
- `color` (body, optional): 标签颜色，默认 "#3B82F6"

**响应**
```json
{
  "id": 1,
  "name": "重要",
  "color": "#EF4444",
  "display_order": 1,
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

**状态码**
- `201`: 创建成功
- `400`: 标签名已存在
- `404`: 案例不存在
- `500`: 服务器错误

### 4. 更新自定义标签

更新现有的自定义标签。

**请求**
```http
PUT /api/v1/cases/{case_id}/tags/custom/{tag_id}
Content-Type: application/json

{
  "name": "非常重要",
  "color": "#DC2626"
}
```

**参数**
- `case_id` (path, required): 案例ID
- `tag_id` (path, required): 标签ID
- `name` (body, optional): 新标签名称
- `color` (body, optional): 新标签颜色

**响应**
```json
{
  "id": 1,
  "name": "非常重要",
  "color": "#DC2626",
  "display_order": 1,
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T11:45:00Z"
}
```

### 5. 删除自定义标签

删除自定义标签及其所有关联。

**请求**
```http
DELETE /api/v1/cases/{case_id}/tags/custom/{tag_id}
```

**参数**
- `case_id` (path, required): 案例ID
- `tag_id` (path, required): 标签ID

**响应**
- 状态码 `204`: 删除成功（无响应体）

### 6. 为文件添加自定义标签

为指定文件添加一个或多个自定义标签。

**请求**
```http
POST /api/v1/cases/{case_id}/tags/files/{file_id}/custom
Content-Type: application/json

{
  "tag_ids": [1, 2, 3]
}
```

**参数**
- `case_id` (path, required): 案例ID
- `file_id` (path, required): 文件ID
- `tag_ids` (body, required): 要添加的标签ID列表

**响应**
```json
{
  "message": "成功添加 2 个标签"
}
```

### 7. 从文件移除自定义标签

从指定文件移除特定的自定义标签。

**请求**
```http
DELETE /api/v1/cases/{case_id}/tags/files/{file_id}/custom/{tag_id}
```

**参数**
- `case_id` (path, required): 案例ID
- `file_id` (path, required): 文件ID
- `tag_id` (path, required): 要移除的标签ID

**响应**
- 状态码 `204`: 移除成功（无响应体）

### 8. 批量标签操作

对多个文件执行批量标签添加或移除操作。

**请求**
```http
POST /api/v1/cases/{case_id}/tags/batch
Content-Type: application/json

{
  "action": "add",
  "file_ids": [1, 2, 3, 4, 5],
  "tag_id": 1
}
```

**参数**
- `case_id` (path, required): 案例ID
- `action` (body, required): 操作类型，"add" 或 "remove"
- `file_ids` (body, required): 目标文件ID列表
- `tag_id` (body, required): 标签ID

**响应**
```json
{
  "message": "批量add操作完成",
  "affected_count": 3
}
```

### 9. 获取标签统计

获取案例的标签使用统计信息。

**请求**
```http
GET /api/v1/cases/{case_id}/tags/stats
```

**参数**
- `case_id` (path, required): 案例ID

**响应**
```json
{
  "total_tags": 45,
  "categories": {
    "metadata": 12,
    "cv": 8,
    "user": 5,
    "ai": 10,
    "properties": 6,
    "custom": 4
  },
  "most_used": [
    {
      "name": "camera",
      "category": "metadata",
      "count": 15
    }
  ]
}
```

### 10. 刷新标签缓存

重新构建案例的标签缓存数据。

**请求**
```http
POST /api/v1/cases/{case_id}/tags/cache/refresh
```

**参数**
- `case_id` (path, required): 案例ID

**响应**
```json
{
  "message": "标签缓存刷新完成，共处理 156 个标签项"
}
```

### 11. 数据库迁移

为案例数据库执行标签管理表结构迁移。

**请求**
```http
POST /api/v1/cases/{case_id}/tags/migrate
```

**参数**
- `case_id` (path, required): 案例ID

**响应**
```json
{
  "message": "标签管理表结构迁移成功",
  "schema_check": {
    "case_id": 1,
    "database_path": "/path/to/case_1/db.sqlite",
    "tables": {
      "tag_cache": true,
      "deleted_files": true,
      "custom_tags": true,
      "file_custom_tags": true
    },
    "migration_needed": false
  }
}
```

### 12. 检查数据库结构

检查案例数据库的标签管理表结构。

**请求**
```http
GET /api/v1/cases/{case_id}/tags/schema/check
```

**参数**
- `case_id` (path, required): 案例ID

**响应**
```json
{
  "case_id": 1,
  "database_path": "/path/to/case_1/db.sqlite",
  "tables": {
    "tag_cache": true,
    "deleted_files": true,
    "custom_tags": false,
    "file_custom_tags": false
  },
  "migration_needed": true
}
```

## 错误处理

### 标准错误响应格式

```json
{
  "detail": "错误描述信息"
}
```

### 常见错误码

- `400 Bad Request`: 请求参数错误
  - 标签名已存在
  - 标签ID不存在
  - 文件ID不存在
  
- `404 Not Found`: 资源不存在
  - 案例不存在
  - 标签不存在
  - 文件不存在
  
- `500 Internal Server Error`: 服务器内部错误
  - 数据库连接失败
  - 数据处理异常

## 使用示例

### JavaScript/TypeScript 示例

```typescript
// 获取标签树
async function getTagTree(caseId: number, includeEmpty: boolean = false) {
  const response = await fetch(`/api/v1/cases/${caseId}/tags/tree?include_empty=${includeEmpty}`);
  return await response.json();
}

// 搜索标签
async function searchTags(caseId: number, query: string, category?: string) {
  const params = new URLSearchParams({ q: query });
  if (category) params.append('category', category);
  
  const response = await fetch(`/api/v1/cases/${caseId}/tags/search?${params}`);
  return await response.json();
}

// 创建自定义标签
async function createCustomTag(caseId: number, name: string, color: string = '#3B82F6') {
  const response = await fetch(`/api/v1/cases/${caseId}/tags/custom`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ name, color })
  });
  return await response.json();
}

// 批量添加标签
async function batchAddTags(caseId: number, fileIds: number[], tagId: number) {
  const response = await fetch(`/api/v1/cases/${caseId}/tags/batch`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      action: 'add',
      file_ids: fileIds,
      tag_id: tagId
    })
  });
  return await response.json();
}
```

### Python 示例

```python
import requests

class TagManagementAPI:
    def __init__(self, base_url: str):
        self.base_url = base_url
    
    def get_tag_tree(self, case_id: int, include_empty: bool = False):
        """获取标签树"""
        url = f"{self.base_url}/api/v1/cases/{case_id}/tags/tree"
        params = {"include_empty": include_empty}
        response = requests.get(url, params=params)
        return response.json()
    
    def search_tags(self, case_id: int, query: str, category: str = None):
        """搜索标签"""
        url = f"{self.base_url}/api/v1/cases/{case_id}/tags/search"
        params = {"q": query}
        if category:
            params["category"] = category
        response = requests.get(url, params=params)
        return response.json()
    
    def create_custom_tag(self, case_id: int, name: str, color: str = "#3B82F6"):
        """创建自定义标签"""
        url = f"{self.base_url}/api/v1/cases/{case_id}/tags/custom"
        data = {"name": name, "color": color}
        response = requests.post(url, json=data)
        return response.json()
    
    def batch_tag_operation(self, case_id: int, action: str, file_ids: list, tag_id: int):
        """批量标签操作"""
        url = f"{self.base_url}/api/v1/cases/{case_id}/tags/batch"
        data = {
            "action": action,
            "file_ids": file_ids,
            "tag_id": tag_id
        }
        response = requests.post(url, json=data)
        return response.json()

# 使用示例
api = TagManagementAPI("http://localhost:8001")

# 获取案例1的标签树
tag_tree = api.get_tag_tree(1, include_empty=False)

# 搜索相机相关标签
camera_tags = api.search_tags(1, "camera", "metadata")

# 创建重要标签
important_tag = api.create_custom_tag(1, "重要", "#EF4444")

# 批量为文件添加标签
result = api.batch_tag_operation(1, "add", [1, 2, 3], important_tag["id"])
```

## 性能考虑

### 缓存机制
- 标签数据会被缓存到 `tag_cache` 表中以提高查询性能
- 建议在大量标签操作后调用缓存刷新接口

### 批量操作
- 使用批量接口处理多个文件的标签操作
- 避免在循环中调用单个文件的标签接口

### 分页查询
- 搜索接口支持 `limit` 参数控制返回结果数量
- 大型案例建议使用适当的限制值

## 版本历史

- **v1.0.0** (2024-01-15): 初始版本
  - 基础标签管理功能
  - 自定义标签支持
  - 批量操作支持
  - 标签统计功能