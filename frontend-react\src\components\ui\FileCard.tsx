import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/utils/cn';

// ============================================================================
// FileCard Variants Definition
// ============================================================================

const fileCardVariants = cva(
  // Base styles
  'group relative overflow-hidden rounded-lg border bg-card text-card-foreground shadow-sm transition-smooth hover:shadow-md focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2',
  {
    variants: {
      variant: {
        default: 'border-border hover:border-border/80',
        selected: 'border-primary ring-2 ring-primary ring-offset-2',
        error: 'border-destructive bg-destructive/5',
      },
      size: {
        sm: 'w-32 h-32',
        md: 'w-40 h-40',
        lg: 'w-48 h-48',
        xl: 'w-56 h-56',
      },
      layout: {
        grid: 'aspect-square',
        list: 'aspect-auto flex-row h-20',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
      layout: 'grid',
    },
  }
);

// ============================================================================
// FileCard Component Interface
// ============================================================================

export interface FileCardProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onDoubleClick' | 'onSelect'>,
    VariantProps<typeof fileCardVariants> {
  /**
   * 文件信息
   */
  file: {
    id: number;
    fileName: string;
    filePath: string;
    fileType: string;
    fileSize: number;
    width?: number;
    height?: number;
    thumbnailPath?: string;
  };

  /**
   * 是否选中
   */
  selected?: boolean;

  /**
   * 选择回调
   */
  onSelect?: (fileId: number, selected: boolean) => void;

  /**
   * 双击回调
   */
  onDoubleClick?: (file: FileCardProps['file']) => void;

  /**
   * 是否显示文件名
   */
  showFileName?: boolean;

  /**
   * 是否显示文件信息
   */
  showFileInfo?: boolean;

  /**
   * 是否显示选择框
   */
  showCheckbox?: boolean;

  /**
   * 加载状态
   */
  loading?: boolean;

  /**
   * 错误状态
   */
  error?: string;
}

/**
 * FileCard 组件 - Project Novak 原子组件
 *
 * 设计原则：
 * 1. 愚蠢原则：只负责文件展示，不包含文件操作逻辑
 * 2. 组合优于配置：支持多种布局和显示选项
 * 3. 无障碍优先：完整的键盘导航和屏幕阅读器支持
 * 4. 性能优化：懒加载和错误处理
 */
const FileCard = React.forwardRef<HTMLDivElement, FileCardProps>(
  ({
    className,
    variant,
    size,
    layout,
    file,
    selected = false,
    onSelect,
    onDoubleClick,
    showFileName = true,
    showFileInfo = false,
    showCheckbox = false,
    loading = false,
    error,
    onClick,
    onKeyDown,
    ...props
  }, ref) => {
    const [imageLoaded, setImageLoaded] = React.useState(false);
    const [imageError, setImageError] = React.useState(false);

    // 处理选择
    const handleSelect = (event: React.MouseEvent<HTMLDivElement>) => {
      if (event.detail === 2) return; // 忽略双击时的选择
      onSelect?.(file.id, !selected);
      onClick?.(event);
    };

    // 处理双击
    const handleDoubleClick = (event: React.MouseEvent) => {
      event.preventDefault();
      onDoubleClick?.(file);
    };

    // 处理键盘事件
    const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
      if (event.key === 'Enter') {
        event.preventDefault();
        onDoubleClick?.(file);
      } else if (event.key === ' ') {
        event.preventDefault();
        onSelect?.(file.id, !selected);
      }
      onKeyDown?.(event);
    };

    // 格式化文件大小
    const formatFileSize = (bytes: number): string => {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    };

    // 获取文件类型图标
    const getFileTypeIcon = (fileType: string) => {
      const type = fileType.toLowerCase();
      if (type.includes('image')) return '🖼️';
      if (type.includes('video')) return '🎥';
      if (type.includes('audio')) return '🎵';
      if (type.includes('pdf')) return '📄';
      return '📁';
    };

    const isImage = file.fileType.startsWith('image/');
    const thumbnailSrc = file.thumbnailPath || file.filePath;

    return (
      <div
        ref={ref}
        className={cn(
          fileCardVariants({
            variant: selected ? 'selected' : error ? 'error' : variant,
            size: layout === 'list' ? undefined : size,
            layout,
            className
          })
        )}
        onClick={handleSelect}
        onDoubleClick={handleDoubleClick}
        onKeyDown={handleKeyDown}
        tabIndex={0}
        role="button"
        aria-pressed={selected}
        aria-label={`File: ${file.fileName}`}
        {...props}
      >
        {/* Checkbox */}
        {showCheckbox && (
          <div className="absolute top-2 left-2 z-10">
            <input
              type="checkbox"
              checked={selected}
              onChange={() => onSelect?.(file.id, !selected)}
              className="w-4 h-4 rounded border-border bg-background"
              aria-label={`Select ${file.fileName}`}
            />
          </div>
        )}

        {/* Content Container */}
        <div className={cn(
          'flex h-full',
          layout === 'list' ? 'flex-row items-center gap-3 p-3' : 'flex-col'
        )}>
          {/* Thumbnail/Preview */}
          <div className={cn(
            'relative overflow-hidden bg-muted flex items-center justify-center',
            layout === 'list'
              ? 'w-14 h-14 rounded flex-shrink-0'
              : 'flex-1 w-full rounded-t-lg'
          )}>
            {loading ? (
              <div className="flex items-center justify-center w-full h-full">
                <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
              </div>
            ) : error || imageError ? (
              <div className="flex flex-col items-center justify-center text-muted-foreground">
                <span className="text-2xl">⚠️</span>
                <span className="text-xs mt-1">Error</span>
              </div>
            ) : isImage ? (
              <>
                <img
                  src={thumbnailSrc}
                  alt={file.fileName}
                  className={cn(
                    'object-cover transition-opacity duration-200',
                    layout === 'list' ? 'w-full h-full' : 'w-full h-full',
                    imageLoaded ? 'opacity-100' : 'opacity-0'
                  )}
                  onLoad={() => setImageLoaded(true)}
                  onError={() => setImageError(true)}
                  loading="lazy"
                />
                {!imageLoaded && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                  </div>
                )}
              </>
            ) : (
              <div className="flex flex-col items-center justify-center text-muted-foreground">
                <span className="text-2xl">{getFileTypeIcon(file.fileType)}</span>
                <span className="text-xs mt-1 uppercase">{file.fileType.split('/')[1]}</span>
              </div>
            )}

            {/* Hover Overlay */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors" />
          </div>

          {/* File Info */}
          {(showFileName || showFileInfo) && (
            <div className={cn(
              'flex flex-col',
              layout === 'list' ? 'flex-1 min-w-0' : 'p-2 pt-1'
            )}>
              {showFileName && (
                <p className="text-sm font-medium truncate" title={file.fileName}>
                  {file.fileName}
                </p>
              )}
              {showFileInfo && (
                <div className="text-xs text-muted-foreground space-y-0.5">
                  <p>{formatFileSize(file.fileSize)}</p>
                  {file.width && file.height && (
                    <p>{file.width} × {file.height}</p>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Selection Indicator */}
        {selected && (
          <div className="absolute top-2 right-2 w-5 h-5 bg-primary rounded-full flex items-center justify-center">
            <svg className="w-3 h-3 text-primary-foreground" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
        )}
      </div>
    );
  }
);

FileCard.displayName = 'FileCard';

export { FileCard, fileCardVariants };
