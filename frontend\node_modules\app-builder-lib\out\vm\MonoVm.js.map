{"version": 3, "file": "MonoVm.js", "sourceRoot": "", "sources": ["../../src/vm/MonoVm.ts"], "names": [], "mappings": ";;;AACA,+CAA6D;AAC7D,6BAAgC;AAEhC,MAAa,aAAc,SAAQ,cAAS;IAC1C;QACE,KAAK,EAAE,CAAA;IACT,CAAC;IAED,IAAI,CAAC,IAAY,EAAE,IAAmB,EAAE,OAAyB,EAAE,eAAe,GAAG,IAAI;QACvF,OAAO,IAAA,mBAAI,EACT,MAAM,EACN,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EACnB;YACE,GAAG,OAAO;SACX,EACD,eAAe,CAChB,CAAA;IACH,CAAC;IAED,KAAK,CAAC,IAAY,EAAE,IAAmB,EAAE,OAAsB,EAAE,YAAgC;QAC/F,OAAO,IAAA,oBAAK,EAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,YAAY,CAAC,CAAA;IAClE,CAAC;CACF;AAnBD,sCAmBC", "sourcesContent": ["import { SpawnOptions, ExecFileOptions } from \"child_process\"\nimport { exec, ExtraSpawnOptions, spawn } from \"builder-util\"\nimport { VmManager } from \"./vm\"\n\nexport class MonoVmManager extends VmManager {\n  constructor() {\n    super()\n  }\n\n  exec(file: string, args: Array<string>, options?: ExecFileOptions, isLogOutIfDebug = true): Promise<string> {\n    return exec(\n      \"mono\",\n      [file].concat(args),\n      {\n        ...options,\n      },\n      isLogOutIfDebug\n    )\n  }\n\n  spawn(file: string, args: Array<string>, options?: SpawnOptions, extraOptions?: ExtraSpawnOptions): Promise<any> {\n    return spawn(\"mono\", [file].concat(args), options, extraOptions)\n  }\n}\n"]}