#!/usr/bin/env python3
"""
缓存管理器
实施LRU缓存和智能缓存策略
"""

import time
import json
import hashlib
from typing import Any, Dict, Optional, Tuple
from functools import lru_cache
from collections import OrderedDict
import threading
import logging

logger = logging.getLogger(__name__)

class LRUCache:
    """线程安全的LRU缓存实现"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 300):
        self.max_size = max_size
        self.ttl = ttl  # 生存时间（秒）
        self.cache = OrderedDict()
        self.timestamps = {}
        self.lock = threading.RLock()
        self.hits = 0
        self.misses = 0
    
    def _is_expired(self, key: str) -> bool:
        """检查缓存项是否过期"""
        if key not in self.timestamps:
            return True
        return time.time() - self.timestamps[key] > self.ttl
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        with self.lock:
            if key in self.cache and not self._is_expired(key):
                # 移动到末尾（最近使用）
                self.cache.move_to_end(key)
                self.hits += 1
                return self.cache[key]
            elif key in self.cache:
                # 过期的缓存项
                del self.cache[key]
                del self.timestamps[key]
            
            self.misses += 1
            return None
    
    def put(self, key: str, value: Any) -> None:
        """设置缓存项"""
        with self.lock:
            if key in self.cache:
                # 更新现有项
                self.cache[key] = value
                self.cache.move_to_end(key)
            else:
                # 新增项
                if len(self.cache) >= self.max_size:
                    # 删除最旧的项
                    oldest_key = next(iter(self.cache))
                    del self.cache[oldest_key]
                    del self.timestamps[oldest_key]
                
                self.cache[key] = value
            
            self.timestamps[key] = time.time()
    
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.timestamps.clear()
            self.hits = 0
            self.misses = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self.lock:
            total_requests = self.hits + self.misses
            hit_rate = (self.hits / total_requests * 100) if total_requests > 0 else 0
            
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hits': self.hits,
                'misses': self.misses,
                'hit_rate': round(hit_rate, 2),
                'ttl': self.ttl
            }

class SearchCacheManager:
    """搜索缓存管理器"""
    
    def __init__(self):
        # 不同类型的缓存，使用不同的策略
        self.query_cache = LRUCache(max_size=1000, ttl=300)  # 查询结果缓存，5分钟
        self.count_cache = LRUCache(max_size=500, ttl=600)   # 计数缓存，10分钟
        self.file_cache = LRUCache(max_size=2000, ttl=1800)  # 文件信息缓存，30分钟
        self.tag_cache = LRUCache(max_size=1000, ttl=3600)   # 标签缓存，1小时
    
    def _generate_cache_key(self, prefix: str, **kwargs) -> str:
        """生成缓存键"""
        # 创建稳定的键
        key_data = json.dumps(kwargs, sort_keys=True, default=str)
        key_hash = hashlib.md5(key_data.encode()).hexdigest()[:16]
        return f"{prefix}:{key_hash}"
    
    def get_query_result(self, case_id: int, user_tags: list = None, 
                        custom_tags: dict = None, quality_min: float = None,
                        limit: int = 10, offset: int = 0) -> Optional[Tuple[list, int]]:
        """获取查询结果缓存"""
        cache_key = self._generate_cache_key(
            "query",
            case_id=case_id,
            user_tags=sorted(user_tags) if user_tags else None,
            custom_tags=dict(sorted(custom_tags.items())) if custom_tags else None,
            quality_min=quality_min,
            limit=limit,
            offset=offset
        )
        
        result = self.query_cache.get(cache_key)
        if result:
            logger.debug(f"查询缓存命中: {cache_key}")
        return result
    
    def set_query_result(self, case_id: int, user_tags: list = None,
                        custom_tags: dict = None, quality_min: float = None,
                        limit: int = 10, offset: int = 0,
                        results: list = None, total: int = 0) -> None:
        """设置查询结果缓存"""
        cache_key = self._generate_cache_key(
            "query",
            case_id=case_id,
            user_tags=sorted(user_tags) if user_tags else None,
            custom_tags=dict(sorted(custom_tags.items())) if custom_tags else None,
            quality_min=quality_min,
            limit=limit,
            offset=offset
        )
        
        self.query_cache.put(cache_key, (results, total))
        logger.debug(f"查询结果已缓存: {cache_key}")
    
    def get_count_result(self, case_id: int, user_tags: list = None,
                        custom_tags: dict = None, quality_min: float = None) -> Optional[int]:
        """获取计数缓存"""
        cache_key = self._generate_cache_key(
            "count",
            case_id=case_id,
            user_tags=sorted(user_tags) if user_tags else None,
            custom_tags=dict(sorted(custom_tags.items())) if custom_tags else None,
            quality_min=quality_min
        )
        
        result = self.count_cache.get(cache_key)
        if result:
            logger.debug(f"计数缓存命中: {cache_key}")
        return result
    
    def set_count_result(self, case_id: int, user_tags: list = None,
                        custom_tags: dict = None, quality_min: float = None,
                        count: int = 0) -> None:
        """设置计数缓存"""
        cache_key = self._generate_cache_key(
            "count",
            case_id=case_id,
            user_tags=sorted(user_tags) if user_tags else None,
            custom_tags=dict(sorted(custom_tags.items())) if custom_tags else None,
            quality_min=quality_min
        )
        
        self.count_cache.put(cache_key, count)
        logger.debug(f"计数结果已缓存: {cache_key}")
    
    def get_file_info(self, file_id: int) -> Optional[Dict]:
        """获取文件信息缓存"""
        cache_key = f"file:{file_id}"
        result = self.file_cache.get(cache_key)
        if result:
            logger.debug(f"文件缓存命中: {cache_key}")
        return result
    
    def set_file_info(self, file_id: int, file_info: Dict) -> None:
        """设置文件信息缓存"""
        cache_key = f"file:{file_id}"
        self.file_cache.put(cache_key, file_info)
        logger.debug(f"文件信息已缓存: {cache_key}")
    
    def invalidate_case_cache(self, case_id: int) -> None:
        """使案例相关缓存失效"""
        # 简化实现：清空所有缓存
        # 实际应该只清空相关的缓存项
        self.query_cache.clear()
        self.count_cache.clear()
        logger.info(f"案例 {case_id} 相关缓存已失效")
    
    def clear_all_cache(self) -> None:
        """清空所有缓存"""
        self.query_cache.clear()
        self.count_cache.clear()
        self.file_cache.clear()
        self.tag_cache.clear()
        logger.info("所有缓存已清空")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'query_cache': self.query_cache.get_stats(),
            'count_cache': self.count_cache.get_stats(),
            'file_cache': self.file_cache.get_stats(),
            'tag_cache': self.tag_cache.get_stats()
        }

# 全局缓存管理器实例
cache_manager = SearchCacheManager()

# 装饰器：自动缓存函数结果
def cached_result(cache_type: str = "query", ttl: int = 300):
    """缓存装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # 选择合适的缓存
            if cache_type == "query":
                cache = cache_manager.query_cache
            elif cache_type == "count":
                cache = cache_manager.count_cache
            elif cache_type == "file":
                cache = cache_manager.file_cache
            else:
                cache = cache_manager.tag_cache
            
            # 尝试从缓存获取
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache.put(cache_key, result)
            
            return result
        return wrapper
    return decorator
