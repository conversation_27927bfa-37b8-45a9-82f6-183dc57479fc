@tailwind base;
@tailwind components;
@tailwind utilities;

/* MizzyStar 新星计划 - 全局样式 */
@layer base {
  :root {
    /* MizzyStar 蓝图配色方案 */
    --mizzy-bg-primary: #191012;    /* 主背景色 */
    --mizzy-bg-secondary: #040709;  /* 次要背景色 */
    --mizzy-text-primary: #A49F9A;  /* 主文字颜色 */
    --mizzy-text-secondary: #6B6B6B; /* 次要文字颜色 */
    --mizzy-accent: #E8E3E0;        /* 强调色 */
    --mizzy-border: #2A2A2A;        /* 边框颜色 */
    --mizzy-hover: #2D2D2D;         /* 悬停状态 */

    /* 更新 shadcn/ui 变量以匹配 MizzyStar 配色 */
    --background: 25 20% 6%;        /* #191012 */
    --foreground: 30 8% 64%;        /* #A49F9A */
    --card: 220 27% 3%;             /* #040709 */
    --card-foreground: 30 8% 64%;   /* #A49F9A */
    --popover: 220 27% 3%;
    --popover-foreground: 30 8% 64%;
    --primary: 30 8% 90%;           /* #E8E3E0 */
    --primary-foreground: 25 20% 6%;
    --secondary: 0 0% 17%;          /* #2A2A2A */
    --secondary-foreground: 30 8% 64%;
    --muted: 0 0% 17%;
    --muted-foreground: 0 0% 42%;   /* #6B6B6B */
    --accent: 0 0% 18%;             /* #2D2D2D */
    --accent-foreground: 30 8% 64%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 0 0% 17%;             /* #2A2A2A */
    --input: 0 0% 17%;
    --ring: 30 8% 64%;
    --radius: 0.5rem;
  }
}

/* MizzyStar 全局基础样式 */
@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-[#191012] text-[#A49F9A];
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #040709;
  }

  ::-webkit-scrollbar-thumb {
    background: #2A2A2A;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #2D2D2D;
  }
}
