# 智慧之眼项目结构

## 📁 项目目录结构

```
mizzy_star_v0.3/
├── backend/                    # 后端服务
│   ├── src/                   # 源代码
│   │   ├── routers/          # API路由
│   │   ├── services/         # 业务服务
│   │   ├── crud/             # 数据库操作
│   │   ├── models.py         # 数据模型
│   │   ├── schemas.py        # API模式
│   │   ├── database.py       # 数据库配置
│   │   └── main.py           # 应用入口
│   ├── docs/                 # 后端文档
│   ├── tests/                # 测试文件
│   └── requirements.txt      # Python依赖
├── frontend/                  # 前端应用
│   ├── src/                  # 源代码
│   │   ├── renderer/         # 渲染器
│   │   │   ├── js/          # JavaScript文件
│   │   │   └── css/         # 样式文件
│   │   └── assets/          # 静态资源
│   ├── *.html               # HTML页面
│   └── package.json         # Node.js依赖
├── docs/                     # 项目文档
│   ├── implementation/       # 实现报告
│   ├── reports/             # 测试报告
│   ├── technical-specs/     # 技术规范
│   ├── bugfixes/           # 错误修复记录
│   └── improvements/       # 改进记录
├── data/                    # 数据目录
│   ├── case_*/             # 案例数据
│   └── mizzy_star.db       # 主数据库
├── postgresql-upgrade/      # PostgreSQL升级相关
└── tests/                  # 集成测试
```

## 🚀 核心功能模块

### 后端服务 (FastAPI)
- **案例管理**: 创建、查看、删除案例
- **文件管理**: 上传、处理、分析图片文件
- **标签系统**: 元数据提取、自定义标签、标签筛选
- **搜索功能**: 基于标签的高性能搜索
- **质量分析**: 图片质量评估和分析
- **回收站**: 软删除和恢复功能

### 前端应用 (Vanilla JS + Tailwind CSS)
- **案例界面**: 案例列表、创建、管理
- **图片画廊**: 图片展示、预览、批量操作
- **标签管理**: 标签树、筛选、跳转功能
- **大图模式**: 图片详情、标签显示、导航
- **响应式设计**: 支持各种屏幕尺寸

## 🔧 技术栈

### 后端
- **框架**: FastAPI
- **数据库**: SQLite (主) + PostgreSQL (可选)
- **ORM**: SQLAlchemy
- **图像处理**: Pillow, OpenCV
- **异步支持**: asyncio, aiofiles

### 前端
- **核心**: Vanilla JavaScript (ES6+)
- **样式**: Tailwind CSS
- **构建**: 无构建工具 (直接运行)
- **图标**: Heroicons

### 数据库设计
- **主数据库**: 案例信息、系统配置
- **案例数据库**: 每个案例独立的SQLite文件
- **标签缓存**: 高性能标签查询优化

## 📊 项目状态

- ✅ **核心功能**: 完全实现
- ✅ **标签系统**: 完全实现
- ✅ **搜索功能**: 完全实现
- ✅ **UI/UX**: 完全实现
- ✅ **性能优化**: 完全实现
- ✅ **错误处理**: 完全实现
- ✅ **文档**: 完全实现

## 🚀 快速启动

1. **启动后端服务**:
   ```bash
   cd backend
   pip install -r requirements.txt
   python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **启动前端服务**:
   ```bash
   cd frontend
   # 使用任意HTTP服务器，如Python的内置服务器
   python -m http.server 80
   ```

3. **访问应用**: http://localhost

## 📝 维护说明

- **日志位置**: 控制台输出
- **数据备份**: `data/` 目录
- **配置文件**: `backend/src/database.py`
- **API文档**: http://localhost:8000/docs
