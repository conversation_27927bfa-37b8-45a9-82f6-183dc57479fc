# src/services/query_optimizer.py
"""
查询优化器
分析查询模式，优化查询性能，提供查询建议
"""

from typing import Dict, Any, List, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import text, inspect
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import logging

from ..database_config import db_config, DatabaseType
from ..database_manager import db_manager

logger = logging.getLogger(__name__)

@dataclass
class QueryPerformanceMetrics:
    """查询性能指标"""
    query_type: str
    execution_time_ms: float
    result_count: int
    index_usage: List[str]
    optimization_suggestions: List[str]
    timestamp: datetime

@dataclass
class IndexRecommendation:
    """索引推荐"""
    table_name: str
    column_expression: str
    index_type: str  # btree, gin, gist
    estimated_benefit: str  # high, medium, low
    reason: str

class QueryOptimizer:
    """查询优化器"""
    
    def __init__(self, case_id: Optional[str] = None):
        self.case_id = case_id
        self.is_postgresql = db_config.case_db_type == DatabaseType.POSTGRESQL
        self.performance_history: List[QueryPerformanceMetrics] = []
    
    def _get_session(self) -> Session:
        """获取数据库会话"""
        if self.case_id:
            return db_manager.get_case_session(self.case_id)
        else:
            return db_manager.get_master_session()
    
    def analyze_query_performance(self, query: str, params: Dict[str, Any] = None) -> QueryPerformanceMetrics:
        """
        分析查询性能
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            QueryPerformanceMetrics: 性能指标
        """
        session = self._get_session()
        try:
            start_time = datetime.now()
            
            # 统一使用PostgreSQL查询分析
            metrics = self._analyze_postgresql_query(session, query, params)
            
            end_time = datetime.now()
            metrics.execution_time_ms = (end_time - start_time).total_seconds() * 1000
            metrics.timestamp = start_time
            
            # 记录性能历史
            self.performance_history.append(metrics)
            
            return metrics
            
        finally:
            session.close()
    
    def _analyze_postgresql_query(self, session: Session, query: str, params: Dict[str, Any]) -> QueryPerformanceMetrics:
        """分析PostgreSQL查询性能"""
        
        # 使用EXPLAIN ANALYZE获取查询计划
        explain_query = f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query}"
        
        try:
            result = session.execute(text(explain_query), params or {})
            explain_result = result.fetchone()[0]
            
            # 解析查询计划
            plan = explain_result[0]['Plan']
            execution_time = explain_result[0]['Execution Time']
            
            # 提取索引使用信息
            index_usage = self._extract_index_usage_postgresql(plan)
            
            # 生成优化建议
            suggestions = self._generate_optimization_suggestions_postgresql(plan, query)
            
            return QueryPerformanceMetrics(
                query_type=self._classify_query_type(query),
                execution_time_ms=execution_time,
                result_count=plan.get('Actual Rows', 0),
                index_usage=index_usage,
                optimization_suggestions=suggestions,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.warning(f"PostgreSQL查询分析失败: {e}")
            return QueryPerformanceMetrics(
                query_type=self._classify_query_type(query),
                execution_time_ms=0,
                result_count=0,
                index_usage=[],
                optimization_suggestions=[f"查询分析失败: {str(e)}"],
                timestamp=datetime.now()
            )
    
    def _analyze_postgresql_query(self, session: Session, query: str, params: Dict[str, Any]) -> QueryPerformanceMetrics:
        """分析PostgreSQL查询性能"""
        
        # PostgreSQL使用EXPLAIN ANALYZE
        explain_query = f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query}"
        
        try:
            result = session.execute(text(explain_query), params or {})
            plan_rows = result.fetchall()
            
            # 分析查询计划
            index_usage = []
            suggestions = []
            
            for row in plan_rows:
                detail = row[3] if len(row) > 3 else str(row)
                if 'USING INDEX' in detail:
                    index_name = detail.split('USING INDEX ')[1].split(' ')[0]
                    index_usage.append(index_name)
                elif 'SCAN TABLE' in detail and 'USING INDEX' not in detail:
                    suggestions.append(f"考虑为表扫描添加索引: {detail}")
            
            return QueryPerformanceMetrics(
                query_type=self._classify_query_type(query),
                execution_time_ms=0,  # PostgreSQL执行时间从EXPLAIN ANALYZE获取
                result_count=0,
                index_usage=index_usage,
                optimization_suggestions=suggestions,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.warning(f"PostgreSQL查询分析失败: {e}")
            return QueryPerformanceMetrics(
                query_type=self._classify_query_type(query),
                execution_time_ms=0,
                result_count=0,
                index_usage=[],
                optimization_suggestions=[f"查询分析失败: {str(e)}"],
                timestamp=datetime.now()
            )
    
    def _extract_index_usage_postgresql(self, plan: Dict[str, Any]) -> List[str]:
        """提取PostgreSQL查询计划中的索引使用信息"""
        indexes = []
        
        def extract_from_node(node):
            if isinstance(node, dict):
                # 检查当前节点
                if node.get('Node Type') in ['Index Scan', 'Index Only Scan', 'Bitmap Index Scan']:
                    index_name = node.get('Index Name')
                    if index_name:
                        indexes.append(index_name)
                
                # 递归检查子节点
                if 'Plans' in node:
                    for child in node['Plans']:
                        extract_from_node(child)
        
        extract_from_node(plan)
        return indexes
    
    def _generate_optimization_suggestions_postgresql(self, plan: Dict[str, Any], query: str) -> List[str]:
        """为PostgreSQL查询生成优化建议"""
        suggestions = []
        
        def analyze_node(node):
            if isinstance(node, dict):
                node_type = node.get('Node Type', '')
                
                # 检查全表扫描
                if node_type == 'Seq Scan':
                    table_name = node.get('Relation Name', '')
                    if table_name:
                        suggestions.append(f"考虑为表 {table_name} 添加索引以避免全表扫描")
                
                # 检查排序操作
                if node_type == 'Sort':
                    sort_key = node.get('Sort Key', [])
                    if sort_key:
                        suggestions.append(f"考虑为排序字段 {sort_key} 添加索引")
                
                # 检查JSONB操作
                if 'Filter' in node:
                    filter_condition = node['Filter']
                    if 'tags->' in filter_condition:
                        suggestions.append("JSONB查询已优化，确保使用了GIN索引")
                
                # 检查执行时间
                actual_time = node.get('Actual Total Time', 0)
                if actual_time > 1000:  # 超过1秒
                    suggestions.append(f"节点 {node_type} 执行时间较长 ({actual_time:.2f}ms)，需要优化")
                
                # 递归分析子节点
                if 'Plans' in node:
                    for child in node['Plans']:
                        analyze_node(child)
        
        analyze_node(plan)
        
        # 基于查询类型的通用建议
        if 'JSONB' in query.upper() or 'tags->' in query:
            suggestions.append("确保JSONB字段使用了GIN索引")
        
        if 'ORDER BY' in query.upper():
            suggestions.append("考虑为ORDER BY字段添加索引")
        
        if 'WHERE' in query.upper() and 'LIKE' in query.upper():
            suggestions.append("考虑使用全文搜索替代LIKE查询")
        
        return list(set(suggestions))  # 去重
    
    def _classify_query_type(self, query: str) -> str:
        """分类查询类型"""
        query_upper = query.upper()
        
        if 'SELECT' in query_upper:
            if 'JSONB' in query_upper or 'tags->' in query:
                return 'jsonb_query'
            elif 'ts_rank' in query or 'plainto_tsquery' in query:
                return 'fulltext_search'
            elif 'JOIN' in query_upper:
                return 'join_query'
            elif 'GROUP BY' in query_upper:
                return 'aggregation_query'
            else:
                return 'simple_select'
        elif 'INSERT' in query_upper:
            return 'insert'
        elif 'UPDATE' in query_upper:
            return 'update'
        elif 'DELETE' in query_upper:
            return 'delete'
        else:
            return 'other'
    
    def get_index_recommendations(self) -> List[IndexRecommendation]:
        """获取索引推荐"""
        session = self._get_session()
        try:
            # 统一使用PostgreSQL索引推荐
            return self._get_postgresql_index_recommendations(session)
        finally:
            session.close()
    
    def _get_postgresql_index_recommendations(self, session: Session) -> List[IndexRecommendation]:
        """获取PostgreSQL索引推荐"""
        recommendations = []
        
        # 检查缺失的索引
        try:
            # 查询pg_stat_user_tables获取表统计信息
            result = session.execute(text("""
                SELECT 
                    schemaname, tablename, seq_scan, seq_tup_read, 
                    idx_scan, idx_tup_fetch, n_tup_ins, n_tup_upd, n_tup_del
                FROM pg_stat_user_tables 
                WHERE schemaname = 'public'
            """))
            
            for row in result:
                table_name = row.tablename
                seq_scan = row.seq_scan or 0
                idx_scan = row.idx_scan or 0
                
                # 如果全表扫描次数远大于索引扫描次数
                if seq_scan > idx_scan * 10 and seq_scan > 100:
                    recommendations.append(IndexRecommendation(
                        table_name=table_name,
                        column_expression="需要分析具体查询模式",
                        index_type="btree",
                        estimated_benefit="high",
                        reason=f"表 {table_name} 全表扫描次数过多 ({seq_scan} vs {idx_scan})"
                    ))
            
            # 针对files表的特殊推荐
            if self.case_id:
                # 检查JSONB查询模式
                recommendations.append(IndexRecommendation(
                    table_name="files",
                    column_expression="tags",
                    index_type="gin",
                    estimated_benefit="high",
                    reason="JSONB字段需要GIN索引以优化标签查询"
                ))
                
                # 检查全文搜索
                recommendations.append(IndexRecommendation(
                    table_name="files",
                    column_expression="search_vector",
                    index_type="gin",
                    estimated_benefit="high",
                    reason="全文搜索向量需要GIN索引"
                ))
        
        except Exception as e:
            logger.warning(f"获取PostgreSQL索引推荐失败: {e}")
        
        return recommendations
    

    
    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """获取性能摘要"""
        
        # 过滤最近的性能记录
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_metrics = [m for m in self.performance_history if m.timestamp > cutoff_time]
        
        if not recent_metrics:
            return {
                'total_queries': 0,
                'avg_execution_time_ms': 0,
                'query_type_distribution': {},
                'top_slow_queries': [],
                'optimization_suggestions': []
            }
        
        # 计算统计信息
        total_queries = len(recent_metrics)
        avg_execution_time = sum(m.execution_time_ms for m in recent_metrics) / total_queries
        
        # 查询类型分布
        query_type_counts = {}
        for metric in recent_metrics:
            query_type_counts[metric.query_type] = query_type_counts.get(metric.query_type, 0) + 1
        
        # 最慢的查询
        slow_queries = sorted(recent_metrics, key=lambda x: x.execution_time_ms, reverse=True)[:5]
        
        # 收集所有优化建议
        all_suggestions = []
        for metric in recent_metrics:
            all_suggestions.extend(metric.optimization_suggestions)
        
        # 统计建议频率
        suggestion_counts = {}
        for suggestion in all_suggestions:
            suggestion_counts[suggestion] = suggestion_counts.get(suggestion, 0) + 1
        
        top_suggestions = sorted(suggestion_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return {
            'total_queries': total_queries,
            'avg_execution_time_ms': avg_execution_time,
            'query_type_distribution': query_type_counts,
            'top_slow_queries': [
                {
                    'query_type': q.query_type,
                    'execution_time_ms': q.execution_time_ms,
                    'result_count': q.result_count,
                    'timestamp': q.timestamp.isoformat()
                }
                for q in slow_queries
            ],
            'optimization_suggestions': [
                {'suggestion': suggestion, 'frequency': count}
                for suggestion, count in top_suggestions
            ]
        }
    
    def optimize_query_automatically(self, query: str, params: Dict[str, Any] = None) -> Tuple[str, Dict[str, Any]]:
        """
        自动优化查询
        
        Args:
            query: 原始查询
            params: 查询参数
            
        Returns:
            Tuple[str, Dict]: 优化后的查询和参数
        """
        
        optimized_query = query
        optimized_params = params or {}
        
        # 基本的查询优化规则
        if self.is_postgresql:
            # PostgreSQL特定优化
            if 'LIKE' in query.upper() and 'tags' in query.lower():
                # 将LIKE查询转换为JSONB查询
                optimized_query = query.replace(
                    "tags LIKE '%{value}%'",
                    "tags @> '{\"tags\": {\"user\": [\"{value}\"]}}'::jsonb"
                )
            
            # 添加LIMIT如果没有的话
            if 'LIMIT' not in query.upper() and 'SELECT' in query.upper():
                optimized_query += " LIMIT 1000"
        
        else:
            # PostgreSQL特定优化
            if 'ORDER BY' in query.upper() and 'LIMIT' not in query.upper():
                optimized_query += " LIMIT 1000"
        
        return optimized_query, optimized_params
