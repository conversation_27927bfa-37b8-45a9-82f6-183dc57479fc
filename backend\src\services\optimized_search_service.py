#!/usr/bin/env python3
"""
优化版高性能搜索服务
实施代码层面的性能优化
"""

import asyncio
import time
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from functools import lru_cache
from collections import defaultdict
import logging

from sqlalchemy.orm import Session
from sqlalchemy import text, and_, or_
from sqlalchemy.orm import joinedload

from ..models import File
from ..database import create_independent_db_session
from .high_performance_search import SearchQuery, SearchResult

logger = logging.getLogger(__name__)

class OptimizedSearchService:
    """优化版搜索服务"""
    
    def __init__(self):
        # 使用哈希表优化查找
        self._tag_lookup_cache = {}
        self._query_cache = {}
        self._file_cache = {}
        
        # 预编译的SQL查询
        self._compiled_queries = self._prepare_compiled_queries()
    
    def _prepare_compiled_queries(self) -> Dict[str, str]:
        """预编译SQL查询"""
        return {
            'base_search': """
                SELECT f.id, f.file_name, f.file_path, f.thumbnail_small_path,
                       f.quality_score, f.created_at, f.tags
                FROM files f
                WHERE f.case_id = :case_id
            """,
            'tag_search': """
                SELECT f.id, f.file_name, f.file_path, f.thumbnail_small_path,
                       f.quality_score, f.created_at, f.tags
                FROM files f
                WHERE f.case_id = :case_id
                  AND f.tags->'tags'->'user' @> :user_tags
            """,
            'complex_search': """
                SELECT f.id, f.file_name, f.file_path, f.thumbnail_small_path,
                       f.quality_score, f.created_at, f.tags
                FROM files f
                WHERE f.case_id = :case_id
                  AND (:user_tags IS NULL OR f.tags->'tags'->'user' @> :user_tags)
                  AND (:quality_min IS NULL OR f.quality_score >= :quality_min)
                  AND (:custom_tags IS NULL OR f.tags->'tags'->'custom' @> :custom_tags)
            """,
            'count_query': """
                SELECT COUNT(*)
                FROM files f
                WHERE f.case_id = :case_id
                  AND (:user_tags IS NULL OR f.tags->'tags'->'user' @> :user_tags)
                  AND (:quality_min IS NULL OR f.quality_score >= :quality_min)
                  AND (:custom_tags IS NULL OR f.tags->'tags'->'custom' @> :custom_tags)
            """
        }
    
    @lru_cache(maxsize=1000)
    def _get_cached_query_key(self, case_id: int, user_tags: tuple = None, 
                             custom_tags: tuple = None, quality_min: float = None,
                             limit: int = 10, offset: int = 0) -> str:
        """生成缓存键"""
        return f"{case_id}:{user_tags}:{custom_tags}:{quality_min}:{limit}:{offset}"
    
    async def search_files_optimized(self, query: SearchQuery) -> Tuple[List[SearchResult], int]:
        """优化版文件搜索"""
        start_time = time.time()
        
        # 1. 缓存检查
        cache_key = self._get_cached_query_key(
            query.case_id,
            tuple(sorted(query.user_tags)) if query.user_tags else None,
            tuple(sorted(query.custom_tags.items())) if query.custom_tags else None,
            query.quality_min,
            query.limit,
            query.offset
        )
        
        if cache_key in self._query_cache:
            cached_result = self._query_cache[cache_key]
            # 检查缓存是否过期（5分钟）
            if time.time() - cached_result['timestamp'] < 300:
                logger.debug(f"缓存命中: {cache_key}")
                return cached_result['results'], cached_result['total']
        
        # 2. 使用原生SQL查询
        db = create_independent_db_session()
        try:
            results, total = await self._execute_optimized_query(db, query)
            
            # 3. 缓存结果
            self._query_cache[cache_key] = {
                'results': results,
                'total': total,
                'timestamp': time.time()
            }
            
            # 4. 限制缓存大小
            if len(self._query_cache) > 1000:
                # 删除最旧的缓存项
                oldest_key = min(self._query_cache.keys(), 
                               key=lambda k: self._query_cache[k]['timestamp'])
                del self._query_cache[oldest_key]
            
            query_time = (time.time() - start_time) * 1000
            logger.debug(f"优化查询耗时: {query_time:.2f}ms")
            
            return results, total
            
        finally:
            db.close()
    
    async def _execute_optimized_query(self, db: Session, query: SearchQuery) -> Tuple[List[SearchResult], int]:
        """执行优化的查询"""
        
        # 选择合适的预编译查询
        if query.user_tags and not query.custom_tags and not query.quality_min:
            # 简单标签查询
            sql_query = self._compiled_queries['tag_search']
            params = {
                'case_id': query.case_id,
                'user_tags': query.user_tags
            }
        else:
            # 复杂查询
            sql_query = self._compiled_queries['complex_search']
            params = {
                'case_id': query.case_id,
                'user_tags': query.user_tags,
                'custom_tags': query.custom_tags,
                'quality_min': query.quality_min
            }
        
        # 添加排序和分页
        if query.sort_by == 'date':
            sql_query += " ORDER BY f.created_at"
        elif query.sort_by == 'quality':
            sql_query += " ORDER BY f.quality_score"
        elif query.sort_by == 'name':
            sql_query += " ORDER BY f.file_name"
        else:
            sql_query += " ORDER BY f.id"
        
        if query.sort_order == 'desc':
            sql_query += " DESC"
        else:
            sql_query += " ASC"
        
        sql_query += f" LIMIT {query.limit} OFFSET {query.offset}"
        
        # 执行查询
        result = db.execute(text(sql_query), params)
        rows = result.fetchall()
        
        # 转换为SearchResult对象
        results = []
        for row in rows:
            # 使用哈希表优化的匹配计数
            match_count = self._calculate_match_count_optimized(row.tags, query)
            
            result_obj = SearchResult(
                file_id=row.id,
                file_name=row.file_name,
                file_path=row.file_path,
                thumbnail_path=row.thumbnail_small_path,
                tag_match_count=match_count,
                relevance_score=1.0,  # 简化相关性计算
                quality_score=row.quality_score or 0.0,
                created_at=row.created_at,
                tags=row.tags or {}
            )
            results.append(result_obj)
        
        # 获取总数（优化：只在需要时计算）
        if len(results) < query.limit:
            total = query.offset + len(results)
        else:
            count_result = db.execute(text(self._compiled_queries['count_query']), params)
            total = count_result.scalar()
        
        return results, total
    
    def _calculate_match_count_optimized(self, tags: Dict, query: SearchQuery) -> int:
        """优化的匹配计数计算"""
        if not tags or not tags.get('tags'):
            return 0
        
        match_count = 0
        tag_data = tags['tags']
        
        # 使用集合操作优化标签匹配
        if query.user_tags and 'user' in tag_data:
            user_tags_set = set(query.user_tags)
            file_user_tags_set = set(tag_data['user']) if tag_data['user'] else set()
            match_count += len(user_tags_set.intersection(file_user_tags_set))
        
        if query.custom_tags and 'custom' in tag_data:
            for key, value in query.custom_tags.items():
                if tag_data['custom'].get(key) == value:
                    match_count += 1
        
        if query.ai_tags and 'ai' in tag_data:
            ai_tags_set = set(query.ai_tags)
            file_ai_tags_set = set(tag_data['ai']) if tag_data['ai'] else set()
            match_count += len(ai_tags_set.intersection(file_ai_tags_set))
        
        return match_count
    
    def clear_cache(self):
        """清理缓存"""
        self._query_cache.clear()
        self._tag_lookup_cache.clear()
        self._file_cache.clear()
        logger.info("搜索缓存已清理")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            'query_cache_size': len(self._query_cache),
            'tag_cache_size': len(self._tag_lookup_cache),
            'file_cache_size': len(self._file_cache),
            'cache_hit_rate': self._calculate_cache_hit_rate()
        }
    
    def _calculate_cache_hit_rate(self) -> float:
        """计算缓存命中率"""
        # 简化实现，实际应该跟踪命中和未命中次数
        return 0.0

# 创建全局实例
optimized_search_service = OptimizedSearchService()
