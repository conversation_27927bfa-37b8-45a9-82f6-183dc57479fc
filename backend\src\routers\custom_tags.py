# src/routers/custom_tags.py
"""
自定义标签API路由
提供自定义标签的创建、管理和使用功能
"""
import logging
from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Body
from sqlalchemy.orm import Session
from pydantic import BaseModel

from ..database_manager import get_master_db
from ..services.custom_tag_manager import CustomTagManager
from .. import schemas

class AddTagsRequest(BaseModel):
    tag_names: List[str]

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1", tags=["custom_tags"])


@router.post("/cases/{case_id}/custom-tags", response_model=schemas.CustomTag)
async def create_custom_tag(
    case_id: int,
    tag_data: schemas.CustomTagCreate,
    db: Session = Depends(get_master_db)
):
    """创建自定义标签"""
    try:
        logger.info(f"创建自定义标签请求: case_id={case_id}, tag_data={tag_data}")
        manager = CustomTagManager(case_id)
        result = manager.create_custom_tag(tag_data)
        logger.info(f"创建自定义标签成功: {result}")
        return result
    except ValueError as e:
        logger.error(f"创建自定义标签参数错误: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"创建自定义标签失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="创建自定义标签失败")


@router.get("/cases/{case_id}/custom-tags", response_model=List[schemas.CustomTag])
async def get_custom_tags(
    case_id: int,
    db: Session = Depends(get_master_db)
):
    """获取案例的所有自定义标签"""
    try:
        manager = CustomTagManager(case_id)
        return manager.get_custom_tags()
    except Exception as e:
        logger.error(f"获取自定义标签失败: {e}")
        raise HTTPException(status_code=500, detail="获取自定义标签失败")


@router.get("/cases/{case_id}/custom-tags/{tag_id}", response_model=schemas.CustomTag)
async def get_custom_tag(
    case_id: int,
    tag_id: int,
    db: Session = Depends(get_master_db)
):
    """获取单个自定义标签"""
    try:
        manager = CustomTagManager(case_id)
        tag = manager.get_custom_tag(tag_id)
        if not tag:
            raise HTTPException(status_code=404, detail="标签不存在")
        return tag
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取自定义标签失败: {e}")
        raise HTTPException(status_code=500, detail="获取自定义标签失败")


@router.put("/cases/{case_id}/custom-tags/{tag_id}", response_model=schemas.CustomTag)
async def update_custom_tag(
    case_id: int,
    tag_id: int,
    tag_data: schemas.CustomTagUpdate,
    db: Session = Depends(get_master_db)
):
    """更新自定义标签"""
    try:
        manager = CustomTagManager(case_id)
        tag = manager.update_custom_tag(tag_id, tag_data)
        if not tag:
            raise HTTPException(status_code=404, detail="标签不存在")
        return tag
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新自定义标签失败: {e}")
        raise HTTPException(status_code=500, detail="更新自定义标签失败")


@router.delete("/cases/{case_id}/custom-tags/{tag_id}")
async def delete_custom_tag(
    case_id: int,
    tag_id: int,
    db: Session = Depends(get_master_db)
):
    """删除自定义标签（不可恢复）"""
    try:
        manager = CustomTagManager(case_id)
        success = manager.delete_custom_tag(tag_id)
        if not success:
            raise HTTPException(status_code=404, detail="标签不存在")
        return {"message": "标签已删除", "success": True}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除自定义标签失败: {e}")
        raise HTTPException(status_code=500, detail="删除自定义标签失败")


@router.post("/cases/{case_id}/files/{file_id}/custom-tags")
async def add_custom_tags_to_file(
    case_id: int,
    file_id: int,
    tag_names: List[str] = Body(...),
    db: Session = Depends(get_master_db)
):
    """为文件添加自定义标签"""
    try:
        manager = CustomTagManager(case_id)
        result = manager.add_tags_to_file(file_id, tag_names)
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"为文件添加自定义标签失败: {e}")
        raise HTTPException(status_code=500, detail="为文件添加自定义标签失败")


@router.delete("/cases/{case_id}/files/{file_id}/custom-tags/{tag_name}")
async def remove_custom_tag_from_file(
    case_id: int,
    file_id: int,
    tag_name: str,
    db: Session = Depends(get_master_db)
):
    """从文件移除自定义标签"""
    try:
        manager = CustomTagManager(case_id)
        success = manager.remove_tag_from_file(file_id, tag_name)
        if not success:
            raise HTTPException(status_code=404, detail="文件没有该标签")
        return {"message": "标签已移除", "success": True}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"从文件移除自定义标签失败: {e}")
        raise HTTPException(status_code=500, detail="从文件移除自定义标签失败")


@router.post("/cases/{case_id}/custom-tags/{tag_name}/batch")
async def batch_tag_operation(
    case_id: int,
    tag_name: str,
    operation: schemas.BatchTagOperation,
    db: Session = Depends(get_master_db)
):
    """批量标签操作（添加或移除）"""
    try:
        manager = CustomTagManager(case_id)
        result = manager.batch_tag_operation(
            operation.file_ids, 
            tag_name, 
            operation.action
        )
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"批量标签操作失败: {e}")
        raise HTTPException(status_code=500, detail="批量标签操作失败")


@router.get("/cases/{case_id}/files/{file_id}/custom-tags", response_model=List[schemas.CustomTag])
async def get_file_custom_tags(
    case_id: int,
    file_id: int,
    db: Session = Depends(get_master_db)
):
    """获取文件的自定义标签"""
    try:
        manager = CustomTagManager(case_id)
        return manager.get_file_custom_tags(file_id)
    except Exception as e:
        logger.error(f"获取文件自定义标签失败: {e}")
        raise HTTPException(status_code=500, detail="获取文件自定义标签失败")
