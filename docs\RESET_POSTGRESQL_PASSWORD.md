# 🔑 PostgreSQL密码重置指南

## 📋 当前状态

✅ PostgreSQL 17.5 已安装并运行  
❌ 无法连接 - 密码未知  
🎯 需要重置postgres用户密码  

## 🔧 方法1: 使用pgAdmin (推荐)

### 步骤1: 打开pgAdmin
1. 在开始菜单搜索 "pgAdmin"
2. 打开 pgAdmin 4

### 步骤2: 连接到服务器
1. 在左侧面板，右键点击 "Servers"
2. 选择 "Register" → "Server"
3. 填写连接信息:
   - **Name**: Local PostgreSQL
   - **Host**: localhost
   - **Port**: 5432
   - **Username**: postgres
   - **Password**: (尝试您记得的密码)

### 步骤3: 重置密码 (如果连接成功)
1. 展开服务器 → Login/Group Roles
2. 右键点击 "postgres" 用户
3. 选择 "Properties"
4. 在 "Definition" 标签页设置新密码
5. 点击 "Save"

## 🔧 方法2: 修改认证配置

### 步骤1: 找到pg_hba.conf文件
文件位置通常在:
```
C:\Program Files\PostgreSQL\17\data\pg_hba.conf
```

### 步骤2: 备份并修改文件
1. **以管理员身份**打开记事本
2. 打开 `pg_hba.conf` 文件
3. 找到这一行:
   ```
   host    all             all             127.0.0.1/32            scram-sha-256
   ```
4. 临时改为:
   ```
   host    all             all             127.0.0.1/32            trust
   ```
5. 保存文件

### 步骤3: 重启PostgreSQL服务
```bash
# 以管理员身份运行命令提示符
net stop postgresql-x64-17
net start postgresql-x64-17
```

### 步骤4: 重置密码
```bash
# 现在可以无密码连接
"C:\Program Files\PostgreSQL\17\bin\psql.exe" -U postgres -h localhost

# 在PostgreSQL命令行中执行:
ALTER USER postgres PASSWORD 'new_password';

# 退出
\q
```

### 步骤5: 恢复认证配置
1. 将 `pg_hba.conf` 中的 `trust` 改回 `scram-sha-256`
2. 重启PostgreSQL服务

## 🔧 方法3: 使用Windows认证

### 步骤1: 检查Windows认证
在 `pg_hba.conf` 中查找:
```
host    all             all             127.0.0.1/32            sspi
```

如果存在，可以尝试:
```bash
"C:\Program Files\PostgreSQL\17\bin\psql.exe" -U postgres -h localhost
```

## 🚀 快速测试脚本

创建一个批处理文件 `test_postgres.bat`:

```batch
@echo off
echo 测试PostgreSQL连接...

set /p password="请输入postgres密码: "

echo SELECT version(); | "C:\Program Files\PostgreSQL\17\bin\psql.exe" -U postgres -h localhost -W

pause
```

## 🎯 推荐密码

为了方便后续使用，建议设置密码为:
- `postgres` (简单)
- `mizzy_star_2025` (项目相关)
- 或您自定义的安全密码

## ✅ 验证连接

密码重置后，运行以下命令验证:

```bash
# 测试连接
python test_postgresql_connection.py

# 如果成功，继续配置
cd backend
python init_database.py --reset
python test_postgresql_migration.py
```

## 🆘 如果所有方法都失败

### 选项1: 重新安装PostgreSQL
1. 卸载当前PostgreSQL
2. 重新下载安装程序
3. 安装时记住设置的密码

### 选项2: 使用Docker PostgreSQL
```bash
# 安装Docker Desktop
# 运行PostgreSQL容器
docker run --name postgres-mizzy -e POSTGRES_PASSWORD=mizzy_star_2025 -p 5432:5432 -d postgres:17

# 更新.env文件中的密码为: mizzy_star_2025
```

## 📞 需要帮助?

如果您成功重置了密码，请告诉我新密码，我将帮您完成后续配置。

常见的成功密码:
- `postgres`
- `password`
- `admin`
- 您在安装时设置的密码

**下一步**: 重置密码后，运行 `python test_postgresql_connection.py` 验证连接。
