# 🌟 迷星 Mizzy Star - 后端服务

## 📊 项目状态

**版本**: v0.3 (PostgreSQL架构)  
**状态**: ✅ **生产就绪**  
**数据库**: PostgreSQL 100%  
**最后更新**: 2025-07-22  

### 🎉 重大更新
- ✅ **PostgreSQL重构完成**: 系统已完全迁移到PostgreSQL架构
- ✅ **SQLite代码清理完成**: 所有SQLite相关代码已完全移除
- ✅ **架构统一**: 100% PostgreSQL单一数据库架构
- ✅ **性能优化**: 充分利用PostgreSQL高级特性

## 🚀 快速开始

### 系统要求
- Python 3.8+
- PostgreSQL 12+
- Redis (可选，用于缓存)

### 安装依赖
```bash
# 安装Python依赖
pip install -r requirements.txt

# 生产环境依赖
pip install -r requirements-production.txt
```

### 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/mizzy_star_db
```

### 数据库初始化
```bash
# 创建数据库表
python -c "from src.database import Base, engine; Base.metadata.create_all(bind=engine)"
```

### 启动服务
```bash
# 开发模式
uvicorn src.main:app --reload --host 0.0.0.0 --port 8000

# 生产模式
gunicorn -c gunicorn.conf.py src.main:app
```

## 🏗️ 架构概览

### 数据库架构
- **主数据库**: PostgreSQL (统一存储所有数据)
- **连接池**: SQLAlchemy + asyncpg
- **ORM**: SQLAlchemy 2.0
- **迁移**: Alembic (可选)

### 服务架构
```
├── src/
│   ├── main.py              # FastAPI应用入口
│   ├── database.py          # 数据库配置
│   ├── models.py            # 数据模型
│   ├── schemas.py           # Pydantic模式
│   ├── routers/             # API路由
│   ├── crud/                # 数据库操作
│   ├── services/            # 业务逻辑
│   └── analysis/            # 分析模块
├── tests/                   # 测试代码
├── docs/                    # 项目文档
└── data/                    # 数据存储
```

## 📚 文档

详细文档请查看 [docs/README.md](docs/README.md)

### 主要文档
- **[PostgreSQL重构报告](docs/migration/POSTGRESQL_REFACTORING_COMPLETE.md)** - 重构项目完成报告
- **[SQLite清理总结](docs/migration/FINAL_SQLITE_CLEANUP_SUMMARY.md)** - 代码清理项目总结
- **[API文档](docs/api/)** - REST API接口文档

## 🔧 开发

### 代码结构
- **路由层** (`routers/`): FastAPI路由处理
- **业务层** (`services/`): 核心业务逻辑
- **数据层** (`crud/`): 数据库操作
- **模型层** (`models.py`): SQLAlchemy模型

### 开发规范
- 使用Python类型提示
- 遵循PEP 8代码规范
- 编写单元测试
- 使用异步编程模式

### 测试
```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/unit/

# 生成覆盖率报告
pytest --cov=src tests/
```

## 🚀 部署

### Docker部署
```bash
# 构建镜像
docker build -f Dockerfile.production -t mizzy-star-backend .

# 运行容器
docker run -p 8000:8000 mizzy-star-backend
```

### 生产配置
- 使用 `gunicorn.conf.py` 配置
- 启用PostgreSQL连接池
- 配置日志记录
- 设置环境变量

## 📈 性能特性

### PostgreSQL优势
- **高性能查询**: 利用PostgreSQL查询优化器
- **JSONB支持**: 高效存储和查询JSON数据
- **全文搜索**: 内置全文搜索功能
- **并发处理**: 优秀的并发性能

### 优化特性
- **连接池**: 高效的数据库连接管理
- **异步处理**: 基于asyncio的异步架构
- **缓存策略**: Redis缓存支持
- **批量操作**: 优化的批量数据处理

## 🔒 安全特性

- **SQL注入防护**: 使用参数化查询
- **输入验证**: Pydantic数据验证
- **错误处理**: 统一的异常处理机制
- **日志记录**: 完整的操作日志

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 支持

- **问题反馈**: GitHub Issues
- **技术支持**: 联系开发团队
- **文档**: [docs/README.md](docs/README.md)

---

**迷星 Mizzy Star** - 基于PostgreSQL的高性能标签数据管理解决方案
**版本**: v0.3 | **架构**: PostgreSQL | **状态**: 生产就绪 ✅
