# 文件导入API使用指南

## 🎯 **API端点职责明确化**

为了避免文件被意外复制的问题，我们将文件导入API重新设计为两个明确职责的端点：

### 📤 **Web上传端点（会复制文件）**

**端点**: `POST /api/v1/cases/{case_id}/files/upload-and-copy`

**用途**:
- 网页文件上传
- 处理临时文件（必须复制到永久位置）
- 需要将文件复制到案例目录的场景

**行为**:
- ⚠️ **会复制文件**到案例的 `uploads/` 目录
- 生成缩略图
- 应用规则引擎处理标签

**前端调用**:
```javascript
// 使用 uploadFile 或 uploadAndCopyFile 方法
await api.uploadFile(caseId, file);
await api.uploadAndCopyFile(caseId, file);
```

### 📁 **本地导入端点（只记录路径）**

**端点**: `POST /api/v1/cases/{case_id}/files/import-by-path`

**用途**:
- 本地文件导入
- 保持原始文件位置不变
- 大文件导入（避免重复存储）

**行为**:
- ✅ **只记录文件路径**，不复制原始文件
- 生成缩略图到案例目录
- 应用规则引擎处理标签

**前端调用**:
```javascript
// 使用 importLocalFile 或 importByPath 方法
await api.importLocalFile(caseId, filePath);
await api.importByPath(caseId, filePath);
```

## 🔧 **使用场景指南**

### **场景1: 文件导入（推荐，不复制文件）**
```javascript
// Electron环境中的文件导入（默认行为）
const handleFileImport = async (files) => {
    for (const file of files) {
        // 在Electron中，优先使用路径导入，不复制文件
        if (file.path) {
            await api.importByPath(caseId, file.path);
        } else {
            // 如果无法获取路径，才使用上传模式
            await api.uploadAndCopyFile(caseId, file);
        }
    }
};
```

### **场景2: 封面设置（需要复制文件）**
```javascript
// 设置案例封面（唯一需要复制文件的场景）
const handleSetCover = async (file) => {
    // 封面需要复制到案例目录以确保稳定性
    await api.setCaseCover(caseId, file);
};
```

### **场景3: 批量本地导入**
```javascript
// 批量导入本地目录中的文件
const handleBatchImport = async (directoryPath) => {
    const response = await api.post(`/cases/${caseId}/files/batch-import`, {
        directory_path: directoryPath,
        recursive: true
    });
    // 批量导入使用 import-by-path 逻辑，不会复制文件
};
```

### **场景4: 特殊上传需求（明确需要复制）**
```javascript
// 用户明确要求复制文件到案例目录的特殊场景
const handleSpecialUpload = async (files) => {
    for (const file of files) {
        // 明确使用复制模式
        await api.uploadAndCopyFile(caseId, file);
    }
};
```

## 📊 **API对比表**

| 特性 | upload-and-copy | import-by-path |
|------|----------------|----------------|
| **文件复制** | ✅ 复制到uploads目录 | ❌ 不复制，只记录路径 |
| **适用场景** | Web上传 | 本地导入 |
| **文件来源** | 临时文件/内存 | 本地文件系统 |
| **存储空间** | 占用额外空间 | 不占用额外空间 |
| **缩略图生成** | ✅ | ✅ |
| **规则引擎处理** | ✅ | ✅ |
| **性能** | 较慢（需要复制） | 较快（只记录路径） |

## 🚨 **重要注意事项**

### **避免错误使用**

❌ **错误**: 在本地文件导入时使用 `upload-and-copy`
```javascript
// 这会导致文件被复制！
await api.uploadFile(caseId, localFile);
```

✅ **正确**: 在本地文件导入时使用 `import-by-path`
```javascript
// 这只会记录路径，不复制文件
await api.importByPath(caseId, localFilePath);
```

### **前端组件使用指南**

**文件上传组件**:
```javascript
// FileUpload.tsx - 用于Web上传
const handleUpload = async (files) => {
    await CaseService.uploadFiles(caseId, files); // 使用 upload-and-copy
};
```

**本地导入组件**:
```javascript
// LocalImport.tsx - 用于本地导入
const handleImport = async (filePaths) => {
    await CaseService.importFilesByPath(caseId, filePaths); // 使用 import-by-path
};
```

## 🔍 **故障排除**

### **问题**: 文件被意外复制
**原因**: 使用了错误的API端点
**解决**: 检查前端代码，确保本地导入使用 `import-by-path` 端点

### **问题**: 文件路径无效
**原因**: 使用 `import-by-path` 时文件路径不存在
**解决**: 在调用API前验证文件路径存在性

### **问题**: 上传失败
**原因**: 可能使用了错误的Content-Type或端点
**解决**:
- Web上传使用 `multipart/form-data`
- 本地导入使用 `multipart/form-data` 传递 `file_path` 参数

## 📈 **性能优化建议**

1. **大文件处理**: 优先使用 `import-by-path` 避免复制
2. **批量操作**: 使用批量导入API而不是单个文件循环
3. **缓存策略**: 缩略图生成会被缓存，重复导入同一文件会更快

## 🔄 **迁移指南**

如果您的现有代码使用了旧的端点：

1. **替换端点URL**:
   - `/upload` → `/upload-and-copy`
   - `/import-local` → `/import-by-path`

2. **更新前端调用**:
   - 检查所有文件导入相关的代码
   - 根据使用场景选择正确的API方法

3. **测试验证**:
   - 验证Web上传功能正常
   - 验证本地导入不会复制文件
   - 检查文件路径记录正确

通过遵循这个指南，您可以确保文件导入功能按预期工作，避免不必要的文件复制。
