# src/main.py
"""
迷星 Mizzy Star - 主应用程序
支持同步和异步操作，提升并发性能
"""
import sys
import os

# 添加backend目录到Python路径
backend_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if backend_dir not in sys.path:
    sys.path.insert(0, backend_dir)

from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 🔧 性能优化：延迟导入核心模块
# 移除启动时的大量模块导入，改为按需导入


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理 - 延迟导入版本"""
    print("🚀 启动时：延迟导入重量级模块...")

    # 延迟导入数据库相关模块
    from src.database_manager import db_manager
    from src.database_config import db_config, DatabaseType
    from src import models

    # 启动时：验证数据库配置
    if db_config.master_db_type == DatabaseType.POSTGRESQL:
        if not db_config.validate_postgresql_connection():
            raise RuntimeError("PostgreSQL连接配置验证失败")

    # 初始化数据库表结构
    master_engine = db_manager.master_engine
    models.Base.metadata.create_all(bind=master_engine, tables=[
        models.Case.__table__,
        models.SystemConfig.__table__,
        models.CaseProcessingRule.__table__
    ])

    print("✅ 数据库初始化完成")

    yield

    # 关闭时：清理数据库连接
    try:
        await db_manager.cleanup_connections()
        print("🗑️ 数据库连接已清理")
    except Exception as e:
        print(f"清理数据库连接时出错: {e}")


# 创建FastAPI应用实例
app = FastAPI(
    title="迷星 Mizzy Star V1.0 API",
    description="一个由可配置规则驱动的、高性能的标签数据核心。支持元信息的自动化、智能化采集与结构化存储，基于PostgreSQL架构，支持回收站功能和异步操作。",
    version="1.0.0",
    lifespan=lifespan,
    debug=False  # 🔧 性能优化：禁用调试模式
)

# 🔧 添加性能优化中间件
from fastapi.middleware.gzip import GZipMiddleware

# 添加Gzip压缩中间件
app.add_middleware(GZipMiddleware, minimum_size=1000)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 🔧 架构重构：延迟导入策略
print("🔧 架构重构模式：实施延迟导入和解耦策略...")

# 只导入必要的核心模块
# 其他模块将在lifespan或按需导入

# 🔧 策略二：路由工厂模式 - 延迟导入路由
def register_core_routes():
    """注册核心路由（高性能要求）"""
    print("📦 注册核心路由...")
    # 只导入gallery_system，保持高性能
    # 其他路由将按需导入

def register_management_routes():
    """注册管理路由（功能完整性）"""
    print("📦 延迟注册管理路由...")
    try:
        from src.routers import cases
        app.include_router(cases.router, prefix="/api/v1")
        print("✅ 案例管理路由已注册")
    except ImportError as e:
        print(f"❌ 案例管理路由导入失败: {e}")

    try:
        from src.routers import tags
        app.include_router(tags.router, prefix="/api/v1/tags")
        app.include_router(tags.router, prefix="/api/v1/cases", tags=["Tags (Cases Compat)"])
        print("✅ 标签管理路由已注册")
    except ImportError as e:
        print(f"❌ 标签管理路由导入失败: {e}")
        
    # 新版标签服务API
    try:
        from src.routers import tag_service_api
        app.include_router(tag_service_api.router)
        print("✅ 标签服务API路由已注册")
    except ImportError as e:
        print(f"❌ 标签服务API路由导入失败: {e}")

    # 自定义标签功能 - 移到管理路由中立即注册
    try:
        from src.routers import custom_tags
        app.include_router(custom_tags.router)
        print("✅ 自定义标签路由已注册")
    except ImportError as e:
        print(f"❌ 自定义标签路由导入失败: {e}")

def register_extended_routes():
    """注册扩展路由（可选功能）"""
    print("📦 延迟注册扩展路由...")

    # 垃圾箱功能
    try:
        from src.routers import trash
        app.include_router(trash.router, prefix="/api/v1")
        print("✅ 垃圾箱路由已注册")
    except ImportError as e:
        print(f"❌ 垃圾箱路由导入失败: {e}")

    # 质量分析功能
    try:
        from src.routers import quality
        app.include_router(quality.router, prefix="/api/v1")
        print("✅ 质量分析路由已注册")
    except ImportError as e:
        print(f"❌ 质量分析路由导入失败: {e}")

    # 封面管理功能
    try:
        from src.routers import cover
        app.include_router(cover.router, prefix="/api/v1")
        print("✅ 封面管理路由已注册")
    except ImportError as e:
        print(f"❌ 封面管理路由导入失败: {e}")

    # 搜索v2功能
    try:
        from src.routers import search_v2
        app.include_router(search_v2.router)
        print("✅ 搜索v2路由已注册")
    except ImportError as e:
        print(f"❌ 搜索v2路由导入失败: {e}")

    # 文件名提取功能
    try:
        from src.routers import filename_extraction
        app.include_router(filename_extraction.router)
        print("✅ 文件名提取路由已注册")
    except ImportError as e:
        print(f"❌ 文件名提取路由导入失败: {e}")



# 立即注册核心路由
register_core_routes()

# 立即注册管理路由（包括自定义标签）
register_management_routes()

# 创建延迟注册端点
@app.post("/admin/register-routes")
async def register_additional_routes(route_type: str = "management"):
    """管理员端点：按需注册额外路由"""
    if route_type == "management":
        register_management_routes()
        return {"message": "管理路由已注册", "type": "management"}
    elif route_type == "extended":
        register_extended_routes()
        return {"message": "扩展路由已注册", "type": "extended"}
    elif route_type == "all":
        register_management_routes()
        register_extended_routes()
        return {"message": "所有路由已注册", "type": "all"}
    else:
        return {"error": "无效的路由类型", "valid_types": ["management", "extended", "all"]}

# 标签即画廊系统路由
try:
    from .routers import gallery_system
    app.include_router(gallery_system.router)  # 标签即画廊系统路由
    print("✅ 标签即画廊系统路由已加载")
except ImportError as e:
    print(f"警告: 无法导入gallery_system模块: {e}")
    # 继续运行，不影响其他功能


@app.get("/", tags=["Root"])
def read_root():
    return {
        "message": "欢迎来到迷星 Mizzy Star V1.0 API！",
        "version": "1.0.0",
        "features": [
            "multi-db",
            "trash-bin",
            "soft-delete",
            "async-operations",
            "connection-pooling",
            "batch-operations",
            "rule-driven-tagging",
            "flexible-tag-structure",
            "tag-based-filtering"
        ]
    }


@app.get("/health", tags=["Health"])
async def health_check():
    """健康检查端点"""
    try:
        # 简化健康检查，避免异步数据库依赖
        return {
            "status": "healthy",
            "version": "1.0.0",
            "features": [
                "postgresql-db",
                "trash-bin",
                "soft-delete",
                "sync-operations",
                "connection-pooling",
                "rule-driven-tagging",
                "flexible-tag-structure",
                "tag-based-filtering"
            ],
            "database": {
                "sync_engine": "active",
                "async_engine": "disabled",
                "type": "PostgreSQL"
            }
        }
    except Exception as e:
        return {
            "status": "degraded",
            "version": "1.0.0",
            "error": str(e)
        }


if __name__ == "__main__":
    import uvicorn
    import argparse
    
    # 添加命令行参数解析
    parser = argparse.ArgumentParser(description="Mizzy Star 服务器")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口号")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--log-level", type=str, default="info", help="日志级别")
    
    args = parser.parse_args()
    
    print(f"🚀 启动优化后的Mizzy Star服务器... 端口: {args.port}")
    uvicorn.run(
        app,
        host=args.host,
        port=args.port,
        log_level=args.log_level
    )

