# 生产环境额外依赖

# Web服务器
gunicorn==21.2.0
uvicorn[standard]==0.24.0

# 缓存
redis==5.0.1
aioredis==2.0.1

# 监控和指标
prometheus-client==0.19.0
prometheus-fastapi-instrumentator==6.1.0

# 日志
structlog==23.2.0
python-json-logger==2.0.7

# 安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# 限流
slowapi==0.1.9

# 数据验证和序列化
pydantic[email]==2.5.0

# 环境配置
python-decouple==3.8

# 数据库迁移
alembic==1.13.0

# 性能分析
py-spy==0.3.14

# 错误追踪
sentry-sdk[fastapi]==1.38.0

# 健康检查
httpx==0.25.2

# 进程管理
supervisor==4.2.5

# 系统监控
psutil==5.9.6

# 时区处理
pytz==2023.3

# 异步任务队列
celery[redis]==5.3.4

# 文件处理
aiofiles==23.2.1

# 压缩
gzip-stream==0.3.0

# 配置管理
dynaconf==3.2.4
