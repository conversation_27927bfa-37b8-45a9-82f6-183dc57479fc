// Project Novak - Core Type Definitions
// 严格类型安全，零容忍any类型

// ============================================================================
// API Response Types
// ============================================================================

export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: 'success' | 'error';
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// ============================================================================
// Core Business Types (基于后端API实际字段)
// ============================================================================

export interface Case {
  id: number;
  case_name: string;
  description: string | null;
  created_at: string;
  status: 'active' | 'deleted' | 'permanently_deleted';
  deleted_at: string | null;
  db_path: string;
}

export interface FileItem {
  id: number;
  case_id: number;
  file_name: string;
  file_path: string;
  file_type: string;
  file_size: number;
  width: number | null;
  height: number | null;
  created_at: string;
  taken_at: string | null;
  thumbnail_small_path: string | null;
  tags: FileTagsData | null;
  quality_score: number | null;
  sharpness: number | null;
  brightness: number | null;
  dynamic_range: number | null;
  num_faces: number | null;
  face_sharpness: number | null;
  face_quality: number | null;
  cluster_id: string | null;
  phash: string | null;
}

// ============================================================================
// Tag System Types (配置驱动的标签系统)
// ============================================================================

export interface FileTagsData {
  properties: {
    filename: string;
    qualityScore?: number;
    fileSize?: number;
    [key: string]: unknown;
  };
  tags: {
    metadata: Record<string, string>;
    cv: Record<string, unknown>;
    user: string[];
    ai: string[];
    custom: Record<string, string>;
    [key: string]: unknown;
  };
}

export interface TagCategory {
  displayName: string;
  icon: string;
  handler: string;
  titleTemplate: string;
  priority: number;
  editable: boolean;
}

export interface TagItem {
  key: string;
  value: string | number;
  count: number;
  fileIds: number[];
  category: string;
}

// ============================================================================
// UI State Types (Zustand Store)
// ============================================================================

export interface GlobalUIState {
  // 面板显示状态
  showCatalogPanel: boolean;
  showInfoPanel: boolean;
  showWorkbench: boolean;
  isFullscreenGallery: boolean;
  
  // 当前选中状态
  selectedCaseId: number | null;
  selectedFileIds: number[];
  
  // 筛选状态
  activeFilters: Record<string, string>;
  searchQuery: string;
  
  // 布局状态
  catalogPanelWidth: number;
  infoPanelWidth: number;
  workbenchHeight: number;
}

// ============================================================================
// Component Props Types
// ============================================================================

export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
}

export interface InputProps {
  type?: 'text' | 'email' | 'password' | 'search';
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  error?: string;
  icon?: React.ReactNode;
  className?: string;
}

export interface FileCardProps {
  file: FileItem;
  selected?: boolean;
  onSelect?: (fileId: number) => void;
  onDoubleClick?: (file: FileItem) => void;
  className?: string;
}

export interface TagItemProps {
  tag: TagItem;
  onClick?: (tag: TagItem) => void;
  onRemove?: (tag: TagItem) => void;
  removable?: boolean;
  className?: string;
}

// ============================================================================
// Utility Types
// ============================================================================

export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

// 严格的事件处理器类型
export type EventHandler<T = void> = (event: T) => void;
export type AsyncEventHandler<T = void> = (event: T) => Promise<void>;
