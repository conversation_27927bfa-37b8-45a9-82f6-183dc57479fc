// Project Novak - Core Type Definitions
// 严格类型安全，零容忍any类型

import { z } from 'zod';

// ============================================================================
// API Response Schemas (Zod)
// ============================================================================

export const ApiResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    data: dataSchema,
    message: z.string().optional(),
    status: z.enum(['success', 'error']),
  });

export const PaginatedResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z.object({
    data: z.array(itemSchema),
    total: z.number(),
    page: z.number(),
    limit: z.number(),
    hasNext: z.boolean(),
    hasPrev: z.boolean(),
  });

// ============================================================================
// API Response Types (Inferred from Schemas)
// ============================================================================

export type ApiResponse<T> = {
  data: T;
  message?: string;
  status: 'success' | 'error';
};

export type PaginatedResponse<T> = {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
};

// ============================================================================
// Core Business Schemas (Zod)
// ============================================================================

export const CaseSchema = z.object({
  id: z.number(),
  case_name: z.string(),
  description: z.string().nullable(),
  created_at: z.string(),
  status: z.enum(['active', 'deleted', 'permanently_deleted']),
  deleted_at: z.string().nullable(),
  db_path: z.string(),
});

export const FileTagsDataSchema = z.object({
  properties: z.record(z.string(), z.unknown()),
  tags: z.object({
    metadata: z.record(z.string(), z.string()),
    cv: z.record(z.string(), z.unknown()),
    user: z.array(z.string()),
    ai: z.array(z.string()),
    custom: z.record(z.string(), z.string()),
  }).and(z.record(z.string(), z.unknown())), // 允许额外字段
});

export const FileItemSchema = z.object({
  id: z.number(),
  case_id: z.number(),
  file_name: z.string(),
  file_path: z.string(),
  file_type: z.string(),
  file_size: z.number(),
  width: z.number().nullable(),
  height: z.number().nullable(),
  created_at: z.string(),
  taken_at: z.string().nullable(),
  thumbnail_small_path: z.string().nullable(),
  tags: FileTagsDataSchema.nullable(),
  quality_score: z.number().nullable(),
  sharpness: z.number().nullable(),
  brightness: z.number().nullable(),
  dynamic_range: z.number().nullable(),
  num_faces: z.number().nullable(),
  face_sharpness: z.number().nullable(),
  face_quality: z.number().nullable(),
  cluster_id: z.string().nullable(),
  phash: z.string().nullable(),
});

// ============================================================================
// Core Business Types (Inferred from Schemas)
// ============================================================================

export type Case = z.infer<typeof CaseSchema>;
export type FileTagsData = z.infer<typeof FileTagsDataSchema>;
export type FileItem = z.infer<typeof FileItemSchema>;

// ============================================================================
// Tag System Schemas (Zod)
// ============================================================================

export const TagCategorySchema = z.object({
  displayName: z.string(),
  icon: z.string(),
  handler: z.string(),
  titleTemplate: z.string(),
  priority: z.number(),
  editable: z.boolean(),
});

export const TagItemSchema = z.object({
  key: z.string(),
  value: z.union([z.string(), z.number()]),
  count: z.number(),
  fileIds: z.array(z.number()),
  category: z.string(),
});

// ============================================================================
// API Request/Response Schemas
// ============================================================================

export const FileFiltersSchema = z.object({
  case_id: z.number().optional(),
  file_type: z.string().optional(),
  tags: z.array(z.string()).optional(),
  search: z.string().optional(),
  page: z.number().default(1),
  limit: z.number().default(50),
});

// 创建一个带默认值的版本
export const FileFiltersWithDefaultsSchema = FileFiltersSchema.partial().transform((data) => ({
  page: 1,
  limit: 50,
  ...data,
}));

export const TagOperationSchema = z.object({
  file_id: z.number(),
  category: z.string(),
  tag: z.string(),
  operation: z.enum(['add', 'remove']),
});

// ============================================================================
// Tag System Types (Inferred from Schemas)
// ============================================================================

export type TagCategory = z.infer<typeof TagCategorySchema>;
export type TagItem = z.infer<typeof TagItemSchema>;
export type FileFilters = z.infer<typeof FileFiltersSchema>;
export type TagOperation = z.infer<typeof TagOperationSchema>;

// ============================================================================
// UI State Types (Zustand Store)
// ============================================================================

export interface GlobalUIState {
  // 面板显示状态
  showCatalogPanel: boolean;
  showInfoPanel: boolean;
  showWorkbench: boolean;
  isFullscreenGallery: boolean;

  // 当前选中状态
  selectedCaseId: number | null;
  selectedFileIds: number[];

  // 筛选状态
  activeFilters: Record<string, string>;
  searchQuery: string;

  // 布局状态
  catalogPanelWidth: number;
  infoPanelWidth: number;
  workbenchHeight: number;
}

// ============================================================================
// Component Props Types
// ============================================================================

export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
}

export interface InputProps {
  type?: 'text' | 'email' | 'password' | 'search';
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  error?: string;
  icon?: React.ReactNode;
  className?: string;
}

export interface FileCardProps {
  file: FileItem;
  selected?: boolean;
  onSelect?: (fileId: number) => void;
  onDoubleClick?: (file: FileItem) => void;
  className?: string;
}

export interface TagItemProps {
  tag: TagItem;
  onClick?: (tag: TagItem) => void;
  onRemove?: (tag: TagItem) => void;
  removable?: boolean;
  className?: string;
}

// ============================================================================
// Utility Types
// ============================================================================

export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

// 严格的事件处理器类型
export type EventHandler<T = void> = (event: T) => void;
export type AsyncEventHandler<T = void> = (event: T) => Promise<void>;
