# src/services/tag_cache_manager.py
"""
标签缓存管理服务
提供高性能的标签数据缓存和查询功能
"""
import json
import logging
from typing import List, Dict, Any, Optional, Set
from pathlib import Path
from sqlalchemy.orm import Session
from sqlalchemy import text

# PostgreSQL模式下暂不支持标签缓存管理器
from ..models import File, TagCache, DeletedFiles, CustomTags, FileCustomTags

logger = logging.getLogger(__name__)

class TagCacheManager:
    """标签缓存管理器"""
    
    def __init__(self, case_id: int):
        self.case_id = case_id
        logger.warning("PostgreSQL模式下暂不支持标签缓存管理器")

    def get_session(self) -> Session:
        """获取数据库会话（PostgreSQL模式暂不支持）"""
        raise NotImplementedError("PostgreSQL模式下暂不支持标签缓存管理器")
    
    def update_cache(self, file_id: int, tags_data: dict) -> bool:
        """
        更新单个文件的标签缓存
        
        Args:
            file_id: 文件ID
            tags_data: 标签数据
            
        Returns:
            bool: 更新是否成功
        """
        try:
            db = self.get_session()
            
            try:
                # 检查文件是否在删除列表中
                deleted_file = db.query(DeletedFiles).filter(
                    DeletedFiles.file_id == file_id
                ).first()
                
                if deleted_file:
                    logger.info(f"文件 {file_id} 已被删除，跳过缓存更新")
                    return True
                
                # 删除该文件的现有缓存条目
                db.query(TagCache).filter(
                    TagCache.file_ids.contains(f'[{file_id}]') |
                    TagCache.file_ids.contains(f'{file_id},') |
                    TagCache.file_ids.contains(f',{file_id}')
                ).delete(synchronize_session=False)
                
                # 生成新的缓存条目
                cache_entries = self._build_cache_entries_for_file(file_id, tags_data)
                
                # 插入新的缓存条目
                for entry in cache_entries:
                    cache_obj = TagCache(**entry)
                    db.add(cache_obj)
                
                db.commit()
                logger.info(f"文件 {file_id} 的标签缓存更新完成，共 {len(cache_entries)} 个条目")
                return True
                
            except Exception as e:
                db.rollback()
                logger.error(f"更新文件 {file_id} 的标签缓存失败: {e}")
                return False
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取数据库连接失败: {e}")
            return False
    
    def rebuild_cache(self) -> Dict[str, Any]:
        """
        重建整个案例的标签缓存
        
        Returns:
            dict: 重建结果统计
        """
        try:
            db = self.get_session()
            
            try:
                # 清空现有缓存
                db.query(TagCache).delete()
                
                # 获取所有活跃文件
                files = db.query(File).all()
                deleted_files = db.query(DeletedFiles).all()
                deleted_file_ids = {df.file_id for df in deleted_files}
                
                active_files = [f for f in files if f.id not in deleted_file_ids]
                
                # 重建缓存
                total_entries = 0
                processed_files = 0
                
                for file in active_files:
                    if file.tags:
                        try:
                            tags_data = json.loads(file.tags) if isinstance(file.tags, str) else file.tags
                            cache_entries = self._build_cache_entries_for_file(file.id, tags_data)
                            
                            for entry in cache_entries:
                                cache_obj = TagCache(**entry)
                                db.add(cache_obj)
                            
                            total_entries += len(cache_entries)
                            processed_files += 1
                            
                        except Exception as e:
                            logger.warning(f"处理文件 {file.id} 的标签时出错: {e}")
                
                db.commit()
                
                result = {
                    "success": True,
                    "total_files": len(files),
                    "active_files": len(active_files),
                    "processed_files": processed_files,
                    "cache_entries": total_entries,
                    "deleted_files": len(deleted_file_ids)
                }
                
                logger.info(f"案例 {self.case_id} 标签缓存重建完成: {result}")
                return result
                
            except Exception as e:
                db.rollback()
                logger.error(f"重建标签缓存失败: {e}")
                return {"success": False, "error": str(e)}
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取数据库连接失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_files_by_tag(self, category: str, name: str, value: str) -> List[int]:
        """
        根据标签获取文件列表
        
        Args:
            category: 标签类别
            name: 标签名称
            value: 标签值
            
        Returns:
            List[int]: 文件ID列表
        """
        try:
            db = self.get_session()
            
            try:
                # 查询缓存
                cache_entry = db.query(TagCache).filter(
                    TagCache.tag_category == category,
                    TagCache.tag_name == name,
                    TagCache.tag_value == value
                ).first()
                
                if cache_entry:
                    file_ids = json.loads(cache_entry.file_ids)
                    
                    # 过滤已删除的文件
                    deleted_files = db.query(DeletedFiles).all()
                    deleted_file_ids = {df.file_id for df in deleted_files}
                    
                    active_file_ids = [fid for fid in file_ids if fid not in deleted_file_ids]
                    return active_file_ids
                else:
                    return []
                    
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"根据标签获取文件列表失败: {e}")
            return []
    
    def handle_file_deletion(self, file_ids: List[int]) -> bool:
        """
        处理文件删除
        
        Args:
            file_ids: 要删除的文件ID列表
            
        Returns:
            bool: 处理是否成功
        """
        try:
            db = self.get_session()
            
            try:
                # 获取文件信息
                files = db.query(File).filter(File.id.in_(file_ids)).all()
                
                # 添加到删除记录表
                for file in files:
                    deleted_file = DeletedFiles(
                        file_id=file.id,
                        file_name=file.file_name
                    )
                    db.add(deleted_file)
                
                # 更新标签缓存（移除删除的文件）
                self._update_cache_after_deletion(db, file_ids)
                
                db.commit()
                logger.info(f"处理文件删除完成: {file_ids}")
                return True
                
            except Exception as e:
                db.rollback()
                logger.error(f"处理文件删除失败: {e}")
                return False
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取数据库连接失败: {e}")
            return False
    
    def handle_file_restoration(self, file_ids: List[int]) -> bool:
        """
        处理文件恢复
        
        Args:
            file_ids: 要恢复的文件ID列表
            
        Returns:
            bool: 处理是否成功
        """
        try:
            db = self.get_session()
            
            try:
                # 从删除记录表移除
                db.query(DeletedFiles).filter(
                    DeletedFiles.file_id.in_(file_ids)
                ).delete(synchronize_session=False)
                
                # 重新加入标签缓存
                files = db.query(File).filter(File.id.in_(file_ids)).all()
                
                for file in files:
                    if file.tags:
                        try:
                            tags_data = json.loads(file.tags) if isinstance(file.tags, str) else file.tags
                            cache_entries = self._build_cache_entries_for_file(file.id, tags_data)
                            
                            for entry in cache_entries:
                                cache_obj = TagCache(**entry)
                                db.add(cache_obj)
                                
                        except Exception as e:
                            logger.warning(f"恢复文件 {file.id} 的标签缓存时出错: {e}")
                
                db.commit()
                logger.info(f"处理文件恢复完成: {file_ids}")
                return True
                
            except Exception as e:
                db.rollback()
                logger.error(f"处理文件恢复失败: {e}")
                return False
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取数据库连接失败: {e}")
            return False
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            dict: 缓存统计信息
        """
        try:
            db = self.get_session()
            
            try:
                # 统计缓存条目数量
                total_entries = db.query(TagCache).count()
                
                # 按类别统计
                category_stats = {}
                for category in ["properties", "metadata", "cv", "user", "ai"]:
                    count = db.query(TagCache).filter(TagCache.tag_category == category).count()
                    category_stats[category] = count
                
                # 统计自定义标签
                custom_tags_count = db.query(CustomTags).count()
                category_stats["custom"] = custom_tags_count
                
                # 统计删除的文件
                deleted_files_count = db.query(DeletedFiles).count()
                
                # 统计活跃文件
                total_files = db.query(File).count()
                active_files = total_files - deleted_files_count
                
                return {
                    "total_cache_entries": total_entries,
                    "category_stats": category_stats,
                    "total_files": total_files,
                    "active_files": active_files,
                    "deleted_files": deleted_files_count
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取缓存统计信息失败: {e}")
            return {"error": str(e)}
    
    def clean_empty_tags(self) -> int:
        """
        清理空标签（没有关联文件的标签）
        
        Returns:
            int: 清理的标签数量
        """
        try:
            db = self.get_session()
            
            try:
                # 获取所有缓存条目
                cache_entries = db.query(TagCache).all()
                cleaned_count = 0
                
                for entry in cache_entries:
                    file_ids = json.loads(entry.file_ids)
                    
                    # 检查文件是否还存在且未被删除
                    deleted_files = db.query(DeletedFiles).filter(
                        DeletedFiles.file_id.in_(file_ids)
                    ).all()
                    deleted_file_ids = {df.file_id for df in deleted_files}
                    
                    active_file_ids = [fid for fid in file_ids if fid not in deleted_file_ids]
                    
                    if not active_file_ids:
                        # 删除空标签
                        db.delete(entry)
                        cleaned_count += 1
                    elif len(active_file_ids) != len(file_ids):
                        # 更新文件ID列表
                        entry.file_ids = json.dumps(active_file_ids)
                        entry.file_count = len(active_file_ids)
                
                db.commit()
                logger.info(f"清理空标签完成，共清理 {cleaned_count} 个标签")
                return cleaned_count
                
            except Exception as e:
                db.rollback()
                logger.error(f"清理空标签失败: {e}")
                return 0
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取数据库连接失败: {e}")
            return 0
    
    def _build_cache_entries_for_file(self, file_id: int, tags_data: dict) -> List[Dict[str, Any]]:
        """为单个文件构建缓存条目"""
        cache_entries = []
        
        try:
            # 处理properties
            if "properties" in tags_data:
                for key, value in tags_data["properties"].items():
                    cache_entries.append({
                        "tag_category": "properties",
                        "tag_name": key,
                        "tag_value": str(value),
                        "file_ids": json.dumps([file_id]),
                        "file_count": 1
                    })
            
            # 处理tags
            if "tags" in tags_data:
                for category in ["metadata", "cv", "user", "ai"]:
                    if category in tags_data["tags"]:
                        cat_data = tags_data["tags"][category]
                        
                        if isinstance(cat_data, dict):
                            for key, value in cat_data.items():
                                if isinstance(value, list):
                                    for item in value:
                                        cache_entries.append({
                                            "tag_category": category,
                                            "tag_name": key,
                                            "tag_value": str(item),
                                            "file_ids": json.dumps([file_id]),
                                            "file_count": 1
                                        })
                                else:
                                    cache_entries.append({
                                        "tag_category": category,
                                        "tag_name": key,
                                        "tag_value": str(value),
                                        "file_ids": json.dumps([file_id]),
                                        "file_count": 1
                                    })
                        
                        elif isinstance(cat_data, list):
                            for item in cat_data:
                                cache_entries.append({
                                    "tag_category": category,
                                    "tag_name": str(item),
                                    "tag_value": str(item),
                                    "file_ids": json.dumps([file_id]),
                                    "file_count": 1
                                })
                                
        except Exception as e:
            logger.warning(f"构建文件 {file_id} 的缓存条目时出错: {e}")
        
        return cache_entries
    
    def _update_cache_after_deletion(self, db: Session, deleted_file_ids: List[int]):
        """删除文件后更新缓存"""
        try:
            # 获取所有包含这些文件的缓存条目
            cache_entries = db.query(TagCache).all()
            
            for entry in cache_entries:
                file_ids = json.loads(entry.file_ids)
                
                # 移除删除的文件ID
                updated_file_ids = [fid for fid in file_ids if fid not in deleted_file_ids]
                
                if len(updated_file_ids) != len(file_ids):
                    if updated_file_ids:
                        # 更新缓存条目
                        entry.file_ids = json.dumps(updated_file_ids)
                        entry.file_count = len(updated_file_ids)
                    else:
                        # 删除空的缓存条目
                        db.delete(entry)
                        
        except Exception as e:
            logger.warning(f"更新删除后的缓存时出错: {e}")


def get_tag_cache_manager(case_id: int) -> TagCacheManager:
    """获取标签缓存管理器实例"""
    return TagCacheManager(case_id)