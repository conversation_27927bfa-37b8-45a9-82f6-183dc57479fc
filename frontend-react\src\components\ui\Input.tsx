import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/utils/cn';

// ============================================================================
// Input Variants Definition
// ============================================================================

const inputVariants = cva(
  // Base styles
  'flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-smooth',
  {
    variants: {
      variant: {
        default: '',
        search: 'pl-10', // 为搜索图标留出空间
        error: 'border-destructive focus-visible:ring-destructive',
      },
      size: {
        sm: 'h-8 text-xs',
        md: 'h-9',
        lg: 'h-10 text-base',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  }
);

// ============================================================================
// Input Component Interface
// ============================================================================

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'>,
    VariantProps<typeof inputVariants> {
  /**
   * 错误信息 - 显示在输入框下方
   */
  error?: string;

  /**
   * 左侧图标
   */
  leftIcon?: React.ReactNode;

  /**
   * 右侧图标
   */
  rightIcon?: React.ReactNode;

  /**
   * 标签文本
   */
  label?: string;

  /**
   * 是否必填
   */
  required?: boolean;
}

// ============================================================================
// Input Component Implementation
// ============================================================================

/**
 * Input 组件 - Project Novak 原子组件
 *
 * 设计原则：
 * 1. 愚蠢原则：只负责输入展示，不包含验证逻辑
 * 2. 无障碍优先：完整的 label 关联和 ARIA 支持
 * 3. 组合优于配置：支持左右图标插槽
 * 4. 类型安全：完整的 TypeScript 支持
 *
 * @example
 * ```tsx
 * // 基础用法
 * <Input placeholder="Enter text..." />
 *
 * // 带标签和错误
 * <Input
 *   label="Email"
 *   type="email"
 *   error="Invalid email format"
 *   required
 * />
 *
 * // 搜索框
 * <Input
 *   variant="search"
 *   placeholder="Search images..."
 *   leftIcon={<SearchIcon />}
 * />
 *
 * // 带右侧操作
 * <Input
 *   placeholder="File name"
 *   rightIcon={<ClearIcon onClick={handleClear} />}
 * />
 * ```
 */
const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({
    className,
    variant,
    size,
    type = 'text',
    error,
    leftIcon,
    rightIcon,
    label,
    required,
    id,
    ...props
  }, ref) => {
    // 生成唯一 ID 用于 label 关联
    const inputId = id || React.useId();
    const errorId = `${inputId}-error`;

    return (
      <div className="space-y-2">
        {/* Label */}
        {label && (
          <label
            htmlFor={inputId}
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {label}
            {required && (
              <span className="text-destructive ml-1" aria-label="required">
                *
              </span>
            )}
          </label>
        )}

        {/* Input Container */}
        <div className="relative">
          {/* Left Icon */}
          {leftIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground pointer-events-none">
              {leftIcon}
            </div>
          )}

          {/* Input Element */}
          <input
            type={type}
            className={cn(
              inputVariants({
                variant: error ? 'error' : variant,
                size,
                className
              }),
              leftIcon && 'pl-10',
              rightIcon && 'pr-10'
            )}
            ref={ref}
            id={inputId}
            aria-invalid={error ? 'true' : 'false'}
            aria-describedby={error ? errorId : undefined}
            aria-required={required}
            {...props}
          />

          {/* Right Icon */}
          {rightIcon && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              {rightIcon}
            </div>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <p
            id={errorId}
            className="text-sm text-destructive"
            role="alert"
            aria-live="polite"
          >
            {error}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export { Input, inputVariants };
