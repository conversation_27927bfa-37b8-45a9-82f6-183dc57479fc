# src/routers/trash.py
from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

# 导入我们的数据库工具、模型、蓝图和 CRUD 层
from .. import models, schemas
from ..database import get_master_db
from sqlalchemy import text
from ..crud import (
    get_trash_cases,
    get_trash_case,
    restore_case,
    permanently_delete_case
)
import os
import shutil
from pathlib import Path

router = APIRouter(
    prefix="/trash",
    tags=["Trash"]
)

@router.get("/", response_model=List[schemas.TrashCase])
def get_trash_cases_endpoint(skip: int = 0, limit: int = 100, db: Session = Depends(get_master_db)):
    """
    获取回收站中的所有案例列表。
    按删除时间倒序排列，最近删除的在前面。
    """
    return get_trash_cases(db, skip, limit)

@router.get("/stats", response_model=dict)
def get_trash_stats_endpoint(db: Session = Depends(get_master_db)):
    """
    获取回收站统计信息。
    """
    try:
        trash_cases = get_trash_cases(db, skip=0, limit=1000)  # 获取所有回收站案例

        total_count = len(trash_cases)
        total_files = sum(len(getattr(case, 'files', [])) for case in trash_cases)

        # 计算最早和最新的删除时间
        if trash_cases:
            deleted_times = [getattr(case, 'deleted_at', None) for case in trash_cases]
            valid_times = [dt for dt in deleted_times if dt is not None]
            if valid_times:
                earliest_deleted = min(valid_times)
                latest_deleted = max(valid_times)
            else:
                earliest_deleted = None
                latest_deleted = None
        else:
            earliest_deleted = None
            latest_deleted = None

        return {
            "total_cases": total_count,
            "total_files": total_files,
            "earliest_deleted": earliest_deleted,
            "latest_deleted": latest_deleted
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取回收站统计失败: {str(e)}")

@router.delete("/empty", response_model=schemas.OperationResponse)
def empty_trash_endpoint(db: Session = Depends(get_master_db)):
    """
    清空回收站，永久删除所有回收站中的案例
    ⚠️ 警告：此操作不可逆！
    """
    try:
        # 获取回收站案例列表
        trash_cases = get_trash_cases(db, skip=0, limit=1000)
        deleted_count = 0
        failed_count = 0

        # 逐个删除案例，每个案例使用独立的数据库会话
        for case in trash_cases:
            case_id = getattr(case, 'id', None)
            if case_id:
                try:
                    # 为每个删除操作创建新的数据库会话
                    fresh_db = next(get_master_db())
                    try:
                        if permanently_delete_case(fresh_db, case_id):
                            deleted_count += 1
                            print(f"✅ 成功删除案例 {case_id}")
                        else:
                            failed_count += 1
                            print(f"❌ 删除案例 {case_id} 失败")
                    finally:
                        fresh_db.close()
                except Exception as e:
                    print(f"删除案例 {case_id} 失败: {e}")
                    failed_count += 1

        # 清理孤立的trash文件夹
        orphaned_count = clean_orphaned_trash_folders(db)

        message = f"回收站已清空，共删除 {deleted_count} 个案例"
        if failed_count > 0:
            message += f"，{failed_count} 个案例删除失败"
        if orphaned_count > 0:
            message += f"，清理了 {orphaned_count} 个孤立文件夹"

        return schemas.OperationResponse(
            success=True,
            message=message,
            data={
                "deleted_count": deleted_count,
                "failed_count": failed_count,
                "orphaned_cleaned": orphaned_count,
                "total_processed": deleted_count + failed_count + orphaned_count
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清空回收站失败: {str(e)}")

@router.get("/{case_id}", response_model=schemas.TrashCase)
def get_trash_case_endpoint(case_id: int, db: Session = Depends(get_master_db)):
    """
    获取回收站中的特定案例详情，包括其文件信息。
    """
    db_case = get_trash_case(db, case_id)
    if db_case is None:
        raise HTTPException(status_code=404, detail="回收站中未找到该案例")
    return db_case

@router.post("/{case_id}/restore", response_model=schemas.OperationResponse)
def restore_case_endpoint(case_id: int, db: Session = Depends(get_master_db)):
    """
    从回收站恢复案例。
    恢复后案例将重新变为活跃状态，可以正常使用。
    """
    success = restore_case(db, case_id)
    if not success:
        raise HTTPException(
            status_code=404, 
            detail="回收站中未找到该案例或恢复失败"
        )
    
    return schemas.OperationResponse(
        success=True,
        message=f"案例 {case_id} 已成功从回收站恢复",
        data={"case_id": case_id, "status": "restored"}
    )

@router.delete("/{case_id}", response_model=schemas.OperationResponse)
def permanently_delete_case_endpoint(case_id: int, db: Session = Depends(get_master_db)):
    """
    彻底删除案例。
    ⚠️ 警告：此操作不可逆！案例及其所有数据将被永久删除。
    """
    # 直接在数据库中删除案例，绕过复杂的permanently_delete_case函数
    try:
        # 查找案例
        case = db.query(models.Case).filter(models.Case.id == case_id).first()
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")

        # 检查案例状态（宽松检查）
        # 如果案例有deleted_at时间戳，就认为它在回收站中
        if not case.deleted_at:
            raise HTTPException(status_code=404, detail="案例不在回收站中")

        # 简化删除逻辑：只删除核心记录

        # 1. 删除文件记录（这是最重要的）
        db.execute(text("DELETE FROM files WHERE case_id = :case_id"), {"case_id": case_id})

        # 2. 删除案例记录
        db.execute(text("DELETE FROM cases WHERE id = :case_id"), {"case_id": case_id})

        db.commit()

        return schemas.OperationResponse(
            success=True,
            message=f"案例 {case_id} 已被永久删除",
            data={"case_id": case_id, "status": "permanently_deleted"}
        )

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"永久删除案例时发生错误: {str(e)}"
        )


def clean_orphaned_trash_folders(db: Session) -> int:
    """
    清理孤立的trash文件夹
    删除那些在文件系统中存在但在数据库中没有对应记录的trash文件夹

    Returns:
        int: 清理的文件夹数量
    """
    try:
        from ..database import TRASH_DIR

        if not os.path.exists(TRASH_DIR):
            return 0

        # 获取数据库中所有DELETED状态的案例ID
        deleted_cases = db.query(models.Case.id).filter(
            models.Case.status == models.CaseStatus.DELETED
        ).all()
        valid_case_ids = {case.id for case in deleted_cases}

        # 扫描trash目录中的所有case_*文件夹
        trash_path = Path(TRASH_DIR)
        cleaned_count = 0

        for item in trash_path.iterdir():
            if item.is_dir() and item.name.startswith('case_'):
                try:
                    # 提取案例ID
                    case_id_str = item.name[5:]  # 移除'case_'前缀
                    case_id = int(case_id_str)

                    # 如果数据库中没有这个案例ID，则删除文件夹
                    if case_id not in valid_case_ids:
                        print(f"🗑️ 发现孤立的trash文件夹: {item}")
                        shutil.rmtree(item)
                        cleaned_count += 1
                        print(f"✅ 已清理孤立文件夹: {item}")

                except (ValueError, OSError) as e:
                    print(f"⚠️ 处理trash文件夹失败 {item}: {e}")
                    continue

        print(f"✅ 清理完成，共清理了 {cleaned_count} 个孤立的trash文件夹")
        return cleaned_count

    except Exception as e:
        print(f"❌ 清理孤立trash文件夹失败: {e}")
        return 0

