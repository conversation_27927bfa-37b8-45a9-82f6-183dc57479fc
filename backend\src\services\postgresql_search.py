# src/services/postgresql_search.py
"""
PostgreSQL优化的搜索服务
利用JSONB、全文搜索、GIN索引等PostgreSQL特性实现高性能查询
"""

from typing import List, Dict, Any, Optional, Union, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import text, and_, or_, func
from sqlalchemy.dialects.postgresql import JSONB
import json
import logging
from datetime import datetime

from ..database_config import db_config, DatabaseType
from ..models import File, TagCache, CustomTags, FileCustomTags
from ..database_manager import db_manager

logger = logging.getLogger(__name__)

class PostgreSQLSearchService:
    """PostgreSQL优化的搜索服务"""
    
    def __init__(self, case_id: Union[int, str]):
        self.case_id = str(case_id)
        self.is_postgresql = db_config.case_db_type == DatabaseType.POSTGRESQL
        
    def _get_session(self) -> Session:
        """获取数据库会话"""
        # 在PostgreSQL模式下，使用主数据库会话
        if db_config.case_db_type == DatabaseType.POSTGRESQL:
            return db_manager.get_master_session()
        else:
            # PostgreSQL模式下，使用主数据库会话
            return db_manager.get_case_session(self.case_id)
    
    def search_files_by_jsonb_tags(
        self,
        metadata_filters: Optional[Dict[str, Any]] = None,
        user_tags: Optional[List[str]] = None,
        ai_tags: Optional[List[str]] = None,
        quality_min: Optional[float] = None,
        quality_max: Optional[float] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        使用JSONB查询优化的文件搜索
        
        Args:
            metadata_filters: 元数据过滤条件 {'camera_make': 'SONY', 'lens': 'FE 24-70mm'}
            user_tags: 用户标签列表 ['重要', '精选']
            ai_tags: AI标签列表 ['人像', '风景']
            quality_min: 最小质量分数
            quality_max: 最大质量分数
            limit: 返回结果数量限制
            offset: 偏移量
            
        Returns:
            List[Dict]: 文件信息列表
        """
        session = self._get_session()
        try:
            # 统一使用PostgreSQL JSONB搜索
            return self._search_postgresql_jsonb(
                session, metadata_filters, user_tags, ai_tags,
                quality_min, quality_max, limit, offset
            )
        finally:
            session.close()
    
    def _search_postgresql_jsonb(
        self,
        session: Session,
        metadata_filters: Optional[Dict[str, Any]],
        user_tags: Optional[List[str]],
        ai_tags: Optional[List[str]],
        quality_min: Optional[float],
        quality_max: Optional[float],
        limit: int,
        offset: int
    ) -> List[Dict[str, Any]]:
        """PostgreSQL JSONB优化查询"""
        
        # 构建查询条件
        conditions = []
        
        # 元数据过滤 - 使用JSONB包含操作符
        if metadata_filters:
            metadata_jsonb = json.dumps(metadata_filters)
            conditions.append(
                text("tags->'tags'->'metadata' @> :metadata_filter")
                .bindparam(metadata_filter=metadata_jsonb)
            )
        
        # 用户标签过滤 - 使用JSONB数组重叠操作符
        if user_tags:
            conditions.append(
                text("tags->'tags'->'user' ?| :user_tags")
                .bindparam(user_tags=user_tags)
            )
        
        # AI标签过滤
        if ai_tags:
            conditions.append(
                text("tags->'tags'->'ai' ?| :ai_tags")
                .bindparam(ai_tags=ai_tags)
            )
        
        # 质量分数过滤 - 使用JSONB路径表达式
        if quality_min is not None:
            conditions.append(
                text("(tags->'properties'->>'qualityScore')::numeric >= :quality_min")
                .bindparam(quality_min=quality_min)
            )
        
        if quality_max is not None:
            conditions.append(
                text("(tags->'properties'->>'qualityScore')::numeric <= :quality_max")
                .bindparam(quality_max=quality_max)
            )
        
        # 构建查询
        query = session.query(File)
        
        if conditions:
            query = query.filter(and_(*conditions))
        
        # 排序：质量分数降序，创建时间降序
        query = query.order_by(
            text("(tags->'properties'->>'qualityScore')::numeric DESC NULLS LAST"),
            File.created_at.desc()
        )
        
        # 分页
        query = query.offset(offset).limit(limit)
        
        # 执行查询并转换结果
        files = query.all()
        return self._convert_files_to_dict(files)
    

    
    def _matches_filters(
        self,
        file: File,
        metadata_filters: Optional[Dict[str, Any]],
        user_tags: Optional[List[str]],
        ai_tags: Optional[List[str]]
    ) -> bool:
        """检查文件是否匹配过滤条件"""
        
        if not file.tags:
            return False
        
        try:
            tags_data = file.tags if isinstance(file.tags, dict) else json.loads(file.tags)
        except (json.JSONDecodeError, TypeError):
            return False
        
        # 检查元数据过滤
        if metadata_filters:
            metadata = tags_data.get('tags', {}).get('metadata', {})
            for key, value in metadata_filters.items():
                if metadata.get(key) != value:
                    return False
        
        # 检查用户标签
        if user_tags:
            file_user_tags = tags_data.get('tags', {}).get('user', [])
            if not any(tag in file_user_tags for tag in user_tags):
                return False
        
        # 检查AI标签
        if ai_tags:
            file_ai_tags = tags_data.get('tags', {}).get('ai', [])
            if not any(tag in file_ai_tags for tag in ai_tags):
                return False
        
        return True
    
    def fulltext_search(
        self,
        search_query: str,
        limit: int = 100,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        全文搜索
        
        Args:
            search_query: 搜索查询字符串
            limit: 返回结果数量限制
            offset: 偏移量
            
        Returns:
            List[Dict]: 搜索结果，包含相关性排序
        """
        session = self._get_session()
        try:
            # 统一使用PostgreSQL全文搜索
            return self._fulltext_search_postgresql(session, search_query, limit, offset)
        finally:
            session.close()
    
    def _fulltext_search_postgresql(
        self,
        session: Session,
        search_query: str,
        limit: int,
        offset: int
    ) -> List[Dict[str, Any]]:
        """PostgreSQL全文搜索"""
        
        # 使用PostgreSQL的全文搜索功能
        sql = text("""
            SELECT 
                id, file_name, file_path, tags, created_at, quality_score,
                ts_rank(search_vector, plainto_tsquery('english', :query)) as rank
            FROM files 
            WHERE search_vector @@ plainto_tsquery('english', :query)
            ORDER BY rank DESC, created_at DESC
            LIMIT :limit OFFSET :offset
        """)
        
        result = session.execute(sql, {
            'query': search_query,
            'limit': limit,
            'offset': offset
        })
        
        files = []
        for row in result:
            files.append({
                'id': row.id,
                'file_name': row.file_name,
                'file_path': row.file_path,
                'tags': row.tags,
                'created_at': row.created_at.isoformat() if row.created_at else None,
                'quality_score': row.quality_score,
                'search_rank': float(row.rank) if row.rank else 0.0
            })
        
        return files
    

    
    def complex_search(
        self,
        search_params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        复杂组合搜索
        
        Args:
            search_params: 搜索参数字典
                {
                    'text_query': '搜索文本',
                    'metadata_filters': {'camera_make': 'SONY'},
                    'user_tags': ['重要', '精选'],
                    'ai_tags': ['人像'],
                    'quality_range': [80, 100],
                    'date_range': ['2023-01-01', '2023-12-31'],
                    'limit': 50,
                    'offset': 0,
                    'sort_by': 'quality_desc'  # quality_desc, date_desc, relevance
                }
                
        Returns:
            Dict: 搜索结果和统计信息
        """
        session = self._get_session()
        try:
            # 解析搜索参数
            text_query = search_params.get('text_query')
            metadata_filters = search_params.get('metadata_filters')
            user_tags = search_params.get('user_tags')
            ai_tags = search_params.get('ai_tags')
            quality_range = search_params.get('quality_range', [])
            date_range = search_params.get('date_range', [])
            limit = search_params.get('limit', 50)
            offset = search_params.get('offset', 0)
            sort_by = search_params.get('sort_by', 'quality_desc')
            
            # 构建查询
            # 统一使用PostgreSQL复杂搜索
            results = self._complex_search_postgresql(
                session, text_query, metadata_filters, user_tags, ai_tags,
                quality_range, date_range, limit, offset, sort_by
            )
            
            # 获取统计信息
            stats = self._get_search_statistics(session, search_params)
            
            return {
                'files': results,
                'total_count': len(results),
                'statistics': stats,
                'search_params': search_params
            }
            
        finally:
            session.close()
    
    def _complex_search_postgresql(
        self,
        session: Session,
        text_query: Optional[str],
        metadata_filters: Optional[Dict[str, Any]],
        user_tags: Optional[List[str]],
        ai_tags: Optional[List[str]],
        quality_range: List[float],
        date_range: List[str],
        limit: int,
        offset: int,
        sort_by: str
    ) -> List[Dict[str, Any]]:
        """PostgreSQL复杂搜索实现"""
        
        # 构建复杂的PostgreSQL查询
        conditions = []
        params = {}
        
        # 全文搜索条件
        if text_query:
            conditions.append("search_vector @@ plainto_tsquery('english', :text_query)")
            params['text_query'] = text_query
        
        # JSONB条件
        if metadata_filters:
            conditions.append("tags->'tags'->'metadata' @> :metadata_filter")
            params['metadata_filter'] = json.dumps(metadata_filters)
        
        if user_tags:
            conditions.append("tags->'tags'->'user' ?| :user_tags")
            params['user_tags'] = user_tags
        
        if ai_tags:
            conditions.append("tags->'tags'->'ai' ?| :ai_tags")
            params['ai_tags'] = ai_tags
        
        # 质量范围
        if quality_range and len(quality_range) >= 2:
            conditions.append("(tags->'properties'->>'qualityScore')::numeric BETWEEN :quality_min AND :quality_max")
            params['quality_min'] = quality_range[0]
            params['quality_max'] = quality_range[1]

        # 日期范围
        if date_range and len(date_range) >= 2:
            conditions.append("created_at BETWEEN :date_start AND :date_end")
            params['date_start'] = date_range[0]
            params['date_end'] = date_range[1]
        
        # 构建WHERE子句
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        
        # 构建ORDER BY子句
        if sort_by == 'quality_desc':
            order_clause = "(tags->'properties'->>'qualityScore')::numeric DESC NULLS LAST, created_at DESC"
        elif sort_by == 'date_desc':
            order_clause = "created_at DESC"
        elif sort_by == 'relevance' and text_query:
            order_clause = "ts_rank(search_vector, plainto_tsquery('english', :text_query)) DESC, created_at DESC"
        else:
            order_clause = "created_at DESC"
        
        # 执行查询
        sql = text(f"""
            SELECT 
                id, file_name, file_path, tags, created_at, quality_score,
                {f"ts_rank(search_vector, plainto_tsquery('english', :text_query)) as search_rank" if text_query else "0 as search_rank"}
            FROM files 
            WHERE {where_clause}
            ORDER BY {order_clause}
            LIMIT :limit OFFSET :offset
        """)
        
        params.update({'limit': limit, 'offset': offset})
        result = session.execute(sql, params)
        
        files = []
        for row in result:
            files.append({
                'id': row.id,
                'file_name': row.file_name,
                'file_path': row.file_path,
                'tags': row.tags,
                'created_at': row.created_at.isoformat() if row.created_at else None,
                'quality_score': row.quality_score,
                'search_rank': float(row.search_rank) if row.search_rank else 0.0
            })
        
        return files
    

    def _get_search_statistics(self, session: Session, search_params: Dict[str, Any]) -> Dict[str, Any]:
        """获取搜索统计信息"""
        
        # 基础统计
        total_files = session.query(File).count()
        
        # 质量分布统计
        if self.is_postgresql:
            quality_stats = session.execute(text("""
                SELECT 
                    COUNT(*) as total,
                    AVG((tags->'properties'->>'qualityScore')::numeric) as avg_quality,
                    MIN((tags->'properties'->>'qualityScore')::numeric) as min_quality,
                    MAX((tags->'properties'->>'qualityScore')::numeric) as max_quality
                FROM files 
                WHERE tags->'properties'->>'qualityScore' IS NOT NULL
            """)).fetchone()
        else:
            quality_stats = session.execute(text("""
                SELECT 
                    COUNT(*) as total,
                    AVG(CAST(JSON_EXTRACT(tags, '$.properties.qualityScore') AS REAL)) as avg_quality,
                    MIN(CAST(JSON_EXTRACT(tags, '$.properties.qualityScore') AS REAL)) as min_quality,
                    MAX(CAST(JSON_EXTRACT(tags, '$.properties.qualityScore') AS REAL)) as max_quality
                FROM files 
                WHERE JSON_EXTRACT(tags, '$.properties.qualityScore') IS NOT NULL
            """)).fetchone()
        
        return {
            'total_files': total_files,
            'quality_stats': {
                'total_with_quality': quality_stats.total if quality_stats else 0,
                'avg_quality': float(quality_stats.avg_quality) if quality_stats and quality_stats.avg_quality else 0,
                'min_quality': float(quality_stats.min_quality) if quality_stats and quality_stats.min_quality else 0,
                'max_quality': float(quality_stats.max_quality) if quality_stats and quality_stats.max_quality else 0,
            }
        }
    
    def _convert_files_to_dict(self, files: List[File], include_rank: bool = True) -> List[Dict[str, Any]]:
        """将File对象转换为字典"""
        result = []
        for file in files:
            file_dict = {
                'id': file.id,
                'file_name': file.file_name,
                'file_path': file.file_path,
                'file_type': file.file_type,
                'width': file.width,
                'height': file.height,
                'created_at': file.created_at.isoformat() if file.created_at else None,
                'taken_at': file.taken_at.isoformat() if file.taken_at else None,
                'quality_score': file.quality_score,
                'tags': file.tags
            }
            
            if include_rank:
                file_dict['search_rank'] = 0.0  # 默认值，实际值在查询中设置
            
            result.append(file_dict)
        
        return result
