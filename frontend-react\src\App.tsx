import { useEffect, useState } from 'react';
import { Button } from '@/components/ui';
import { MainLayout } from '@/components/layout';
import { CatalogPanel, GalleryPanel, WorkbenchPanel, InfoPanel } from '@/components/panels';
import { useUIStore, usePanelState, useSelectionState, useGalleryState, useSearchState } from '@/store';
import { useCases, useFiles, useTags } from '@/hooks';
import { MSWTest } from '@/components/MSWTest';
import { MosaicSpikePOC } from '@/pages/MosaicSpikePOC';

function App() {
  // ========================================
  // 开发模式：测试页面开关
  // ========================================
  const [showMSWTest, setShowMSWTest] = useState(false);
  const [showMosaicSpike, setShowMosaicSpike] = useState(false);

  // ========================================
  // Zustand 全局状态
  // ========================================
  const panelState = usePanelState();
  const selectionState = useSelectionState();
  const galleryState = useGalleryState();
  const searchState = useSearchState();

  // UI 控制函数
  const {
    toggleCatalogPanel,
    toggleInfoPanel,
    toggleWorkbench,
    toggleFullscreenGallery,
    setSelectedCase,
    toggleFileSelection,
    setSearchQuery,
    setActiveWorkbench,
    catalogPanelWidth,
    infoPanelWidth,
    workbenchHeight,
    activeWorkbench,
  } = useUIStore();

  // ========================================
  // TanStack Query 数据获取
  // ========================================
  const { data: cases, isLoading: casesLoading, error: casesError } = useCases();

  // 当前选中的案例（默认选择第一个）
  const currentCaseId = selectionState.selectedCaseId || cases?.[0]?.id || null;

  // 获取文件列表（基于当前案例和搜索条件）
  const { data: filesData, isLoading: filesLoading, error: filesError } = useFiles({
    case_id: currentCaseId || undefined,
    search: searchState.searchQuery || undefined,
  });

  // 获取标签列表
  const { isLoading: tagsLoading, error: tagsError } = useTags(currentCaseId || undefined);

  // ========================================
  // 初始化逻辑
  // ========================================
  useEffect(() => {
    // 当案例加载完成且没有选中案例时，自动选择第一个
    if (cases && cases.length > 0 && !selectionState.selectedCaseId) {
      setSelectedCase(cases[0].id);
    }
  }, [cases, selectionState.selectedCaseId, setSelectedCase]);

  // ========================================
  // 数据处理和错误处理
  // ========================================

  // 处理加载状态
  const isLoading = casesLoading || filesLoading || tagsLoading;

  // 处理错误状态
  const hasError = casesError || filesError || tagsError;

  // 获取文件列表
  const files = filesData?.files || [];

  // 获取当前选中的文件
  const selectedFile = files.find(file =>
    selectionState.selectedFileIds.includes(file.id)
  );

  // ========================================
  // 事件处理函数
  // ========================================

  const handleFileSelect = (fileId: number, selected: boolean) => {
    if (selected) {
      toggleFileSelection(fileId);
    } else {
      toggleFileSelection(fileId);
    }
  };

  const handleFileDoubleClick = (file: any) => {
    console.log('预览文件:', file.fileName);
    // 这里可以添加文件预览逻辑
  };

  // 布局切换控制
  const togglePanel = (panel: 'catalog' | 'workbench' | 'info' | 'fullscreen') => {
    switch (panel) {
      case 'catalog':
        toggleCatalogPanel();
        break;
      case 'workbench':
        toggleWorkbench();
        if (!panelState.showWorkbench) {
          setActiveWorkbench('clipboard');
        }
        break;
      case 'info':
        toggleInfoPanel();
        break;
      case 'fullscreen':
        toggleFullscreenGallery();
        break;
    }
  };

  // ========================================
  // 错误和加载状态处理
  // ========================================

  if (hasError) {
    return (
      <div className="h-screen w-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="text-6xl">⚠️</div>
          <h1 className="text-2xl font-bold">数据加载失败</h1>
          <p className="text-muted-foreground">
            {casesError?.message || filesError?.message || tagsError?.message || '未知错误'}
          </p>
          <Button onClick={() => window.location.reload()}>
            🔄 重新加载
          </Button>
        </div>
      </div>
    );
  }

  // 如果显示 Mosaic Spike POC
  if (showMosaicSpike) {
    return (
      <div className="h-screen w-screen">
        <div className="absolute top-4 right-4 z-50">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowMosaicSpike(false)}
          >
            ← 返回主应用
          </Button>
        </div>
        <MosaicSpikePOC />
      </div>
    );
  }

  // 如果显示 MSW 测试页面
  if (showMSWTest) {
    return (
      <div className="h-screen w-screen">
        <div className="absolute top-4 right-4 z-50">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowMSWTest(false)}
          >
            ← 返回主应用
          </Button>
        </div>
        <MSWTest />
      </div>
    );
  }

  return (
    <div className="h-screen w-screen">
      {/* 布局控制面板 - 仅在非全屏模式显示 */}
      {!panelState.isFullscreenGallery && (
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-50 bg-card border rounded-lg p-2 shadow-lg">
          <div className="flex gap-2">
            <Button
              variant={panelState.showCatalogPanel ? "primary" : "outline"}
              size="sm"
              onClick={() => togglePanel('catalog')}
            >
              📚 目录栏
            </Button>
            <Button
              variant={panelState.showWorkbench ? "primary" : "outline"}
              size="sm"
              onClick={() => togglePanel('workbench')}
            >
              🛠️ 工作台
            </Button>
            <Button
              variant={panelState.showInfoPanel ? "primary" : "outline"}
              size="sm"
              onClick={() => togglePanel('info')}
            >
              📄 信息栏
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => togglePanel('fullscreen')}
            >
              🔍 全屏
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setShowMosaicSpike(true)}
            >
              🔬 Mosaic Spike
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setShowMSWTest(true)}
            >
              🧪 MSW 测试
            </Button>
            {isLoading && (
              <div className="flex items-center gap-2 px-2">
                <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                <span className="text-sm text-muted-foreground">加载中...</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 主布局 */}
      <MainLayout
        catalogPanel={
          <CatalogPanel
            currentLibraryName={cases?.find(c => c.id === currentCaseId)?.case_name || "加载中..."}
            searchQuery={searchState.searchQuery}
            onSearchChange={setSearchQuery}
            tagCategories={[]} // TODO: 将标签数据转换为分类格式
          />
        }
        galleryPanel={
          <GalleryPanel
            files={files.map(file => ({
              id: file.id,
              fileName: file.file_name,
              filePath: file.file_path,
              fileType: file.file_type,
              fileSize: file.file_size,
              width: file.width || undefined,
              height: file.height || undefined,
              thumbnailPath: file.thumbnail_small_path || undefined,
            }))}
            selectedFileIds={selectionState.selectedFileIds}
            layout={galleryState.layout}
            zoomLevel={galleryState.zoomLevel}
            searchQuery={searchState.searchQuery}
            showFileName={galleryState.showFileName}
            showFileInfo={galleryState.showFileInfo}
            loading={filesLoading}
            onFileSelect={handleFileSelect}
            onFileDoubleClick={handleFileDoubleClick}
            onSearchChange={setSearchQuery}
          />
        }
        workbenchPanel={
          <WorkbenchPanel
            activeWorkbench={activeWorkbench}
            onWorkbenchChange={setActiveWorkbench}
          />
        }
        infoPanel={
          <InfoPanel
            selectedFile={selectedFile ? {
              id: selectedFile.id,
              fileName: selectedFile.file_name,
              filePath: selectedFile.file_path,
              fileType: selectedFile.file_type,
              fileSize: selectedFile.file_size,
              width: selectedFile.width || undefined,
              height: selectedFile.height || undefined,
              thumbnailPath: selectedFile.thumbnail_small_path || undefined,
              createdAt: selectedFile.created_at,
              tags: selectedFile.tags?.tags || undefined,
            } : undefined}
            selectedCount={selectionState.selectedFileIds.length}
          />
        }
        showCatalogPanel={panelState.showCatalogPanel}
        showWorkbench={panelState.showWorkbench}
        showInfoPanel={panelState.showInfoPanel}
        isFullscreenGallery={panelState.isFullscreenGallery}
        catalogPanelWidth={catalogPanelWidth}
        infoPanelWidth={infoPanelWidth}
        workbenchHeight={workbenchHeight}
      />

      {/* 全屏模式退出按钮 */}
      {panelState.isFullscreenGallery && (
        <Button
          variant="primary"
          size="sm"
          className="absolute top-4 right-4 z-50"
          onClick={() => toggleFullscreenGallery()}
        >
          ✕ 退出全屏
        </Button>
      )}
    </div>
  );
}

export default App;