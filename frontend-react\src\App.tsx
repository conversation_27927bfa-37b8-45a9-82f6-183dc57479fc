import { useState } from 'react';
import { Button } from '@/components/ui';
import { MainLayout } from '@/components/layout';
import { CatalogPanel, GalleryPanel, WorkbenchPanel, InfoPanel } from '@/components/panels';

function App() {
  // 布局状态
  const [showCatalogPanel, setShowCatalogPanel] = useState(true);
  const [showWorkbench, setShowWorkbench] = useState(false);
  const [showInfoPanel, setShowInfoPanel] = useState(true);
  const [isFullscreenGallery, setIsFullscreenGallery] = useState(false);

  // 面板尺寸
  const [catalogPanelWidth] = useState(280);
  const [infoPanelWidth] = useState(320);
  const [workbenchHeight] = useState(300);

  // 数据状态
  const [selectedFiles, setSelectedFiles] = useState<number[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeWorkbench, setActiveWorkbench] = useState<'clipboard' | 'cluster' | null>(null);

  // 模拟文件数据
  const mockFiles = [
    {
      id: 1,
      fileName: 'sunset-landscape.jpg',
      filePath: 'https://picsum.photos/800/600?random=1',
      fileType: 'image/jpeg',
      fileSize: 2048000,
      width: 1920,
      height: 1080,
      thumbnailPath: 'https://picsum.photos/200/200?random=1',
      createdAt: '2024-01-15T10:30:00Z',
      tags: {
        metadata: { camera: 'Canon EOS R5', lens: '24-70mm' },
        cv: {},
        user: ['风景', '日落'],
        ai: ['自然', '天空', '云朵'],
        custom: {}
      }
    },
    {
      id: 2,
      fileName: 'city-night.jpg',
      filePath: 'https://picsum.photos/800/600?random=2',
      fileType: 'image/jpeg',
      fileSize: 1536000,
      width: 1920,
      height: 1080,
      thumbnailPath: 'https://picsum.photos/200/200?random=2',
      createdAt: '2024-01-16T20:15:00Z',
      tags: {
        metadata: { camera: 'Sony A7R4', lens: '85mm' },
        cv: {},
        user: ['城市', '夜景'],
        ai: ['建筑', '灯光', '街道'],
        custom: {}
      }
    },
    {
      id: 3,
      fileName: 'portrait-studio.jpg',
      filePath: 'https://picsum.photos/800/600?random=3',
      fileType: 'image/jpeg',
      fileSize: 3072000,
      width: 2400,
      height: 1600,
      thumbnailPath: 'https://picsum.photos/200/200?random=3',
      createdAt: '2024-01-17T14:45:00Z',
      tags: {
        metadata: { camera: 'Nikon D850', lens: '105mm' },
        cv: {},
        user: ['人像', '工作室'],
        ai: ['人物', '肖像', '专业'],
        custom: {}
      }
    }
  ];

  // 事件处理函数
  const handleFileSelect = (fileId: number, selected: boolean) => {
    if (selected) {
      setSelectedFiles(prev => [...prev, fileId]);
    } else {
      setSelectedFiles(prev => prev.filter(id => id !== fileId));
    }
  };

  const handleFileDoubleClick = (file: any) => {
    console.log('预览文件:', file.fileName);
  };

  const selectedFile = mockFiles.find(file => selectedFiles.includes(file.id));

  // 布局切换控制
  const togglePanel = (panel: 'catalog' | 'workbench' | 'info' | 'fullscreen') => {
    switch (panel) {
      case 'catalog':
        setShowCatalogPanel(!showCatalogPanel);
        break;
      case 'workbench':
        setShowWorkbench(!showWorkbench);
        if (!showWorkbench) {
          setActiveWorkbench('clipboard');
        }
        break;
      case 'info':
        setShowInfoPanel(!showInfoPanel);
        break;
      case 'fullscreen':
        setIsFullscreenGallery(!isFullscreenGallery);
        break;
    }
  };

  return (
    <div className="h-screen w-screen">
      {/* 布局控制面板 - 仅在非全屏模式显示 */}
      {!isFullscreenGallery && (
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-50 bg-card border rounded-lg p-2 shadow-lg">
          <div className="flex gap-2">
            <Button
              variant={showCatalogPanel ? "primary" : "outline"}
              size="sm"
              onClick={() => togglePanel('catalog')}
            >
              📚 目录栏
            </Button>
            <Button
              variant={showWorkbench ? "primary" : "outline"}
              size="sm"
              onClick={() => togglePanel('workbench')}
            >
              🛠️ 工作台
            </Button>
            <Button
              variant={showInfoPanel ? "primary" : "outline"}
              size="sm"
              onClick={() => togglePanel('info')}
            >
              📄 信息栏
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => togglePanel('fullscreen')}
            >
              🔍 全屏
            </Button>
          </div>
        </div>
      )}

      {/* 主布局 */}
      <MainLayout
        catalogPanel={
          <CatalogPanel
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
          />
        }
        galleryPanel={
          <GalleryPanel
            files={mockFiles}
            selectedFileIds={selectedFiles}
            searchQuery={searchQuery}
            onFileSelect={handleFileSelect}
            onFileDoubleClick={handleFileDoubleClick}
            onSearchChange={setSearchQuery}
          />
        }
        workbenchPanel={
          <WorkbenchPanel
            activeWorkbench={activeWorkbench}
            onWorkbenchChange={setActiveWorkbench}
          />
        }
        infoPanel={
          <InfoPanel
            selectedFile={selectedFile}
            selectedCount={selectedFiles.length}
          />
        }
        showCatalogPanel={showCatalogPanel}
        showWorkbench={showWorkbench}
        showInfoPanel={showInfoPanel}
        isFullscreenGallery={isFullscreenGallery}
        catalogPanelWidth={catalogPanelWidth}
        infoPanelWidth={infoPanelWidth}
        workbenchHeight={workbenchHeight}
      />

      {/* 全屏模式退出按钮 */}
      {isFullscreenGallery && (
        <Button
          variant="primary"
          size="sm"
          className="absolute top-4 right-4 z-50"
          onClick={() => setIsFullscreenGallery(false)}
        >
          ✕ 退出全屏
        </Button>
      )}
    </div>
  );
}

export default App;