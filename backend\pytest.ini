[tool:pytest]
# pytest配置文件
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 测试输出格式
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --cov=src
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=70

# 标记
markers =
    unit: 单元测试
    integration: 集成测试
    slow: 慢速测试
    
# 异步测试配置
asyncio_mode = auto

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning 