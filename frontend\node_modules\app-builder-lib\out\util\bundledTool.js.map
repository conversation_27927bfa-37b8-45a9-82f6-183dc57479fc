{"version": 3, "file": "bundledTool.js", "sourceRoot": "", "sources": ["../../src/util/bundledTool.ts"], "names": [], "mappings": ";;;AAKA,SAAgB,UAAU,CAAC,QAAmC,EAAE,SAAwB;IACtF,MAAM,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;IAC1D,OAAO,SAAS;SACb,MAAM,CAAC,cAAc,CAAC;SACtB,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;SAC3B,IAAI,CAAC,GAAG,CAAC,CAAA;AACd,CAAC;AAND,gCAMC;AAED,SAAgB,cAAc,CAAC,OAAsB;IACnD,uCAAuC;IACvC,OAAO;QACL,GAAG,OAAO,CAAC,GAAG;QACd,iBAAiB,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,OAAO,CAAC;KACtE,CAAA;AACH,CAAC;AAND,wCAMC", "sourcesContent": ["export interface ToolInfo {\n  path: string\n  env?: any\n}\n\nexport function computeEnv(oldValue: string | null | undefined, newValues: Array<string>): string {\n  const parsedOldValue = oldValue ? oldValue.split(\":\") : []\n  return newValues\n    .concat(parsedOldValue)\n    .filter(it => it.length > 0)\n    .join(\":\")\n}\n\nexport function computeToolEnv(libPath: Array<string>): any {\n  // noinspection SpellCheckingInspection\n  return {\n    ...process.env,\n    DYLD_LIBRARY_PATH: computeEnv(process.env.DYLD_LIBRARY_PATH, libPath),\n  }\n}\n"]}