# src/routers/search.py
"""
搜索筛选路由 - 统一的搜索和筛选API
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
import logging
import json

from ..database import get_master_db
from ..crud.case_crud import get_case
from ..crud.file_crud import get_files_by_case
from .. import models, schemas

logger = logging.getLogger(__name__)

router = APIRouter()

# ==================== 基础搜索API ====================

@router.get("/cases/{case_id}/search")
def search_files(
    case_id: int,
    q: Optional[str] = Query(None, description="搜索关键词"),
    limit: int = Query(50, ge=1, le=200, description="返回结果数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    db: Session = Depends(get_master_db)
):
    """
    基础文件搜索 - 支持文件名和标签内容搜索
    """
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")
        
        # 构建搜索查询
        query = db.query(models.File).filter(models.File.case_id == case_id)
        
        if q:
            # 搜索文件名
            query = query.filter(models.File.file_name.ilike(f"%{q}%"))
        
        # 应用分页
        total_count = query.count()
        files = query.offset(offset).limit(limit).all()
        
        return {
            "files": [_file_to_dict(file) for file in files],
            "total_count": total_count,
            "limit": limit,
            "offset": offset,
            "query": q
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"搜索文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索文件失败: {str(e)}")

# ==================== 标签筛选API ====================

@router.get("/cases/{case_id}/filter")
def filter_files_by_tags(
    case_id: int,
    # 标签筛选参数
    metadata_filters: Optional[str] = Query(None, description="元数据过滤条件 (JSON字符串)"),
    user_tags: Optional[str] = Query(None, description="用户标签 (逗号分隔)"),
    ai_tags: Optional[str] = Query(None, description="AI标签 (逗号分隔)"),
    custom_tags: Optional[str] = Query(None, description="自定义标签 (逗号分隔)"),
    # 质量筛选
    quality_min: Optional[float] = Query(None, ge=0, le=100, description="最小质量分数"),
    quality_max: Optional[float] = Query(None, ge=0, le=100, description="最大质量分数"),
    # 分页参数
    limit: int = Query(50, ge=1, le=200, description="返回结果数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    db: Session = Depends(get_master_db)
):
    """
    基于标签的文件筛选
    """
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")
        
        # 构建基础查询
        query = db.query(models.File).filter(models.File.case_id == case_id)
        
        # 应用质量筛选
        if quality_min is not None:
            query = query.filter(models.File.quality_score >= quality_min)
        if quality_max is not None:
            query = query.filter(models.File.quality_score <= quality_max)
        
        # 应用标签筛选
        filters_applied = []
        
        # 元数据筛选
        if metadata_filters:
            try:
                metadata_dict = json.loads(metadata_filters)
                for key, value in metadata_dict.items():
                    query = query.filter(
                        models.File.tags['tags']['metadata'][key].astext == str(value)
                    )
                    filters_applied.append(f"metadata.{key}={value}")
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="metadata_filters必须是有效的JSON字符串")
        
        # 用户标签筛选
        if user_tags:
            user_tag_list = [tag.strip() for tag in user_tags.split(',')]
            for tag in user_tag_list:
                query = query.filter(
                    models.File.tags['tags']['user'].op('@>')([tag])
                )
                filters_applied.append(f"user_tag={tag}")
        
        # AI标签筛选
        if ai_tags:
            ai_tag_list = [tag.strip() for tag in ai_tags.split(',')]
            for tag in ai_tag_list:
                query = query.filter(
                    models.File.tags['tags']['ai'].op('@>')([tag])
                )
                filters_applied.append(f"ai_tag={tag}")
        
        # 自定义标签筛选 (需要关联custom_tags表)
        if custom_tags:
            custom_tag_list = [tag.strip() for tag in custom_tags.split(',')]
            # 这里需要更复杂的查询，暂时简化处理
            filters_applied.append(f"custom_tags={custom_tags}")
        
        # 获取结果
        total_count = query.count()
        files = query.offset(offset).limit(limit).all()
        
        return {
            "files": [_file_to_dict(file) for file in files],
            "total_count": total_count,
            "limit": limit,
            "offset": offset,
            "filters_applied": filters_applied,
            "filter_summary": {
                "metadata_filters": metadata_filters,
                "user_tags": user_tags,
                "ai_tags": ai_tags,
                "custom_tags": custom_tags,
                "quality_range": [quality_min, quality_max] if quality_min is not None or quality_max is not None else None
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"标签筛选失败: {e}")
        raise HTTPException(status_code=500, detail=f"标签筛选失败: {str(e)}")

# ==================== 高级搜索API ====================

@router.post("/cases/{case_id}/advanced-search")
def advanced_search(
    case_id: int,
    search_request: Dict[str, Any],
    db: Session = Depends(get_master_db)
):
    """
    高级搜索 - 支持复杂的多条件搜索
    """
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")
        
        # 解析搜索请求
        text_query = search_request.get("text_query")
        metadata_filters = search_request.get("metadata_filters", {})
        user_tags = search_request.get("user_tags", [])
        ai_tags = search_request.get("ai_tags", [])
        quality_range = search_request.get("quality_range")
        limit = search_request.get("limit", 50)
        offset = search_request.get("offset", 0)
        sort_by = search_request.get("sort_by", "created_at")
        sort_order = search_request.get("sort_order", "desc")
        
        # 构建查询
        query = db.query(models.File).filter(models.File.case_id == case_id)
        
        # 文本搜索
        if text_query:
            query = query.filter(models.File.file_name.ilike(f"%{text_query}%"))
        
        # 元数据筛选
        for key, value in metadata_filters.items():
            query = query.filter(
                models.File.tags['tags']['metadata'][key].astext == str(value)
            )
        
        # 用户标签筛选
        for tag in user_tags:
            query = query.filter(
                models.File.tags['tags']['user'].op('@>')([tag])
            )
        
        # AI标签筛选
        for tag in ai_tags:
            query = query.filter(
                models.File.tags['tags']['ai'].op('@>')([tag])
            )
        
        # 质量范围筛选
        if quality_range and len(quality_range) == 2:
            min_quality, max_quality = quality_range
            if min_quality is not None:
                query = query.filter(models.File.quality_score >= min_quality)
            if max_quality is not None:
                query = query.filter(models.File.quality_score <= max_quality)
        
        # 排序
        if sort_by == "quality_desc":
            query = query.order_by(models.File.quality_score.desc())
        elif sort_by == "date_desc":
            query = query.order_by(models.File.created_at.desc())
        elif sort_by == "name_asc":
            query = query.order_by(models.File.file_name.asc())
        else:
            query = query.order_by(models.File.created_at.desc())
        
        # 获取结果
        total_count = query.count()
        files = query.offset(offset).limit(limit).all()
        
        return {
            "files": [_file_to_dict(file) for file in files],
            "total_count": total_count,
            "limit": limit,
            "offset": offset,
            "search_request": search_request,
            "execution_time_ms": 0  # 可以添加实际的执行时间统计
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"高级搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"高级搜索失败: {str(e)}")

# ==================== 搜索建议API ====================

@router.get("/cases/{case_id}/search-suggestions")
def get_search_suggestions(
    case_id: int,
    q: str = Query(..., description="搜索关键词"),
    limit: int = Query(10, ge=1, le=50, description="建议数量限制"),
    db: Session = Depends(get_master_db)
):
    """
    获取搜索建议
    """
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")
        
        suggestions = []
        
        # 文件名建议
        file_suggestions = db.query(models.File.file_name).filter(
            models.File.case_id == case_id,
            models.File.file_name.ilike(f"%{q}%")
        ).limit(limit).all()
        
        for file_name, in file_suggestions:
            suggestions.append({
                "type": "filename",
                "value": file_name,
                "display": f"文件名: {file_name}"
            })
        
        return {
            "query": q,
            "suggestions": suggestions[:limit]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取搜索建议失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取搜索建议失败: {str(e)}")

# ==================== 辅助函数 ====================

def _file_to_dict(file: models.File) -> Dict[str, Any]:
    """将文件对象转换为字典"""
    return {
        "id": file.id,
        "case_id": file.case_id,
        "file_name": file.file_name,
        "file_type": file.file_type,
        "file_path": file.file_path,
        "thumbnail_small_path": file.thumbnail_small_path,
        "width": file.width,
        "height": file.height,
        "quality_score": file.quality_score,
        "created_at": file.created_at.isoformat() if file.created_at else None,
        "taken_at": file.taken_at.isoformat() if file.taken_at else None,
        "tags": file.tags
    }
