# src/services/rule_engine.py
"""
规则引擎核心 - 迷星 Mizzy Star V1.0
负责应用规则到文件，生成标签数据
"""

import re
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from pathlib import Path

from .. import models, schemas
from ..crud.rule_crud import get_active_rules_for_case

logger = logging.getLogger(__name__)


class RuleEngine:
    """规则引擎核心类"""
    
    def __init__(self):
        # 使用字符串值作为键，避免枚举对象比较问题
        self.processors = {
            "FILENAME_PARSING": self._process_filename_parsing,
            "DATE_TAGGING_FORMAT": self._process_date_tagging
        }
    
    def process_file(self, db, case_id: int, file_obj: models.File) -> Dict[str, Any]:
        """
        对文件应用所有激活的规则，生成标签数据
        
        Args:
            db: 数据库会话
            case_id: 案例ID
            file_obj: 文件对象
            
        Returns:
            生成的标签数据字典
        """
        logger.info(f"开始处理文件: {file_obj.file_name} (案例ID: {case_id})")
        
        # 初始化标签结构 - 按照新的分类规则
        tags_data = {
            "properties": {
                # 移除文件名、文件大小、质量分（按照用户要求）
                # "filename": file_obj.file_name,        # 移除：不作为标签
                # "qualityScore": file_obj.quality_score, # 移除：暂时移除质量分
                # "fileSize": self._get_file_size(file_obj.file_path) # 移除：不作为标签
            },
            "tags": {
                "metadata": {},  # 元数据标签：拍摄相关的技术参数
                "cv": {},
                "user": [],
                "ai": []
            }
        }
        
        # 获取案例的所有激活规则
        active_rules = get_active_rules_for_case(db, case_id)
        logger.info(f"找到 {len(active_rules)} 个激活规则")
        
        # 按创建时间顺序应用规则
        for rule in active_rules:
            try:
                logger.info(f"应用规则: {rule.rule_type.value} (ID: {rule.id})")
                # 获取规则类型的字符串值
                rule_type_value = rule.rule_type.value if hasattr(rule.rule_type, 'value') else str(rule.rule_type)
                processor = self.processors.get(rule_type_value)

                if processor:
                    rule_result = processor(file_obj, rule.rule_config)
                    if rule_result:
                        # 合并规则结果到标签数据
                        self._merge_rule_result(tags_data, rule_result)
                        logger.info(f"规则 {rule.id} 应用成功")
                    else:
                        logger.warning(f"规则 {rule.id} 未产生结果")
                else:
                    logger.warning(f"未找到规则类型 {rule_type_value} 的处理器")
                    
            except Exception as e:
                logger.error(f"应用规则 {rule.id} 时出错: {e}")
                continue
        
        # 添加系统默认标签
        self._add_system_tags(tags_data, file_obj)
        
        logger.info(f"文件 {file_obj.file_name} 处理完成")
        return tags_data
    
    def _process_filename_parsing(self, file_obj: models.File, rule_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        处理文件名解析规则
        
        Args:
            file_obj: 文件对象
            rule_config: 规则配置
            
        Returns:
            解析结果字典
        """
        try:
            pattern = rule_config.get("pattern", "")
            delimiter = rule_config.get("delimiter", "-")
            fields = rule_config.get("fields", [])
            
            if not pattern or not fields:
                logger.warning("文件名解析规则配置不完整")
                return None
            
            # 从文件名中移除扩展名
            filename_without_ext = Path(file_obj.file_name).stem
            
            # 按分隔符分割文件名
            parts = filename_without_ext.split(delimiter)
            
            result = {
                "properties": {},
                "tags": {
                    "metadata": {}
                }
            }
            
            # 根据字段配置提取信息
            for field in fields:
                field_name = field.get("name")
                position = field.get("position")
                tag_category = field.get("tagCategory", "metadata")
                field_format = field.get("format")
                
                if position < len(parts):
                    value = parts[position].strip()
                    
                    # 应用格式化
                    if field_format and field_name == "date":
                        value = self._format_date_value(value, field_format)
                    
                    # 存储到相应类别
                    if tag_category == "metadata":
                        result["tags"]["metadata"][field_name] = value
                    else:
                        # 可以扩展支持其他类别
                        result["tags"]["metadata"][field_name] = value
                    
                    logger.debug(f"提取字段 {field_name}: {value}")
            
            return result
            
        except Exception as e:
            logger.error(f"文件名解析规则处理失败: {e}")
            return None
    
    def _process_date_tagging(self, file_obj: models.File, rule_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        处理日期标签格式化规则
        
        Args:
            file_obj: 文件对象
            rule_config: 规则配置
            
        Returns:
            处理结果字典
        """
        try:
            source_field = rule_config.get("sourceField", "")
            output_format = rule_config.get("outputFormat", "YYYY-MM-DD")
            tag_category = rule_config.get("tagCategory", "metadata")
            tag_name = rule_config.get("tagName", "processedDate")
            
            # 目前简化实现：使用文件的taken_at字段
            date_value = None
            if source_field == "exif.dateTime" and file_obj.taken_at:
                date_value = file_obj.taken_at
            elif source_field == "file.created_at":
                date_value = file_obj.created_at
            
            if not date_value:
                logger.warning(f"未找到日期源字段: {source_field}")
                return None
            
            # 格式化日期
            formatted_date = self._format_datetime(date_value, output_format)
            
            result = {
                "tags": {
                    tag_category: {
                        tag_name: formatted_date
                    }
                }
            }
            
            logger.debug(f"日期标签生成: {tag_name} = {formatted_date}")
            return result
            
        except Exception as e:
            logger.error(f"日期标签规则处理失败: {e}")
            return None
    
    def _merge_rule_result(self, tags_data: Dict[str, Any], rule_result: Dict[str, Any]):
        """
        合并规则结果到标签数据
        
        Args:
            tags_data: 主标签数据
            rule_result: 规则处理结果
        """
        # 合并properties
        if "properties" in rule_result:
            tags_data["properties"].update(rule_result["properties"])
        
        # 合并tags
        if "tags" in rule_result:
            for category, category_data in rule_result["tags"].items():
                if category not in tags_data["tags"]:
                    tags_data["tags"][category] = {}
                
                if isinstance(category_data, dict):
                    if isinstance(tags_data["tags"][category], dict):
                        tags_data["tags"][category].update(category_data)
                    else:
                        tags_data["tags"][category] = category_data
                elif isinstance(category_data, list):
                    if isinstance(tags_data["tags"][category], list):
                        tags_data["tags"][category].extend(category_data)
                    else:
                        tags_data["tags"][category] = category_data
    
    def _add_system_tags(self, tags_data: Dict[str, Any], file_obj: models.File):
        """
        添加系统默认标签，包括完整的EXIF元数据

        Args:
            tags_data: 标签数据
            file_obj: 文件对象
        """
        # 注意：基本文件信息（fileType、dimensions等）现在由EXIF提取器处理
        # 不再在这里重复添加，避免标签重复

        # 提取完整的EXIF元数据 - 按照新的分类规则处理
        if file_obj.file_path and file_obj.file_type and file_obj.file_type.startswith('image/'):
            try:
                from .exif_extractor import extract_complete_metadata
                logger.info(f"🔍 开始提取EXIF数据: {file_obj.file_name} -> {file_obj.file_path}")
                complete_metadata = extract_complete_metadata(file_obj.file_path)
                logger.info(f"📊 提取到的EXIF数据结构: {list(complete_metadata.keys())}")

                # 新的EXIF数据结构包含properties和metadata两个部分
                if isinstance(complete_metadata, dict):
                    # 合并属性标签
                    if 'properties' in complete_metadata:
                        properties_data = complete_metadata['properties']
                        for key, value in properties_data.items():
                            if value is not None and value != '':
                                tags_data["properties"][key] = value
                                logger.info(f"✅ 添加属性标签: {key} = {value}")

                    # 合并元数据标签（过滤掉不需要的字段）
                    if 'metadata' in complete_metadata:
                        metadata_data = complete_metadata['metadata']
                        # 需要过滤的字段列表
                        excluded_fields = ['camera_make', 'camera_model', 'software']

                        for key, value in metadata_data.items():
                            if key not in excluded_fields and value is not None and value != '':
                                tags_data["tags"]["metadata"][key] = value
                                logger.info(f"✅ 添加元数据标签: {key} = {value}")

                    # 兼容旧格式：如果没有分类结构，按旧逻辑处理
                    elif 'properties' not in complete_metadata and 'metadata' not in complete_metadata:
                        logger.info("检测到旧格式EXIF数据，按旧逻辑处理")
                        # 需要过滤的字段列表
                        excluded_fields = ['filename', 'file_size', 'file_type', 'dimensions', 'width', 'height', 'format', 'mode', 'software', 'camera_make', 'camera_model']

                        for key, value in complete_metadata.items():
                            if key not in excluded_fields and value is not None and value != '':
                                tags_data["tags"]["metadata"][key] = value
                                logger.info(f"✅ 添加元数据字段(兼容): {key} = {value}")

                logger.info(f"🎉 EXIF数据处理完成: {file_obj.file_name}")
                logger.info(f"   属性标签: {len(tags_data['properties'])} 个")
                logger.info(f"   元数据标签: {len(tags_data['tags']['metadata'])} 个")

            except Exception as e:
                logger.error(f"❌ 提取EXIF元数据失败 {file_obj.file_name}: {e}")
                import traceback
                logger.error(f"错误详情: {traceback.format_exc()}")

        # 移除文件大小和质量分析结果的添加（按照用户要求）
        # file_size = self._get_file_size(file_obj.file_path)
        # if file_size:
        #     tags_data["properties"]["fileSize"] = file_size

        # if file_obj.quality_score:
        #     tags_data["properties"]["qualityScore"] = file_obj.quality_score
        #     # 根据质量分数添加AI标签
        #     if file_obj.quality_score >= 80:
        #         tags_data["tags"]["ai"].append("高质量")
        #     elif file_obj.quality_score >= 60:
        #         tags_data["tags"]["ai"].append("中等质量")
        #     else:
        #         tags_data["tags"]["ai"].append("低质量")
        
        # 添加人脸检测结果
        if file_obj.num_faces and file_obj.num_faces > 0:
            tags_data["tags"]["cv"]["faces"] = file_obj.num_faces
            tags_data["tags"]["ai"].append("人像")
    
    def _get_file_size(self, file_path: str) -> Optional[int]:
        """获取文件大小"""
        try:
            return Path(file_path).stat().st_size
        except:
            return None
    
    def _format_date_value(self, value: str, format_str: str) -> str:
        """格式化日期值"""
        try:
            # 简化实现：假设输入是YYYY-MM-DD格式
            if format_str == "YYYY-MM-DD":
                return value
            # 可以扩展支持更多格式
            return value
        except:
            return value
    
    def _format_datetime(self, dt: datetime, format_str: str) -> str:
        """格式化datetime对象"""
        try:
            if format_str == "YYYY-MM-DD":
                return dt.strftime("%Y-%m-%d")
            elif format_str == "YYYY-MM-DD HH:mm:ss":
                return dt.strftime("%Y-%m-%d %H:%M:%S")
            else:
                return dt.isoformat()
        except:
            return str(dt)


# 全局规则引擎实例
rule_engine = RuleEngine()


def process_file_with_rules(db, case_id: int, file_obj: models.File) -> Dict[str, Any]:
    """
    使用规则引擎处理文件
    
    Args:
        db: 数据库会话
        case_id: 案例ID
        file_obj: 文件对象
        
    Returns:
        生成的标签数据
    """
    return rule_engine.process_file(db, case_id, file_obj)
