{"version": 3, "sources": ["../../src/utils/offsets.ts"], "sourcesContent": ["import type { XYCoord } from 'dnd-core'\n\nimport { isTouchEvent } from './predicates.js'\n\nconst ELEMENT_NODE = 1\n\nexport function getNodeClientOffset(node: Element): XYCoord | undefined {\n\tconst el = node.nodeType === ELEMENT_NODE ? node : node.parentElement\n\tif (!el) {\n\t\treturn undefined\n\t}\n\tconst { top, left } = el.getBoundingClientRect()\n\treturn { x: left, y: top }\n}\n\nexport function getEventClientTouchOffset(\n\te: TouchEvent,\n\tlastTargetTouchFallback?: Touch,\n): XYCoord | undefined {\n\tif (e.targetTouches.length === 1) {\n\t\treturn getEventClientOffset(e.targetTouches[0] as Touch)\n\t} else if (lastTargetTouchFallback && e.touches.length === 1) {\n\t\tif ((e.touches[0] as Touch).target === lastTargetTouchFallback.target) {\n\t\t\treturn getEventClientOffset(e.touches[0] as Touch)\n\t\t}\n\t}\n\treturn\n}\n\nexport function getEventClientOffset(\n\te: TouchEvent | Touch | MouseEvent,\n\tlastTargetTouchFallback?: Touch,\n): XYCoord | undefined {\n\tif (isTouchEvent(e)) {\n\t\treturn getEventClientTouchOffset(e, lastTargetTouchFallback)\n\t} else {\n\t\treturn {\n\t\t\tx: e.clientX,\n\t\t\ty: e.clientY,\n\t\t}\n\t}\n}\n"], "names": ["isTouchEvent", "ELEMENT_NODE", "getNodeClientOffset", "node", "el", "nodeType", "parentElement", "undefined", "top", "left", "getBoundingClientRect", "x", "y", "getEventClientTouchOffset", "e", "lastTargetTouchFallback", "targetTouches", "length", "getEventClientOffset", "touches", "target", "clientX", "clientY"], "mappings": "AAEA,SAASA,YAAY,QAAQ,iBAAiB,CAAA;AAE9C,MAAMC,YAAY,GAAG,CAAC;AAEtB,OAAO,SAASC,mBAAmB,CAACC,IAAa,EAAuB;IACvE,MAAMC,EAAE,GAAGD,IAAI,CAACE,QAAQ,KAAKJ,YAAY,GAAGE,IAAI,GAAGA,IAAI,CAACG,aAAa;IACrE,IAAI,CAACF,EAAE,EAAE;QACR,OAAOG,SAAS,CAAA;KAChB;IACD,MAAM,EAAEC,GAAG,CAAA,EAAEC,IAAI,CAAA,EAAE,GAAGL,EAAE,CAACM,qBAAqB,EAAE;IAChD,OAAO;QAAEC,CAAC,EAAEF,IAAI;QAAEG,CAAC,EAAEJ,GAAG;KAAE,CAAA;CAC1B;AAED,OAAO,SAASK,yBAAyB,CACxCC,CAAa,EACbC,uBAA+B,EACT;IACtB,IAAID,CAAC,CAACE,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;QACjC,OAAOC,oBAAoB,CAACJ,CAAC,CAACE,aAAa,CAAC,CAAC,CAAC,CAAU,CAAA;KACxD,MAAM,IAAID,uBAAuB,IAAID,CAAC,CAACK,OAAO,CAACF,MAAM,KAAK,CAAC,EAAE;QAC7D,IAAI,AAACH,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,CAAWC,MAAM,KAAKL,uBAAuB,CAACK,MAAM,EAAE;YACtE,OAAOF,oBAAoB,CAACJ,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,CAAU,CAAA;SAClD;KACD;IACD,OAAM;CACN;AAED,OAAO,SAASD,oBAAoB,CACnCJ,CAAkC,EAClCC,uBAA+B,EACT;IACtB,IAAIf,YAAY,CAACc,CAAC,CAAC,EAAE;QACpB,OAAOD,yBAAyB,CAACC,CAAC,EAAEC,uBAAuB,CAAC,CAAA;KAC5D,MAAM;QACN,OAAO;YACNJ,CAAC,EAAEG,CAAC,CAACO,OAAO;YACZT,CAAC,EAAEE,CAAC,CAACQ,OAAO;SACZ,CAAA;KACD;CACD"}