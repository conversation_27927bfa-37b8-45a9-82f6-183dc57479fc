# src/services/tag_migration.py
"""
标签管理数据库迁移服务
确保案例数据库包含标签管理所需的表结构
"""
import logging
from pathlib import Path
from sqlalchemy import text
from sqlalchemy.orm import Session

# PostgreSQL模式下不需要SQLite数据库函数
from ..models import TagCache, DeletedFiles, CustomTags, FileCustomTags

logger = logging.getLogger(__name__)

def migrate_case_database(case_id: int) -> bool:
    """
    为指定案例数据库添加标签管理表结构（PostgreSQL模式暂不支持）

    Args:
        case_id: 案例ID

    Returns:
        bool: 迁移是否成功
    """
    logger.warning("PostgreSQL模式下不支持SQLite案例数据库迁移")
    return True  # 返回True避免阻塞其他功能


def migrate_all_case_databases() -> dict:
    """
    为所有案例数据库执行标签管理表结构迁移
    
    Returns:
        dict: 迁移结果统计
    """
    from ..crud import get_cases
    from ..database import get_master_db
    
    results = {
        "total": 0,
        "success": 0,
        "failed": 0,
        "errors": []
    }
    
    try:
        # 获取主数据库连接
        master_db = next(get_master_db())
        
        try:
            # 获取所有案例
            cases = get_cases(master_db, skip=0, limit=1000)
            results["total"] = len(cases)
            
            for case in cases:
                try:
                    if migrate_case_database(case.id):
                        results["success"] += 1
                    else:
                        results["failed"] += 1
                        results["errors"].append(f"案例 {case.id} 迁移失败")
                except Exception as e:
                    results["failed"] += 1
                    results["errors"].append(f"案例 {case.id} 迁移异常: {str(e)}")
            
        finally:
            master_db.close()
            
    except Exception as e:
        logger.error(f"获取案例列表失败: {e}")
        results["errors"].append(f"获取案例列表失败: {str(e)}")
    
    logger.info(f"批量迁移完成: 总计 {results['total']}, 成功 {results['success']}, 失败 {results['failed']}")
    return results


def _get_existing_tables(db: Session) -> set:
    """获取数据库中已存在的表名"""
    try:
        result = db.execute(text("SELECT tablename FROM pg_tables WHERE schemaname = 'public'"))
        return {row[0] for row in result.fetchall()}
    except Exception as e:
        logger.warning(f"获取表列表失败: {e}")
        return set()


def _create_tag_cache_table(db: Session):
    """创建标签缓存表"""
    # 创建表
    create_table_sql = """
    CREATE TABLE tag_cache (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tag_category VARCHAR(50) NOT NULL,
        tag_name VARCHAR(100) NOT NULL,
        tag_value VARCHAR(500) NOT NULL,
        file_ids TEXT NOT NULL,
        file_count INTEGER NOT NULL DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
    """
    db.execute(text(create_table_sql))

    # 创建索引
    index_sqls = [
        "CREATE INDEX ix_tag_cache_category ON tag_cache (tag_category)",
        "CREATE INDEX ix_tag_cache_name ON tag_cache (tag_name)",
        "CREATE INDEX ix_tag_cache_value ON tag_cache (tag_value)"
    ]

    for index_sql in index_sqls:
        db.execute(text(index_sql))


def _create_deleted_files_table(db: Session):
    """创建删除文件记录表"""
    # 创建表
    create_table_sql = """
    CREATE TABLE deleted_files (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_id INTEGER NOT NULL,
        file_name VARCHAR(255) NOT NULL,
        deleted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_permanent BOOLEAN NOT NULL DEFAULT 0
    )
    """
    db.execute(text(create_table_sql))

    # 创建索引
    db.execute(text("CREATE INDEX ix_deleted_files_file_id ON deleted_files (file_id)"))


def _create_custom_tags_table(db: Session):
    """创建自定义标签表"""
    sql = """
    CREATE TABLE custom_tags (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tag_name VARCHAR(100) NOT NULL UNIQUE,
        tag_color VARCHAR(7) NOT NULL DEFAULT '#3B82F6',
        display_order INTEGER NOT NULL DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
    """
    db.execute(text(sql))


def _create_file_custom_tags_table(db: Session):
    """创建文件自定义标签关联表"""
    # 创建表
    create_table_sql = """
    CREATE TABLE file_custom_tags (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_id INTEGER NOT NULL,
        custom_tag_id INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (file_id) REFERENCES files (id),
        FOREIGN KEY (custom_tag_id) REFERENCES custom_tags (id),
        UNIQUE (file_id, custom_tag_id)
    )
    """
    db.execute(text(create_table_sql))

    # 创建索引
    index_sqls = [
        "CREATE INDEX ix_file_custom_tags_file_id ON file_custom_tags (file_id)",
        "CREATE INDEX ix_file_custom_tags_custom_tag_id ON file_custom_tags (custom_tag_id)"
    ]

    for index_sql in index_sqls:
        db.execute(text(index_sql))


def check_case_database_schema(case_id: int) -> dict:
    """
    检查案例数据库的标签管理表结构（PostgreSQL模式暂不支持）

    Args:
        case_id: 案例ID

    Returns:
        dict: 表结构检查结果
    """
    logger.warning("PostgreSQL模式下不支持SQLite案例数据库结构检查")
    return {
        "case_id": case_id,
        "database_path": "PostgreSQL模式",
        "tables": {
            "tag_cache": False,
            "deleted_files": False,
            "custom_tags": False,
            "file_custom_tags": False
        },
        "migration_needed": False
    }


if __name__ == "__main__":
    # 测试迁移功能
    import sys
    
    if len(sys.argv) > 1:
        case_id = int(sys.argv[1])
        print(f"迁移案例 {case_id} 的数据库...")
        result = migrate_case_database(case_id)
        print(f"迁移结果: {'成功' if result else '失败'}")
    else:
        print("批量迁移所有案例数据库...")
        results = migrate_all_case_databases()
        print(f"迁移完成: {results}")