# tests/unit/test_services.py
"""
图像处理服务的单元测试
"""
import os
from pathlib import Path
from sqlalchemy.orm import Session

from src.services import process_local_file_path, _get_image_metadata, _create_thumbnail
from src.crud.case_crud import create_case
from src import schemas


class TestImageServices:
    """图像处理服务测试类"""
    
    def test_get_image_metadata_success(self, test_image_file: str):
        """测试提取图像元数据"""
        width, height, taken_at = _get_image_metadata(Path(test_image_file))
        
        # 验证返回的元数据
        assert width == 100
        assert height == 100
        # taken_at可能为None，因为测试图像没有EXIF数据
    
    def test_get_image_metadata_invalid_file(self):
        """测试处理无效图像文件"""
        invalid_path = Path("/path/to/nonexistent.jpg")
        width, height, taken_at = _get_image_metadata(invalid_path)
        
        # 应该返回None值
        assert width is None
        assert height is None
        assert taken_at is None
    
    def test_create_thumbnail_success(self, test_image_file: str, test_data_dir: str):
        """测试创建缩略图"""
        thumbnail_dir = Path(test_data_dir) / "thumbnails"
        thumbnail_dir.mkdir(exist_ok=True)
        
        thumbnail_path = _create_thumbnail(
            Path(test_image_file), 
            thumbnail_dir, 
            "test_thumb"
        )
        
        # 验证缩略图文件创建成功
        assert thumbnail_path is not None
        assert Path(thumbnail_path).exists()
        assert Path(thumbnail_path).suffix == ".jpg"
    
    def test_process_local_file_path_success(
        self, 
        test_db: Session, 
        test_image_file: str, 
        test_data_dir: str,
        mock_uploads_dir: str
    ):
        """测试处理本地文件路径"""
        # 临时修改DATA_DIR
        import src.database
        original_data_dir = src.database.DATA_DIR
        src.database.DATA_DIR = test_data_dir
        
        try:
            # 创建测试案例
            case_create = schemas.CaseCreate(case_name="测试案例", description="测试描述")
            created_case = create_case(test_db, case_create)
            
            # 处理图像文件
            result = process_local_file_path(test_db, created_case.id, test_image_file)  # type: ignore
            
            # 验证处理结果
            assert result is not None
            assert result.file_name == os.path.basename(test_image_file)
            assert result.file_path == test_image_file
            assert result.file_type == "image/jpeg"
            assert result.width == 100
            assert result.height == 100
            assert result.thumbnail_small_path is not None
            
        finally:
            src.database.DATA_DIR = original_data_dir
    
    def test_process_local_file_path_non_image(
        self, 
        test_db: Session, 
        test_data_dir: str
    ):
        """测试处理非图像文件"""
        # 创建一个文本文件
        text_file_path = os.path.join(test_data_dir, "test.txt")
        with open(text_file_path, "w") as f:
            f.write("This is a test file")
        
        # 临时修改DATA_DIR
        import src.database
        original_data_dir = src.database.DATA_DIR
        src.database.DATA_DIR = test_data_dir
        
        try:
            # 创建测试案例
            case_create = schemas.CaseCreate(case_name="测试案例", description="测试描述")
            created_case = create_case(test_db, case_create)
            
            # 处理文本文件（应该被跳过）
            result = process_local_file_path(test_db, created_case.id, text_file_path)  # type: ignore
            
            # 非图像文件应该返回None
            assert result is None
            
        finally:
            src.database.DATA_DIR = original_data_dir
            # 清理测试文件
            if os.path.exists(text_file_path):
                os.remove(text_file_path)
    
    def test_process_local_file_path_invalid_file(
        self, 
        test_db: Session, 
        test_data_dir: str
    ):
        """测试处理不存在的文件"""
        # 临时修改DATA_DIR
        import src.database
        original_data_dir = src.database.DATA_DIR
        src.database.DATA_DIR = test_data_dir
        
        try:
            # 创建测试案例
            case_create = schemas.CaseCreate(case_name="测试案例", description="测试描述")
            created_case = create_case(test_db, case_create)
            
            # 处理不存在的文件
            result = process_local_file_path(test_db, created_case.id, "/path/to/nonexistent.jpg")  # type: ignore
            
            # 不存在的文件应该返回None
            assert result is None
            
        finally:
            src.database.DATA_DIR = original_data_dir 