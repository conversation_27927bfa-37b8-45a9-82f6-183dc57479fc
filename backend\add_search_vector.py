#!/usr/bin/env python3
"""
添加search_vector字段到files表的迁移脚本
修复全文搜索功能
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(project_root / "backend"))

from sqlalchemy import create_engine, text
from urllib.parse import quote_plus
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_database_url():
    """获取数据库连接URL"""
    user = os.getenv('POSTGRES_USER', 'postgres')
    password = quote_plus(os.getenv('POSTGRES_PASSWORD', '2025'))
    host = os.getenv('POSTGRES_HOST', 'localhost')
    port = os.getenv('POSTGRES_PORT', '5432')
    db = os.getenv('POSTGRES_DB', 'mizzy_star_db')
    return f"postgresql://{user}:{password}@{host}:{port}/{db}"

def add_search_vector_field():
    """添加search_vector字段到files表"""
    
    try:
        # 创建数据库连接
        engine = create_engine(get_database_url())
        
        with engine.connect() as conn:
            # 开始事务
            trans = conn.begin()
            
            try:
                # 1. 检查search_vector字段是否已存在
                logger.info("检查search_vector字段是否存在...")
                result = conn.execute(text("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'files' AND column_name = 'search_vector'
                """))
                
                if result.fetchone():
                    logger.info("✅ search_vector字段已存在，无需添加")
                    trans.rollback()
                    return True
                
                # 2. 添加search_vector字段
                logger.info("添加search_vector字段...")
                conn.execute(text("""
                    ALTER TABLE files 
                    ADD COLUMN search_vector TSVECTOR 
                    GENERATED ALWAYS AS (
                        to_tsvector('english', 
                            COALESCE(file_name, '') || ' ' ||
                            COALESCE(tags::text, '')
                        )
                    ) STORED
                """))
                
                # 3. 创建GIN索引以提高搜索性能
                logger.info("创建search_vector的GIN索引...")
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_files_search_vector 
                    ON files USING GIN(search_vector)
                """))
                
                # 4. 验证字段添加成功
                logger.info("验证字段添加...")
                result = conn.execute(text("""
                    SELECT column_name, data_type 
                    FROM information_schema.columns 
                    WHERE table_name = 'files' AND column_name = 'search_vector'
                """))
                
                field_info = result.fetchone()
                if field_info:
                    logger.info(f"✅ search_vector字段添加成功: {field_info[0]} ({field_info[1]})")
                else:
                    raise Exception("字段添加验证失败")
                
                # 5. 测试搜索功能
                logger.info("测试搜索功能...")
                test_result = conn.execute(text("""
                    SELECT COUNT(*) as total_files,
                           COUNT(search_vector) as files_with_search_vector
                    FROM files
                """))
                
                counts = test_result.fetchone()
                logger.info(f"📊 总文件数: {counts[0]}, 有搜索向量的文件数: {counts[1]}")
                
                # 提交事务
                trans.commit()
                logger.info("🎉 search_vector字段添加完成！")
                return True
                
            except Exception as e:
                trans.rollback()
                logger.error(f"❌ 添加search_vector字段失败: {e}")
                return False
                
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        return False

def test_search_functionality():
    """测试搜索功能是否正常工作"""
    
    try:
        engine = create_engine(get_database_url())
        
        with engine.connect() as conn:
            # 测试全文搜索查询
            logger.info("测试全文搜索查询...")
            result = conn.execute(text("""
                SELECT 
                    id, file_name,
                    ts_rank(search_vector, plainto_tsquery('english', :query)) as rank
                FROM files 
                WHERE search_vector @@ plainto_tsquery('english', :query)
                ORDER BY rank DESC
                LIMIT 5
            """), {'query': 'test'})
            
            search_results = result.fetchall()
            logger.info(f"🔍 搜索'test'找到 {len(search_results)} 个结果")
            
            for row in search_results:
                logger.info(f"  - ID: {row[0]}, 文件名: {row[1]}, 相关度: {row[2]:.4f}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 搜索功能测试失败: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 开始修复搜索功能...")
    
    # 添加search_vector字段
    if add_search_vector_field():
        # 测试搜索功能
        if test_search_functionality():
            logger.info("✅ 搜索功能修复完成！")
        else:
            logger.error("❌ 搜索功能测试失败")
    else:
        logger.error("❌ 搜索功能修复失败")
