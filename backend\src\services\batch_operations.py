#!/usr/bin/env python3
"""
批量数据库操作优化
实施高效的批量插入、更新和查询操作
"""

import asyncio
import time
from typing import List, Dict, Any, Optional, Tuple
from contextlib import asynccontextmanager
import logging

from sqlalchemy import text, insert, update, delete
from sqlalchemy.orm import Session
from sqlalchemy.dialects.postgresql import insert as pg_insert

from ..models import File, Case
from ..database import create_independent_db_session

logger = logging.getLogger(__name__)

class BatchOperationManager:
    """批量操作管理器"""
    
    def __init__(self, batch_size: int = 1000):
        self.batch_size = batch_size
        self.pending_operations = []
        self.operation_stats = {
            'inserts': 0,
            'updates': 0,
            'deletes': 0,
            'queries': 0
        }
    
    async def batch_insert_files(self, files_data: List[Dict[str, Any]]) -> List[int]:
        """批量插入文件记录"""
        start_time = time.time()
        inserted_ids = []
        
        db = create_independent_db_session()
        try:
            # 分批处理
            for i in range(0, len(files_data), self.batch_size):
                batch = files_data[i:i + self.batch_size]
                
                # 使用PostgreSQL的RETURNING子句获取插入的ID
                stmt = pg_insert(File).values(batch).returning(File.id)
                result = db.execute(stmt)
                batch_ids = [row[0] for row in result.fetchall()]
                inserted_ids.extend(batch_ids)
                
                # 提交批次
                db.commit()
                
                self.operation_stats['inserts'] += len(batch)
                logger.debug(f"批量插入 {len(batch)} 条文件记录")
            
            operation_time = (time.time() - start_time) * 1000
            logger.info(f"批量插入完成: {len(files_data)} 条记录, 耗时: {operation_time:.2f}ms")
            
            return inserted_ids
            
        except Exception as e:
            db.rollback()
            logger.error(f"批量插入失败: {e}")
            raise
        finally:
            db.close()
    
    async def batch_update_files(self, updates: List[Dict[str, Any]]) -> int:
        """批量更新文件记录"""
        start_time = time.time()
        updated_count = 0
        
        db = create_independent_db_session()
        try:
            # 按文件ID分组更新
            for i in range(0, len(updates), self.batch_size):
                batch = updates[i:i + self.batch_size]
                
                # 构建批量更新语句
                cases = []
                file_ids = []
                
                for update_data in batch:
                    file_id = update_data.pop('id')
                    file_ids.append(file_id)
                    
                    # 构建CASE语句
                    for field, value in update_data.items():
                        cases.append(f"WHEN {file_id} THEN '{value}'")
                
                if cases and file_ids:
                    # 执行批量更新
                    sql = f"""
                    UPDATE files SET
                        quality_score = CASE id {' '.join(cases)} END,
                        updated_at = NOW()
                    WHERE id IN ({','.join(map(str, file_ids))})
                    """
                    
                    result = db.execute(text(sql))
                    updated_count += result.rowcount
                    db.commit()
                
                self.operation_stats['updates'] += len(batch)
                logger.debug(f"批量更新 {len(batch)} 条文件记录")
            
            operation_time = (time.time() - start_time) * 1000
            logger.info(f"批量更新完成: {updated_count} 条记录, 耗时: {operation_time:.2f}ms")
            
            return updated_count
            
        except Exception as e:
            db.rollback()
            logger.error(f"批量更新失败: {e}")
            raise
        finally:
            db.close()
    
    async def batch_upsert_files(self, files_data: List[Dict[str, Any]]) -> Tuple[int, int]:
        """批量插入或更新文件记录（PostgreSQL UPSERT）"""
        start_time = time.time()
        inserted_count = 0
        updated_count = 0
        
        db = create_independent_db_session()
        try:
            for i in range(0, len(files_data), self.batch_size):
                batch = files_data[i:i + self.batch_size]
                
                # 使用PostgreSQL的ON CONFLICT DO UPDATE
                stmt = pg_insert(File).values(batch)
                stmt = stmt.on_conflict_do_update(
                    index_elements=['file_path', 'case_id'],
                    set_={
                        'file_name': stmt.excluded.file_name,
                        'quality_score': stmt.excluded.quality_score,
                        'tags': stmt.excluded.tags,
                        'updated_at': stmt.excluded.updated_at
                    }
                )
                
                result = db.execute(stmt)
                db.commit()
                
                # 注意：PostgreSQL的upsert不直接返回插入/更新计数
                # 这里简化处理
                inserted_count += len(batch)
                
                logger.debug(f"批量upsert {len(batch)} 条文件记录")
            
            operation_time = (time.time() - start_time) * 1000
            logger.info(f"批量upsert完成: {len(files_data)} 条记录, 耗时: {operation_time:.2f}ms")
            
            return inserted_count, updated_count
            
        except Exception as e:
            db.rollback()
            logger.error(f"批量upsert失败: {e}")
            raise
        finally:
            db.close()
    
    async def batch_query_files(self, file_ids: List[int], 
                               fields: List[str] = None) -> List[Dict[str, Any]]:
        """批量查询文件记录"""
        start_time = time.time()
        results = []
        
        # 默认查询字段
        if fields is None:
            fields = ['id', 'file_name', 'file_path', 'quality_score', 'tags']
        
        db = create_independent_db_session()
        try:
            # 分批查询
            for i in range(0, len(file_ids), self.batch_size):
                batch_ids = file_ids[i:i + self.batch_size]
                
                # 构建查询
                fields_str = ', '.join(fields)
                ids_str = ','.join(map(str, batch_ids))
                
                sql = f"""
                SELECT {fields_str}
                FROM files
                WHERE id IN ({ids_str})
                ORDER BY id
                """
                
                result = db.execute(text(sql))
                batch_results = []
                
                for row in result.fetchall():
                    row_dict = {}
                    for i, field in enumerate(fields):
                        row_dict[field] = row[i]
                    batch_results.append(row_dict)
                
                results.extend(batch_results)
                
                self.operation_stats['queries'] += len(batch_ids)
                logger.debug(f"批量查询 {len(batch_ids)} 条文件记录")
            
            operation_time = (time.time() - start_time) * 1000
            logger.info(f"批量查询完成: {len(results)} 条记录, 耗时: {operation_time:.2f}ms")
            
            return results
            
        except Exception as e:
            logger.error(f"批量查询失败: {e}")
            raise
        finally:
            db.close()
    
    async def batch_delete_files(self, file_ids: List[int]) -> int:
        """批量删除文件记录"""
        start_time = time.time()
        deleted_count = 0
        
        db = create_independent_db_session()
        try:
            # 分批删除
            for i in range(0, len(file_ids), self.batch_size):
                batch_ids = file_ids[i:i + self.batch_size]
                
                # 执行批量删除
                stmt = delete(File).where(File.id.in_(batch_ids))
                result = db.execute(stmt)
                deleted_count += result.rowcount
                db.commit()
                
                self.operation_stats['deletes'] += len(batch_ids)
                logger.debug(f"批量删除 {len(batch_ids)} 条文件记录")
            
            operation_time = (time.time() - start_time) * 1000
            logger.info(f"批量删除完成: {deleted_count} 条记录, 耗时: {operation_time:.2f}ms")
            
            return deleted_count
            
        except Exception as e:
            db.rollback()
            logger.error(f"批量删除失败: {e}")
            raise
        finally:
            db.close()
    
    async def optimize_table_statistics(self, table_name: str = 'files') -> None:
        """优化表统计信息"""
        start_time = time.time()
        
        db = create_independent_db_session()
        try:
            # 更新表统计信息
            db.execute(text(f"ANALYZE {table_name}"))
            db.commit()
            
            operation_time = (time.time() - start_time) * 1000
            logger.info(f"表 {table_name} 统计信息更新完成, 耗时: {operation_time:.2f}ms")
            
        except Exception as e:
            logger.error(f"更新表统计信息失败: {e}")
            raise
        finally:
            db.close()
    
    def get_operation_stats(self) -> Dict[str, Any]:
        """获取操作统计"""
        return {
            'batch_size': self.batch_size,
            'operations': self.operation_stats.copy(),
            'total_operations': sum(self.operation_stats.values())
        }
    
    def reset_stats(self) -> None:
        """重置统计"""
        self.operation_stats = {
            'inserts': 0,
            'updates': 0,
            'deletes': 0,
            'queries': 0
        }

# 全局批量操作管理器
batch_manager = BatchOperationManager(batch_size=1000)

# 异步上下文管理器：批量操作事务
@asynccontextmanager
async def batch_transaction():
    """批量操作事务上下文管理器"""
    db = create_independent_db_session()
    try:
        yield db
        db.commit()
        logger.debug("批量事务提交成功")
    except Exception as e:
        db.rollback()
        logger.error(f"批量事务回滚: {e}")
        raise
    finally:
        db.close()

# 装饰器：自动批量处理
def batch_operation(batch_size: int = 1000):
    """批量操作装饰器"""
    def decorator(func):
        async def wrapper(data_list: List[Any], *args, **kwargs):
            results = []
            
            # 分批处理
            for i in range(0, len(data_list), batch_size):
                batch = data_list[i:i + batch_size]
                batch_result = await func(batch, *args, **kwargs)
                
                if isinstance(batch_result, list):
                    results.extend(batch_result)
                else:
                    results.append(batch_result)
            
            return results
        return wrapper
    return decorator
