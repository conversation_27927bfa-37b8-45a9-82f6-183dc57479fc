"""
EXIF元数据提取服务
提取图像文件中的完整EXIF数据，包括相机信息、拍摄参数等
"""

import logging
from typing import Dict, Any, Optional, Union
from pathlib import Path
from PIL import Image, ExifTags
from datetime import datetime
import fractions

logger = logging.getLogger(__name__)

class ExifExtractor:
    """EXIF元数据提取器"""
    
    def __init__(self):
        """初始化EXIF提取器"""
        # 创建EXIF标签名称映射
        self.exif_tags = {v: k for k, v in ExifTags.TAGS.items()}
        
        # 重要的EXIF字段映射 - 按照新的标签分类规则
        self.important_fields = {
            # 相机信息（用于合并为camera标签）
            'Make': 'camera_make',           # 相机制造商（临时字段，后续合并）
            'Model': 'camera_model',         # 相机型号（临时字段，后续合并）

            # 元数据标签 (metadata) - 拍摄相关的技术参数
            'FNumber': 'aperture',           # 光圈值 -> f/数值
            'ApertureValue': 'aperture',     # 光圈值（备用）
            'ExposureTime': 'shutter_speed', # 快门速度 -> 数值+秒
            'ISOSpeedRatings': 'iso',        # ISO感光度 -> ISO-数值
            'ISO': 'iso',                    # ISO感光度（备用）
            'ColorSpace': 'color_standard',  # 色彩标准 -> 直接显示
            'FocalLength': 'focal_length',   # 焦距
            'FocalLengthIn35mmFilm': 'focal_length_35mm', # 35mm等效焦距
            'WhiteBalance': 'white_balance', # 白平衡
            'Flash': 'flash',                # 闪光灯

            # 时间信息
            'DateTimeOriginal': 'shooting_date', # 拍摄日期 -> 年/月/日
            'DateTime': 'shooting_date',     # 拍摄日期（备用）

            # 属性标签 (properties) - 文件和图像的基本属性
            'XResolution': 'resolution_x',   # X分辨率（用于合并）
            'YResolution': 'resolution_y',   # Y分辨率（用于合并）
            'ResolutionUnit': 'resolution_unit', # 分辨率单位
            'BitsPerSample': 'color_depth',  # 色彩深度/位深度 -> 数值-bit

            # 其他技术信息（保留但不作为主要标签）
            'LensModel': 'lens_model',       # 镜头型号
            'ExposureMode': 'exposure_mode', # 曝光模式
            'MeteringMode': 'metering_mode', # 测光模式
            'Artist': 'artist',             # 艺术家/摄影师
            'Copyright': 'copyright',       # 版权信息
            'ImageDescription': 'description', # 图像描述

            # 移除的字段（按照用户要求）：
            # 'Software': 'software',          # 移除软件信息标签
            # 'GPSInfo': 'gps_info',          # GPS信息（暂时移除）
        }
    
    def extract_exif_data(self, image_path: Union[str, Path]) -> Dict[str, Any]:
        """
        从图像文件中提取完整的EXIF数据
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            包含所有EXIF元数据的字典
        """
        try:
            image_path = Path(image_path)
            
            with Image.open(image_path) as img:
                # 获取EXIF数据
                exif_data = img.getexif()
                
                if not exif_data:
                    logger.info(f"图像 {image_path.name} 没有EXIF数据")
                    return {}
                
                # 提取并处理EXIF数据
                extracted_data = {}
                temp_data = {}  # 临时存储需要合并的数据

                # 处理主EXIF数据
                for tag_id, value in exif_data.items():
                    tag_name = ExifTags.TAGS.get(tag_id)

                    if tag_name and tag_name in self.important_fields:
                        field_name = self.important_fields[tag_name]
                        processed_value = self._process_exif_value(tag_name, value)

                        if processed_value is not None:
                            temp_data[field_name] = processed_value
                            logger.debug(f"提取主EXIF字段 {tag_name}: {processed_value}")

                # 处理EXIF子IFD数据（包含拍摄参数）
                try:
                    exif_ifd = exif_data.get_ifd(0x8769)  # EXIF IFD
                    logger.info(f"找到EXIF子IFD，包含 {len(exif_ifd)} 个字段")

                    for tag_id, value in exif_ifd.items():
                        tag_name = ExifTags.TAGS.get(tag_id)

                        if tag_name and tag_name in self.important_fields:
                            field_name = self.important_fields[tag_name]
                            processed_value = self._process_exif_value(tag_name, value)

                            if processed_value is not None:
                                temp_data[field_name] = processed_value
                                logger.debug(f"提取子IFD字段 {tag_name}: {processed_value}")

                except KeyError:
                    logger.info("没有找到EXIF子IFD数据")

                # 按照新规则处理和合并数据
                extracted_data = self._format_exif_data(temp_data, img)
                
                # 处理GPS信息
                if 'GPSInfo' in exif_data:
                    gps_data = self._extract_gps_data(exif_data['GPSInfo'])
                    if gps_data:
                        extracted_data.update(gps_data)
                
                logger.info(f"从 {image_path.name} 提取了 {len(extracted_data)} 个EXIF字段")
                return extracted_data
                
        except Exception as e:
            logger.error(f"提取EXIF数据失败 {image_path}: {e}")
            return {}
    
    def _process_exif_value(self, tag_name: str, value: Any) -> Any:
        """
        处理EXIF值，转换为合适的格式（原始值处理，不做最终格式化）

        Args:
            tag_name: EXIF标签名
            value: 原始值

        Returns:
            处理后的原始值（用于后续格式化）
        """
        try:
            # 处理时间字段 - 保留原始格式用于后续处理
            if tag_name in ['DateTimeOriginal', 'DateTime']:
                if isinstance(value, str):
                    return value  # 保留原始时间字符串
                return str(value)

            # 处理分数值（如光圈、快门速度、焦距）
            if isinstance(value, tuple) and len(value) == 2:
                try:
                    if value[1] != 0:
                        fraction_value = fractions.Fraction(value[0], value[1])
                        return float(fraction_value)  # 返回数值，格式化在后续处理
                except (ZeroDivisionError, TypeError):
                    pass

            # 处理字符串值
            if isinstance(value, bytes):
                try:
                    return value.decode('utf-8', errors='ignore').strip()
                except:
                    return str(value)

            # 处理数值 - 返回原始数值
            if isinstance(value, (int, float)):
                return value

            # 处理其他类型
            return str(value) if value is not None else None

        except Exception as e:
            logger.debug(f"处理EXIF值失败 {tag_name}: {e}")
            return None

    def _format_exif_data(self, temp_data: Dict[str, Any], img: Image.Image) -> Dict[str, Any]:
        """
        按照新的标签分类规则格式化EXIF数据

        分类规则：
        1. 属性标签 (properties): 文件和图像的基本属性
        2. 元数据标签 (metadata): 拍摄相关的技术参数
        3. 移除: 文件名、文件大小、软件信息、质量分

        Args:
            temp_data: 临时提取的原始EXIF数据
            img: PIL图像对象

        Returns:
            按新分类规则格式化的数据字典，包含properties和metadata两个部分
        """
        # 初始化分类数据结构
        result = {
            'properties': {},  # 属性标签
            'metadata': {}     # 元数据标签
        }

        try:
            # === 属性标签 (properties) - 文件和图像的基本属性 ===

            # 1. 图像类型（属性标签）- 合并jpeg和jpg
            if hasattr(img, 'format') and img.format:
                file_type = img.format.lower()
                if file_type in ['jpeg', 'jpg']:
                    result['properties']['fileType'] = 'jpg'  # 统一为jpg
                else:
                    result['properties']['fileType'] = file_type

            # 2. 图像尺寸（属性标签）
            if hasattr(img, 'size') and img.size:
                width, height = img.size
                result['properties']['dimensions'] = f"{width}x{height}"

            # 3. 分辨率（属性标签）
            if 'resolution_x' in temp_data and 'resolution_unit' in temp_data:
                resolution_value = temp_data['resolution_x']
                resolution_unit = temp_data['resolution_unit']

                # 安全转换分辨率值
                try:
                    if isinstance(resolution_value, str):
                        resolution_num = int(float(resolution_value))
                    else:
                        resolution_num = int(resolution_value)
                except (ValueError, TypeError):
                    resolution_num = 300  # 默认值

                # 转换分辨率单位
                if resolution_unit == 2:  # DPI
                    result['properties']['resolution'] = f"{resolution_num} DPI"
                elif resolution_unit == 3:  # DPC (dots per cm)
                    result['properties']['resolution'] = f"{resolution_num} DPC"
                else:
                    result['properties']['resolution'] = f"{resolution_num} DPI"

            # 4. 色深信息（属性标签）
            if 'color_depth' in temp_data:
                color_depth = temp_data['color_depth']
                if isinstance(color_depth, (list, tuple)) and len(color_depth) > 0:
                    depth_value = color_depth[0] if isinstance(color_depth[0], (int, float)) else 24
                elif isinstance(color_depth, (int, float)):
                    depth_value = color_depth
                else:
                    depth_value = 24  # 默认值
                result['properties']['color_depth'] = f"{int(depth_value)}-bit"
                # 添加位深度标签（中文命名）
                result['properties']['位深度'] = f"{int(depth_value)}位"
            else:
                # 如果没有EXIF色深信息，根据图像模式推断
                if hasattr(img, 'mode'):
                    if img.mode == 'RGB':
                        result['properties']['color_depth'] = '24-bit'
                        result['properties']['位深度'] = '24位'
                    elif img.mode == 'RGBA':
                        result['properties']['color_depth'] = '32-bit'
                        result['properties']['位深度'] = '32位'
                    elif img.mode == 'L':
                        result['properties']['color_depth'] = '8-bit'
                        result['properties']['位深度'] = '8位'
                    else:
                        result['properties']['color_depth'] = '24-bit'  # 默认值
                        result['properties']['位深度'] = '24位'

            # === 元数据标签 (metadata) - 拍摄相关的技术参数 ===

            # 1. 相机信息（合并camera_make + camera_model为camera）
            if 'camera_make' in temp_data and 'camera_model' in temp_data:
                make = temp_data['camera_make'].strip()
                model = temp_data['camera_model'].strip()
                # 避免重复品牌名
                if not model.startswith(make):
                    result['metadata']['camera'] = f"{make} {model}"  # 如：SONY ILCE-7RM4
                else:
                    result['metadata']['camera'] = model
            elif 'camera_model' in temp_data:
                result['metadata']['camera'] = temp_data['camera_model'].strip()

            # 注意：不再单独添加camera_make和camera_model字段

            # 2. ISO感光度
            if 'iso' in temp_data:
                iso_value = temp_data['iso']
                if isinstance(iso_value, (int, float)):
                    result['metadata']['iso'] = f"ISO-{int(iso_value)}"

            # 3. 光圈
            if 'aperture' in temp_data:
                aperture_value = temp_data['aperture']
                if isinstance(aperture_value, (int, float)) and aperture_value > 0:
                    result['metadata']['aperture'] = f"f/{aperture_value:.1f}"

            # 4. 快门速度
            if 'shutter_speed' in temp_data:
                shutter_value = temp_data['shutter_speed']
                if isinstance(shutter_value, (int, float)) and shutter_value > 0:
                    if shutter_value < 1:
                        # 快门速度小于1秒，显示为分数
                        result['metadata']['shutter_speed'] = f"1/{int(1/shutter_value)} 秒"
                    else:
                        # 快门速度大于等于1秒
                        result['metadata']['shutter_speed'] = f"{shutter_value:.1f} 秒"

            # 5. 色彩标准（color_standard）
            if 'color_standard' in temp_data:
                color_space = temp_data['color_standard']
                if isinstance(color_space, (int, float)):
                    # 转换色彩空间数值
                    if color_space == 1:
                        result['metadata']['color_standard'] = 'sRGB'
                    elif color_space == 2:
                        result['metadata']['color_standard'] = 'Adobe RGB'
                    else:
                        result['metadata']['color_standard'] = f"ColorSpace-{int(color_space)}"
                else:
                    result['metadata']['color_standard'] = str(color_space)

            # 6. 焦距
            if 'focal_length' in temp_data:
                focal_length = temp_data['focal_length']
                if isinstance(focal_length, (int, float)) and focal_length > 0:
                    result['metadata']['focal_length'] = f"{focal_length:.0f}mm"
            elif 'focal_length_35mm' in temp_data:
                # 如果没有实际焦距，使用35mm等效焦距
                focal_length_35mm = temp_data['focal_length_35mm']
                if isinstance(focal_length_35mm, (int, float)) and focal_length_35mm > 0:
                    result['metadata']['focal_length'] = f"{focal_length_35mm:.0f}mm"

            # 7. 白平衡（排除默认的"自动"值）
            if 'white_balance' in temp_data:
                wb_value = temp_data['white_balance']
                if isinstance(wb_value, (int, float)):
                    wb_names = {0: '自动', 1: '手动', 2: '白炽灯', 3: '荧光灯', 4: '闪光灯', 5: '晴天', 6: '阴天'}
                    wb_text = wb_names.get(int(wb_value), f"WB-{int(wb_value)}")
                    # 只有非默认值才添加到标签中
                    if wb_text != '自动':
                        result['metadata']['white_balance'] = wb_text
                else:
                    wb_text = str(wb_value)
                    if wb_text.lower() not in ['auto', '自动', 'null', '']:
                        result['metadata']['white_balance'] = wb_text

            # 8. 闪光灯（排除"未闪光"的默认值）
            if 'flash' in temp_data:
                flash_value = temp_data['flash']
                if isinstance(flash_value, (int, float)):
                    flash_text = self._decode_flash_value(int(flash_value))
                    # 只有实际闪光的情况才添加到标签中
                    if flash_text and not flash_text.startswith('未闪光') and 'No Flash' not in flash_text:
                        result['metadata']['flash'] = flash_text
                else:
                    flash_text = str(flash_value)
                    if flash_text.lower() not in ['no flash', '未闪光', 'null', ''] and not flash_text.startswith('未闪光'):
                        result['metadata']['flash'] = flash_text

            # 9. 拍摄日期
            if 'shooting_date' in temp_data:
                date_str = temp_data['shooting_date']
                if isinstance(date_str, str):
                    try:
                        # 解析日期时间字符串 (格式: 2023:08:22 11:43:53)
                        dt = datetime.strptime(date_str, '%Y:%m:%d %H:%M:%S')
                        result['metadata']['shooting_date'] = f"{dt.year}/{dt.month}/{dt.day}"
                    except ValueError:
                        # 如果解析失败，尝试其他格式或保留原始值
                        result['metadata']['shooting_date'] = date_str.split(' ')[0].replace(':', '/')

            # === 移除的字段（按照用户要求） ===
            # - 文件名 (filename): 不作为标签
            # - 文件大小 (file_size): 不作为标签
            # - 软件信息 (software): 移除软件标签
            # - 质量分 (quality_score): 暂时移除

            logger.info(f"✅ 按新分类规则格式化EXIF数据完成")
            logger.info(f"   属性标签: {len(result['properties'])} 个字段")
            logger.info(f"   元数据标签: {len(result['metadata'])} 个字段")
            return result

        except Exception as e:
            logger.error(f"格式化EXIF数据失败: {e}")
            return {}

    
    def _decode_flash_value(self, flash_value: int) -> str:
        """解码闪光灯值"""
        flash_modes = []
        
        if flash_value & 0x01:
            flash_modes.append("闪光")
        else:
            flash_modes.append("未闪光")
        
        if flash_value & 0x04:
            flash_modes.append("强制")
        elif flash_value & 0x08:
            flash_modes.append("强制关闭")
        elif flash_value & 0x10:
            flash_modes.append("自动")
        
        if flash_value & 0x20:
            flash_modes.append("无闪光功能")
        
        if flash_value & 0x40:
            flash_modes.append("红眼减少")
        
        return ", ".join(flash_modes) if flash_modes else "未知"
    
    def _extract_gps_data(self, gps_info: Dict) -> Dict[str, Any]:
        """提取GPS数据"""
        try:
            gps_data = {}
            
            # 这里可以添加GPS数据的详细提取逻辑
            # 由于GPS数据结构复杂，暂时简化处理
            if gps_info:
                gps_data['has_gps'] = True
                # 可以进一步提取经纬度等信息
            
            return gps_data
            
        except Exception as e:
            logger.warning(f"提取GPS数据失败: {e}")
            return {}


# 全局EXIF提取器实例
exif_extractor = ExifExtractor()


def extract_complete_metadata(image_path: Union[str, Path]) -> Dict[str, Any]:
    """
    提取图像的完整元数据，包括基本信息和EXIF数据
    
    Args:
        image_path: 图像文件路径
        
    Returns:
        完整的元数据字典
    """
    try:
        image_path = Path(image_path)
        
        with Image.open(image_path) as img:
            # 基本图像信息
            width, height = img.size
            format_name = img.format
            mode = img.mode
            
            # 文件信息
            file_size = image_path.stat().st_size
            
            # 提取格式化的EXIF数据
            exif_data = exif_extractor.extract_exif_data(image_path)

            # 组合所有元数据 - 使用新的格式化数据
            metadata = {
                # 基本文件信息
                'filename': image_path.name,
                'file_size': file_size,

                # 基本图像信息（保留用于兼容性）
                'width': width,
                'height': height,
                'format': format_name,
                'mode': mode,

                # 格式化的EXIF数据（按照新规则）
                **exif_data
            }
            
            logger.info(f"提取完整元数据成功: {image_path.name}, {len(metadata)} 个字段")
            return metadata
            
    except Exception as e:
        logger.error(f"提取完整元数据失败 {image_path}: {e}")
        return {
            'filename': image_path.name if isinstance(image_path, Path) else str(image_path),
            'error': str(e)
        }
