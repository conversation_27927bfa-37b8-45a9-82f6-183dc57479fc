import { useState } from 'react';
import { Button } from '@/components/ui';
import { SimpleTestLayout } from '@/components/layout/SimpleTestLayout';
import { useAppStore } from '@/store';

/**
 * 最小化的 MizzyStarApp - 用于隔离问题
 */
function MinimalMizzyStarApp() {
  console.log('MinimalMizzyStarApp rendering...');
  
  // 本地状态
  const [showDevControls, setShowDevControls] = useState(true);
  
  // 从 AppStore 获取状态
  const {
    isCatalogVisible,
    isInfoPanelVisible,
    isWorkbenchVisible,
    isFullscreenGallery,
    toggleCatalog,
    toggleInfoPanel,
    toggleWorkbench,
    toggleFullscreenGallery,
  } = useAppStore();
  
  console.log('Store state:', {
    isCatalogVisible,
    isInfoPanelVisible,
    isWorkbenchVisible,
    isFullscreenGallery,
  });

  // 布局切换控制
  const togglePanel = (panel: 'catalog' | 'workbench' | 'info' | 'fullscreen') => {
    switch (panel) {
      case 'catalog':
        toggleCatalog();
        break;
      case 'workbench':
        toggleWorkbench();
        break;
      case 'info':
        toggleInfoPanel();
        break;
      case 'fullscreen':
        toggleFullscreenGallery();
        break;
    }
  };

  return (
    <div className="h-screen w-screen bg-[#191012]">
      {/* 开发控制面板 */}
      {!isFullscreenGallery && showDevControls && (
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-50 bg-[#040709] border border-[#2A2A2A] rounded-lg p-2 shadow-lg">
          <div className="flex gap-2">
            <Button
              variant={isCatalogVisible ? "primary" : "outline"}
              size="sm"
              onClick={() => togglePanel('catalog')}
            >
              📚 目录栏
            </Button>
            <Button
              variant={isWorkbenchVisible ? "primary" : "outline"}
              size="sm"
              onClick={() => togglePanel('workbench')}
            >
              🛠️ 工作台
            </Button>
            <Button
              variant={isInfoPanelVisible ? "primary" : "outline"}
              size="sm"
              onClick={() => togglePanel('info')}
            >
              📄 信息栏
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => togglePanel('fullscreen')}
            >
              🔍 全屏
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setShowDevControls(false)}
            >
              ✕ 隐藏
            </Button>
          </div>
        </div>
      )}

      {/* 简单测试布局 */}
      <SimpleTestLayout
        catalogPanel={
          <div className="h-full p-4 text-[#A49F9A]">
            <h3 className="text-lg font-semibold mb-4">📚 目录栏</h3>
            <div className="space-y-2">
              <div className="p-2 bg-[#2A2A2A] rounded">状态: 正常</div>
              <div className="p-2 bg-[#2A2A2A] rounded">可见: {isCatalogVisible ? '是' : '否'}</div>
            </div>
          </div>
        }
        galleryPanel={
          <div className="h-full p-4 text-[#A49F9A]">
            <h3 className="text-lg font-semibold mb-4">🖼️ 画廊面板</h3>
            <div className="space-y-2">
              <div className="p-2 bg-[#2A2A2A] rounded">状态: 正常</div>
              <div className="p-2 bg-[#2A2A2A] rounded">全屏: {isFullscreenGallery ? '是' : '否'}</div>
            </div>
          </div>
        }
        workbenchPanel={
          <div className="h-full p-4 text-[#A49F9A]">
            <h3 className="text-lg font-semibold mb-4">🛠️ 工作台</h3>
            <div className="space-y-2">
              <div className="p-2 bg-[#2A2A2A] rounded">状态: 正常</div>
              <div className="p-2 bg-[#2A2A2A] rounded">可见: {isWorkbenchVisible ? '是' : '否'}</div>
            </div>
          </div>
        }
        infoPanel={
          <div className="h-full p-4 text-[#A49F9A]">
            <h3 className="text-lg font-semibold mb-4">📄 信息栏</h3>
            <div className="space-y-2">
              <div className="p-2 bg-[#2A2A2A] rounded">状态: 正常</div>
              <div className="p-2 bg-[#2A2A2A] rounded">可见: {isInfoPanelVisible ? '是' : '否'}</div>
            </div>
          </div>
        }
        showCatalogPanel={isCatalogVisible}
        showWorkbench={isWorkbenchVisible}
        showInfoPanel={isInfoPanelVisible}
        isFullscreenGallery={isFullscreenGallery}
      />

      {/* 全屏模式退出按钮 */}
      {isFullscreenGallery && (
        <Button
          variant="primary"
          size="sm"
          className="absolute top-4 right-4 z-50"
          onClick={() => toggleFullscreenGallery()}
        >
          ✕ 退出全屏
        </Button>
      )}

      {/* 隐藏的开发控制按钮 */}
      {!showDevControls && !isFullscreenGallery && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-4 right-4 z-50 opacity-50 hover:opacity-100"
          onClick={() => setShowDevControls(true)}
        >
          ⚙️
        </Button>
      )}
    </div>
  );
}

export default MinimalMizzyStarApp;
