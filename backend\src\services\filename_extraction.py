# src/services/filename_extraction.py
"""
文件名标签提取服务
提供从文件名中提取标签的核心业务逻辑
"""
import logging
import uuid
import asyncio
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from pathlib import Path

from ..database import get_independent_db
from ..crud.file_crud import get_file, update_file_custom_tags
from .. import schemas

logger = logging.getLogger(__name__)

class FilenameExtractionService:
    """文件名标签提取服务"""

    def __init__(self):
        self.active_tasks: Dict[str, Dict[str, Any]] = {}

    def generate_task_id(self) -> str:
        """生成任务ID"""
        return f"extract_task_{uuid.uuid4().hex[:8]}"

    def validate_extraction_rules(self, rules: List[schemas.FilenameExtractionRule]) -> List[str]:
        """验证提取规则的有效性"""
        errors = []

        # 检查规则重叠
        for i, rule1 in enumerate(rules):
            if rule1.start >= rule1.end:
                errors.append(f"规则 {rule1.id}: 起始位置必须小于结束位置")
                continue

            for j, rule2 in enumerate(rules[i+1:], i+1):
                # 检查字符范围重叠
                if not (rule1.end <= rule2.start or rule2.end <= rule1.start):
                    errors.append(f"规则 {rule1.id} 和 {rule2.id} 的字符范围重叠")

        # 检查标签名重复
        tag_names = [rule.tag_name for rule in rules]
        if len(tag_names) != len(set(tag_names)):
            errors.append("标签名称不能重复")

        return errors

    def extract_tags_from_filename(
        self,
        filename: str,
        rules: List[schemas.FilenameExtractionRule]
    ) -> Dict[str, str]:
        """从单个文件名中提取标签"""
        logger.info(f"🔍 开始从文件名提取标签: {filename}")
        extracted_tags = {}

        # 移除文件扩展名
        name_without_ext = Path(filename).stem
        logger.info(f"📝 处理文件名: {name_without_ext} (长度: {len(name_without_ext)})")

        for rule in rules:
            try:
                logger.info(f"🔧 应用规则: {rule.tag_name} [{rule.start}:{rule.end}]")
                if rule.end <= len(name_without_ext):
                    tag_value = name_without_ext[rule.start:rule.end].strip()
                    logger.info(f"📤 提取的值: '{tag_value}'")
                    if tag_value:  # 只添加非空标签
                        extracted_tags[rule.tag_name] = tag_value
                        logger.info(f"✅ 添加标签: {rule.tag_name} = {tag_value}")
                    else:
                        logger.warning(f"⚠️  提取的值为空: {rule.tag_name}")
                else:
                    logger.warning(f"❌ 规则 {rule.id} 的结束位置 {rule.end} 超出文件名长度 {len(name_without_ext)}")
            except Exception as e:
                logger.error(f"❌ 提取标签时出错，规则 {rule.id}: {e}")

        logger.info(f"🎯 最终提取的标签: {extracted_tags}")
        return extracted_tags

    def update_file_custom_tags(
        self,
        db: Session,
        file_id: int,
        case_id: int,
        new_tags: Dict[str, str],
        options: schemas.FilenameExtractionOptions
    ) -> bool:
        """更新文件的自定义标签 - 使用专用的CRUD函数"""
        try:
            logger.info(f"🔍 开始更新文件 {file_id} 的标签，使用专用CRUD函数")

            # 使用新的专用CRUD函数
            success = update_file_custom_tags(
                db=db,
                file_id=file_id,
                case_id=case_id,
                new_tags=new_tags,
                overwrite_existing=options.overwrite_existing
            )

            if success:
                logger.info(f"✅ 成功更新文件 {file_id} 的自定义标签: {new_tags}")
            else:
                logger.error(f"❌ 更新文件 {file_id} 的标签失败")

            return success

        except Exception as e:
            logger.error(f"❌ 更新文件 {file_id} 标签时出错: {e}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            return False

    def process_filename_extraction_task(
        self,
        task_id: str,
        case_id: int,
        file_ids: List[int],
        extraction_rules: List[schemas.FilenameExtractionRule],
        options: schemas.FilenameExtractionOptions
    ) -> schemas.FilenameExtractionResult:
        """处理文件名提取任务"""
        logger.info(f"开始处理文件名提取任务 {task_id}")

        # 初始化任务状态
        self.active_tasks[task_id] = {
            "status": "processing",
            "progress": 0,
            "total": len(file_ids),
            "current_file": None
        }

        results = {
            "success_count": 0,
            "error_count": 0,
            "errors": [],
            "generated_tags": list(set(rule.tag_name for rule in extraction_rules))
        }

        # 使用独立的数据库会话（用于后台任务）
        try:
            with get_independent_db() as db:
                logger.info(f"🔗 获得独立数据库会话，开始处理任务 {task_id}")

                for i, file_id in enumerate(file_ids):
                    try:
                        # 更新任务进度
                        self.active_tasks[task_id]["progress"] = i
                        logger.info(f"📊 处理进度: {i+1}/{len(file_ids)} - 文件ID: {file_id}")

                        # 获取文件信息
                        file_record = get_file(db, file_id, case_id)
                        if not file_record:
                            raise Exception(f"文件 {file_id} 不存在")

                        self.active_tasks[task_id]["current_file"] = file_record.file_name
                        logger.info(f"📁 处理文件: {file_record.file_name}")

                        # 提取标签
                        extracted_tags = self.extract_tags_from_filename(
                            file_record.file_name,
                            extraction_rules
                        )
                        logger.info(f"🏷️  提取到的标签: {extracted_tags}")

                        if extracted_tags:
                            # 更新文件标签
                            logger.info(f"💾 开始更新文件 {file_id} 的标签")
                            success = self.update_file_custom_tags(
                                db, file_id, case_id, extracted_tags, options
                            )

                            if success:
                                results["success_count"] += 1
                                logger.info(f"✅ 文件 {file_id} 标签更新成功")
                            else:
                                raise Exception("更新标签失败")
                        else:
                            logger.warning(f"文件 {file_record.file_name} 未提取到任何标签")
                            results["success_count"] += 1  # 仍然算作成功

                    except Exception as e:
                        results["error_count"] += 1
                        error_info = {
                            "file_id": file_id,
                            "error": str(e)
                        }
                        results["errors"].append(error_info)

                        # 详细记录错误信息
                        logger.error(f"❌ 处理文件 {file_id} 时发生错误: {e}")
                        import traceback
                        logger.error(f"错误堆栈: {traceback.format_exc()}")

                        if not options.skip_on_error:
                            logger.error(f"任务 {task_id} 因错误终止: {e}")
                            break

                        logger.warning(f"跳过文件 {file_id} 的错误: {e}")

                    # 添加小延迟避免过度占用资源
                    import time
                    time.sleep(0.01)

                # 更新任务状态为完成
                self.active_tasks[task_id]["status"] = "completed"
                self.active_tasks[task_id]["progress"] = len(file_ids)
                logger.info(f"🎉 任务 {task_id} 执行完成")

        except Exception as e:
            logger.error(f"❌ 任务 {task_id} 执行失败: {e}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            self.active_tasks[task_id]["status"] = "failed"
            results["error_count"] = len(file_ids)
            results["success_count"] = 0

        logger.info(f"任务 {task_id} 完成: 成功 {results['success_count']}, 失败 {results['error_count']}")

        return schemas.FilenameExtractionResult(
            task_id=task_id,
            success_count=results["success_count"],
            error_count=results["error_count"],
            generated_tags=results["generated_tags"],
            errors=results["errors"]
        )

    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        return self.active_tasks.get(task_id)

    async def _cleanup_task_after_delay(self, task_id: str, delay_seconds: int):
        """延迟清理任务状态"""
        await asyncio.sleep(delay_seconds)
        if task_id in self.active_tasks:
            del self.active_tasks[task_id]
            logger.info(f"清理任务状态: {task_id}")

# 全局服务实例
filename_extraction_service = FilenameExtractionService()
