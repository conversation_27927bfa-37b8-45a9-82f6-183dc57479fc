{"version": 3, "file": "packageMetadata.js", "sourceRoot": "", "sources": ["../../src/util/packageMetadata.ts"], "names": [], "mappings": ";;;AAAA,+CAA8E;AAC9E,uCAA6C;AAC7C,6BAA4B;AAC5B,iCAAgC;AAEhC,iEAA6D;AAE7D,gBAAgB;AACT,KAAK,UAAU,eAAe,CAAC,IAAY;IAChD,MAAM,IAAI,GAAG,MAAM,IAAA,mBAAQ,EAAC,IAAI,CAAC,CAAA;IACjC,MAAM,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IACzB,kEAAkE;IAClE,OAAO,IAAI,CAAC,OAAO,CAAA;IACnB,OAAO,IAAI,CAAC,MAAM,CAAA;IAClB,IAAA,2CAAoB,EAAC,IAAI,CAAC,CAAA;IAC1B,OAAO,IAAI,CAAA;AACb,CAAC;AARD,0CAQC;AAED,KAAK,UAAU,OAAO,CAAC,IAAY,EAAE,IAAS;IAC5C,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,EAAE,CAAC;QAC9B,OAAM;IACR,CAAC;IAED,IAAI,UAAU,CAAA;IACd,IAAI,CAAC;QACH,UAAU,GAAG,MAAM,IAAA,mBAAQ,EAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,EAAE,MAAM,CAAC,CAAA;IAClF,CAAC;IAAC,OAAO,OAAO,EAAE,CAAC;QACjB,OAAM;IACR,CAAC;IAED,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;AAC7F,CAAC;AAED,gBAAgB;AAChB,SAAgB,aAAa,CAAC,QAAkB,EAAE,WAAuB,EAAE,cAAsB,EAAE,iBAAyB;IAC1H,MAAM,MAAM,GAAkB,EAAE,CAAA;IAChC,MAAM,WAAW,GAAG,CAAC,eAAuB,EAAE,EAAE;QAC9C,MAAM,CAAC,IAAI,CAAC,mBAAmB,eAAe,0BAA0B,cAAc,GAAG,CAAC,CAAA;IAC5F,CAAC,CAAA;IAED,MAAM,aAAa,GAAG,CAAC,IAAY,EAAE,KAAgC,EAAE,EAAE;QACvE,IAAI,IAAA,8BAAe,EAAC,KAAK,CAAC,EAAE,CAAC;YAC3B,WAAW,CAAC,IAAI,CAAC,CAAA;QACnB,CAAC;IACH,CAAC,CAAA;IAED,IAAK,QAAgB,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;QAC1C,MAAM,CAAC,IAAI,CAAC,wEAAwE,CAAC,CAAA;IACvF,CAAC;IAED,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAA;IAEpC,IAAI,IAAA,8BAAe,EAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;QAC1C,kBAAG,CAAC,IAAI,CAAC,EAAE,cAAc,EAAE,EAAE,2CAA2C,CAAC,CAAA;IAC3E,CAAC;IACD,IAAI,QAAQ,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;QAC5B,kBAAG,CAAC,IAAI,CAAC,EAAE,cAAc,EAAE,EAAE,sCAAsC,CAAC,CAAA;IACtE,CAAC;IACD,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAA;IAE1C,iBAAiB,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC,CAAA;IAChD,IAAI,QAAQ,KAAK,WAAW,EAAE,CAAC;QAC7B,IAAI,QAAQ,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;YAC3B,MAAM,CAAC,IAAI,CACT,4CAA4C,cAAc,gGAAgG,iBAAiB,GAAG,CAC/K,CAAA;QACH,CAAC;IACH,CAAC;IAED,MAAM,eAAe,GAAI,QAAgB,CAAC,eAAe,CAAA;IACzD,IAAI,eAAe,IAAI,IAAI,IAAI,CAAC,kBAAkB,IAAI,eAAe,IAAI,mBAAmB,IAAI,eAAe,CAAC,EAAE,CAAC;QACjH,kBAAG,CAAC,IAAI,CACN,sSAAsS,CACvS,CAAA;IACH,CAAC;IAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACtB,MAAM,IAAI,wCAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;IACxD,CAAC;AACH,CAAC;AA7CD,sCA6CC;AAED,SAAS,gBAAgB,CAAC,OAAsC,EAAE,KAA4B,EAAE,KAAe;IAC7G,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,OAAO,KAAK,CAAA;IACd,CAAC;IAED,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IACtC,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,OAAO,KAAK,CAAA;IACd,CAAC;IAED,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;AAChD,CAAC;AAED,SAAS,iBAAiB,CAAC,YAA0D,EAAE,MAAqB;IAC1G,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;QACzB,OAAM;IACR,CAAC;IAED,MAAM,cAAc,GAAG,YAAY,CAAC,kBAAkB,CAAC,CAAA;IACvD,MAAM,8BAA8B,GAAG,OAAO,CAAA;IAC9C,IAAI,cAAc,IAAI,IAAI,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,KAAK,8BAA8B,EAAE,CAAC,EAAE,CAAC;QACvG,MAAM,CAAC,IAAI,CACT,6BAA6B,8BAA8B,iGAAiG,8BAA8B,GAAG,CAC9L,CAAA;IACH,CAAC;IAED,MAAM,SAAS,GAAG,YAAY,CAAC,mCAAmC,CAAC,CAAA;IACnE,IAAI,SAAS,IAAI,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,CAAC;QACnE,MAAM,CAAC,IAAI,CAAC,gKAAgK,CAAC,CAAA;IAC/K,CAAC;IAED,MAAM,IAAI,GAAG,CAAC,UAAU,EAAE,mBAAmB,EAAE,kBAAkB,CAAC,CAAA;IAClE,IAAI,OAAO,CAAC,GAAG,CAAC,+CAA+C,KAAK,MAAM,EAAE,CAAC;QAC3E,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;IAC/B,CAAC;IACD,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC;QACxB,IAAI,IAAI,IAAI,YAAY,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,YAAY,IAAI,0CAA0C,GAAG,wEAAwE,CAAC,CAAA;QACpJ,CAAC;IACH,CAAC;AACH,CAAC", "sourcesContent": ["import { isEmptyOrSpaces, log, InvalidConfigurationError } from \"builder-util\"\nimport { readFile, readJson } from \"fs-extra\"\nimport * as path from \"path\"\nimport * as semver from \"semver\"\nimport { Metadata } from \"../options/metadata\"\nimport { normalizePackageData } from \"./normalizePackageData\"\n\n/** @internal */\nexport async function readPackageJson(file: string): Promise<any> {\n  const data = await readJson(file)\n  await authors(file, data)\n  // remove not required fields because can be used for remote build\n  delete data.scripts\n  delete data.readme\n  normalizePackageData(data)\n  return data\n}\n\nasync function authors(file: string, data: any) {\n  if (data.contributors != null) {\n    return\n  }\n\n  let authorData\n  try {\n    authorData = await readFile(path.resolve(path.dirname(file), \"AUTHORS\"), \"utf8\")\n  } catch (ignored) {\n    return\n  }\n\n  data.contributors = authorData.split(/\\r?\\n/g).map(it => it.replace(/^\\s*#.*$/, \"\").trim())\n}\n\n/** @internal */\nexport function checkMetadata(metadata: Metadata, devMetadata: any | null, appPackageFile: string, devAppPackageFile: string): void {\n  const errors: Array<string> = []\n  const reportError = (missedFieldName: string) => {\n    errors.push(`Please specify '${missedFieldName}' in the package.json (${appPackageFile})`)\n  }\n\n  const checkNotEmpty = (name: string, value: string | null | undefined) => {\n    if (isEmptyOrSpaces(value)) {\n      reportError(name)\n    }\n  }\n\n  if ((metadata as any).directories != null) {\n    errors.push(`\"directories\" in the root is deprecated, please specify in the \"build\"`)\n  }\n\n  checkNotEmpty(\"name\", metadata.name)\n\n  if (isEmptyOrSpaces(metadata.description)) {\n    log.warn({ appPackageFile }, `description is missed in the package.json`)\n  }\n  if (metadata.author == null) {\n    log.warn({ appPackageFile }, `author is missed in the package.json`)\n  }\n  checkNotEmpty(\"version\", metadata.version)\n\n  checkDependencies(metadata.dependencies, errors)\n  if (metadata !== devMetadata) {\n    if (metadata.build != null) {\n      errors.push(\n        `'build' in the application package.json (${appPackageFile}) is not supported since 3.0 anymore. Please move 'build' into the development package.json (${devAppPackageFile})`\n      )\n    }\n  }\n\n  const devDependencies = (metadata as any).devDependencies\n  if (devDependencies != null && (\"electron-rebuild\" in devDependencies || \"@electron/rebuild\" in devDependencies)) {\n    log.info(\n      '@electron/rebuild not required if you use electron-builder, please consider to remove excess dependency from devDependencies\\n\\nTo ensure your native dependencies are always matched electron version, simply add script `\"postinstall\": \"electron-builder install-app-deps\" to your `package.json`'\n    )\n  }\n\n  if (errors.length > 0) {\n    throw new InvalidConfigurationError(errors.join(\"\\n\"))\n  }\n}\n\nfunction versionSatisfies(version: string | semver.SemVer | null, range: string | semver.Range, loose?: boolean): boolean {\n  if (version == null) {\n    return false\n  }\n\n  const coerced = semver.coerce(version)\n  if (coerced == null) {\n    return false\n  }\n\n  return semver.satisfies(coerced, range, loose)\n}\n\nfunction checkDependencies(dependencies: { [key: string]: string } | null | undefined, errors: Array<string>) {\n  if (dependencies == null) {\n    return\n  }\n\n  const updaterVersion = dependencies[\"electron-updater\"]\n  const requiredElectronUpdaterVersion = \"4.0.0\"\n  if (updaterVersion != null && !versionSatisfies(updaterVersion, `>=${requiredElectronUpdaterVersion}`)) {\n    errors.push(\n      `At least electron-updater ${requiredElectronUpdaterVersion} is recommended by current electron-builder version. Please set electron-updater version to \"^${requiredElectronUpdaterVersion}\"`\n    )\n  }\n\n  const swVersion = dependencies[\"electron-builder-squirrel-windows\"]\n  if (swVersion != null && !versionSatisfies(swVersion, \">=20.32.0\")) {\n    errors.push(`At least electron-builder-squirrel-windows 20.32.0 is required by current electron-builder version. Please set electron-builder-squirrel-windows to \"^20.32.0\"`)\n  }\n\n  const deps = [\"electron\", \"electron-prebuilt\", \"electron-rebuild\"]\n  if (process.env.ALLOW_ELECTRON_BUILDER_AS_PRODUCTION_DEPENDENCY !== \"true\") {\n    deps.push(\"electron-builder\")\n  }\n  for (const name of deps) {\n    if (name in dependencies) {\n      errors.push(`Package \"${name}\" is only allowed in \"devDependencies\". ` + `Please remove it from the \"dependencies\" section in your package.json.`)\n    }\n  }\n}\n"]}