# tests/conftest.py
"""
pytest配置文件，包含测试fixtures和数据库设置
"""
import os
import tempfile
import shutil
from pathlib import Path
from typing import Generator

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from fastapi.testclient import TestClient

from src.database import Base, get_master_db, create_case_database
from src.main import app
from src import models


@pytest.fixture(scope="session")
def test_data_dir() -> Generator[str, None, None]:
    """创建临时测试数据目录"""
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = Path(temp_dir) / "test_data"
        test_dir.mkdir(exist_ok=True)
        yield str(test_dir)


@pytest.fixture(scope="function")
def test_db(test_data_dir: str) -> Generator[Session, None, None]:
    """创建测试用的主数据库会话"""
    import uuid
    # 使用UUID确保每个测试有独立的数据库文件
    # PostgreSQL测试数据库配置
    unique_id = str(uuid.uuid4())[:8]
    test_db_name = f"test_mizzy_star_{unique_id}"

    # 使用PostgreSQL测试数据库
    engine = create_engine(
        f"postgresql://postgres:password@localhost:5432/{test_db_name}",
        pool_pre_ping=True,
        pool_recycle=300
    )
    
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    
    # 创建会话
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.close()
        engine.dispose()  # 确保关闭所有连接
        
        # 等待一小段时间让Windows释放文件锁
        import time
        time.sleep(0.1)
        
        # 清理测试数据库文件
        try:
            if os.path.exists(db_path):
                os.remove(db_path)
        except PermissionError:
            # Windows上可能仍然有文件锁，忽略删除错误
            pass
        
        # 同时清理可能创建的案例数据库文件
        # PostgreSQL模式下不需要清理数据库文件
        pass


@pytest.fixture(scope="function")
def test_case_db(test_data_dir: str) -> Generator[str, None, None]:
    """创建测试用的案例数据库（PostgreSQL模式暂不支持）"""
    # PostgreSQL模式下不创建SQLite数据库
    yield "postgresql_mode"


@pytest.fixture(scope="function")
def test_client(test_db: Session) -> Generator[TestClient, None, None]:
    """创建测试用的FastAPI客户端"""
    
    def override_get_master_db():
        try:
            yield test_db
        finally:
            pass
    
    app.dependency_overrides[get_master_db] = override_get_master_db
    
    try:
        with TestClient(app) as client:
            yield client
    finally:
        app.dependency_overrides.clear()


@pytest.fixture
def sample_case_data():
    """提供示例案例数据"""
    return {
        "case_name": "测试案例",
        "description": "这是一个测试案例的描述"
    }


@pytest.fixture
def sample_file_data():
    """提供示例文件数据"""
    return {
        "file_name": "test_image.jpg",
        "file_path": "/path/to/test_image.jpg",
        "file_type": "image/jpeg",
        "width": 1920,
        "height": 1080
    }


@pytest.fixture(scope="function")
def test_image_file(test_data_dir: str) -> Generator[str, None, None]:
    """创建测试用的图像文件"""
    from PIL import Image
    
    # 创建一个简单的测试图像
    image = Image.new('RGB', (100, 100), color='red')
    image_path = os.path.join(test_data_dir, "test_image.jpg")
    image.save(image_path, "JPEG")
    
    try:
        yield image_path
    finally:
        # 清理测试图像文件
        if os.path.exists(image_path):
            os.remove(image_path)


@pytest.fixture
def mock_uploads_dir(test_data_dir: str) -> Generator[str, None, None]:
    """模拟上传目录"""
    uploads_dir = os.path.join(test_data_dir, "uploads")
    os.makedirs(uploads_dir, exist_ok=True)
    
    # 临时修改上传目录路径
    import src.services
    original_uploads_root = src.services.UPLOADS_ROOT_DIR
    src.services.UPLOADS_ROOT_DIR = Path(uploads_dir)
    
    try:
        yield uploads_dir
    finally:
        # 恢复原始路径
        src.services.UPLOADS_ROOT_DIR = original_uploads_root
        # 清理上传目录
        if os.path.exists(uploads_dir):
            shutil.rmtree(uploads_dir) 