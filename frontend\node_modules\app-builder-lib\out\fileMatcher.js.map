{"version": 3, "file": "fileMatcher.js", "sourceRoot": "", "sources": ["../src/fileMatcher.ts"], "names": [], "mappings": ";;;AAAA,+CAA0C;AAC1C,+CAA2C;AAC3C,4CAAkH;AAClH,0CAAmC;AACnC,yCAAqC;AACrC,6BAA4B;AAG5B,0CAAsD;AAEtD,mEAAmE;AACnE,MAAM,gBAAgB,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,CAAA;AAEtC,uCAAuC;AAC1B,QAAA,aAAa,GACxB,6BAA6B;IAC7B,gFAAgF;IAChF,sDAAsD;IACtD,+FAA+F;IAC/F,qFAAqF,CAAA;AAE1E,QAAA,YAAY,GACvB,8DAA8D;IAC9D,oEAAoE;IACpE,mBAAmB,CAAA;AAErB,SAAS,gBAAgB,CAAC,IAAY;IACpC,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;QACrB,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;IACtC,CAAC;IACD,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,EAAE,CAAC;QACtB,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;IACtC,CAAC;IAED,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IAC3C,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,CAAA;IACb,CAAC;AACH,CAAC;AAED,gBAAgB;AAChB,MAAa,WAAW;IAUtB,YACE,IAAY,EACZ,EAAU,EACD,aAA0C,EACnD,QAAoD;QAD3C,kBAAa,GAAb,aAAa,CAA6B;QAPrD,oBAAe,GAA4B,IAAI,CAAA;QAU7C,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;QACjD,IAAI,CAAC,EAAE,GAAG,gBAAgB,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAA;QAC7C,IAAI,CAAC,QAAQ,GAAG,IAAA,sBAAO,EAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAA;QACtE,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAA;IACjF,CAAC;IAED,gBAAgB,CAAC,OAAe;QAC9B,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7B,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC1C,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAA;IAC9E,CAAC;IAED,UAAU,CAAC,OAAe;QACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAA;IACpD,CAAC;IAED,cAAc,CAAC,OAAe;QAC5B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAA;IACvD,CAAC;IAED,OAAO;QACL,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAA;IACnC,CAAC;IAED,kBAAkB;QAChB,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAA;IACjF,CAAC;IAED,qBAAqB,CAAC,MAAwB,EAAE,OAAgB;QAC9D,MAAM,YAAY,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QAE/E,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;YACvD,qCAAqC;YACrC,MAAM,CAAC,IAAI,CAAC,IAAI,qBAAS,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC,CAAA;YAC1D,OAAM;QACR,CAAC;QAED,KAAK,IAAI,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;gBACzB,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,CAAA;YAC5C,CAAC;YAED,MAAM,aAAa,GAAG,IAAI,qBAAS,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAA;YAC9D,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;YAE1B,8DAA8D;YAC9D,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAA,iBAAQ,EAAC,aAAa,CAAC,EAAE,CAAC;gBACvD,mEAAmE;gBACnE,WAAW;gBACX,MAAM,CAAC,IAAI,CAAC,IAAI,qBAAS,CAAC,GAAG,OAAO,OAAO,EAAE,gBAAgB,CAAC,CAAC,CAAA;YACjE,CAAC;QACH,CAAC;IACH,CAAC;IAED,YAAY;QACV,MAAM,cAAc,GAAqB,EAAE,CAAA;QAC3C,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAA;QAC1C,OAAO,IAAA,qBAAY,EAAC,IAAI,CAAC,IAAI,EAAE,cAAc,EAAE,IAAI,CAAC,eAAe,CAAC,CAAA;IACtE,CAAC;IAED,QAAQ;QACN,OAAO,SAAS,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,EAAE,eAAe,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAA;IACpF,CAAC;CACF;AAhFD,kCAgFC;AAED,gBAAgB;AAChB,SAAgB,mBAAmB,CACjC,MAAc,EACd,WAAmB,EACnB,aAA0C,EAC1C,4BAA0D,EAC1D,gBAAuC,EACvC,MAAc,EACd,iBAA0B;IAE1B,MAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAA;IACtC,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,iBAAiB,CAAC,CAAA;IAEtF,IAAI,QAAQ,GAAG,QAAQ,CAAC,kBAAkB;QACxC,CAAC,CAAC,IAAI;QACN,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE;YACrD,aAAa;YACb,kBAAkB,EAAE,4BAA4B;YAChD,YAAY,EAAE,MAAM;YACpB,UAAU,EAAE,MAAM;SACnB,CAAC,CAAA;IACN,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;QACrB,QAAQ,GAAG,CAAC,IAAI,WAAW,CAAC,MAAM,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC,CAAA;IAClE,CAAC;IAED,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;IAC3B,2DAA2D;IAC3D,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;QAC5B,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,6HAA6H;IAC7H,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;IAEjC,MAAM,mBAAmB,GAAkB,EAAE,CAAA;IAC7C,oKAAoK;IACpK,IAAI,CAAC,OAAO,CAAC,uBAAuB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC;QAC5F,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IAClC,CAAC;SAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;QAC9C,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;IAC/B,CAAC;IAED,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;IAE5C,oEAAoE;IACpE,MAAM,wBAAwB,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAA;IAC9E,IAAI,wBAAwB,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QACvF,mBAAmB,CAAC,IAAI,CAAC,IAAI,wBAAwB,UAAU,CAAC,CAAA;IAClE,CAAC;IAED,MAAM,cAAc,GAAG,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAA;IAC3F,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QACpC,mBAAmB,CAAC,IAAI,CAAC,IAAI,cAAc,UAAU,CAAC,CAAA;IACxD,CAAC;IAED,uFAAuF;IACvF,IAAI,WAAW,GAAG,CAAC,CAAA;IACnB,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QAC9C,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YAClC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAA;YACnB,MAAK;QACP,CAAC;IACH,CAAC;IACD,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,EAAE,GAAG,mBAAmB,CAAC,CAAA;IAEvD,QAAQ,CAAC,IAAI,CAAC,UAAU,oBAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAA;IAC5F,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;IACxB,QAAQ,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAA;IACnE,QAAQ,CAAC,IAAI,CAAC,QAAQ,qBAAa,GAAG,CAAC,CAAA;IAEvC,IAAI,iBAAiB,EAAE,CAAC;QACtB,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;IAClC,CAAC;IACD,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;IAE/B,oEAAoE;IACpE,iEAAiE;IACjE,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;IAC/B,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;IAE7B,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAA;IACxC,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;QAC1B,sDAAsD;QACtD,WAAW,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,SAAS,CAAC,6BAA6B,EAAE,QAAQ,CAAC,CAAA;IACrF,CAAC;IACD,OAAO,QAAQ,CAAA;AACjB,CAAC;AArFD,kDAqFC;AAED,gBAAgB;AAChB,SAAgB,wBAAwB,CACtC,MAAc,EACd,WAAmB,EACnB,aAA0C,EAC1C,4BAA0D,EAC1D,QAAkB;IAElB,yFAAyF;IACzF,qBAAqB;IACrB,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,MAAM,EAAE,WAAW,EAAE,aAAa,CAAC,CAAA;IAEnE,SAAS,WAAW,CAAC,QAAuE;QAC1F,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,OAAM;QACR,CAAC;aAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpC,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7D,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;gBAC5B,OAAM;YACR,CAAC;YACD,qBAAqB;YACrB,OAAM;QACR,CAAC;QAED,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;gBAChC,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC5B,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;gBAC7B,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,OAAO,GAAG,OAAO,CAAA;gBACvB,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,OAAO,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;oBACjD,KAAK,MAAM,CAAC,IAAI,IAAA,sBAAO,EAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;wBACxC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;oBACvB,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IAClC,WAAW,CAAC,4BAA4B,CAAC,KAAK,CAAC,CAAA;IAE/C,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;QACvB,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;IAChC,CAAC;IAED,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAA;IACxC,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;QAC1B,sDAAsD;QACtD,WAAW,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,SAAS,CAAC,yBAAyB,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAA;IACzF,CAAC;IAED,OAAO,OAAO,CAAA;AAChB,CAAC;AArDD,4DAqDC;AAUD,gBAAgB;AAChB,SAAgB,eAAe,CAC7B,MAAqB,EACrB,IAAiF,EACjF,kBAA0B,EAC1B,OAA+B;IAE/B,MAAM,cAAc,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,kBAAkB,EAAE,OAAO,CAAC,aAAa,CAAC,CAAA;IACrG,MAAM,YAAY,GAAuB,EAAE,CAAA;IAE3C,SAAS,WAAW,CAAC,QAAuE;QAC1F,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,OAAM;QACR,CAAC;aAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpC,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACjC,cAAc,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;gBACnC,OAAM;YACR,CAAC;YACD,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAA;QACvB,CAAC;QAED,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;gBAChC,0CAA0C;gBAC1C,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;YACpC,CAAC;iBAAM,IAAI,IAAI,KAAK,YAAY,EAAE,CAAC;gBACjC,MAAM,IAAI,KAAK,CAAC,4CAA4C,IAAI,GAAG,CAAC,CAAA;YACtE,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;gBACvG,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,OAAO,CAAC,EAAE,CAAC,CAAA;gBACjG,YAAY,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,IAAI,EAAE,EAAE,EAAE,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAA;YACrF,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,IAAI,KAAK,gBAAgB,EAAE,CAAC;QAC9B,WAAW,CAAE,MAAc,CAAC,IAAI,CAAC,CAAC,CAAA;IACpC,CAAC;IACD,WAAW,CAAE,OAAO,CAAC,kBAA0B,CAAC,IAAI,CAAC,CAAC,CAAA;IAEtD,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;QAC9B,+CAA+C;QAC/C,YAAY,CAAC,OAAO,CAAC,cAAc,CAAC,CAAA;IACtC,CAAC;IAED,oHAAoH;IACpH,MAAM,cAAc,GAAG,cAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC,CAAA;IAC/G,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QACpC,cAAc,CAAC,UAAU,CAAC,IAAI,cAAc,qBAAqB,CAAC,CAAA;IACpE,CAAC;IAED,OAAO,YAAY,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAA;AACxD,CAAC;AAnDD,0CAmDC;AAED,gBAAgB;AAChB,SAAgB,SAAS,CAAC,QAAmC,EAAE,WAAmC,EAAE,aAAuB;IACzH,IAAI,QAAQ,IAAI,IAAI,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC9C,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC1B,CAAC;IAED,OAAO,sBAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAoB,EAAE,EAAE;QAClE,MAAM,QAAQ,GAAG,MAAM,IAAA,eAAU,EAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QAC/C,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,2BAA2B,CAAC,CAAA;YAC7D,OAAM;QACR,CAAC;QAED,IAAI,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;YACtB,MAAM,MAAM,GAAG,MAAM,IAAA,eAAU,EAAC,OAAO,CAAC,EAAE,CAAC,CAAA;YAC3C,oEAAoE;YACpE,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;gBAC3C,OAAO,MAAM,IAAA,mBAAc,EAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAA;YACxH,CAAC;YAED,MAAM,IAAA,gBAAK,EAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;YAC1D,OAAO,MAAM,IAAA,mBAAc,EAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAA;QACjE,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,EAAE,IAAI,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;YACtD,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAChC,CAAC;QACD,kBAAG,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,EAAE,6BAA6B,CAAC,CAAA;QACrD,OAAO,MAAM,IAAA,YAAO,EAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,YAAY,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC,mBAAc,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;IACvJ,CAAC,CAAC,CAAA;AACJ,CAAC;AA7BD,8BA6BC", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { asArray, log } from \"builder-util\"\nimport { copyDir, copyOrLinkFile, Filter, statOrNull, FileTransformer, USE_HARD_LINKS } from \"builder-util/out/fs\"\nimport { mkdir } from \"fs/promises\"\nimport { Minimatch } from \"minimatch\"\nimport * as path from \"path\"\nimport { Configuration, FileSet, Packager, PlatformSpecificBuildOptions } from \"./index\"\nimport { PlatformPackager } from \"./platformPackager\"\nimport { createFilter, hasMagic } from \"./util/filter\"\n\n// https://github.com/electron-userland/electron-builder/issues/733\nconst minimatchOptions = { dot: true }\n\n// noinspection SpellCheckingInspection\nexport const excludedNames =\n  \".git,.hg,.svn,CVS,RCS,SCCS,\" +\n  \"__pycache__,.DS_Store,thumbs.db,.gitignore,.gitkeep,.gitattributes,.npmignore,\" +\n  \".idea,.vs,.flowconfig,.jshintrc,.eslintrc,.circleci,\" +\n  \".yarn-integrity,.yarn-metadata.json,yarn-error.log,yarn.lock,package-lock.json,npm-debug.log,\" +\n  \"appveyor.yml,.travis.yml,circle.yml,.nyc_output,.husky,.github,electron-builder.env\"\n\nexport const excludedExts =\n  \"iml,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,suo,xproj,cc,d.ts,\" +\n  // https://github.com/electron-userland/electron-builder/issues/7512\n  \"mk,a,o,forge-meta\"\n\nfunction ensureNoEndSlash(file: string): string {\n  if (path.sep !== \"/\") {\n    file = file.replace(/\\//g, path.sep)\n  }\n  if (path.sep !== \"\\\\\") {\n    file = file.replace(/\\\\/g, path.sep)\n  }\n\n  if (file.endsWith(path.sep)) {\n    return file.substring(0, file.length - 1)\n  } else {\n    return file\n  }\n}\n\n/** @internal */\nexport class FileMatcher {\n  readonly from: string\n  readonly to: string\n\n  readonly patterns: Array<string>\n\n  excludePatterns: Array<Minimatch> | null = null\n\n  readonly isSpecifiedAsEmptyArray: boolean\n\n  constructor(\n    from: string,\n    to: string,\n    readonly macroExpander: (pattern: string) => string,\n    patterns?: Array<string> | string | null | undefined\n  ) {\n    this.from = ensureNoEndSlash(macroExpander(from))\n    this.to = ensureNoEndSlash(macroExpander(to))\n    this.patterns = asArray(patterns).map(it => this.normalizePattern(it))\n    this.isSpecifiedAsEmptyArray = Array.isArray(patterns) && patterns.length === 0\n  }\n\n  normalizePattern(pattern: string) {\n    if (pattern.startsWith(\"./\")) {\n      pattern = pattern.substring(\"./\".length)\n    }\n    return path.posix.normalize(this.macroExpander(pattern.replace(/\\\\/g, \"/\")))\n  }\n\n  addPattern(pattern: string) {\n    this.patterns.push(this.normalizePattern(pattern))\n  }\n\n  prependPattern(pattern: string) {\n    this.patterns.unshift(this.normalizePattern(pattern))\n  }\n\n  isEmpty() {\n    return this.patterns.length === 0\n  }\n\n  containsOnlyIgnore(): boolean {\n    return !this.isEmpty() && this.patterns.find(it => !it.startsWith(\"!\")) == null\n  }\n\n  computeParsedPatterns(result: Array<Minimatch>, fromDir?: string): void {\n    const relativeFrom = fromDir == null ? null : path.relative(fromDir, this.from)\n\n    if (this.patterns.length === 0 && relativeFrom != null) {\n      // file mappings, from here is a file\n      result.push(new Minimatch(relativeFrom, minimatchOptions))\n      return\n    }\n\n    for (let pattern of this.patterns) {\n      if (relativeFrom != null) {\n        pattern = path.join(relativeFrom, pattern)\n      }\n\n      const parsedPattern = new Minimatch(pattern, minimatchOptions)\n      result.push(parsedPattern)\n\n      // do not add if contains dot (possibly file if has extension)\n      if (!pattern.includes(\".\") && !hasMagic(parsedPattern)) {\n        // https://github.com/electron-userland/electron-builder/issues/545\n        // add **/*\n        result.push(new Minimatch(`${pattern}/**/*`, minimatchOptions))\n      }\n    }\n  }\n\n  createFilter(): Filter {\n    const parsedPatterns: Array<Minimatch> = []\n    this.computeParsedPatterns(parsedPatterns)\n    return createFilter(this.from, parsedPatterns, this.excludePatterns)\n  }\n\n  toString() {\n    return `from: ${this.from}, to: ${this.to}, patterns: ${this.patterns.join(\", \")}`\n  }\n}\n\n/** @internal */\nexport function getMainFileMatchers(\n  appDir: string,\n  destination: string,\n  macroExpander: (pattern: string) => string,\n  platformSpecificBuildOptions: PlatformSpecificBuildOptions,\n  platformPackager: PlatformPackager<any>,\n  outDir: string,\n  isElectronCompile: boolean\n): Array<FileMatcher> {\n  const packager = platformPackager.info\n  const buildResourceDir = path.resolve(packager.projectDir, packager.buildResourcesDir)\n\n  let matchers = packager.isPrepackedAppAsar\n    ? null\n    : getFileMatchers(packager.config, \"files\", destination, {\n        macroExpander,\n        customBuildOptions: platformSpecificBuildOptions,\n        globalOutDir: outDir,\n        defaultSrc: appDir,\n      })\n  if (matchers == null) {\n    matchers = [new FileMatcher(appDir, destination, macroExpander)]\n  }\n\n  const matcher = matchers[0]\n  // add default patterns, but only if from equals to app dir\n  if (matcher.from !== appDir) {\n    return matchers\n  }\n\n  // https://github.com/electron-userland/electron-builder/issues/1741#issuecomment-311111418 so, do not use inclusive patterns\n  const patterns = matcher.patterns\n\n  const customFirstPatterns: Array<string> = []\n  // electron-webpack - we need to copy only package.json and node_modules from root dir (and these files are added by default), so, explicit empty array is specified\n  if (!matcher.isSpecifiedAsEmptyArray && (matcher.isEmpty() || matcher.containsOnlyIgnore())) {\n    customFirstPatterns.push(\"**/*\")\n  } else if (!patterns.includes(\"package.json\")) {\n    patterns.push(\"package.json\")\n  }\n\n  customFirstPatterns.push(\"!**/node_modules\")\n\n  // https://github.com/electron-userland/electron-builder/issues/1482\n  const relativeBuildResourceDir = path.relative(matcher.from, buildResourceDir)\n  if (relativeBuildResourceDir.length !== 0 && !relativeBuildResourceDir.startsWith(\".\")) {\n    customFirstPatterns.push(`!${relativeBuildResourceDir}{,/**/*}`)\n  }\n\n  const relativeOutDir = matcher.normalizePattern(path.relative(packager.projectDir, outDir))\n  if (!relativeOutDir.startsWith(\".\")) {\n    customFirstPatterns.push(`!${relativeOutDir}{,/**/*}`)\n  }\n\n  // add our default exclusions after last user possibly defined \"all\"/permissive pattern\n  let insertIndex = 0\n  for (let i = patterns.length - 1; i >= 0; i--) {\n    if (patterns[i].startsWith(\"**/\")) {\n      insertIndex = i + 1\n      break\n    }\n  }\n  patterns.splice(insertIndex, 0, ...customFirstPatterns)\n\n  patterns.push(`!**/*.{${excludedExts}${packager.config.includePdb === true ? \"\" : \",pdb\"}}`)\n  patterns.push(\"!**/._*\")\n  patterns.push(\"!**/electron-builder.{yaml,yml,json,json5,toml,ts}\")\n  patterns.push(`!**/{${excludedNames}}`)\n\n  if (isElectronCompile) {\n    patterns.push(\"!.cache{,/**/*}\")\n  }\n  patterns.push(\"!.yarn{,/**/*}\")\n\n  // https://github.com/electron-userland/electron-builder/issues/1969\n  // exclude ony for app root, use .yarnclean to clean node_modules\n  patterns.push(\"!.editorconfig\")\n  patterns.push(\"!.yarnrc.yml\")\n\n  const debugLogger = packager.debugLogger\n  if (debugLogger.isEnabled) {\n    //tslint:disable-next-line:no-invalid-template-strings\n    debugLogger.add(`${macroExpander(\"${arch}\")}.firstOrDefaultFilePatterns`, patterns)\n  }\n  return matchers\n}\n\n/** @internal */\nexport function getNodeModuleFileMatcher(\n  appDir: string,\n  destination: string,\n  macroExpander: (pattern: string) => string,\n  platformSpecificBuildOptions: PlatformSpecificBuildOptions,\n  packager: Packager\n): FileMatcher {\n  // https://github.com/electron-userland/electron-builder/pull/2948#issuecomment-392241632\n  // grab only excludes\n  const matcher = new FileMatcher(appDir, destination, macroExpander)\n\n  function addPatterns(patterns: Array<string | FileSet> | string | null | undefined | FileSet) {\n    if (patterns == null) {\n      return\n    } else if (!Array.isArray(patterns)) {\n      if (typeof patterns === \"string\" && patterns.startsWith(\"!\")) {\n        matcher.addPattern(patterns)\n        return\n      }\n      // ignore object form\n      return\n    }\n\n    for (const pattern of patterns) {\n      if (typeof pattern === \"string\") {\n        if (pattern.startsWith(\"!\")) {\n          matcher.addPattern(pattern)\n        }\n      } else {\n        const fileSet = pattern\n        if (fileSet.from == null || fileSet.from === \".\") {\n          for (const p of asArray(fileSet.filter)) {\n            matcher.addPattern(p)\n          }\n        }\n      }\n    }\n  }\n\n  addPatterns(packager.config.files)\n  addPatterns(platformSpecificBuildOptions.files)\n\n  if (!matcher.isEmpty()) {\n    matcher.prependPattern(\"**/*\")\n  }\n\n  const debugLogger = packager.debugLogger\n  if (debugLogger.isEnabled) {\n    //tslint:disable-next-line:no-invalid-template-strings\n    debugLogger.add(`${macroExpander(\"${arch}\")}.nodeModuleFilePatterns`, matcher.patterns)\n  }\n\n  return matcher\n}\n\nexport interface GetFileMatchersOptions {\n  readonly macroExpander: (pattern: string) => string\n  readonly customBuildOptions: PlatformSpecificBuildOptions\n  readonly globalOutDir: string\n\n  readonly defaultSrc: string\n}\n\n/** @internal */\nexport function getFileMatchers(\n  config: Configuration,\n  name: \"files\" | \"extraFiles\" | \"extraResources\" | \"asarUnpack\" | \"extraDistFiles\",\n  defaultDestination: string,\n  options: GetFileMatchersOptions\n): Array<FileMatcher> | null {\n  const defaultMatcher = new FileMatcher(options.defaultSrc, defaultDestination, options.macroExpander)\n  const fileMatchers: Array<FileMatcher> = []\n\n  function addPatterns(patterns: Array<string | FileSet> | string | null | undefined | FileSet) {\n    if (patterns == null) {\n      return\n    } else if (!Array.isArray(patterns)) {\n      if (typeof patterns === \"string\") {\n        defaultMatcher.addPattern(patterns)\n        return\n      }\n      patterns = [patterns]\n    }\n\n    for (const pattern of patterns) {\n      if (typeof pattern === \"string\") {\n        // use normalize to transform ./foo to foo\n        defaultMatcher.addPattern(pattern)\n      } else if (name === \"asarUnpack\") {\n        throw new Error(`Advanced file copying not supported for \"${name}\"`)\n      } else {\n        const from = pattern.from == null ? options.defaultSrc : path.resolve(options.defaultSrc, pattern.from)\n        const to = pattern.to == null ? defaultDestination : path.resolve(defaultDestination, pattern.to)\n        fileMatchers.push(new FileMatcher(from, to, options.macroExpander, pattern.filter))\n      }\n    }\n  }\n\n  if (name !== \"extraDistFiles\") {\n    addPatterns((config as any)[name])\n  }\n  addPatterns((options.customBuildOptions as any)[name])\n\n  if (!defaultMatcher.isEmpty()) {\n    // default matcher should be first in the array\n    fileMatchers.unshift(defaultMatcher)\n  }\n\n  // we cannot exclude the whole out dir, because sometimes users want to use some file in the out dir in the patterns\n  const relativeOutDir = defaultMatcher.normalizePattern(path.relative(options.defaultSrc, options.globalOutDir))\n  if (!relativeOutDir.startsWith(\".\")) {\n    defaultMatcher.addPattern(`!${relativeOutDir}/*-unpacked{,/**/*}`)\n  }\n\n  return fileMatchers.length === 0 ? null : fileMatchers\n}\n\n/** @internal */\nexport function copyFiles(matchers: Array<FileMatcher> | null, transformer: FileTransformer | null, isUseHardLink?: boolean): Promise<any> {\n  if (matchers == null || matchers.length === 0) {\n    return Promise.resolve()\n  }\n\n  return BluebirdPromise.map(matchers, async (matcher: FileMatcher) => {\n    const fromStat = await statOrNull(matcher.from)\n    if (fromStat == null) {\n      log.warn({ from: matcher.from }, `file source doesn't exist`)\n      return\n    }\n\n    if (fromStat.isFile()) {\n      const toStat = await statOrNull(matcher.to)\n      // https://github.com/electron-userland/electron-builder/issues/1245\n      if (toStat != null && toStat.isDirectory()) {\n        return await copyOrLinkFile(matcher.from, path.join(matcher.to, path.basename(matcher.from)), fromStat, isUseHardLink)\n      }\n\n      await mkdir(path.dirname(matcher.to), { recursive: true })\n      return await copyOrLinkFile(matcher.from, matcher.to, fromStat)\n    }\n\n    if (matcher.isEmpty() || matcher.containsOnlyIgnore()) {\n      matcher.prependPattern(\"**/*\")\n    }\n    log.debug({ matcher }, \"copying files using pattern\")\n    return await copyDir(matcher.from, matcher.to, { filter: matcher.createFilter(), transformer, isUseHardLink: isUseHardLink ? USE_HARD_LINKS : null })\n  })\n}\n"]}