# src/business/file_business.py
"""
文件相关的业务逻辑
"""
import os
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session

from .. import models, schemas
from ..crud import create_file_for_case, get_files_for_case


def upload_file_to_case(db: Session, case_id: int, file_data: schemas.FileCreate) -> Optional[models.File]:
    """
    上传文件到指定案例并进行业务验证
    """
    # 业务验证：检查文件是否已存在
    existing_files = get_files_for_case(db, case_id)
    for existing_file in existing_files:
        if existing_file.file_name == file_data.file_name:  # type: ignore
            raise ValueError(f"文件名 '{file_data.file_name}' 已存在于案例中")
    
    # 业务验证：检查文件路径是否存在
    if file_data.file_path and not os.path.exists(file_data.file_path):
        raise ValueError(f"文件路径 '{file_data.file_path}' 不存在")
    
    # 创建文件记录
    return create_file_for_case(db, case_id, file_data)


def batch_upload_files(db: Session, case_id: int, files_data: List[schemas.FileCreate]) -> Dict[str, Any]:
    """
    批量上传文件到指定案例
    """
    results = {
        "success": [],
        "failed": [],
        "total": len(files_data)
    }
    
    for file_data in files_data:
        try:
            file_record = upload_file_to_case(db, case_id, file_data)
            if file_record:
                results["success"].append(file_record.id)
            else:
                results["failed"].append({"file_name": file_data.file_name, "reason": "创建失败"})
        except Exception as e:
            results["failed"].append({"file_name": file_data.file_name, "reason": str(e)})
    
    return results


def generate_file_thumbnail(file_path: str, thumbnail_path: str) -> bool:
    """
    生成文件缩略图（占位函数）
    """
    # 这里应该实现实际的缩略图生成逻辑
    # 例如：使用PIL或其他图像处理库
    try:
        # 占位实现
        print(f"生成缩略图: {file_path} -> {thumbnail_path}")
        return True
    except Exception as e:
        print(f"生成缩略图失败: {e}")
        return False 