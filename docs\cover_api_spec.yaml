openapi: 3.0.3
info:
  title: 封面图展示功能 API
  description: |
    为案例管理系统提供封面图展示功能的完整API规范。
    
    ## 功能特性
    - 自动选择最高质量图片作为封面
    - 支持手动指定封面图片
    - 优先级：手动选择 > 自动选择 > 默认占位图
    - 文件删除时自动重选封面
    
    ## 封面类型说明
    - `manual`: 用户手动选择的封面
    - `automatic`: 系统自动选择的封面
    - `placeholder`: 默认占位图
    
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: http://localhost:8000/api/v1
    description: 开发环境

paths:
  # 案例相关API扩展
  /cases:
    get:
      summary: 获取案例列表（包含封面信息）
      tags: [Cases]
      responses:
        '200':
          description: 成功获取案例列表
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CaseWithCover'

  /cases/{caseId}:
    get:
      summary: 获取案例详情（包含封面信息）
      tags: [Cases]
      parameters:
        - name: caseId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功获取案例详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CaseDetailWithCover'
        '404':
          description: 案例不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # 封面管理专用API
  /cases/{caseId}/cover:
    put:
      summary: 手动设置案例封面
      description: |
        用户手动指定某个图片文件作为案例封面。
        - 只能选择属于该案例的图片文件
        - 设置成功后封面类型变为 `manual`
        - 会复制原文件并重命名为 `ChooseFront.jpg`
      tags: [Cover Management]
      parameters:
        - name: caseId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetCoverRequest'
      responses:
        '200':
          description: 封面设置成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CaseWithCover'
        '404':
          description: 案例或文件不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

    delete:
      summary: 移除手动封面设置
      description: |
        移除用户的手动封面选择，系统将：
        1. 删除 `ChooseFront.jpg` 文件
        2. 尝试自动选择最佳封面
        3. 如果无法自动选择，则使用占位图
      tags: [Cover Management]
      parameters:
        - name: caseId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功移除手动封面
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RemoveCoverResponse'
        '404':
          description: 案例不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /cases/{caseId}/cover/reselect:
    post:
      summary: 重新选择封面
      description: |
        手动触发封面重选逻辑，用于：
        - 文件被外部删除后的恢复
        - 质量分析更新后的重选
        - 系统维护和故障恢复
      tags: [Cover Management]
      parameters:
        - name: caseId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 重选完成
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CoverReselectionResult'
        '404':
          description: 案例不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # 扩展现有API以包含封面副作用
  /cases/{caseId}/files/{fileId}:
    delete:
      summary: 删除文件（包含封面重选逻辑）
      description: |
        删除指定文件，如果删除的是当前封面的源文件，将自动触发封面重选。
      tags: [Files]
      parameters:
        - name: caseId
          in: path
          required: true
          schema:
            type: integer
        - name: fileId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 文件删除成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileDeleteResponse'
        '404':
          description: 案例或文件不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /quality/{caseId}/analyze:
    post:
      summary: 质量分析（包含封面自动选择）
      description: |
        执行图像质量分析，分析完成后自动选择最高质量分数的图片作为封面。
        只有在当前没有手动设置封面的情况下才会自动选择。
      tags: [Quality Analysis]
      parameters:
        - name: caseId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QualityAnalysisRequest'
      responses:
        '200':
          description: 分析完成
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QualityAnalysisWithCoverResponse'

components:
  schemas:
    # 基础数据模型
    CoverInfo:
      type: object
      properties:
        coverImageUrl:
          type: string
          description: 封面图片的访问URL
          example: "/static/covers/case_123/Front.jpg"
        coverType:
          type: string
          enum: [manual, automatic, placeholder]
          description: 封面类型
          example: "automatic"
        coverSourceFileId:
          type: integer
          nullable: true
          description: 封面源文件ID（如果是从文件生成）
          example: 456
        coverNeedsAttention:
          type: boolean
          description: 是否需要用户关注（如自动选择失败）
          example: false
        coverUpdatedAt:
          type: string
          format: date-time
          nullable: true
          description: 封面最后更新时间
          example: "2024-01-15T10:30:00Z"

    CaseWithCover:
      allOf:
        - $ref: '#/components/schemas/Case'
        - $ref: '#/components/schemas/CoverInfo'

    CaseDetailWithCover:
      allOf:
        - $ref: '#/components/schemas/CaseDetail'
        - $ref: '#/components/schemas/CoverInfo'
        - type: object
          properties:
            files:
              type: array
              items:
                allOf:
                  - $ref: '#/components/schemas/File'
                  - type: object
                    properties:
                      isCurrentCoverSource:
                        type: boolean
                        description: 是否是当前封面的源文件
                        example: true

    # 请求模型
    SetCoverRequest:
      type: object
      required:
        - fileId
      properties:
        fileId:
          type: integer
          description: 要设置为封面的文件ID
          example: 456

    QualityAnalysisRequest:
      type: object
      properties:
        fileIds:
          type: array
          items:
            type: integer
          description: 指定分析的文件ID列表（可选）
        analyzeSimilarity:
          type: boolean
          default: true
          description: 是否分析相似度
        exportExcel:
          type: boolean
          default: false
          description: 是否导出Excel报告

    # 响应模型
    RemoveCoverResponse:
      type: object
      properties:
        case:
          $ref: '#/components/schemas/CaseWithCover'
        revertedTo:
          type: string
          enum: [automatic, placeholder]
          description: 回退到的封面类型
          example: "automatic"

    CoverReselectionResult:
      type: object
      properties:
        coverReselected:
          type: boolean
          description: 是否成功重选了封面
          example: true
        reselectionStatus:
          type: string
          enum: [NOT_NEEDED, SUCCESS, FAILED_NO_IMAGES, FAILED_ERROR]
          description: 重选状态
          example: "SUCCESS"
        requiresManualSelection:
          type: boolean
          description: 是否需要用户手动选择
          example: false
        newCoverUrl:
          type: string
          nullable: true
          description: 新的封面URL
          example: "/static/covers/case_123/Front.jpg"

    FileDeleteResponse:
      type: object
      properties:
        success:
          type: boolean
          description: 删除是否成功
          example: true
        message:
          type: string
          description: 操作消息
          example: "文件删除成功"
        coverReselected:
          type: boolean
          description: 是否触发了封面重选
          example: true
        reselectionStatus:
          type: string
          enum: [NOT_NEEDED, SUCCESS, FAILED_NO_IMAGES, FAILED_ERROR]
          description: 封面重选状态
          example: "SUCCESS"
        requiresManualSelection:
          type: boolean
          description: 是否需要用户手动选择封面
          example: false
        newCoverUrl:
          type: string
          nullable: true
          description: 新的封面URL
          example: "/static/covers/case_123/Front.jpg"

    QualityAnalysisWithCoverResponse:
      allOf:
        - $ref: '#/components/schemas/QualityAnalysisResponse'
        - type: object
          properties:
            coverUpdated:
              type: boolean
              description: 是否更新了封面
              example: true
            newCoverUrl:
              type: string
              nullable: true
              description: 新的封面URL
              example: "/static/covers/case_123/Front.jpg"

    # 错误响应模型
    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          description: 错误代码
          example: "CASE_NOT_FOUND"
        message:
          type: string
          description: 错误消息
          example: "指定的案例不存在"
        details:
          type: object
          description: 错误详细信息
          additionalProperties: true

    ValidationErrorResponse:
      type: object
      properties:
        error:
          type: string
          description: 错误代码
          example: "VALIDATION_ERROR"
        message:
          type: string
          description: 错误消息
          example: "请求参数验证失败"
        details:
          type: object
          properties:
            field:
              type: string
              description: 出错的字段名
              example: "fileId"
            reason:
              type: string
              description: 错误原因
              example: "文件不属于指定案例"

    # 引用现有模型（假设已存在）
    Case:
      type: object
      properties:
        id:
          type: integer
        caseName:
          type: string
        description:
          type: string
        createdAt:
          type: string
          format: date-time
        status:
          type: string

    CaseDetail:
      allOf:
        - $ref: '#/components/schemas/Case'
        - type: object
          properties:
            dbPath:
              type: string

    File:
      type: object
      properties:
        id:
          type: integer
        fileName:
          type: string
        fileType:
          type: string
        filePath:
          type: string
        thumbnailSmallPath:
          type: string
        width:
          type: integer
        height:
          type: integer
        qualityScore:
          type: number
          format: float

    QualityAnalysisResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
        totalFiles:
          type: integer
        clustersCount:
          type: integer
        reportPath:
          type: string
