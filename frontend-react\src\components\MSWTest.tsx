// Project Novak - MSW Test Component
// 验证 Mock Service Worker 是否正常工作

import React from 'react';
import { useCases, useFiles } from '@/hooks';
import { Button } from '@/components/ui';

export const MSWTest: React.FC = () => {
  const { data: cases, isLoading: casesLoading, error: casesError } = useCases();
  const { data: filesData, isLoading: filesLoading, error: filesError } = useFiles();

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">MSW 数据验证</h1>
      
      {/* Cases 测试 */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">📚 Cases API 测试</h2>
        {casesLoading && <p className="text-blue-600">加载案例中...</p>}
        {casesError && <p className="text-red-600">错误: {casesError.message}</p>}
        {cases && (
          <div className="space-y-2">
            <p className="text-green-600">✅ 成功获取 {cases.length} 个案例</p>
            {cases.map(case_ => (
              <div key={case_.id} className="p-3 border rounded">
                <h3 className="font-medium">{case_.case_name}</h3>
                <p className="text-sm text-gray-600">{case_.description}</p>
                <p className="text-xs text-gray-500">ID: {case_.id}</p>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Files 测试 */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">📁 Files API 测试</h2>
        {filesLoading && <p className="text-blue-600">加载文件中...</p>}
        {filesError && <p className="text-red-600">错误: {filesError.message}</p>}
        {filesData && (
          <div className="space-y-2">
            <p className="text-green-600">✅ 成功获取 {filesData.files.length} 个文件</p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filesData.files.slice(0, 4).map(file => (
                <div key={file.id} className="p-3 border rounded">
                  <h3 className="font-medium">{file.file_name}</h3>
                  <p className="text-sm text-gray-600">类型: {file.file_type}</p>
                  <p className="text-sm text-gray-600">大小: {Math.round(file.file_size / 1024)} KB</p>
                  <p className="text-xs text-gray-500">ID: {file.id}</p>
                  {file.thumbnail_small_path && (
                    <img 
                      src={file.thumbnail_small_path} 
                      alt={file.file_name}
                      className="w-16 h-16 object-cover rounded mt-2"
                    />
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 手动测试按钮 */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">🧪 手动测试</h2>
        <div className="space-x-4">
          <Button 
            onClick={async () => {
              try {
                const response = await fetch('/api/cases');
                const data = await response.json();
                console.log('Manual API test - Cases:', data);
                alert(`手动测试成功！获取到 ${data.data.length} 个案例`);
              } catch (error) {
                console.error('Manual API test failed:', error);
                alert('手动测试失败：' + error);
              }
            }}
          >
            测试 Cases API
          </Button>
          
          <Button 
            onClick={async () => {
              try {
                const response = await fetch('/api/files');
                const data = await response.json();
                console.log('Manual API test - Files:', data);
                alert(`手动测试成功！获取到 ${data.data.length} 个文件`);
              } catch (error) {
                console.error('Manual API test failed:', error);
                alert('手动测试失败：' + error);
              }
            }}
          >
            测试 Files API
          </Button>
        </div>
      </div>

      {/* MSW 状态 */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">🔧 MSW 状态</h2>
        <div className="p-4 bg-gray-100 rounded">
          <p className="text-sm">
            如果你看到上面的数据，说明 MSW 正在正常工作！
          </p>
          <p className="text-sm mt-2">
            检查浏览器控制台，应该能看到 MSW 的启动信息和 API 拦截日志。
          </p>
        </div>
      </div>
    </div>
  );
};
