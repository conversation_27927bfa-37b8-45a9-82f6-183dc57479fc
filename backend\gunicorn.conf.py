#!/usr/bin/env python3
"""
Gunicorn配置文件
生产环境异步API服务器配置
"""

import os
import multiprocessing
from production_config import get_production_config

# 获取生产环境配置
config = get_production_config()
gunicorn_config = config.get_gunicorn_config()

# 基本配置
bind = gunicorn_config["bind"]
workers = gunicorn_config["workers"]
worker_class = gunicorn_config["worker_class"]
worker_connections = gunicorn_config["worker_connections"]

# 性能配置
max_requests = gunicorn_config["max_requests"]
max_requests_jitter = gunicorn_config["max_requests_jitter"]
timeout = gunicorn_config["timeout"]
keepalive = gunicorn_config["keepalive"]
preload_app = gunicorn_config["preload_app"]

# 日志配置
loglevel = gunicorn_config["loglevel"]
accesslog = gunicorn_config["accesslog"]
errorlog = gunicorn_config["errorlog"]
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程配置
daemon = False
pidfile = "/tmp/gunicorn.pid"
user = None
group = None
tmp_upload_dir = None

# 安全配置
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# SSL配置 (如果需要)
keyfile = None
certfile = None
ssl_version = 2
cert_reqs = 0
ca_certs = None
suppress_ragged_eofs = True

# 服务器钩子
def on_starting(server):
    """服务器启动时调用"""
    server.log.info("🚀 异步API服务器启动中...")
    server.log.info(f"📊 配置: {workers}个worker, {worker_connections}个连接/worker")

def on_reload(server):
    """服务器重载时调用"""
    server.log.info("🔄 异步API服务器重载中...")

def worker_int(worker):
    """Worker进程中断时调用"""
    worker.log.info(f"⚠️ Worker {worker.pid} 收到中断信号")

def pre_fork(server, worker):
    """Fork worker前调用"""
    server.log.info(f"🔧 准备启动worker {worker.age}")

def post_fork(server, worker):
    """Fork worker后调用"""
    server.log.info(f"✅ Worker {worker.pid} 启动完成")

def post_worker_init(worker):
    """Worker初始化后调用"""
    worker.log.info(f"🎯 Worker {worker.pid} 初始化完成")

def worker_abort(worker):
    """Worker异常终止时调用"""
    worker.log.error(f"❌ Worker {worker.pid} 异常终止")

def pre_exec(server):
    """执行前调用"""
    server.log.info("🔄 准备重新执行服务器")

def when_ready(server):
    """服务器准备就绪时调用"""
    server.log.info("✅ 异步API服务器准备就绪")
    server.log.info(f"🌐 监听地址: {bind}")
    server.log.info(f"👥 Worker数量: {workers}")
    server.log.info(f"🔗 每个Worker连接数: {worker_connections}")

def on_exit(server):
    """服务器退出时调用"""
    server.log.info("👋 异步API服务器正在关闭...")

# 环境变量配置覆盖
if os.getenv('GUNICORN_WORKERS'):
    workers = int(os.getenv('GUNICORN_WORKERS'))

if os.getenv('GUNICORN_WORKER_CONNECTIONS'):
    worker_connections = int(os.getenv('GUNICORN_WORKER_CONNECTIONS'))

if os.getenv('GUNICORN_MAX_REQUESTS'):
    max_requests = int(os.getenv('GUNICORN_MAX_REQUESTS'))

if os.getenv('GUNICORN_TIMEOUT'):
    timeout = int(os.getenv('GUNICORN_TIMEOUT'))

if os.getenv('GUNICORN_BIND'):
    bind = os.getenv('GUNICORN_BIND')

if os.getenv('GUNICORN_LOG_LEVEL'):
    loglevel = os.getenv('GUNICORN_LOG_LEVEL')

# 动态worker数量计算
if os.getenv('AUTO_WORKERS', 'false').lower() == 'true':
    workers = (multiprocessing.cpu_count() * 2) + 1
    print(f"🔧 自动计算worker数量: {workers} (基于CPU核心数: {multiprocessing.cpu_count()})")

# 开发环境特殊配置
if os.getenv('DEPLOYMENT_ENV', 'production').lower() == 'development':
    workers = 1
    loglevel = 'debug'
    reload = True
    print("🔧 开发环境配置已应用")

# 配置验证
if workers < 1:
    workers = 1
    print("⚠️ Worker数量不能小于1，已设置为1")

if worker_connections < 100:
    worker_connections = 100
    print("⚠️ Worker连接数不能小于100，已设置为100")

print(f"📋 最终配置:")
print(f"   - 绑定地址: {bind}")
print(f"   - Worker数量: {workers}")
print(f"   - Worker类型: {worker_class}")
print(f"   - 每Worker连接数: {worker_connections}")
print(f"   - 最大请求数: {max_requests}")
print(f"   - 超时时间: {timeout}秒")
print(f"   - 日志级别: {loglevel}")
print(f"   - 预加载应用: {preload_app}")
