import React from 'react';
import { MosaicContext } from './contextTypes';
import { <PERSON>saic<PERSON>ey, MosaicNode, ResizeO<PERSON><PERSON>, TileRenderer } from './types';
export interface MosaicRootProps<T extends MosaicKey> {
    root: MosaicNode<T>;
    renderTile: Tile<PERSON><PERSON>er<T>;
    resize?: ResizeOptions;
}
export declare class MosaicRoot<T extends MosaicKey> extends React.PureComponent<MosaicRootProps<T>> {
    static contextType: React.Context<MosaicContext<MosaicKey>>;
    context: MosaicContext<T>;
    render(): JSX.Element;
    private renderRecursively;
    private renderSplit;
    private onResize;
}
