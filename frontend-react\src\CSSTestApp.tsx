/**
 * 极简 CSS 测试应用 - 验证样式是否正确加载
 */
function CSSTestApp() {
  console.log('CSSTestApp rendering...');
  
  return (
    <div 
      style={{
        width: '100vw',
        height: '100vh',
        backgroundColor: '#191012',
        color: '#A49F9A',
        display: 'flex',
        padding: '20px',
        boxSizing: 'border-box'
      }}
    >
      {/* 左侧面板 */}
      <div 
        style={{
          width: '280px',
          backgroundColor: '#040709',
          border: '1px solid #2A2A2A',
          padding: '16px',
          marginRight: '4px'
        }}
      >
        <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px' }}>
          📚 目录栏 (CSS)
        </h3>
        <div style={{ padding: '8px', backgroundColor: '#2A2A2A', borderRadius: '4px', marginBottom: '8px' }}>
          状态: 正常
        </div>
        <div style={{ padding: '8px', backgroundColor: '#2A2A2A', borderRadius: '4px' }}>
          宽度: 280px
        </div>
      </div>
      
      {/* 中央面板 */}
      <div 
        style={{
          flex: 1,
          backgroundColor: '#191012',
          padding: '16px',
          marginRight: '4px'
        }}
      >
        <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px' }}>
          🖼️ 画廊面板 (CSS)
        </h3>
        <div style={{ padding: '8px', backgroundColor: '#2A2A2A', borderRadius: '4px', marginBottom: '8px' }}>
          状态: 正常
        </div>
        <div style={{ padding: '8px', backgroundColor: '#2A2A2A', borderRadius: '4px' }}>
          布局: flex-1 (自适应)
        </div>
      </div>
      
      {/* 右侧面板 */}
      <div 
        style={{
          width: '280px',
          backgroundColor: '#040709',
          border: '1px solid #2A2A2A',
          padding: '16px'
        }}
      >
        <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px' }}>
          📄 信息栏 (CSS)
        </h3>
        <div style={{ padding: '8px', backgroundColor: '#2A2A2A', borderRadius: '4px', marginBottom: '8px' }}>
          状态: 正常
        </div>
        <div style={{ padding: '8px', backgroundColor: '#2A2A2A', borderRadius: '4px' }}>
          宽度: 280px
        </div>
      </div>
    </div>
  );
}

export default CSSTestApp;
