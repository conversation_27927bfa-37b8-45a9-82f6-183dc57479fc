# src/services/search_cache.py
"""
搜索缓存服务
实现智能的查询结果缓存，提升重复查询的性能
"""

import hashlib
import json
import time
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    data: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int
    ttl_seconds: int
    size_bytes: int

class SearchCache:
    """搜索缓存管理器"""
    
    def __init__(self, max_size_mb: int = 100, default_ttl_seconds: int = 3600):
        """
        初始化搜索缓存
        
        Args:
            max_size_mb: 最大缓存大小（MB）
            default_ttl_seconds: 默认TTL（秒）
        """
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.default_ttl_seconds = default_ttl_seconds
        self.cache: Dict[str, CacheEntry] = {}
        self.current_size_bytes = 0
        
        # 缓存统计
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'total_requests': 0
        }
    
    def _generate_cache_key(self, query_type: str, params: Dict[str, Any]) -> str:
        """生成缓存键"""
        # 创建一个稳定的参数字符串
        sorted_params = json.dumps(params, sort_keys=True, default=str)
        key_string = f"{query_type}:{sorted_params}"
        
        # 使用SHA256生成哈希
        return hashlib.sha256(key_string.encode()).hexdigest()[:32]
    
    def _calculate_size(self, data: Any) -> int:
        """计算数据大小（字节）"""
        try:
            return len(json.dumps(data, default=str).encode('utf-8'))
        except:
            # 如果无法序列化，使用估算
            return len(str(data)) * 2
    
    def _is_expired(self, entry: CacheEntry) -> bool:
        """检查缓存条目是否过期"""
        if entry.ttl_seconds <= 0:
            return False  # 永不过期
        
        age_seconds = (datetime.now() - entry.created_at).total_seconds()
        return age_seconds > entry.ttl_seconds
    
    def _evict_expired(self):
        """清理过期的缓存条目"""
        expired_keys = []
        
        for key, entry in self.cache.items():
            if self._is_expired(entry):
                expired_keys.append(key)
        
        for key in expired_keys:
            self._remove_entry(key)
            self.stats['evictions'] += 1
    
    def _evict_lru(self, needed_space: int):
        """使用LRU策略清理缓存"""
        if not self.cache:
            return
        
        # 按最后访问时间排序
        sorted_entries = sorted(
            self.cache.items(),
            key=lambda x: x[1].last_accessed
        )
        
        freed_space = 0
        for key, entry in sorted_entries:
            if freed_space >= needed_space:
                break
            
            freed_space += entry.size_bytes
            self._remove_entry(key)
            self.stats['evictions'] += 1
    
    def _remove_entry(self, key: str):
        """移除缓存条目"""
        if key in self.cache:
            entry = self.cache[key]
            self.current_size_bytes -= entry.size_bytes
            del self.cache[key]
    
    def _ensure_space(self, needed_space: int):
        """确保有足够的缓存空间"""
        # 首先清理过期条目
        self._evict_expired()
        
        # 如果还需要更多空间，使用LRU清理
        available_space = self.max_size_bytes - self.current_size_bytes
        if available_space < needed_space:
            additional_space_needed = needed_space - available_space
            self._evict_lru(additional_space_needed)
    
    def get(self, query_type: str, params: Dict[str, Any]) -> Optional[Any]:
        """
        获取缓存的查询结果
        
        Args:
            query_type: 查询类型
            params: 查询参数
            
        Returns:
            缓存的结果或None
        """
        self.stats['total_requests'] += 1
        
        cache_key = self._generate_cache_key(query_type, params)
        
        if cache_key not in self.cache:
            self.stats['misses'] += 1
            return None
        
        entry = self.cache[cache_key]
        
        # 检查是否过期
        if self._is_expired(entry):
            self._remove_entry(cache_key)
            self.stats['misses'] += 1
            return None
        
        # 更新访问信息
        entry.last_accessed = datetime.now()
        entry.access_count += 1
        
        self.stats['hits'] += 1
        logger.debug(f"缓存命中: {cache_key}")
        
        return entry.data
    
    def set(
        self,
        query_type: str,
        params: Dict[str, Any],
        data: Any,
        ttl_seconds: Optional[int] = None
    ):
        """
        设置缓存
        
        Args:
            query_type: 查询类型
            params: 查询参数
            data: 要缓存的数据
            ttl_seconds: TTL（秒），None使用默认值
        """
        cache_key = self._generate_cache_key(query_type, params)
        data_size = self._calculate_size(data)
        
        # 检查数据是否太大
        if data_size > self.max_size_bytes * 0.1:  # 单个条目不能超过总缓存的10%
            logger.warning(f"数据太大，跳过缓存: {data_size} bytes")
            return
        
        # 确保有足够空间
        self._ensure_space(data_size)
        
        # 如果键已存在，先移除旧条目
        if cache_key in self.cache:
            self._remove_entry(cache_key)
        
        # 创建新条目
        now = datetime.now()
        entry = CacheEntry(
            key=cache_key,
            data=data,
            created_at=now,
            last_accessed=now,
            access_count=0,
            ttl_seconds=ttl_seconds or self.default_ttl_seconds,
            size_bytes=data_size
        )
        
        self.cache[cache_key] = entry
        self.current_size_bytes += data_size
        
        logger.debug(f"缓存设置: {cache_key}, 大小: {data_size} bytes")
    
    def invalidate_pattern(self, pattern: str):
        """
        根据模式失效缓存
        
        Args:
            pattern: 匹配模式（简单的字符串包含匹配）
        """
        keys_to_remove = []
        
        for key, entry in self.cache.items():
            if pattern in key:
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            self._remove_entry(key)
        
        logger.info(f"根据模式 '{pattern}' 失效了 {len(keys_to_remove)} 个缓存条目")
    
    def invalidate_by_case(self, case_id: str):
        """根据案例ID失效相关缓存"""
        self.invalidate_pattern(f"case_{case_id}")
    
    def clear(self):
        """清空所有缓存"""
        self.cache.clear()
        self.current_size_bytes = 0
        logger.info("缓存已清空")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        hit_rate = 0
        if self.stats['total_requests'] > 0:
            hit_rate = self.stats['hits'] / self.stats['total_requests']
        
        return {
            'total_entries': len(self.cache),
            'current_size_mb': self.current_size_bytes / (1024 * 1024),
            'max_size_mb': self.max_size_bytes / (1024 * 1024),
            'usage_percentage': (self.current_size_bytes / self.max_size_bytes) * 100,
            'hit_rate': hit_rate,
            'hits': self.stats['hits'],
            'misses': self.stats['misses'],
            'evictions': self.stats['evictions'],
            'total_requests': self.stats['total_requests']
        }
    
    def get_cache_info(self) -> List[Dict[str, Any]]:
        """获取缓存条目信息"""
        entries = []
        
        for key, entry in self.cache.items():
            entries.append({
                'key': key,
                'created_at': entry.created_at.isoformat(),
                'last_accessed': entry.last_accessed.isoformat(),
                'access_count': entry.access_count,
                'ttl_seconds': entry.ttl_seconds,
                'size_bytes': entry.size_bytes,
                'age_seconds': (datetime.now() - entry.created_at).total_seconds(),
                'expires_in_seconds': max(0, entry.ttl_seconds - (datetime.now() - entry.created_at).total_seconds()) if entry.ttl_seconds > 0 else -1
            })
        
        # 按访问次数排序
        entries.sort(key=lambda x: x['access_count'], reverse=True)
        
        return entries

class SmartSearchCache:
    """智能搜索缓存"""
    
    def __init__(self, max_size_mb: int = 100):
        self.cache = SearchCache(max_size_mb)
        self.query_patterns = {}  # 记录查询模式
    
    def _analyze_query_pattern(self, query_type: str, params: Dict[str, Any]):
        """分析查询模式"""
        pattern_key = f"{query_type}:{len(params)}"
        
        if pattern_key not in self.query_patterns:
            self.query_patterns[pattern_key] = {
                'count': 0,
                'avg_result_size': 0,
                'last_seen': datetime.now()
            }
        
        self.query_patterns[pattern_key]['count'] += 1
        self.query_patterns[pattern_key]['last_seen'] = datetime.now()
    
    def _get_smart_ttl(self, query_type: str, params: Dict[str, Any], result_size: int) -> int:
        """根据查询模式智能确定TTL"""
        
        # 基础TTL
        base_ttl = 3600  # 1小时
        
        # 根据查询类型调整
        if query_type == 'fulltext_search':
            base_ttl = 1800  # 全文搜索结果30分钟
        elif query_type == 'jsonb_query':
            base_ttl = 7200  # JSONB查询2小时
        elif query_type == 'simple_select':
            base_ttl = 14400  # 简单查询4小时
        
        # 根据结果大小调整
        if result_size > 1000:
            base_ttl = int(base_ttl * 0.5)  # 大结果集缓存时间减半
        elif result_size < 10:
            base_ttl = int(base_ttl * 2)  # 小结果集缓存时间加倍
        
        # 根据查询频率调整
        pattern_key = f"{query_type}:{len(params)}"
        if pattern_key in self.query_patterns:
            pattern = self.query_patterns[pattern_key]
            if pattern['count'] > 10:  # 高频查询
                base_ttl = int(base_ttl * 1.5)
        
        return max(300, min(base_ttl, 86400))  # 最少5分钟，最多24小时
    
    def get(self, query_type: str, params: Dict[str, Any]) -> Optional[Any]:
        """智能获取缓存"""
        self._analyze_query_pattern(query_type, params)
        return self.cache.get(query_type, params)
    
    def set(self, query_type: str, params: Dict[str, Any], data: Any):
        """智能设置缓存"""
        result_size = len(data) if isinstance(data, (list, dict)) else 1
        ttl = self._get_smart_ttl(query_type, params, result_size)
        
        self.cache.set(query_type, params, data, ttl)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        cache_stats = self.cache.get_stats()
        
        # 添加查询模式统计
        pattern_stats = {}
        for pattern, info in self.query_patterns.items():
            pattern_stats[pattern] = {
                'count': info['count'],
                'last_seen': info['last_seen'].isoformat()
            }
        
        cache_stats['query_patterns'] = pattern_stats
        return cache_stats

# 全局缓存实例
_global_cache = SmartSearchCache()

def get_search_cache() -> SmartSearchCache:
    """获取全局搜索缓存实例"""
    return _global_cache

def cached_search(query_type: str, cache_key_params: Dict[str, Any] = None):
    """
    搜索缓存装饰器
    
    Args:
        query_type: 查询类型
        cache_key_params: 缓存键参数（如果为None，使用函数参数）
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            cache = get_search_cache()
            
            # 构建缓存键参数
            if cache_key_params:
                key_params = cache_key_params
            else:
                # 使用函数参数作为缓存键
                key_params = kwargs.copy()
                if args:
                    key_params['_args'] = args
            
            # 尝试从缓存获取
            cached_result = cache.get(query_type, key_params)
            if cached_result is not None:
                return cached_result
            
            # 执行函数
            result = func(*args, **kwargs)
            
            # 缓存结果
            if result is not None:
                cache.set(query_type, key_params, result)
            
            return result
        
        return wrapper
    return decorator
