# tests/integration/test_api_cases.py
"""
案例API端点的集成测试
"""
import json
from fastapi.testclient import TestClient


class TestCasesAPI:
    """案例API集成测试类"""
    
    def test_create_case_success(self, test_client: TestClient, sample_case_data: dict):
        """测试成功创建案例的API"""
        response = test_client.post("/api/v1/cases/", json=sample_case_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["case_name"] == sample_case_data["case_name"]
        assert data["description"] == sample_case_data["description"]
        assert data["status"] == "active"
        assert "id" in data
        assert "created_at" in data
        assert "db_path" in data
    
    def test_create_case_invalid_data(self, test_client: TestClient):
        """测试创建案例时传入无效数据"""
        # 缺少必需字段
        invalid_data = {"description": "只有描述，没有案例名"}
        response = test_client.post("/api/v1/cases/", json=invalid_data)
        
        assert response.status_code == 422  # Validation error
    
    def test_get_cases_empty(self, test_client: TestClient):
        """测试获取空案例列表"""
        response = test_client.get("/api/v1/cases/")
        
        assert response.status_code == 200
        data = response.json()
        assert data == []
    
    def test_get_cases_with_data(self, test_client: TestClient, sample_case_data: dict):
        """测试获取有数据的案例列表"""
        # 创建几个案例
        case1_response = test_client.post("/api/v1/cases/", json=sample_case_data)
        case2_data = {"case_name": "第二个案例", "description": "第二个案例的描述"}
        case2_response = test_client.post("/api/v1/cases/", json=case2_data)
        
        assert case1_response.status_code == 201
        assert case2_response.status_code == 201
        
        # 获取案例列表
        response = test_client.get("/api/v1/cases/")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        # 验证按ID倒序排列
        assert data[0]["id"] > data[1]["id"]
    
    def test_get_cases_pagination(self, test_client: TestClient):
        """测试案例列表分页"""
        # 创建3个案例
        for i in range(3):
            case_data = {"case_name": f"案例{i+1}", "description": f"第{i+1}个案例"}
            response = test_client.post("/api/v1/cases/", json=case_data)
            assert response.status_code == 201
        
        # 测试分页
        response1 = test_client.get("/api/v1/cases/?skip=0&limit=2")
        response2 = test_client.get("/api/v1/cases/?skip=2&limit=2")
        
        assert response1.status_code == 200
        assert response2.status_code == 200
        
        data1 = response1.json()
        data2 = response2.json()
        
        assert len(data1) == 2
        assert len(data2) == 1
    
    def test_get_case_by_id_success(self, test_client: TestClient, sample_case_data: dict):
        """测试通过ID成功获取案例"""
        # 创建案例
        create_response = test_client.post("/api/v1/cases/", json=sample_case_data)
        assert create_response.status_code == 201
        created_case = create_response.json()
        
        # 通过ID获取案例
        case_id = created_case["id"]
        response = test_client.get(f"/api/v1/cases/{case_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == case_id
        assert data["case_name"] == sample_case_data["case_name"]
    
    def test_get_case_by_id_not_found(self, test_client: TestClient):
        """测试获取不存在的案例"""
        response = test_client.get("/api/v1/cases/999")
        
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data
    
    def test_delete_case_success(self, test_client: TestClient, sample_case_data: dict):
        """测试成功删除案例"""
        # 创建案例
        create_response = test_client.post("/api/v1/cases/", json=sample_case_data)
        assert create_response.status_code == 201
        created_case = create_response.json()
        
        # 删除案例
        case_id = created_case["id"]
        response = test_client.delete(f"/api/v1/cases/{case_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "deleted"
        assert "deleted_at" in data
        
        # 验证案例不再出现在正常列表中
        list_response = test_client.get("/api/v1/cases/")
        list_data = list_response.json()
        assert len(list_data) == 0
    
    def test_delete_case_not_found(self, test_client: TestClient):
        """测试删除不存在的案例"""
        response = test_client.delete("/api/v1/cases/999")
        
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data
    
    def test_health_check(self, test_client: TestClient):
        """测试健康检查端点"""
        response = test_client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "version" in data
        assert "features" in data
    
    def test_root_endpoint(self, test_client: TestClient):
        """测试根端点"""
        response = test_client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data 