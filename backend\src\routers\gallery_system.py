"""
标签即画廊系统API路由
包含高性能搜索、资产处理、智能导出功能
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime

from ..database import get_independent_db
from ..services.high_performance_search import (
    HighPerformanceSearchService, SearchQuery, search_service
)
from ..services.asset_pipeline import (
    AssetPipelineManager, ProcessingStage, ProcessingPriority, pipeline_manager
)
from ..services.export_system import (
    ExportTaskManager, ExportPreset, ExportTask, export_manager
)
from .. import schemas

logger = logging.getLogger(__name__)
router = APIRouter()

# =============================================================================
# 高性能标签检索API
# =============================================================================

@router.post("/api/v1/gallery/search")
async def advanced_search(
    search_request: Dict[str, Any]
):
    """
    高性能标签检索
    支持10,000+文件的实时搜索
    """
    try:
        # 构建搜索查询
        query = SearchQuery(
            case_id=search_request.get('case_id'),
            user_tags=search_request.get('user_tags'),
            custom_tags=search_request.get('custom_tags'),
            ai_tags=search_request.get('ai_tags'),
            operator=search_request.get('operator', 'AND'),
            quality_min=search_request.get('quality_min'),
            date_range=search_request.get('date_range'),
            limit=search_request.get('limit', 100),
            offset=search_request.get('offset', 0),
            sort_by=search_request.get('sort_by', 'relevance'),
            sort_order=search_request.get('sort_order', 'desc')
        )

        # 执行搜索
        results, total = await search_service.search_files(query)

        # 转换结果格式 - 性能优化
        search_results = []
        for result in results:
            # 优化：避免重复的字典创建和日期转换
            item = {
                'file_id': result.file_id,
                'file_name': result.file_name,
                'file_path': result.file_path,
                'thumbnail_path': result.thumbnail_path,
                'tag_match_count': result.tag_match_count,
                'relevance_score': result.relevance_score,
                'quality_score': result.quality_score,
                'tags': result.tags
            }

            # 优化：只在需要时转换日期
            if result.created_at:
                item['created_at'] = result.created_at.isoformat()
            else:
                item['created_at'] = None

            search_results.append(item)

        # 优化：简化返回结果，移除不必要的字段
        return {
            'results': search_results,
            'total': total
        }

    except Exception as e:
        logger.error(f"高性能搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.get("/api/v1/gallery/search/suggestions")
async def get_search_suggestions(
    case_id: int,
    query: str,
    category: Optional[str] = None,
    limit: int = 20
):
    """获取搜索建议"""
    try:
        suggestions = await search_service.get_tag_suggestions(
            case_id, query, category, limit
        )

        return {
            'suggestions': suggestions,
            'query': query,
            'category': category
        }

    except Exception as e:
        logger.error(f"获取搜索建议失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取建议失败: {str(e)}")

# =============================================================================
# 资产处理流水线API
# =============================================================================

@router.get("/api/v1/gallery/processors")
async def list_processors():
    """列出所有可用的处理器"""
    try:
        processors = pipeline_manager.list_processors()
        return {
            'processors': processors,
            'total': len(processors)
        }

    except Exception as e:
        logger.error(f"获取处理器列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取处理器失败: {str(e)}")

@router.post("/api/v1/gallery/process/file/{file_id}")
async def process_single_file(
    file_id: int,
    processor_name: str,
    stage: str = "on_demand",
    priority: str = "normal",
    metadata: Optional[Dict[str, Any]] = None,
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """处理单个文件"""
    try:
        # 转换枚举值
        processing_stage = ProcessingStage(stage)
        processing_priority = ProcessingPriority[priority.upper()]

        # 执行处理
        result = await pipeline_manager.process_file(
            file_id=file_id,
            processor_name=processor_name,
            stage=processing_stage,
            priority=processing_priority,
            metadata=metadata or {}
        )

        return {
            'success': result.success,
            'data': result.data,
            'error': result.error,
            'timestamp': result.timestamp.isoformat()
        }

    except Exception as e:
        logger.error(f"处理文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")

@router.post("/api/v1/gallery/process/batch")
async def process_batch_files(
    request: Dict[str, Any],
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """批量处理文件"""
    try:
        file_ids = request.get('file_ids', [])
        processor_name = request.get('processor_name')
        stage = request.get('stage', 'batch')
        priority = request.get('priority', 'normal')
        metadata = request.get('metadata', {})
        max_concurrent = request.get('max_concurrent', 4)

        if not file_ids or not processor_name:
            raise HTTPException(status_code=400, detail="缺少必要参数")

        # 转换枚举值
        processing_stage = ProcessingStage(stage)
        processing_priority = ProcessingPriority[priority.upper()]

        # 执行批量处理
        result = await pipeline_manager.batch_process_files(
            file_ids=file_ids,
            processor_name=processor_name,
            stage=processing_stage,
            priority=processing_priority,
            metadata=metadata,
            max_concurrent=max_concurrent
        )

        return result

    except Exception as e:
        logger.error(f"批量处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量处理失败: {str(e)}")

# =============================================================================
# 智能导出系统API
# =============================================================================

@router.get("/api/v1/gallery/export/presets")
async def list_export_presets():
    """列出所有导出预设"""
    try:
        presets = export_manager.preset_manager.list_presets()

        preset_list = []
        for preset in presets:
            preset_list.append({
                'id': preset.id,
                'name': preset.name,
                'description': preset.description,
                'format': preset.format.value,
                'structure': preset.structure.value,
                'naming_pattern': preset.naming_pattern.value,
                'quality': preset.quality,
                'max_size': preset.max_size,
                'watermark': preset.watermark,
                'metadata_export': preset.metadata_export,
                'created_at': preset.created_at.isoformat(),
                'updated_at': preset.updated_at.isoformat()
            })

        return {
            'presets': preset_list,
            'total': len(preset_list)
        }

    except Exception as e:
        logger.error(f"获取导出预设失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取预设失败: {str(e)}")

@router.post("/api/v1/gallery/export/create-task")
async def create_export_task(
    request: Dict[str, Any],
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """创建导出任务"""
    try:
        case_id = request.get('case_id')
        preset_id = request.get('preset_id')
        search_query_data = request.get('search_query', {})
        output_path = request.get('output_path')

        if not all([case_id, preset_id, output_path]):
            raise HTTPException(status_code=400, detail="缺少必要参数")

        # 构建搜索查询
        search_query = SearchQuery(
            case_id=case_id,
            user_tags=search_query_data.get('user_tags'),
            custom_tags=search_query_data.get('custom_tags'),
            ai_tags=search_query_data.get('ai_tags'),
            operator=search_query_data.get('operator', 'AND'),
            quality_min=search_query_data.get('quality_min'),
            date_range=search_query_data.get('date_range'),
            limit=search_query_data.get('limit', 1000),
            offset=search_query_data.get('offset', 0)
        )

        # 创建导出任务
        task_id = await export_manager.create_export_task(
            case_id=case_id,
            preset_id=preset_id,
            search_query=search_query,
            output_path=output_path
        )

        if not task_id:
            raise HTTPException(status_code=400, detail="创建导出任务失败")

        return {
            'task_id': task_id,
            'status': 'created',
            'message': '导出任务已创建，正在队列中等待处理'
        }

    except Exception as e:
        logger.error(f"创建导出任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建任务失败: {str(e)}")

@router.get("/api/v1/gallery/export/tasks/{task_id}/status")
async def get_export_task_status(task_id: str):
    """获取导出任务状态"""
    try:
        status = export_manager.get_task_status(task_id)

        if not status:
            raise HTTPException(status_code=404, detail="任务不存在")

        return status

    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.get("/api/v1/gallery/export/tasks")
async def list_export_tasks(case_id: Optional[int] = None):
    """列出导出任务"""
    try:
        tasks = export_manager.list_tasks(case_id)

        return {
            'tasks': tasks,
            'total': len(tasks)
        }

    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务失败: {str(e)}")

# =============================================================================
# 缓存管理API
# =============================================================================

@router.post("/api/v1/gallery/cache/invalidate/{case_id}")
async def invalidate_cache(case_id: int):
    """使指定案例的缓存失效"""
    try:
        await search_service.invalidate_cache(case_id)

        return {
            'success': True,
            'message': f'案例 {case_id} 的缓存已清除'
        }

    except Exception as e:
        logger.error(f"清除缓存失败: {e}")
        raise HTTPException(status_code=500, detail=f"清除缓存失败: {str(e)}")

@router.get("/api/v1/gallery/health")
async def gallery_health_check():
    """画廊系统健康检查"""
    try:
        return {
            'status': 'healthy',
            'services': {
                'search_service': 'active',
                'pipeline_manager': 'active',
                'export_manager': 'active'
            },
            'timestamp': datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")

@router.post("/api/v1/gallery/search-fast")
async def fast_search(search_request: Dict[str, Any]):
    """🚀 优化版快速搜索端点"""
    try:
        # 🔧 使用优化的搜索服务
        from ..services.optimized_search_service import optimized_search_service
        from ..services.cache_manager import cache_manager

        # 创建搜索查询
        query = SearchQuery(
            case_id=search_request.get('case_id'),
            user_tags=search_request.get('user_tags'),
            custom_tags=search_request.get('custom_tags'),
            ai_tags=search_request.get('ai_tags'),
            operator=search_request.get('operator', 'AND'),
            quality_min=search_request.get('quality_min'),
            date_range=search_request.get('date_range'),
            limit=search_request.get('limit', 10),
            offset=search_request.get('offset', 0)
        )

        # 🚀 先尝试从缓存获取
        cached_result = cache_manager.get_query_result(
            case_id=query.case_id,
            user_tags=query.user_tags,
            custom_tags=query.custom_tags,
            quality_min=query.quality_min,
            limit=query.limit,
            offset=query.offset
        )

        if cached_result:
            results, total = cached_result
            return {
                'total': total,
                'count': len(results),
                'cached': True
            }

        # 执行优化搜索
        results, total = await optimized_search_service.search_files_optimized(query)

        # 缓存结果
        cache_manager.set_query_result(
            case_id=query.case_id,
            user_tags=query.user_tags,
            custom_tags=query.custom_tags,
            quality_min=query.quality_min,
            limit=query.limit,
            offset=query.offset,
            results=results,
            total=total
        )

        # 极简返回格式
        return {
            'total': total,
            'count': len(results),
            'cached': False
        }

    except Exception as e:
        logger.error(f"快速搜索失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
