import * as React from 'react';
import { Button, Input, FileCard } from '@/components/ui';
import { cn } from '@/utils/cn';

// ============================================================================
// GalleryPanel Component Interface
// ============================================================================

export interface GalleryPanelProps {
  /**
   * 文件列表
   */
  files?: Array<{
    id: number;
    fileName: string;
    filePath: string;
    fileType: string;
    fileSize: number;
    width?: number;
    height?: number;
    thumbnailPath?: string;
  }>;

  /**
   * 选中的文件ID列表
   */
  selectedFileIds?: number[];

  /**
   * 显示方式
   */
  layout?: 'grid' | 'list';

  /**
   * 缩放级别 (0-100)
   */
  zoomLevel?: number;

  /**
   * 排序方式
   */
  sortBy?: 'name' | 'date' | 'size' | 'type';

  /**
   * 排序方向
   */
  sortOrder?: 'asc' | 'desc';

  /**
   * 搜索查询
   */
  searchQuery?: string;

  /**
   * 是否显示文件名
   */
  showFileName?: boolean;

  /**
   * 是否显示文件信息
   */
  showFileInfo?: boolean;

  /**
   * 加载状态
   */
  loading?: boolean;

  /**
   * 文件选择回调
   */
  onFileSelect?: (fileId: number, selected: boolean) => void;

  /**
   * 文件双击回调
   */
  onFileDoubleClick?: (file: NonNullable<GalleryPanelProps['files']>[0]) => void;

  /**
   * 搜索变化回调
   */
  onSearchChange?: (query: string) => void;

  /**
   * 布局变化回调
   */
  onLayoutChange?: (layout: 'grid' | 'list') => void;

  /**
   * 缩放变化回调
   */
  onZoomChange?: (level: number) => void;

  /**
   * 排序变化回调
   */
  onSortChange?: (sortBy: string, sortOrder: 'asc' | 'desc') => void;

  /**
   * 文件上传回调
   */
  onFileUpload?: (files: FileList) => void;

  className?: string;
}

// ============================================================================
// GalleryPanel Component Implementation
// ============================================================================

/**
 * GalleryPanel 组件 - 画廊面板
 *
 * 实现 Mizzy Star 的图像展示和管理功能：
 * 1. 多种布局模式（网格、列表、瀑布流）
 * 2. 缩放和排序控制
 * 3. 图像搜索和筛选
 * 4. 文件上传和管理
 * 5. 批量选择和操作
 *
 * 对应原始需求中的【画廊】功能
 */
const GalleryPanel = React.forwardRef<HTMLDivElement, GalleryPanelProps>(
  ({
    files = [],
    selectedFileIds = [],
    layout = 'grid',
    zoomLevel = 50,
    sortBy = 'name',
    sortOrder = 'asc',
    searchQuery = '',
    showFileName = true,
    showFileInfo = false,
    loading = false,
    onFileSelect,
    onFileDoubleClick,
    onSearchChange,
    onLayoutChange,
    onZoomChange,
    onSortChange,
    onFileUpload,
    className,
    ...props
  }, ref) => {

    // 拖拽上传处理
    const handleDragOver = (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
    };

    const handleDrop = (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();

      const droppedFiles = e.dataTransfer.files;
      if (droppedFiles.length > 0) {
        onFileUpload?.(droppedFiles);
      }
    };

    // 计算网格尺寸
    const getCardSize = () => {
      const baseSize = 120;
      const scaleFactor = zoomLevel / 50;
      return Math.max(80, Math.min(300, baseSize * scaleFactor));
    };

    const cardSize = getCardSize();

    return (
      <div
        ref={ref}
        className={cn(
          'h-full flex flex-col bg-background',
          className
        )}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        {...props}
      >
        {/* 顶部工具栏 */}
        <div className="flex-shrink-0 p-4 border-b border-border space-y-3">
          {/* 第一行：显示方式和缩放 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">显示方式:</span>
              <Button
                variant={layout === 'grid' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => onLayoutChange?.('grid')}
              >
                🔲 网格
              </Button>
              <Button
                variant={layout === 'list' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => onLayoutChange?.('list')}
              >
                📋 列表
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">缩放:</span>
              <input
                type="range"
                min="20"
                max="100"
                value={zoomLevel}
                onChange={(e) => onZoomChange?.(Number(e.target.value))}
                className="w-20"
              />
              <span className="text-xs text-muted-foreground w-8">
                {zoomLevel}%
              </span>
            </div>
          </div>

          {/* 第二行：搜索和排序 */}
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <Input
                placeholder="搜索图像..."
                value={searchQuery}
                onChange={(e) => onSearchChange?.(e.target.value)}
                leftIcon={<span>🔍</span>}
                variant="search"
              />
            </div>

            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                📊 排序: {sortBy}
                <span className="ml-1">
                  {sortOrder === 'asc' ? '↑' : '↓'}
                </span>
              </Button>

              <Button variant="outline" size="sm">
                🔽 筛选
              </Button>
            </div>
          </div>

          {/* 第三行：文件统计和上传 */}
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              共 {files.length} 个文件
              {selectedFileIds.length > 0 && (
                <span className="ml-2 text-primary">
                  已选择 {selectedFileIds.length} 个
                </span>
              )}
            </div>

            <Button variant="primary" size="sm">
              📁 上传图像
            </Button>
          </div>
        </div>

        {/* 画廊内容区域 */}
        <div className="flex-1 overflow-auto">
          {loading ? (
            <div className="h-full flex items-center justify-center">
              <div className="text-center space-y-4">
                <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto" />
                <p className="text-muted-foreground">加载中...</p>
              </div>
            </div>
          ) : files.length === 0 ? (
            <div className="h-full flex items-center justify-center">
              <div className="text-center space-y-4 max-w-md">
                <div className="text-6xl">📁</div>
                <h3 className="text-lg font-medium">暂无图像</h3>
                <p className="text-muted-foreground">
                  拖拽图片到此处上传，或点击上传按钮选择文件
                </p>
                <Button variant="primary">
                  📁 选择图像
                </Button>
              </div>
            </div>
          ) : (
            <div className="p-4">
              {layout === 'grid' ? (
                <div
                  className="grid gap-4"
                  style={{
                    gridTemplateColumns: `repeat(auto-fill, minmax(${cardSize}px, 1fr))`,
                  }}
                >
                  {files.map((file) => (
                    <FileCard
                      key={file.id}
                      file={file}
                      selected={selectedFileIds.includes(file.id)}
                      onSelect={onFileSelect}
                      onDoubleClick={onFileDoubleClick}
                      showFileName={showFileName}
                      showFileInfo={showFileInfo}
                      layout="grid"
                      style={{
                        width: `${cardSize}px`,
                        height: `${cardSize}px`,
                      }}
                    />
                  ))}
                </div>
              ) : (
                <div className="space-y-2">
                  {files.map((file) => (
                    <FileCard
                      key={file.id}
                      file={file}
                      selected={selectedFileIds.includes(file.id)}
                      onSelect={onFileSelect}
                      onDoubleClick={onFileDoubleClick}
                      showFileName={showFileName}
                      showFileInfo={showFileInfo}
                      layout="list"
                      className="w-full"
                    />
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* 底部状态栏 */}
        <div className="flex-shrink-0 p-2 border-t border-border bg-muted/30">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>
              {layout === 'grid' ? '网格视图' : '列表视图'} •
              缩放 {zoomLevel}%
            </span>
            <span>
              {files.length} 项
            </span>
          </div>
        </div>
      </div>
    );
  }
);

GalleryPanel.displayName = 'GalleryPanel';

export { GalleryPanel };
