import * as React from 'react';
import { cn } from '@/utils/cn';

// ============================================================================
// MizzyStar 新星计划 - 四栏可调整布局
// ============================================================================

export interface MizzyStarLayoutProps {
  /**
   * 目录栏内容
   */
  catalogPanel?: React.ReactNode;

  /**
   * 画廊面板内容
   */
  galleryPanel?: React.ReactNode;

  /**
   * 工作台面板内容
   */
  workbenchPanel?: React.ReactNode;

  /**
   * 信息栏内容
   */
  infoPanel?: React.ReactNode;

  /**
   * 是否显示目录栏
   */
  showCatalogPanel?: boolean;

  /**
   * 是否显示工作台
   */
  showWorkbench?: boolean;

  /**
   * 是否显示信息栏
   */
  showInfoPanel?: boolean;

  /**
   * 是否全屏画廊模式
   */
  isFullscreenGallery?: boolean;

  /**
   * 目录栏宽度 (px)
   */
  catalogPanelWidth?: number;

  /**
   * 信息栏宽度 (px)
   */
  infoPanelWidth?: number;

  /**
   * 工作台高度 (px)
   */
  workbenchHeight?: number;

  /**
   * 自定义类名
   */
  className?: string;
}

/**
 * MizzyStar 新星计划主布局组件
 * 
 * 实现蓝图中的四栏可调整大小布局：
 * 1. 左侧：目录栏 (CatalogPanel)
 * 2. 中央：画廊面板 (GalleryPanel) 
 * 3. 底部：工作台面板 (WorkbenchPanel)
 * 4. 右侧：信息栏 (InfoPanel)
 * 
 * 特性：
 * - 可拖拽调整面板大小
 * - 面板显示/隐藏切换
 * - 全屏画廊模式
 * - 响应式设计
 * - MizzyStar 配色方案
 */
const MizzyStarLayout = React.forwardRef<HTMLDivElement, MizzyStarLayoutProps>(
  ({
    catalogPanel,
    galleryPanel,
    workbenchPanel,
    infoPanel,
    showCatalogPanel = true,
    showWorkbench = false,
    showInfoPanel = true,
    isFullscreenGallery = false,
    catalogPanelWidth = 280,
    infoPanelWidth = 320,
    workbenchHeight = 200,
    className,
    ...props
  }, ref) => {

    // 全屏画廊模式：只显示画廊面板
    if (isFullscreenGallery) {
      return (
        <div
          ref={ref}
          className={cn(
            "h-screen w-screen bg-[#191012] overflow-hidden",
            className
          )}
          {...props}
        >
          {galleryPanel}
        </div>
      );
    }

    // 计算网格布局
    const gridTemplateColumns = [
      showCatalogPanel ? `${catalogPanelWidth}px` : '0px',
      '1fr', // 画廊面板占据剩余空间
      showInfoPanel ? `${infoPanelWidth}px` : '0px'
    ].join(' ');

    const gridTemplateRows = [
      '1fr', // 主要内容区域
      showWorkbench ? `${workbenchHeight}px` : '0px'
    ].join(' ');

    return (
      <div
        ref={ref}
        className={cn(
          "h-screen w-screen bg-[#191012] overflow-hidden",
          "grid gap-0",
          className
        )}
        style={{
          gridTemplateColumns,
          gridTemplateRows,
          gridTemplateAreas: `
            "catalog gallery info"
            "workbench workbench workbench"
          `
        }}
        {...props}
      >
        {/* 目录栏 */}
        {showCatalogPanel && (
          <div
            className="bg-[#040709] border-r border-[#2A2A2A] overflow-hidden"
            style={{ gridArea: 'catalog' }}
          >
            <div className="h-full overflow-auto">
              {catalogPanel}
            </div>
          </div>
        )}

        {/* 画廊面板 */}
        <div
          className="bg-[#191012] overflow-hidden"
          style={{ gridArea: 'gallery' }}
        >
          <div className="h-full overflow-auto">
            {galleryPanel}
          </div>
        </div>

        {/* 信息栏 */}
        {showInfoPanel && (
          <div
            className="bg-[#040709] border-l border-[#2A2A2A] overflow-hidden"
            style={{ gridArea: 'info' }}
          >
            <div className="h-full overflow-auto">
              {infoPanel}
            </div>
          </div>
        )}

        {/* 工作台面板 */}
        {showWorkbench && (
          <div
            className="bg-[#040709] border-t border-[#2A2A2A] overflow-hidden"
            style={{ gridArea: 'workbench' }}
          >
            <div className="h-full overflow-auto">
              {workbenchPanel}
            </div>
          </div>
        )}
      </div>
    );
  }
);

MizzyStarLayout.displayName = 'MizzyStarLayout';

export { MizzyStarLayout };
