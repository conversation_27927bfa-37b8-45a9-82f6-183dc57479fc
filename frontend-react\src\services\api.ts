// Project Novak - API Service Layer
// 严格的服务层抽象，组件不应该知道axios的存在

import axios from 'axios';
import { z } from 'zod';
import {
  ApiResponseSchema,
  PaginatedResponseSchema,
  CaseSchema,
  FileItemSchema,
  TagItemSchema,
  FileFiltersSchema,
  TagOperationSchema,
  type Case,
  type FileItem,
  type TagItem,
  type FileFilters,
  type TagOperation,
} from '@/types';

// ============================================================================
// API Client Configuration
// ============================================================================

const apiClient = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 统一错误处理
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// ============================================================================
// Type-Safe API Response Validator
// ============================================================================

/**
 * 类型安全的API响应验证器
 * 确保运行时数据与TypeScript类型一致
 */
function validateApiResponse<T>(
  data: unknown,
  schema: z.ZodSchema<T>
): T {
  try {
    return schema.parse(data);
  } catch (error) {
    console.error('API Response Validation Error:', error);
    throw new Error('Invalid API response format');
  }
}

// ============================================================================
// Cases API
// ============================================================================

/**
 * 获取所有案例/档案库
 */
export async function getCases(): Promise<Case[]> {
  const response = await apiClient.get('/cases');
  const schema = ApiResponseSchema(z.array(CaseSchema));
  const validatedData = validateApiResponse(response.data, schema);
  return validatedData.data;
}

/**
 * 获取单个案例详情
 */
export async function getCase(caseId: number): Promise<Case> {
  const response = await apiClient.get(`/cases/${caseId}`);
  const schema = ApiResponseSchema(CaseSchema);
  const validatedData = validateApiResponse(response.data, schema);
  return validatedData.data;
}

/**
 * 创建新案例
 */
export async function createCase(caseData: {
  case_name: string;
  description?: string;
}): Promise<Case> {
  const response = await apiClient.post('/cases', caseData);
  const schema = ApiResponseSchema(CaseSchema);
  const validatedData = validateApiResponse(response.data, schema);
  return validatedData.data;
}

// ============================================================================
// Files API
// ============================================================================

/**
 * 获取文件列表（支持筛选和分页）
 */
export async function getFiles(filters: Partial<FileFilters> = {}): Promise<{
  files: FileItem[];
  total: number;
  hasNext: boolean;
  hasPrev: boolean;
}> {
  // 验证筛选参数并应用默认值
  const validatedFilters = FileFiltersSchema.parse({
    page: 1,
    limit: 50,
    ...filters,
  });

  const response = await apiClient.get('/files', {
    params: validatedFilters,
  });

  const schema = PaginatedResponseSchema(FileItemSchema);
  const validatedData = validateApiResponse(response.data, schema);

  return {
    files: validatedData.data,
    total: validatedData.total,
    hasNext: validatedData.hasNext,
    hasPrev: validatedData.hasPrev,
  };
}

/**
 * 获取单个文件详情
 */
export async function getFile(fileId: number): Promise<FileItem> {
  const response = await apiClient.get(`/files/${fileId}`);
  const schema = ApiResponseSchema(FileItemSchema);
  const validatedData = validateApiResponse(response.data, schema);
  return validatedData.data;
}

/**
 * 上传文件
 */
export async function uploadFiles(
  caseId: number,
  files: FileList
): Promise<FileItem[]> {
  const formData = new FormData();
  formData.append('case_id', caseId.toString());

  Array.from(files).forEach((file) => {
    formData.append('files', file);
  });

  const response = await apiClient.post('/files/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  const schema = ApiResponseSchema(z.array(FileItemSchema));
  const validatedData = validateApiResponse(response.data, schema);
  return validatedData.data;
}

/**
 * 删除文件
 * 符合后端 API 规范: DELETE /api/v1/cases/{case_id}/files/{file_id}
 */
export async function deleteFile(caseId: number, fileId: number): Promise<void> {
  // 定义删除响应的 schema
  const DeleteResponseSchema = z.object({
    success: z.boolean(),
    message: z.string(),
    data: z.object({
      case_id: z.number(),
      file_id: z.number(),
      status: z.string(),
    }).optional(),
  });

  const response = await apiClient.delete(`/cases/${caseId}/files/${fileId}`);

  // 验证响应格式
  const validatedData = DeleteResponseSchema.parse(response.data);

  if (!validatedData.success) {
    throw new Error(validatedData.message || 'File deletion failed');
  }
}

// ============================================================================
// Tags API
// ============================================================================

/**
 * 获取标签列表
 */
export async function getTags(caseId?: number): Promise<TagItem[]> {
  const response = await apiClient.get('/tags', {
    params: caseId ? { case_id: caseId } : {},
  });

  const schema = ApiResponseSchema(z.array(TagItemSchema));
  const validatedData = validateApiResponse(response.data, schema);
  return validatedData.data;
}

/**
 * 为文件添加或移除标签
 */
export async function updateFileTag(operation: TagOperation): Promise<void> {
  // 验证操作参数
  const validatedOperation = TagOperationSchema.parse(operation);

  await apiClient.post('/tags/update', validatedOperation);
}

/**
 * 批量标签操作
 */
export async function batchUpdateTags(operations: TagOperation[]): Promise<void> {
  // 验证所有操作参数
  const validatedOperations = operations.map(op => TagOperationSchema.parse(op));

  await apiClient.post('/tags/batch-update', {
    operations: validatedOperations,
  });
}

// ============================================================================
// Search API
// ============================================================================

/**
 * 搜索文件
 */
export async function searchFiles(query: string, caseId?: number): Promise<FileItem[]> {
  const response = await apiClient.get('/search/files', {
    params: {
      q: query,
      case_id: caseId,
    },
  });

  const schema = ApiResponseSchema(z.array(FileItemSchema));
  const validatedData = validateApiResponse(response.data, schema);
  return validatedData.data;
}

/**
 * 搜索标签
 */
export async function searchTags(query: string, caseId?: number): Promise<TagItem[]> {
  const response = await apiClient.get('/search/tags', {
    params: {
      q: query,
      case_id: caseId,
    },
  });

  const schema = ApiResponseSchema(z.array(TagItemSchema));
  const validatedData = validateApiResponse(response.data, schema);
  return validatedData.data;
}

// ============================================================================
// Export API Client for Advanced Usage
// ============================================================================

export { apiClient };
