{"version": 3, "sources": ["../src/OptionsReader.ts"], "sourcesContent": ["import type { HTML5BackendContext, HTML5BackendOptions } from './types.js'\n\nexport class OptionsReader {\n\tpublic ownerDocument: Document | null = null\n\tprivate globalContext: HTML5BackendContext\n\tprivate optionsArgs: HTML5BackendOptions | undefined\n\n\tpublic constructor(\n\t\tglobalContext: HTML5BackendContext,\n\t\toptions?: HTML5BackendOptions,\n\t) {\n\t\tthis.globalContext = globalContext\n\t\tthis.optionsArgs = options\n\t}\n\n\tpublic get window(): Window | undefined {\n\t\tif (this.globalContext) {\n\t\t\treturn this.globalContext\n\t\t} else if (typeof window !== 'undefined') {\n\t\t\treturn window\n\t\t}\n\t\treturn undefined\n\t}\n\n\tpublic get document(): Document | undefined {\n\t\tif (this.globalContext?.document) {\n\t\t\treturn this.globalContext.document\n\t\t} else if (this.window) {\n\t\t\treturn this.window.document\n\t\t} else {\n\t\t\treturn undefined\n\t\t}\n\t}\n\n\tpublic get rootElement(): Node | undefined {\n\t\treturn this.optionsArgs?.rootElement || this.window\n\t}\n}\n"], "names": ["OptionsReader", "window", "globalContext", "undefined", "document", "rootElement", "optionsArgs", "options", "ownerDocument"], "mappings": "AAEA,OAAO,MAAMA,aAAa;IAazB,IAAWC,MAAM,GAAuB;QACvC,IAAI,IAAI,CAACC,aAAa,EAAE;YACvB,OAAO,IAAI,CAACA,aAAa,CAAA;SACzB,MAAM,IAAI,OAAOD,MAAM,KAAK,WAAW,EAAE;YACzC,OAAOA,MAAM,CAAA;SACb;QACD,OAAOE,SAAS,CAAA;KAChB;IAED,IAAWC,QAAQ,GAAyB;YACvC,GAAkB;QAAtB,IAAI,CAAA,GAAkB,GAAlB,IAAI,CAACF,aAAa,cAAlB,GAAkB,WAAU,GAA5B,KAAA,CAA4B,GAA5B,GAAkB,CAAEE,QAAQ,EAAE;YACjC,OAAO,IAAI,CAACF,aAAa,CAACE,QAAQ,CAAA;SAClC,MAAM,IAAI,IAAI,CAACH,MAAM,EAAE;YACvB,OAAO,IAAI,CAACA,MAAM,CAACG,QAAQ,CAAA;SAC3B,MAAM;YACN,OAAOD,SAAS,CAAA;SAChB;KACD;IAED,IAAWE,WAAW,GAAqB;YACnC,GAAgB;QAAvB,OAAO,CAAA,CAAA,GAAgB,GAAhB,IAAI,CAACC,WAAW,cAAhB,GAAgB,WAAa,GAA7B,KAAA,CAA6B,GAA7B,GAAgB,CAAED,WAAW,CAAA,IAAI,IAAI,CAACJ,MAAM,CAAA;KACnD;IA7BD,YACCC,aAAkC,EAClCK,OAA6B,CAC5B;QAPF,KAAOC,aAAa,GAAoB,IAAI,AAH7C,CAG6C;QAQ3C,IAAI,CAACN,aAAa,GAAGA,aAAa;QAClC,IAAI,CAACI,WAAW,GAAGC,OAAO;KAC1B;CAwBD"}