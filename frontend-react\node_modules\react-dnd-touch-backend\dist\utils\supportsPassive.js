export const supportsPassive = (()=>{
    // simular to <PERSON><PERSON><PERSON><PERSON>'s test
    let supported = false;
    try {
        addEventListener('test', ()=>{
        // do nothing
        }, Object.defineProperty({}, 'passive', {
            get () {
                supported = true;
                return true;
            }
        }));
    } catch (e) {
    // do nothing
    }
    return supported;
})();

//# sourceMappingURL=supportsPassive.js.map