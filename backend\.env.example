# 数据库配置示例文件
# 复制此文件为 .env 并根据需要修改配置

# === 主数据库配置 ===
# 主数据库类型: sqlite 或 postgresql
MASTER_DB_TYPE=sqlite

# SQLite配置 (当MASTER_DB_TYPE=sqlite时使用)
SQLITE_MASTER_PATH=data/mizzy_star.db
SQLITE_TIMEOUT=30.0

# PostgreSQL配置 (当MASTER_DB_TYPE=postgresql时使用)
POSTGRES_HOST=127.0.0.1
POSTGRES_PORT=5432
POSTGRES_USER=mizzy_user
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_DB=mizzy_main

# === 案例数据库配置 ===
# 案例数据库类型: sqlite 或 postgresql
CASE_DB_TYPE=sqlite
CASE_DB_PREFIX=case_

# === 连接池配置 ===
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# === 性能配置 ===
DB_ECHO_SQL=false
DB_ENABLE_QUERY_CACHE=true

# === 应用配置 ===
APP_ENV=development
DEBUG=true
LOG_LEVEL=INFO

# === PostgreSQL高性能配置示例 ===
# 如果使用PostgreSQL，可以启用以下配置以获得最佳性能

# 主数据库使用PostgreSQL，案例数据库使用SQLite (推荐的混合模式)
# MASTER_DB_TYPE=postgresql
# CASE_DB_TYPE=sqlite

# 全PostgreSQL模式 (最高性能)
# MASTER_DB_TYPE=postgresql
# CASE_DB_TYPE=postgresql

# === 开发环境快速配置 ===
# 开发环境推荐配置：主数据库SQLite，案例数据库SQLite
# MASTER_DB_TYPE=sqlite
# CASE_DB_TYPE=sqlite

# === 生产环境推荐配置 ===
# 生产环境推荐配置：主数据库PostgreSQL，案例数据库PostgreSQL
# MASTER_DB_TYPE=postgresql
# CASE_DB_TYPE=postgresql
# DB_POOL_SIZE=20
# DB_MAX_OVERFLOW=40
# DB_ECHO_SQL=false
