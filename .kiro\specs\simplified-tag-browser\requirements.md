# 精简标签浏览器需求文档

## 介绍

创建一个精简的标签浏览界面，专注于核心功能：左侧展示所有可用标签的层级结构，右侧展示选中标签下的图片画廊。去除复杂的标签规则设置和管理功能，提供直观的标签浏览体验。

## 需求

### 需求 1：双面板布局界面

**用户故事：** 作为用户，我想要一个简洁的双面板标签浏览界面，以便快速查看标签和对应的图片。

#### 验收标准
1. WHEN 用户打开标签浏览页面 THEN 系统应该显示左右分栏布局
2. WHEN 页面加载 THEN 左侧应该显示标签树，右侧应该显示图片画廊
3. WHEN 用户调整分栏 THEN 系统应该支持拖拽调整左右面板大小
4. WHEN 窗口大小改变 THEN 界面应该自适应调整

### 需求 2：标签树展示

**用户故事：** 作为用户，我想要看到所有可用标签的层级结构，以便了解图片的分类情况。

#### 验收标准
1. WHEN 显示标签树 THEN 系统应该按类别显示所有标签（metadata、cv、user、ai）
2. WHEN 标签有子级 THEN 系统应该支持展开/折叠显示
3. WHEN 标签为空 THEN 系统应该隐藏该标签项或显示为灰色
4. WHEN 显示标签 THEN 系统应该显示每个标签下的图片数量

### 需求 3：标签选择和过滤

**用户故事：** 作为用户，我想要点击标签来查看对应的图片，以便浏览特定分类的内容。

#### 验收标准
1. WHEN 用户点击标签 THEN 右侧画廊应该显示该标签下的所有图片
2. WHEN 没有选择标签 THEN 画廊应该显示案例中的所有图片
3. WHEN 用户选择不同标签 THEN 画廊应该立即更新显示内容
4. WHEN 标签下没有图片 THEN 画廊应该显示空状态提示

### 需求 4：图片画廊展示

**用户故事：** 作为用户，我想要以网格形式查看选中标签下的图片，以便快速浏览内容。

#### 验收标准
1. WHEN 显示图片画廊 THEN 系统应该以网格布局显示图片缩略图
2. WHEN 图片加载 THEN 系统应该显示加载状态和进度
3. WHEN 图片数量较多 THEN 系统应该支持滚动加载或分页
4. WHEN 用户点击图片 THEN 系统应该打开大图查看模式

### 需求 5：大图查看功能

**用户故事：** 作为用户，我想要点击缩略图查看大图，以便仔细查看图片内容。

#### 验收标准
1. WHEN 用户点击缩略图 THEN 系统应该打开大图查看模态框
2. WHEN 大图显示 THEN 系统应该显示该图片的基本信息
3. WHEN 在大图模式 THEN 用户应该能够使用键盘导航到上一张/下一张
4. WHEN 用户点击关闭 THEN 系统应该返回到画廊视图

### 需求 6：标签搜索功能

**用户故事：** 作为用户，我想要搜索特定的标签，以便快速找到需要的分类。

#### 验收标准
1. WHEN 用户在搜索框输入 THEN 系统应该实时过滤标签列表
2. WHEN 搜索匹配 THEN 系统应该高亮显示匹配的标签
3. WHEN 清空搜索 THEN 系统应该恢复完整标签列表
4. WHEN 搜索无结果 THEN 系统应该显示无结果提示

### 需求 7：数据加载和缓存

**用户故事：** 作为开发者，我想要确保标签数据的快速加载和一致性，以便用户获得流畅的浏览体验。

#### 验收标准
1. WHEN 页面初始化 THEN 系统应该从缓存快速加载标签数据
2. WHEN 标签数据更新 THEN 系统应该自动刷新显示
3. WHEN 图片被删除 THEN 系统应该从标签统计中排除已删除文件
4. WHEN 缓存失效 THEN 系统应该重新构建标签缓存

### 需求 8：响应式设计

**用户故事：** 作为用户，我想要界面能够适应不同的屏幕尺寸，以便在各种设备上使用。

#### 验收标准
1. WHEN 屏幕宽度较小 THEN 系统应该调整为上下布局或隐藏侧边栏
2. WHEN 用户在移动设备 THEN 系统应该提供触摸友好的交互
3. WHEN 分辨率改变 THEN 图片网格应该自适应调整列数
4. WHEN 面板过小 THEN 系统应该自动调整元素大小或隐藏次要信息