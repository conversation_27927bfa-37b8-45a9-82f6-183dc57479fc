(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const f of o)if(f.type==="childList")for(const h of f.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&s(h)}).observe(document,{childList:!0,subtree:!0});function u(o){const f={};return o.integrity&&(f.integrity=o.integrity),o.referrerPolicy&&(f.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?f.credentials="include":o.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function s(o){if(o.ep)return;o.ep=!0;const f=u(o);fetch(o.href,f)}})();function Fg(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var Tc={exports:{}},li={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var om;function Jg(){if(om)return li;om=1;var n=Symbol.for("react.transitional.element"),l=Symbol.for("react.fragment");function u(s,o,f){var h=null;if(f!==void 0&&(h=""+f),o.key!==void 0&&(h=""+o.key),"key"in o){f={};for(var y in o)y!=="key"&&(f[y]=o[y])}else f=o;return o=f.ref,{$$typeof:n,type:s,key:h,ref:o!==void 0?o:null,props:f}}return li.Fragment=l,li.jsx=u,li.jsxs=u,li}var fm;function Pg(){return fm||(fm=1,Tc.exports=Jg()),Tc.exports}var m=Pg(),Oc={exports:{}},ce={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dm;function Wg(){if(dm)return ce;dm=1;var n=Symbol.for("react.transitional.element"),l=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),h=Symbol.for("react.context"),y=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),v=Symbol.for("react.memo"),b=Symbol.for("react.lazy"),z=Symbol.iterator;function w(S){return S===null||typeof S!="object"?null:(S=z&&S[z]||S["@@iterator"],typeof S=="function"?S:null)}var k={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},M=Object.assign,D={};function Z(S,H,X){this.props=S,this.context=H,this.refs=D,this.updater=X||k}Z.prototype.isReactComponent={},Z.prototype.setState=function(S,H){if(typeof S!="object"&&typeof S!="function"&&S!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,S,H,"setState")},Z.prototype.forceUpdate=function(S){this.updater.enqueueForceUpdate(this,S,"forceUpdate")};function L(){}L.prototype=Z.prototype;function G(S,H,X){this.props=S,this.context=H,this.refs=D,this.updater=X||k}var Q=G.prototype=new L;Q.constructor=G,M(Q,Z.prototype),Q.isPureReactComponent=!0;var ee=Array.isArray,K={H:null,A:null,T:null,S:null,V:null},pe=Object.prototype.hasOwnProperty;function ue(S,H,X,V,W,ge){return X=ge.ref,{$$typeof:n,type:S,key:H,ref:X!==void 0?X:null,props:ge}}function J(S,H){return ue(S.type,H,void 0,void 0,void 0,S.props)}function Oe(S){return typeof S=="object"&&S!==null&&S.$$typeof===n}function tt(S){var H={"=":"=0",":":"=2"};return"$"+S.replace(/[=:]/g,function(X){return H[X]})}var $e=/\/+/g;function le(S,H){return typeof S=="object"&&S!==null&&S.key!=null?tt(""+S.key):H.toString(36)}function nt(){}function Qt(S){switch(S.status){case"fulfilled":return S.value;case"rejected":throw S.reason;default:switch(typeof S.status=="string"?S.then(nt,nt):(S.status="pending",S.then(function(H){S.status==="pending"&&(S.status="fulfilled",S.value=H)},function(H){S.status==="pending"&&(S.status="rejected",S.reason=H)})),S.status){case"fulfilled":return S.value;case"rejected":throw S.reason}}throw S}function De(S,H,X,V,W){var ge=typeof S;(ge==="undefined"||ge==="boolean")&&(S=null);var ie=!1;if(S===null)ie=!0;else switch(ge){case"bigint":case"string":case"number":ie=!0;break;case"object":switch(S.$$typeof){case n:case l:ie=!0;break;case b:return ie=S._init,De(ie(S._payload),H,X,V,W)}}if(ie)return W=W(S),ie=V===""?"."+le(S,0):V,ee(W)?(X="",ie!=null&&(X=ie.replace($e,"$&/")+"/"),De(W,H,X,"",function(yn){return yn})):W!=null&&(Oe(W)&&(W=J(W,X+(W.key==null||S&&S.key===W.key?"":(""+W.key).replace($e,"$&/")+"/")+ie)),H.push(W)),1;ie=0;var _t=V===""?".":V+":";if(ee(S))for(var Ce=0;Ce<S.length;Ce++)V=S[Ce],ge=_t+le(V,Ce),ie+=De(V,H,X,ge,W);else if(Ce=w(S),typeof Ce=="function")for(S=Ce.call(S),Ce=0;!(V=S.next()).done;)V=V.value,ge=_t+le(V,Ce++),ie+=De(V,H,X,ge,W);else if(ge==="object"){if(typeof S.then=="function")return De(Qt(S),H,X,V,W);throw H=String(S),Error("Objects are not valid as a React child (found: "+(H==="[object Object]"?"object with keys {"+Object.keys(S).join(", ")+"}":H)+"). If you meant to render a collection of children, use an array instead.")}return ie}function A(S,H,X){if(S==null)return S;var V=[],W=0;return De(S,V,"","",function(ge){return H.call(X,ge,W++)}),V}function Y(S){if(S._status===-1){var H=S._result;H=H(),H.then(function(X){(S._status===0||S._status===-1)&&(S._status=1,S._result=X)},function(X){(S._status===0||S._status===-1)&&(S._status=2,S._result=X)}),S._status===-1&&(S._status=0,S._result=H)}if(S._status===1)return S._result.default;throw S._result}var ne=typeof reportError=="function"?reportError:function(S){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var H=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof S=="object"&&S!==null&&typeof S.message=="string"?String(S.message):String(S),error:S});if(!window.dispatchEvent(H))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",S);return}console.error(S)};function Ne(){}return ce.Children={map:A,forEach:function(S,H,X){A(S,function(){H.apply(this,arguments)},X)},count:function(S){var H=0;return A(S,function(){H++}),H},toArray:function(S){return A(S,function(H){return H})||[]},only:function(S){if(!Oe(S))throw Error("React.Children.only expected to receive a single React element child.");return S}},ce.Component=Z,ce.Fragment=u,ce.Profiler=o,ce.PureComponent=G,ce.StrictMode=s,ce.Suspense=g,ce.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=K,ce.__COMPILER_RUNTIME={__proto__:null,c:function(S){return K.H.useMemoCache(S)}},ce.cache=function(S){return function(){return S.apply(null,arguments)}},ce.cloneElement=function(S,H,X){if(S==null)throw Error("The argument must be a React element, but you passed "+S+".");var V=M({},S.props),W=S.key,ge=void 0;if(H!=null)for(ie in H.ref!==void 0&&(ge=void 0),H.key!==void 0&&(W=""+H.key),H)!pe.call(H,ie)||ie==="key"||ie==="__self"||ie==="__source"||ie==="ref"&&H.ref===void 0||(V[ie]=H[ie]);var ie=arguments.length-2;if(ie===1)V.children=X;else if(1<ie){for(var _t=Array(ie),Ce=0;Ce<ie;Ce++)_t[Ce]=arguments[Ce+2];V.children=_t}return ue(S.type,W,void 0,void 0,ge,V)},ce.createContext=function(S){return S={$$typeof:h,_currentValue:S,_currentValue2:S,_threadCount:0,Provider:null,Consumer:null},S.Provider=S,S.Consumer={$$typeof:f,_context:S},S},ce.createElement=function(S,H,X){var V,W={},ge=null;if(H!=null)for(V in H.key!==void 0&&(ge=""+H.key),H)pe.call(H,V)&&V!=="key"&&V!=="__self"&&V!=="__source"&&(W[V]=H[V]);var ie=arguments.length-2;if(ie===1)W.children=X;else if(1<ie){for(var _t=Array(ie),Ce=0;Ce<ie;Ce++)_t[Ce]=arguments[Ce+2];W.children=_t}if(S&&S.defaultProps)for(V in ie=S.defaultProps,ie)W[V]===void 0&&(W[V]=ie[V]);return ue(S,ge,void 0,void 0,null,W)},ce.createRef=function(){return{current:null}},ce.forwardRef=function(S){return{$$typeof:y,render:S}},ce.isValidElement=Oe,ce.lazy=function(S){return{$$typeof:b,_payload:{_status:-1,_result:S},_init:Y}},ce.memo=function(S,H){return{$$typeof:v,type:S,compare:H===void 0?null:H}},ce.startTransition=function(S){var H=K.T,X={};K.T=X;try{var V=S(),W=K.S;W!==null&&W(X,V),typeof V=="object"&&V!==null&&typeof V.then=="function"&&V.then(Ne,ne)}catch(ge){ne(ge)}finally{K.T=H}},ce.unstable_useCacheRefresh=function(){return K.H.useCacheRefresh()},ce.use=function(S){return K.H.use(S)},ce.useActionState=function(S,H,X){return K.H.useActionState(S,H,X)},ce.useCallback=function(S,H){return K.H.useCallback(S,H)},ce.useContext=function(S){return K.H.useContext(S)},ce.useDebugValue=function(){},ce.useDeferredValue=function(S,H){return K.H.useDeferredValue(S,H)},ce.useEffect=function(S,H,X){var V=K.H;if(typeof X=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return V.useEffect(S,H)},ce.useId=function(){return K.H.useId()},ce.useImperativeHandle=function(S,H,X){return K.H.useImperativeHandle(S,H,X)},ce.useInsertionEffect=function(S,H){return K.H.useInsertionEffect(S,H)},ce.useLayoutEffect=function(S,H){return K.H.useLayoutEffect(S,H)},ce.useMemo=function(S,H){return K.H.useMemo(S,H)},ce.useOptimistic=function(S,H){return K.H.useOptimistic(S,H)},ce.useReducer=function(S,H,X){return K.H.useReducer(S,H,X)},ce.useRef=function(S){return K.H.useRef(S)},ce.useState=function(S){return K.H.useState(S)},ce.useSyncExternalStore=function(S,H,X){return K.H.useSyncExternalStore(S,H,X)},ce.useTransition=function(){return K.H.useTransition()},ce.version="19.1.0",ce}var hm;function to(){return hm||(hm=1,Oc.exports=Wg()),Oc.exports}var re=to();const mm=Fg(re);var Nc={exports:{}},ii={},jc={exports:{}},Ac={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var pm;function Ig(){return pm||(pm=1,function(n){function l(A,Y){var ne=A.length;A.push(Y);e:for(;0<ne;){var Ne=ne-1>>>1,S=A[Ne];if(0<o(S,Y))A[Ne]=Y,A[ne]=S,ne=Ne;else break e}}function u(A){return A.length===0?null:A[0]}function s(A){if(A.length===0)return null;var Y=A[0],ne=A.pop();if(ne!==Y){A[0]=ne;e:for(var Ne=0,S=A.length,H=S>>>1;Ne<H;){var X=2*(Ne+1)-1,V=A[X],W=X+1,ge=A[W];if(0>o(V,ne))W<S&&0>o(ge,V)?(A[Ne]=ge,A[W]=ne,Ne=W):(A[Ne]=V,A[X]=ne,Ne=X);else if(W<S&&0>o(ge,ne))A[Ne]=ge,A[W]=ne,Ne=W;else break e}}return Y}function o(A,Y){var ne=A.sortIndex-Y.sortIndex;return ne!==0?ne:A.id-Y.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;n.unstable_now=function(){return f.now()}}else{var h=Date,y=h.now();n.unstable_now=function(){return h.now()-y}}var g=[],v=[],b=1,z=null,w=3,k=!1,M=!1,D=!1,Z=!1,L=typeof setTimeout=="function"?setTimeout:null,G=typeof clearTimeout=="function"?clearTimeout:null,Q=typeof setImmediate<"u"?setImmediate:null;function ee(A){for(var Y=u(v);Y!==null;){if(Y.callback===null)s(v);else if(Y.startTime<=A)s(v),Y.sortIndex=Y.expirationTime,l(g,Y);else break;Y=u(v)}}function K(A){if(D=!1,ee(A),!M)if(u(g)!==null)M=!0,pe||(pe=!0,le());else{var Y=u(v);Y!==null&&De(K,Y.startTime-A)}}var pe=!1,ue=-1,J=5,Oe=-1;function tt(){return Z?!0:!(n.unstable_now()-Oe<J)}function $e(){if(Z=!1,pe){var A=n.unstable_now();Oe=A;var Y=!0;try{e:{M=!1,D&&(D=!1,G(ue),ue=-1),k=!0;var ne=w;try{t:{for(ee(A),z=u(g);z!==null&&!(z.expirationTime>A&&tt());){var Ne=z.callback;if(typeof Ne=="function"){z.callback=null,w=z.priorityLevel;var S=Ne(z.expirationTime<=A);if(A=n.unstable_now(),typeof S=="function"){z.callback=S,ee(A),Y=!0;break t}z===u(g)&&s(g),ee(A)}else s(g);z=u(g)}if(z!==null)Y=!0;else{var H=u(v);H!==null&&De(K,H.startTime-A),Y=!1}}break e}finally{z=null,w=ne,k=!1}Y=void 0}}finally{Y?le():pe=!1}}}var le;if(typeof Q=="function")le=function(){Q($e)};else if(typeof MessageChannel<"u"){var nt=new MessageChannel,Qt=nt.port2;nt.port1.onmessage=$e,le=function(){Qt.postMessage(null)}}else le=function(){L($e,0)};function De(A,Y){ue=L(function(){A(n.unstable_now())},Y)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(A){A.callback=null},n.unstable_forceFrameRate=function(A){0>A||125<A?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):J=0<A?Math.floor(1e3/A):5},n.unstable_getCurrentPriorityLevel=function(){return w},n.unstable_next=function(A){switch(w){case 1:case 2:case 3:var Y=3;break;default:Y=w}var ne=w;w=Y;try{return A()}finally{w=ne}},n.unstable_requestPaint=function(){Z=!0},n.unstable_runWithPriority=function(A,Y){switch(A){case 1:case 2:case 3:case 4:case 5:break;default:A=3}var ne=w;w=A;try{return Y()}finally{w=ne}},n.unstable_scheduleCallback=function(A,Y,ne){var Ne=n.unstable_now();switch(typeof ne=="object"&&ne!==null?(ne=ne.delay,ne=typeof ne=="number"&&0<ne?Ne+ne:Ne):ne=Ne,A){case 1:var S=-1;break;case 2:S=250;break;case 5:S=1073741823;break;case 4:S=1e4;break;default:S=5e3}return S=ne+S,A={id:b++,callback:Y,priorityLevel:A,startTime:ne,expirationTime:S,sortIndex:-1},ne>Ne?(A.sortIndex=ne,l(v,A),u(g)===null&&A===u(v)&&(D?(G(ue),ue=-1):D=!0,De(K,ne-Ne))):(A.sortIndex=S,l(g,A),M||k||(M=!0,pe||(pe=!0,le()))),A},n.unstable_shouldYield=tt,n.unstable_wrapCallback=function(A){var Y=w;return function(){var ne=w;w=Y;try{return A.apply(this,arguments)}finally{w=ne}}}}(Ac)),Ac}var vm;function e0(){return vm||(vm=1,jc.exports=Ig()),jc.exports}var Rc={exports:{}},ct={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ym;function t0(){if(ym)return ct;ym=1;var n=to();function l(g){var v="https://react.dev/errors/"+g;if(1<arguments.length){v+="?args[]="+encodeURIComponent(arguments[1]);for(var b=2;b<arguments.length;b++)v+="&args[]="+encodeURIComponent(arguments[b])}return"Minified React error #"+g+"; visit "+v+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(){}var s={d:{f:u,r:function(){throw Error(l(522))},D:u,C:u,L:u,m:u,X:u,S:u,M:u},p:0,findDOMNode:null},o=Symbol.for("react.portal");function f(g,v,b){var z=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:z==null?null:""+z,children:g,containerInfo:v,implementation:b}}var h=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function y(g,v){if(g==="font")return"";if(typeof v=="string")return v==="use-credentials"?v:""}return ct.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,ct.createPortal=function(g,v){var b=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!v||v.nodeType!==1&&v.nodeType!==9&&v.nodeType!==11)throw Error(l(299));return f(g,v,null,b)},ct.flushSync=function(g){var v=h.T,b=s.p;try{if(h.T=null,s.p=2,g)return g()}finally{h.T=v,s.p=b,s.d.f()}},ct.preconnect=function(g,v){typeof g=="string"&&(v?(v=v.crossOrigin,v=typeof v=="string"?v==="use-credentials"?v:"":void 0):v=null,s.d.C(g,v))},ct.prefetchDNS=function(g){typeof g=="string"&&s.d.D(g)},ct.preinit=function(g,v){if(typeof g=="string"&&v&&typeof v.as=="string"){var b=v.as,z=y(b,v.crossOrigin),w=typeof v.integrity=="string"?v.integrity:void 0,k=typeof v.fetchPriority=="string"?v.fetchPriority:void 0;b==="style"?s.d.S(g,typeof v.precedence=="string"?v.precedence:void 0,{crossOrigin:z,integrity:w,fetchPriority:k}):b==="script"&&s.d.X(g,{crossOrigin:z,integrity:w,fetchPriority:k,nonce:typeof v.nonce=="string"?v.nonce:void 0})}},ct.preinitModule=function(g,v){if(typeof g=="string")if(typeof v=="object"&&v!==null){if(v.as==null||v.as==="script"){var b=y(v.as,v.crossOrigin);s.d.M(g,{crossOrigin:b,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0})}}else v==null&&s.d.M(g)},ct.preload=function(g,v){if(typeof g=="string"&&typeof v=="object"&&v!==null&&typeof v.as=="string"){var b=v.as,z=y(b,v.crossOrigin);s.d.L(g,b,{crossOrigin:z,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0,type:typeof v.type=="string"?v.type:void 0,fetchPriority:typeof v.fetchPriority=="string"?v.fetchPriority:void 0,referrerPolicy:typeof v.referrerPolicy=="string"?v.referrerPolicy:void 0,imageSrcSet:typeof v.imageSrcSet=="string"?v.imageSrcSet:void 0,imageSizes:typeof v.imageSizes=="string"?v.imageSizes:void 0,media:typeof v.media=="string"?v.media:void 0})}},ct.preloadModule=function(g,v){if(typeof g=="string")if(v){var b=y(v.as,v.crossOrigin);s.d.m(g,{as:typeof v.as=="string"&&v.as!=="script"?v.as:void 0,crossOrigin:b,integrity:typeof v.integrity=="string"?v.integrity:void 0})}else s.d.m(g)},ct.requestFormReset=function(g){s.d.r(g)},ct.unstable_batchedUpdates=function(g,v){return g(v)},ct.useFormState=function(g,v,b){return h.H.useFormState(g,v,b)},ct.useFormStatus=function(){return h.H.useHostTransitionStatus()},ct.version="19.1.0",ct}var gm;function n0(){if(gm)return Rc.exports;gm=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(l){console.error(l)}}return n(),Rc.exports=t0(),Rc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bm;function a0(){if(bm)return ii;bm=1;var n=e0(),l=to(),u=n0();function s(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function h(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function y(e){if(f(e)!==e)throw Error(s(188))}function g(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(s(188));return t!==e?null:e}for(var a=e,i=t;;){var r=a.return;if(r===null)break;var c=r.alternate;if(c===null){if(i=r.return,i!==null){a=i;continue}break}if(r.child===c.child){for(c=r.child;c;){if(c===a)return y(r),e;if(c===i)return y(r),t;c=c.sibling}throw Error(s(188))}if(a.return!==i.return)a=r,i=c;else{for(var d=!1,p=r.child;p;){if(p===a){d=!0,a=r,i=c;break}if(p===i){d=!0,i=r,a=c;break}p=p.sibling}if(!d){for(p=c.child;p;){if(p===a){d=!0,a=c,i=r;break}if(p===i){d=!0,i=c,a=r;break}p=p.sibling}if(!d)throw Error(s(189))}}if(a.alternate!==i)throw Error(s(190))}if(a.tag!==3)throw Error(s(188));return a.stateNode.current===a?e:t}function v(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=v(e),t!==null)return t;e=e.sibling}return null}var b=Object.assign,z=Symbol.for("react.element"),w=Symbol.for("react.transitional.element"),k=Symbol.for("react.portal"),M=Symbol.for("react.fragment"),D=Symbol.for("react.strict_mode"),Z=Symbol.for("react.profiler"),L=Symbol.for("react.provider"),G=Symbol.for("react.consumer"),Q=Symbol.for("react.context"),ee=Symbol.for("react.forward_ref"),K=Symbol.for("react.suspense"),pe=Symbol.for("react.suspense_list"),ue=Symbol.for("react.memo"),J=Symbol.for("react.lazy"),Oe=Symbol.for("react.activity"),tt=Symbol.for("react.memo_cache_sentinel"),$e=Symbol.iterator;function le(e){return e===null||typeof e!="object"?null:(e=$e&&e[$e]||e["@@iterator"],typeof e=="function"?e:null)}var nt=Symbol.for("react.client.reference");function Qt(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===nt?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case M:return"Fragment";case Z:return"Profiler";case D:return"StrictMode";case K:return"Suspense";case pe:return"SuspenseList";case Oe:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case k:return"Portal";case Q:return(e.displayName||"Context")+".Provider";case G:return(e._context.displayName||"Context")+".Consumer";case ee:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ue:return t=e.displayName||null,t!==null?t:Qt(e.type)||"Memo";case J:t=e._payload,e=e._init;try{return Qt(e(t))}catch{}}return null}var De=Array.isArray,A=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Y=u.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ne={pending:!1,data:null,method:null,action:null},Ne=[],S=-1;function H(e){return{current:e}}function X(e){0>S||(e.current=Ne[S],Ne[S]=null,S--)}function V(e,t){S++,Ne[S]=e.current,e.current=t}var W=H(null),ge=H(null),ie=H(null),_t=H(null);function Ce(e,t){switch(V(ie,t),V(ge,e),V(W,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Bh(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Bh(t),e=Hh(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}X(W),V(W,e)}function yn(){X(W),X(ge),X(ie)}function os(e){e.memoizedState!==null&&V(_t,e);var t=W.current,a=Hh(t,e.type);t!==a&&(V(ge,e),V(W,a))}function zi(e){ge.current===e&&(X(W),X(ge)),_t.current===e&&(X(_t),Il._currentValue=ne)}var fs=Object.prototype.hasOwnProperty,ds=n.unstable_scheduleCallback,hs=n.unstable_cancelCallback,Nv=n.unstable_shouldYield,jv=n.unstable_requestPaint,Kt=n.unstable_now,Av=n.unstable_getCurrentPriorityLevel,yo=n.unstable_ImmediatePriority,go=n.unstable_UserBlockingPriority,Ti=n.unstable_NormalPriority,Rv=n.unstable_LowPriority,bo=n.unstable_IdlePriority,wv=n.log,Dv=n.unstable_setDisableYieldValue,sl=null,Et=null;function gn(e){if(typeof wv=="function"&&Dv(e),Et&&typeof Et.setStrictMode=="function")try{Et.setStrictMode(sl,e)}catch{}}var zt=Math.clz32?Math.clz32:Uv,Cv=Math.log,Mv=Math.LN2;function Uv(e){return e>>>=0,e===0?32:31-(Cv(e)/Mv|0)|0}var Oi=256,Ni=4194304;function Xn(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function ji(e,t,a){var i=e.pendingLanes;if(i===0)return 0;var r=0,c=e.suspendedLanes,d=e.pingedLanes;e=e.warmLanes;var p=i&134217727;return p!==0?(i=p&~c,i!==0?r=Xn(i):(d&=p,d!==0?r=Xn(d):a||(a=p&~e,a!==0&&(r=Xn(a))))):(p=i&~c,p!==0?r=Xn(p):d!==0?r=Xn(d):a||(a=i&~e,a!==0&&(r=Xn(a)))),r===0?0:t!==0&&t!==r&&(t&c)===0&&(c=r&-r,a=t&-t,c>=a||c===32&&(a&4194048)!==0)?t:r}function rl(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Zv(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function xo(){var e=Oi;return Oi<<=1,(Oi&4194048)===0&&(Oi=256),e}function So(){var e=Ni;return Ni<<=1,(Ni&62914560)===0&&(Ni=4194304),e}function ms(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function cl(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function qv(e,t,a,i,r,c){var d=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var p=e.entanglements,x=e.expirationTimes,O=e.hiddenUpdates;for(a=d&~a;0<a;){var C=31-zt(a),B=1<<C;p[C]=0,x[C]=-1;var N=O[C];if(N!==null)for(O[C]=null,C=0;C<N.length;C++){var j=N[C];j!==null&&(j.lane&=-536870913)}a&=~B}i!==0&&_o(e,i,0),c!==0&&r===0&&e.tag!==0&&(e.suspendedLanes|=c&~(d&~t))}function _o(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var i=31-zt(t);e.entangledLanes|=t,e.entanglements[i]=e.entanglements[i]|1073741824|a&4194090}function Eo(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var i=31-zt(a),r=1<<i;r&t|e[i]&t&&(e[i]|=t),a&=~r}}function ps(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function vs(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function zo(){var e=Y.p;return e!==0?e:(e=window.event,e===void 0?32:lm(e.type))}function Bv(e,t){var a=Y.p;try{return Y.p=e,t()}finally{Y.p=a}}var bn=Math.random().toString(36).slice(2),st="__reactFiber$"+bn,dt="__reactProps$"+bn,ya="__reactContainer$"+bn,ys="__reactEvents$"+bn,Hv="__reactListeners$"+bn,kv="__reactHandles$"+bn,To="__reactResources$"+bn,ol="__reactMarker$"+bn;function gs(e){delete e[st],delete e[dt],delete e[ys],delete e[Hv],delete e[kv]}function ga(e){var t=e[st];if(t)return t;for(var a=e.parentNode;a;){if(t=a[ya]||a[st]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=Gh(e);e!==null;){if(a=e[st])return a;e=Gh(e)}return t}e=a,a=e.parentNode}return null}function ba(e){if(e=e[st]||e[ya]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function fl(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(s(33))}function xa(e){var t=e[To];return t||(t=e[To]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Pe(e){e[ol]=!0}var Oo=new Set,No={};function Kn(e,t){Sa(e,t),Sa(e+"Capture",t)}function Sa(e,t){for(No[e]=t,e=0;e<t.length;e++)Oo.add(t[e])}var Lv=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),jo={},Ao={};function Qv(e){return fs.call(Ao,e)?!0:fs.call(jo,e)?!1:Lv.test(e)?Ao[e]=!0:(jo[e]=!0,!1)}function Ai(e,t,a){if(Qv(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var i=t.toLowerCase().slice(0,5);if(i!=="data-"&&i!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function Ri(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function tn(e,t,a,i){if(i===null)e.removeAttribute(a);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+i)}}var bs,Ro;function _a(e){if(bs===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);bs=t&&t[1]||"",Ro=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+bs+e+Ro}var xs=!1;function Ss(e,t){if(!e||xs)return"";xs=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var i={DetermineComponentFrameRoot:function(){try{if(t){var B=function(){throw Error()};if(Object.defineProperty(B.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(B,[])}catch(j){var N=j}Reflect.construct(e,[],B)}else{try{B.call()}catch(j){N=j}e.call(B.prototype)}}else{try{throw Error()}catch(j){N=j}(B=e())&&typeof B.catch=="function"&&B.catch(function(){})}}catch(j){if(j&&N&&typeof j.stack=="string")return[j.stack,N.stack]}return[null,null]}};i.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var r=Object.getOwnPropertyDescriptor(i.DetermineComponentFrameRoot,"name");r&&r.configurable&&Object.defineProperty(i.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=i.DetermineComponentFrameRoot(),d=c[0],p=c[1];if(d&&p){var x=d.split(`
`),O=p.split(`
`);for(r=i=0;i<x.length&&!x[i].includes("DetermineComponentFrameRoot");)i++;for(;r<O.length&&!O[r].includes("DetermineComponentFrameRoot");)r++;if(i===x.length||r===O.length)for(i=x.length-1,r=O.length-1;1<=i&&0<=r&&x[i]!==O[r];)r--;for(;1<=i&&0<=r;i--,r--)if(x[i]!==O[r]){if(i!==1||r!==1)do if(i--,r--,0>r||x[i]!==O[r]){var C=`
`+x[i].replace(" at new "," at ");return e.displayName&&C.includes("<anonymous>")&&(C=C.replace("<anonymous>",e.displayName)),C}while(1<=i&&0<=r);break}}}finally{xs=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?_a(a):""}function Gv(e){switch(e.tag){case 26:case 27:case 5:return _a(e.type);case 16:return _a("Lazy");case 13:return _a("Suspense");case 19:return _a("SuspenseList");case 0:case 15:return Ss(e.type,!1);case 11:return Ss(e.type.render,!1);case 1:return Ss(e.type,!0);case 31:return _a("Activity");default:return""}}function wo(e){try{var t="";do t+=Gv(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function Dt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Do(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Yv(e){var t=Do(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),i=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var r=a.get,c=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return r.call(this)},set:function(d){i=""+d,c.call(this,d)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return i},setValue:function(d){i=""+d},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function wi(e){e._valueTracker||(e._valueTracker=Yv(e))}function Co(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),i="";return e&&(i=Do(e)?e.checked?"true":"false":e.value),e=i,e!==a?(t.setValue(e),!0):!1}function Di(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Vv=/[\n"\\]/g;function Ct(e){return e.replace(Vv,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function _s(e,t,a,i,r,c,d,p){e.name="",d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?e.type=d:e.removeAttribute("type"),t!=null?d==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Dt(t)):e.value!==""+Dt(t)&&(e.value=""+Dt(t)):d!=="submit"&&d!=="reset"||e.removeAttribute("value"),t!=null?Es(e,d,Dt(t)):a!=null?Es(e,d,Dt(a)):i!=null&&e.removeAttribute("value"),r==null&&c!=null&&(e.defaultChecked=!!c),r!=null&&(e.checked=r&&typeof r!="function"&&typeof r!="symbol"),p!=null&&typeof p!="function"&&typeof p!="symbol"&&typeof p!="boolean"?e.name=""+Dt(p):e.removeAttribute("name")}function Mo(e,t,a,i,r,c,d,p){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||a!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;a=a!=null?""+Dt(a):"",t=t!=null?""+Dt(t):a,p||t===e.value||(e.value=t),e.defaultValue=t}i=i??r,i=typeof i!="function"&&typeof i!="symbol"&&!!i,e.checked=p?e.checked:!!i,e.defaultChecked=!!i,d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"&&(e.name=d)}function Es(e,t,a){t==="number"&&Di(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function Ea(e,t,a,i){if(e=e.options,t){t={};for(var r=0;r<a.length;r++)t["$"+a[r]]=!0;for(a=0;a<e.length;a++)r=t.hasOwnProperty("$"+e[a].value),e[a].selected!==r&&(e[a].selected=r),r&&i&&(e[a].defaultSelected=!0)}else{for(a=""+Dt(a),t=null,r=0;r<e.length;r++){if(e[r].value===a){e[r].selected=!0,i&&(e[r].defaultSelected=!0);return}t!==null||e[r].disabled||(t=e[r])}t!==null&&(t.selected=!0)}}function Uo(e,t,a){if(t!=null&&(t=""+Dt(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+Dt(a):""}function Zo(e,t,a,i){if(t==null){if(i!=null){if(a!=null)throw Error(s(92));if(De(i)){if(1<i.length)throw Error(s(93));i=i[0]}a=i}a==null&&(a=""),t=a}a=Dt(t),e.defaultValue=a,i=e.textContent,i===a&&i!==""&&i!==null&&(e.value=i)}function za(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var Xv=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function qo(e,t,a){var i=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?i?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":i?e.setProperty(t,a):typeof a!="number"||a===0||Xv.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function Bo(e,t,a){if(t!=null&&typeof t!="object")throw Error(s(62));if(e=e.style,a!=null){for(var i in a)!a.hasOwnProperty(i)||t!=null&&t.hasOwnProperty(i)||(i.indexOf("--")===0?e.setProperty(i,""):i==="float"?e.cssFloat="":e[i]="");for(var r in t)i=t[r],t.hasOwnProperty(r)&&a[r]!==i&&qo(e,r,i)}else for(var c in t)t.hasOwnProperty(c)&&qo(e,c,t[c])}function zs(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Kv=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),$v=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Ci(e){return $v.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Ts=null;function Os(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ta=null,Oa=null;function Ho(e){var t=ba(e);if(t&&(e=t.stateNode)){var a=e[dt]||null;e:switch(e=t.stateNode,t.type){case"input":if(_s(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+Ct(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var i=a[t];if(i!==e&&i.form===e.form){var r=i[dt]||null;if(!r)throw Error(s(90));_s(i,r.value,r.defaultValue,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name)}}for(t=0;t<a.length;t++)i=a[t],i.form===e.form&&Co(i)}break e;case"textarea":Uo(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&Ea(e,!!a.multiple,t,!1)}}}var Ns=!1;function ko(e,t,a){if(Ns)return e(t,a);Ns=!0;try{var i=e(t);return i}finally{if(Ns=!1,(Ta!==null||Oa!==null)&&(gu(),Ta&&(t=Ta,e=Oa,Oa=Ta=null,Ho(t),e)))for(t=0;t<e.length;t++)Ho(e[t])}}function dl(e,t){var a=e.stateNode;if(a===null)return null;var i=a[dt]||null;if(i===null)return null;a=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(e=e.type,i=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!i;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(s(231,t,typeof a));return a}var nn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),js=!1;if(nn)try{var hl={};Object.defineProperty(hl,"passive",{get:function(){js=!0}}),window.addEventListener("test",hl,hl),window.removeEventListener("test",hl,hl)}catch{js=!1}var xn=null,As=null,Mi=null;function Lo(){if(Mi)return Mi;var e,t=As,a=t.length,i,r="value"in xn?xn.value:xn.textContent,c=r.length;for(e=0;e<a&&t[e]===r[e];e++);var d=a-e;for(i=1;i<=d&&t[a-i]===r[c-i];i++);return Mi=r.slice(e,1<i?1-i:void 0)}function Ui(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Zi(){return!0}function Qo(){return!1}function ht(e){function t(a,i,r,c,d){this._reactName=a,this._targetInst=r,this.type=i,this.nativeEvent=c,this.target=d,this.currentTarget=null;for(var p in e)e.hasOwnProperty(p)&&(a=e[p],this[p]=a?a(c):c[p]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?Zi:Qo,this.isPropagationStopped=Qo,this}return b(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=Zi)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=Zi)},persist:function(){},isPersistent:Zi}),t}var $n={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},qi=ht($n),ml=b({},$n,{view:0,detail:0}),Fv=ht(ml),Rs,ws,pl,Bi=b({},ml,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Cs,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==pl&&(pl&&e.type==="mousemove"?(Rs=e.screenX-pl.screenX,ws=e.screenY-pl.screenY):ws=Rs=0,pl=e),Rs)},movementY:function(e){return"movementY"in e?e.movementY:ws}}),Go=ht(Bi),Jv=b({},Bi,{dataTransfer:0}),Pv=ht(Jv),Wv=b({},ml,{relatedTarget:0}),Ds=ht(Wv),Iv=b({},$n,{animationName:0,elapsedTime:0,pseudoElement:0}),ey=ht(Iv),ty=b({},$n,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ny=ht(ty),ay=b({},$n,{data:0}),Yo=ht(ay),ly={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},iy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},uy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function sy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=uy[e])?!!t[e]:!1}function Cs(){return sy}var ry=b({},ml,{key:function(e){if(e.key){var t=ly[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ui(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?iy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Cs,charCode:function(e){return e.type==="keypress"?Ui(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ui(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),cy=ht(ry),oy=b({},Bi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Vo=ht(oy),fy=b({},ml,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Cs}),dy=ht(fy),hy=b({},$n,{propertyName:0,elapsedTime:0,pseudoElement:0}),my=ht(hy),py=b({},Bi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),vy=ht(py),yy=b({},$n,{newState:0,oldState:0}),gy=ht(yy),by=[9,13,27,32],Ms=nn&&"CompositionEvent"in window,vl=null;nn&&"documentMode"in document&&(vl=document.documentMode);var xy=nn&&"TextEvent"in window&&!vl,Xo=nn&&(!Ms||vl&&8<vl&&11>=vl),Ko=" ",$o=!1;function Fo(e,t){switch(e){case"keyup":return by.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Jo(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Na=!1;function Sy(e,t){switch(e){case"compositionend":return Jo(t);case"keypress":return t.which!==32?null:($o=!0,Ko);case"textInput":return e=t.data,e===Ko&&$o?null:e;default:return null}}function _y(e,t){if(Na)return e==="compositionend"||!Ms&&Fo(e,t)?(e=Lo(),Mi=As=xn=null,Na=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Xo&&t.locale!=="ko"?null:t.data;default:return null}}var Ey={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Po(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Ey[e.type]:t==="textarea"}function Wo(e,t,a,i){Ta?Oa?Oa.push(i):Oa=[i]:Ta=i,t=zu(t,"onChange"),0<t.length&&(a=new qi("onChange","change",null,a,i),e.push({event:a,listeners:t}))}var yl=null,gl=null;function zy(e){Ch(e,0)}function Hi(e){var t=fl(e);if(Co(t))return e}function Io(e,t){if(e==="change")return t}var ef=!1;if(nn){var Us;if(nn){var Zs="oninput"in document;if(!Zs){var tf=document.createElement("div");tf.setAttribute("oninput","return;"),Zs=typeof tf.oninput=="function"}Us=Zs}else Us=!1;ef=Us&&(!document.documentMode||9<document.documentMode)}function nf(){yl&&(yl.detachEvent("onpropertychange",af),gl=yl=null)}function af(e){if(e.propertyName==="value"&&Hi(gl)){var t=[];Wo(t,gl,e,Os(e)),ko(zy,t)}}function Ty(e,t,a){e==="focusin"?(nf(),yl=t,gl=a,yl.attachEvent("onpropertychange",af)):e==="focusout"&&nf()}function Oy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Hi(gl)}function Ny(e,t){if(e==="click")return Hi(t)}function jy(e,t){if(e==="input"||e==="change")return Hi(t)}function Ay(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Tt=typeof Object.is=="function"?Object.is:Ay;function bl(e,t){if(Tt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),i=Object.keys(t);if(a.length!==i.length)return!1;for(i=0;i<a.length;i++){var r=a[i];if(!fs.call(t,r)||!Tt(e[r],t[r]))return!1}return!0}function lf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function uf(e,t){var a=lf(e);e=0;for(var i;a;){if(a.nodeType===3){if(i=e+a.textContent.length,e<=t&&i>=t)return{node:a,offset:t-e};e=i}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=lf(a)}}function sf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?sf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function rf(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Di(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=Di(e.document)}return t}function qs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var Ry=nn&&"documentMode"in document&&11>=document.documentMode,ja=null,Bs=null,xl=null,Hs=!1;function cf(e,t,a){var i=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;Hs||ja==null||ja!==Di(i)||(i=ja,"selectionStart"in i&&qs(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),xl&&bl(xl,i)||(xl=i,i=zu(Bs,"onSelect"),0<i.length&&(t=new qi("onSelect","select",null,t,a),e.push({event:t,listeners:i}),t.target=ja)))}function Fn(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var Aa={animationend:Fn("Animation","AnimationEnd"),animationiteration:Fn("Animation","AnimationIteration"),animationstart:Fn("Animation","AnimationStart"),transitionrun:Fn("Transition","TransitionRun"),transitionstart:Fn("Transition","TransitionStart"),transitioncancel:Fn("Transition","TransitionCancel"),transitionend:Fn("Transition","TransitionEnd")},ks={},of={};nn&&(of=document.createElement("div").style,"AnimationEvent"in window||(delete Aa.animationend.animation,delete Aa.animationiteration.animation,delete Aa.animationstart.animation),"TransitionEvent"in window||delete Aa.transitionend.transition);function Jn(e){if(ks[e])return ks[e];if(!Aa[e])return e;var t=Aa[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in of)return ks[e]=t[a];return e}var ff=Jn("animationend"),df=Jn("animationiteration"),hf=Jn("animationstart"),wy=Jn("transitionrun"),Dy=Jn("transitionstart"),Cy=Jn("transitioncancel"),mf=Jn("transitionend"),pf=new Map,Ls="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Ls.push("scrollEnd");function Gt(e,t){pf.set(e,t),Kn(t,[e])}var vf=new WeakMap;function Mt(e,t){if(typeof e=="object"&&e!==null){var a=vf.get(e);return a!==void 0?a:(t={value:e,source:t,stack:wo(t)},vf.set(e,t),t)}return{value:e,source:t,stack:wo(t)}}var Ut=[],Ra=0,Qs=0;function ki(){for(var e=Ra,t=Qs=Ra=0;t<e;){var a=Ut[t];Ut[t++]=null;var i=Ut[t];Ut[t++]=null;var r=Ut[t];Ut[t++]=null;var c=Ut[t];if(Ut[t++]=null,i!==null&&r!==null){var d=i.pending;d===null?r.next=r:(r.next=d.next,d.next=r),i.pending=r}c!==0&&yf(a,r,c)}}function Li(e,t,a,i){Ut[Ra++]=e,Ut[Ra++]=t,Ut[Ra++]=a,Ut[Ra++]=i,Qs|=i,e.lanes|=i,e=e.alternate,e!==null&&(e.lanes|=i)}function Gs(e,t,a,i){return Li(e,t,a,i),Qi(e)}function wa(e,t){return Li(e,null,null,t),Qi(e)}function yf(e,t,a){e.lanes|=a;var i=e.alternate;i!==null&&(i.lanes|=a);for(var r=!1,c=e.return;c!==null;)c.childLanes|=a,i=c.alternate,i!==null&&(i.childLanes|=a),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(r=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,r&&t!==null&&(r=31-zt(a),e=c.hiddenUpdates,i=e[r],i===null?e[r]=[t]:i.push(t),t.lane=a|536870912),c):null}function Qi(e){if(50<Vl)throw Vl=0,Fr=null,Error(s(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Da={};function My(e,t,a,i){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ot(e,t,a,i){return new My(e,t,a,i)}function Ys(e){return e=e.prototype,!(!e||!e.isReactComponent)}function an(e,t){var a=e.alternate;return a===null?(a=Ot(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function gf(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Gi(e,t,a,i,r,c){var d=0;if(i=e,typeof e=="function")Ys(e)&&(d=1);else if(typeof e=="string")d=Zg(e,a,W.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case Oe:return e=Ot(31,a,t,r),e.elementType=Oe,e.lanes=c,e;case M:return Pn(a.children,r,c,t);case D:d=8,r|=24;break;case Z:return e=Ot(12,a,t,r|2),e.elementType=Z,e.lanes=c,e;case K:return e=Ot(13,a,t,r),e.elementType=K,e.lanes=c,e;case pe:return e=Ot(19,a,t,r),e.elementType=pe,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case L:case Q:d=10;break e;case G:d=9;break e;case ee:d=11;break e;case ue:d=14;break e;case J:d=16,i=null;break e}d=29,a=Error(s(130,e===null?"null":typeof e,"")),i=null}return t=Ot(d,a,t,r),t.elementType=e,t.type=i,t.lanes=c,t}function Pn(e,t,a,i){return e=Ot(7,e,i,t),e.lanes=a,e}function Vs(e,t,a){return e=Ot(6,e,null,t),e.lanes=a,e}function Xs(e,t,a){return t=Ot(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Ca=[],Ma=0,Yi=null,Vi=0,Zt=[],qt=0,Wn=null,ln=1,un="";function In(e,t){Ca[Ma++]=Vi,Ca[Ma++]=Yi,Yi=e,Vi=t}function bf(e,t,a){Zt[qt++]=ln,Zt[qt++]=un,Zt[qt++]=Wn,Wn=e;var i=ln;e=un;var r=32-zt(i)-1;i&=~(1<<r),a+=1;var c=32-zt(t)+r;if(30<c){var d=r-r%5;c=(i&(1<<d)-1).toString(32),i>>=d,r-=d,ln=1<<32-zt(t)+r|a<<r|i,un=c+e}else ln=1<<c|a<<r|i,un=e}function Ks(e){e.return!==null&&(In(e,1),bf(e,1,0))}function $s(e){for(;e===Yi;)Yi=Ca[--Ma],Ca[Ma]=null,Vi=Ca[--Ma],Ca[Ma]=null;for(;e===Wn;)Wn=Zt[--qt],Zt[qt]=null,un=Zt[--qt],Zt[qt]=null,ln=Zt[--qt],Zt[qt]=null}var ft=null,ke=null,xe=!1,ea=null,$t=!1,Fs=Error(s(519));function ta(e){var t=Error(s(418,""));throw El(Mt(t,e)),Fs}function xf(e){var t=e.stateNode,a=e.type,i=e.memoizedProps;switch(t[st]=e,t[dt]=i,a){case"dialog":me("cancel",t),me("close",t);break;case"iframe":case"object":case"embed":me("load",t);break;case"video":case"audio":for(a=0;a<Kl.length;a++)me(Kl[a],t);break;case"source":me("error",t);break;case"img":case"image":case"link":me("error",t),me("load",t);break;case"details":me("toggle",t);break;case"input":me("invalid",t),Mo(t,i.value,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name,!0),wi(t);break;case"select":me("invalid",t);break;case"textarea":me("invalid",t),Zo(t,i.value,i.defaultValue,i.children),wi(t)}a=i.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||i.suppressHydrationWarning===!0||qh(t.textContent,a)?(i.popover!=null&&(me("beforetoggle",t),me("toggle",t)),i.onScroll!=null&&me("scroll",t),i.onScrollEnd!=null&&me("scrollend",t),i.onClick!=null&&(t.onclick=Tu),t=!0):t=!1,t||ta(e)}function Sf(e){for(ft=e.return;ft;)switch(ft.tag){case 5:case 13:$t=!1;return;case 27:case 3:$t=!0;return;default:ft=ft.return}}function Sl(e){if(e!==ft)return!1;if(!xe)return Sf(e),xe=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||fc(e.type,e.memoizedProps)),a=!a),a&&ke&&ta(e),Sf(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(s(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){ke=Vt(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}ke=null}}else t===27?(t=ke,Zn(e.type)?(e=pc,pc=null,ke=e):ke=t):ke=ft?Vt(e.stateNode.nextSibling):null;return!0}function _l(){ke=ft=null,xe=!1}function _f(){var e=ea;return e!==null&&(vt===null?vt=e:vt.push.apply(vt,e),ea=null),e}function El(e){ea===null?ea=[e]:ea.push(e)}var Js=H(null),na=null,sn=null;function Sn(e,t,a){V(Js,t._currentValue),t._currentValue=a}function rn(e){e._currentValue=Js.current,X(Js)}function Ps(e,t,a){for(;e!==null;){var i=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,i!==null&&(i.childLanes|=t)):i!==null&&(i.childLanes&t)!==t&&(i.childLanes|=t),e===a)break;e=e.return}}function Ws(e,t,a,i){var r=e.child;for(r!==null&&(r.return=e);r!==null;){var c=r.dependencies;if(c!==null){var d=r.child;c=c.firstContext;e:for(;c!==null;){var p=c;c=r;for(var x=0;x<t.length;x++)if(p.context===t[x]){c.lanes|=a,p=c.alternate,p!==null&&(p.lanes|=a),Ps(c.return,a,e),i||(d=null);break e}c=p.next}}else if(r.tag===18){if(d=r.return,d===null)throw Error(s(341));d.lanes|=a,c=d.alternate,c!==null&&(c.lanes|=a),Ps(d,a,e),d=null}else d=r.child;if(d!==null)d.return=r;else for(d=r;d!==null;){if(d===e){d=null;break}if(r=d.sibling,r!==null){r.return=d.return,d=r;break}d=d.return}r=d}}function zl(e,t,a,i){e=null;for(var r=t,c=!1;r!==null;){if(!c){if((r.flags&524288)!==0)c=!0;else if((r.flags&262144)!==0)break}if(r.tag===10){var d=r.alternate;if(d===null)throw Error(s(387));if(d=d.memoizedProps,d!==null){var p=r.type;Tt(r.pendingProps.value,d.value)||(e!==null?e.push(p):e=[p])}}else if(r===_t.current){if(d=r.alternate,d===null)throw Error(s(387));d.memoizedState.memoizedState!==r.memoizedState.memoizedState&&(e!==null?e.push(Il):e=[Il])}r=r.return}e!==null&&Ws(t,e,a,i),t.flags|=262144}function Xi(e){for(e=e.firstContext;e!==null;){if(!Tt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function aa(e){na=e,sn=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function rt(e){return Ef(na,e)}function Ki(e,t){return na===null&&aa(e),Ef(e,t)}function Ef(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},sn===null){if(e===null)throw Error(s(308));sn=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else sn=sn.next=t;return a}var Uy=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,i){e.push(i)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},Zy=n.unstable_scheduleCallback,qy=n.unstable_NormalPriority,Fe={$$typeof:Q,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Is(){return{controller:new Uy,data:new Map,refCount:0}}function Tl(e){e.refCount--,e.refCount===0&&Zy(qy,function(){e.controller.abort()})}var Ol=null,er=0,Ua=0,Za=null;function By(e,t){if(Ol===null){var a=Ol=[];er=0,Ua=nc(),Za={status:"pending",value:void 0,then:function(i){a.push(i)}}}return er++,t.then(zf,zf),t}function zf(){if(--er===0&&Ol!==null){Za!==null&&(Za.status="fulfilled");var e=Ol;Ol=null,Ua=0,Za=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Hy(e,t){var a=[],i={status:"pending",value:null,reason:null,then:function(r){a.push(r)}};return e.then(function(){i.status="fulfilled",i.value=t;for(var r=0;r<a.length;r++)(0,a[r])(t)},function(r){for(i.status="rejected",i.reason=r,r=0;r<a.length;r++)(0,a[r])(void 0)}),i}var Tf=A.S;A.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&By(e,t),Tf!==null&&Tf(e,t)};var la=H(null);function tr(){var e=la.current;return e!==null?e:Ae.pooledCache}function $i(e,t){t===null?V(la,la.current):V(la,t.pool)}function Of(){var e=tr();return e===null?null:{parent:Fe._currentValue,pool:e}}var Nl=Error(s(460)),Nf=Error(s(474)),Fi=Error(s(542)),nr={then:function(){}};function jf(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Ji(){}function Af(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(Ji,Ji),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,wf(e),e;default:if(typeof t.status=="string")t.then(Ji,Ji);else{if(e=Ae,e!==null&&100<e.shellSuspendCounter)throw Error(s(482));e=t,e.status="pending",e.then(function(i){if(t.status==="pending"){var r=t;r.status="fulfilled",r.value=i}},function(i){if(t.status==="pending"){var r=t;r.status="rejected",r.reason=i}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,wf(e),e}throw jl=t,Nl}}var jl=null;function Rf(){if(jl===null)throw Error(s(459));var e=jl;return jl=null,e}function wf(e){if(e===Nl||e===Fi)throw Error(s(483))}var _n=!1;function ar(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function lr(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function En(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function zn(e,t,a){var i=e.updateQueue;if(i===null)return null;if(i=i.shared,(Se&2)!==0){var r=i.pending;return r===null?t.next=t:(t.next=r.next,r.next=t),i.pending=t,t=Qi(e),yf(e,null,a),t}return Li(e,i,t,a),Qi(e)}function Al(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var i=t.lanes;i&=e.pendingLanes,a|=i,t.lanes=a,Eo(e,a)}}function ir(e,t){var a=e.updateQueue,i=e.alternate;if(i!==null&&(i=i.updateQueue,a===i)){var r=null,c=null;if(a=a.firstBaseUpdate,a!==null){do{var d={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};c===null?r=c=d:c=c.next=d,a=a.next}while(a!==null);c===null?r=c=t:c=c.next=t}else r=c=t;a={baseState:i.baseState,firstBaseUpdate:r,lastBaseUpdate:c,shared:i.shared,callbacks:i.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var ur=!1;function Rl(){if(ur){var e=Za;if(e!==null)throw e}}function wl(e,t,a,i){ur=!1;var r=e.updateQueue;_n=!1;var c=r.firstBaseUpdate,d=r.lastBaseUpdate,p=r.shared.pending;if(p!==null){r.shared.pending=null;var x=p,O=x.next;x.next=null,d===null?c=O:d.next=O,d=x;var C=e.alternate;C!==null&&(C=C.updateQueue,p=C.lastBaseUpdate,p!==d&&(p===null?C.firstBaseUpdate=O:p.next=O,C.lastBaseUpdate=x))}if(c!==null){var B=r.baseState;d=0,C=O=x=null,p=c;do{var N=p.lane&-536870913,j=N!==p.lane;if(j?(ye&N)===N:(i&N)===N){N!==0&&N===Ua&&(ur=!0),C!==null&&(C=C.next={lane:0,tag:p.tag,payload:p.payload,callback:null,next:null});e:{var ae=e,I=p;N=t;var Te=a;switch(I.tag){case 1:if(ae=I.payload,typeof ae=="function"){B=ae.call(Te,B,N);break e}B=ae;break e;case 3:ae.flags=ae.flags&-65537|128;case 0:if(ae=I.payload,N=typeof ae=="function"?ae.call(Te,B,N):ae,N==null)break e;B=b({},B,N);break e;case 2:_n=!0}}N=p.callback,N!==null&&(e.flags|=64,j&&(e.flags|=8192),j=r.callbacks,j===null?r.callbacks=[N]:j.push(N))}else j={lane:N,tag:p.tag,payload:p.payload,callback:p.callback,next:null},C===null?(O=C=j,x=B):C=C.next=j,d|=N;if(p=p.next,p===null){if(p=r.shared.pending,p===null)break;j=p,p=j.next,j.next=null,r.lastBaseUpdate=j,r.shared.pending=null}}while(!0);C===null&&(x=B),r.baseState=x,r.firstBaseUpdate=O,r.lastBaseUpdate=C,c===null&&(r.shared.lanes=0),Dn|=d,e.lanes=d,e.memoizedState=B}}function Df(e,t){if(typeof e!="function")throw Error(s(191,e));e.call(t)}function Cf(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)Df(a[e],t)}var qa=H(null),Pi=H(0);function Mf(e,t){e=pn,V(Pi,e),V(qa,t),pn=e|t.baseLanes}function sr(){V(Pi,pn),V(qa,qa.current)}function rr(){pn=Pi.current,X(qa),X(Pi)}var Tn=0,oe=null,Ee=null,Xe=null,Wi=!1,Ba=!1,ia=!1,Ii=0,Dl=0,Ha=null,ky=0;function Qe(){throw Error(s(321))}function cr(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!Tt(e[a],t[a]))return!1;return!0}function or(e,t,a,i,r,c){return Tn=c,oe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,A.H=e===null||e.memoizedState===null?yd:gd,ia=!1,c=a(i,r),ia=!1,Ba&&(c=Zf(t,a,i,r)),Uf(e),c}function Uf(e){A.H=iu;var t=Ee!==null&&Ee.next!==null;if(Tn=0,Xe=Ee=oe=null,Wi=!1,Dl=0,Ha=null,t)throw Error(s(300));e===null||We||(e=e.dependencies,e!==null&&Xi(e)&&(We=!0))}function Zf(e,t,a,i){oe=e;var r=0;do{if(Ba&&(Ha=null),Dl=0,Ba=!1,25<=r)throw Error(s(301));if(r+=1,Xe=Ee=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}A.H=Ky,c=t(a,i)}while(Ba);return c}function Ly(){var e=A.H,t=e.useState()[0];return t=typeof t.then=="function"?Cl(t):t,e=e.useState()[0],(Ee!==null?Ee.memoizedState:null)!==e&&(oe.flags|=1024),t}function fr(){var e=Ii!==0;return Ii=0,e}function dr(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function hr(e){if(Wi){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Wi=!1}Tn=0,Xe=Ee=oe=null,Ba=!1,Dl=Ii=0,Ha=null}function mt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Xe===null?oe.memoizedState=Xe=e:Xe=Xe.next=e,Xe}function Ke(){if(Ee===null){var e=oe.alternate;e=e!==null?e.memoizedState:null}else e=Ee.next;var t=Xe===null?oe.memoizedState:Xe.next;if(t!==null)Xe=t,Ee=e;else{if(e===null)throw oe.alternate===null?Error(s(467)):Error(s(310));Ee=e,e={memoizedState:Ee.memoizedState,baseState:Ee.baseState,baseQueue:Ee.baseQueue,queue:Ee.queue,next:null},Xe===null?oe.memoizedState=Xe=e:Xe=Xe.next=e}return Xe}function mr(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Cl(e){var t=Dl;return Dl+=1,Ha===null&&(Ha=[]),e=Af(Ha,e,t),t=oe,(Xe===null?t.memoizedState:Xe.next)===null&&(t=t.alternate,A.H=t===null||t.memoizedState===null?yd:gd),e}function eu(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Cl(e);if(e.$$typeof===Q)return rt(e)}throw Error(s(438,String(e)))}function pr(e){var t=null,a=oe.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var i=oe.alternate;i!==null&&(i=i.updateQueue,i!==null&&(i=i.memoCache,i!=null&&(t={data:i.data.map(function(r){return r.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=mr(),oe.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),i=0;i<e;i++)a[i]=tt;return t.index++,a}function cn(e,t){return typeof t=="function"?t(e):t}function tu(e){var t=Ke();return vr(t,Ee,e)}function vr(e,t,a){var i=e.queue;if(i===null)throw Error(s(311));i.lastRenderedReducer=a;var r=e.baseQueue,c=i.pending;if(c!==null){if(r!==null){var d=r.next;r.next=c.next,c.next=d}t.baseQueue=r=c,i.pending=null}if(c=e.baseState,r===null)e.memoizedState=c;else{t=r.next;var p=d=null,x=null,O=t,C=!1;do{var B=O.lane&-536870913;if(B!==O.lane?(ye&B)===B:(Tn&B)===B){var N=O.revertLane;if(N===0)x!==null&&(x=x.next={lane:0,revertLane:0,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null}),B===Ua&&(C=!0);else if((Tn&N)===N){O=O.next,N===Ua&&(C=!0);continue}else B={lane:0,revertLane:O.revertLane,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null},x===null?(p=x=B,d=c):x=x.next=B,oe.lanes|=N,Dn|=N;B=O.action,ia&&a(c,B),c=O.hasEagerState?O.eagerState:a(c,B)}else N={lane:B,revertLane:O.revertLane,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null},x===null?(p=x=N,d=c):x=x.next=N,oe.lanes|=B,Dn|=B;O=O.next}while(O!==null&&O!==t);if(x===null?d=c:x.next=p,!Tt(c,e.memoizedState)&&(We=!0,C&&(a=Za,a!==null)))throw a;e.memoizedState=c,e.baseState=d,e.baseQueue=x,i.lastRenderedState=c}return r===null&&(i.lanes=0),[e.memoizedState,i.dispatch]}function yr(e){var t=Ke(),a=t.queue;if(a===null)throw Error(s(311));a.lastRenderedReducer=e;var i=a.dispatch,r=a.pending,c=t.memoizedState;if(r!==null){a.pending=null;var d=r=r.next;do c=e(c,d.action),d=d.next;while(d!==r);Tt(c,t.memoizedState)||(We=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),a.lastRenderedState=c}return[c,i]}function qf(e,t,a){var i=oe,r=Ke(),c=xe;if(c){if(a===void 0)throw Error(s(407));a=a()}else a=t();var d=!Tt((Ee||r).memoizedState,a);d&&(r.memoizedState=a,We=!0),r=r.queue;var p=kf.bind(null,i,r,e);if(Ml(2048,8,p,[e]),r.getSnapshot!==t||d||Xe!==null&&Xe.memoizedState.tag&1){if(i.flags|=2048,ka(9,nu(),Hf.bind(null,i,r,a,t),null),Ae===null)throw Error(s(349));c||(Tn&124)!==0||Bf(i,t,a)}return a}function Bf(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=oe.updateQueue,t===null?(t=mr(),oe.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function Hf(e,t,a,i){t.value=a,t.getSnapshot=i,Lf(t)&&Qf(e)}function kf(e,t,a){return a(function(){Lf(t)&&Qf(e)})}function Lf(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!Tt(e,a)}catch{return!0}}function Qf(e){var t=wa(e,2);t!==null&&wt(t,e,2)}function gr(e){var t=mt();if(typeof e=="function"){var a=e;if(e=a(),ia){gn(!0);try{a()}finally{gn(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:cn,lastRenderedState:e},t}function Gf(e,t,a,i){return e.baseState=a,vr(e,Ee,typeof i=="function"?i:cn)}function Qy(e,t,a,i,r){if(lu(e))throw Error(s(485));if(e=t.action,e!==null){var c={payload:r,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(d){c.listeners.push(d)}};A.T!==null?a(!0):c.isTransition=!1,i(c),a=t.pending,a===null?(c.next=t.pending=c,Yf(t,c)):(c.next=a.next,t.pending=a.next=c)}}function Yf(e,t){var a=t.action,i=t.payload,r=e.state;if(t.isTransition){var c=A.T,d={};A.T=d;try{var p=a(r,i),x=A.S;x!==null&&x(d,p),Vf(e,t,p)}catch(O){br(e,t,O)}finally{A.T=c}}else try{c=a(r,i),Vf(e,t,c)}catch(O){br(e,t,O)}}function Vf(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(i){Xf(e,t,i)},function(i){return br(e,t,i)}):Xf(e,t,a)}function Xf(e,t,a){t.status="fulfilled",t.value=a,Kf(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,Yf(e,a)))}function br(e,t,a){var i=e.pending;if(e.pending=null,i!==null){i=i.next;do t.status="rejected",t.reason=a,Kf(t),t=t.next;while(t!==i)}e.action=null}function Kf(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function $f(e,t){return t}function Ff(e,t){if(xe){var a=Ae.formState;if(a!==null){e:{var i=oe;if(xe){if(ke){t:{for(var r=ke,c=$t;r.nodeType!==8;){if(!c){r=null;break t}if(r=Vt(r.nextSibling),r===null){r=null;break t}}c=r.data,r=c==="F!"||c==="F"?r:null}if(r){ke=Vt(r.nextSibling),i=r.data==="F!";break e}}ta(i)}i=!1}i&&(t=a[0])}}return a=mt(),a.memoizedState=a.baseState=t,i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:$f,lastRenderedState:t},a.queue=i,a=md.bind(null,oe,i),i.dispatch=a,i=gr(!1),c=zr.bind(null,oe,!1,i.queue),i=mt(),r={state:t,dispatch:null,action:e,pending:null},i.queue=r,a=Qy.bind(null,oe,r,c,a),r.dispatch=a,i.memoizedState=e,[t,a,!1]}function Jf(e){var t=Ke();return Pf(t,Ee,e)}function Pf(e,t,a){if(t=vr(e,t,$f)[0],e=tu(cn)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var i=Cl(t)}catch(d){throw d===Nl?Fi:d}else i=t;t=Ke();var r=t.queue,c=r.dispatch;return a!==t.memoizedState&&(oe.flags|=2048,ka(9,nu(),Gy.bind(null,r,a),null)),[i,c,e]}function Gy(e,t){e.action=t}function Wf(e){var t=Ke(),a=Ee;if(a!==null)return Pf(t,a,e);Ke(),t=t.memoizedState,a=Ke();var i=a.queue.dispatch;return a.memoizedState=e,[t,i,!1]}function ka(e,t,a,i){return e={tag:e,create:a,deps:i,inst:t,next:null},t=oe.updateQueue,t===null&&(t=mr(),oe.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(i=a.next,a.next=e,e.next=i,t.lastEffect=e),e}function nu(){return{destroy:void 0,resource:void 0}}function If(){return Ke().memoizedState}function au(e,t,a,i){var r=mt();i=i===void 0?null:i,oe.flags|=e,r.memoizedState=ka(1|t,nu(),a,i)}function Ml(e,t,a,i){var r=Ke();i=i===void 0?null:i;var c=r.memoizedState.inst;Ee!==null&&i!==null&&cr(i,Ee.memoizedState.deps)?r.memoizedState=ka(t,c,a,i):(oe.flags|=e,r.memoizedState=ka(1|t,c,a,i))}function ed(e,t){au(8390656,8,e,t)}function td(e,t){Ml(2048,8,e,t)}function nd(e,t){return Ml(4,2,e,t)}function ad(e,t){return Ml(4,4,e,t)}function ld(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function id(e,t,a){a=a!=null?a.concat([e]):null,Ml(4,4,ld.bind(null,t,e),a)}function xr(){}function ud(e,t){var a=Ke();t=t===void 0?null:t;var i=a.memoizedState;return t!==null&&cr(t,i[1])?i[0]:(a.memoizedState=[e,t],e)}function sd(e,t){var a=Ke();t=t===void 0?null:t;var i=a.memoizedState;if(t!==null&&cr(t,i[1]))return i[0];if(i=e(),ia){gn(!0);try{e()}finally{gn(!1)}}return a.memoizedState=[i,t],i}function Sr(e,t,a){return a===void 0||(Tn&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=oh(),oe.lanes|=e,Dn|=e,a)}function rd(e,t,a,i){return Tt(a,t)?a:qa.current!==null?(e=Sr(e,a,i),Tt(e,t)||(We=!0),e):(Tn&42)===0?(We=!0,e.memoizedState=a):(e=oh(),oe.lanes|=e,Dn|=e,t)}function cd(e,t,a,i,r){var c=Y.p;Y.p=c!==0&&8>c?c:8;var d=A.T,p={};A.T=p,zr(e,!1,t,a);try{var x=r(),O=A.S;if(O!==null&&O(p,x),x!==null&&typeof x=="object"&&typeof x.then=="function"){var C=Hy(x,i);Ul(e,t,C,Rt(e))}else Ul(e,t,i,Rt(e))}catch(B){Ul(e,t,{then:function(){},status:"rejected",reason:B},Rt())}finally{Y.p=c,A.T=d}}function Yy(){}function _r(e,t,a,i){if(e.tag!==5)throw Error(s(476));var r=od(e).queue;cd(e,r,t,ne,a===null?Yy:function(){return fd(e),a(i)})}function od(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:ne,baseState:ne,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:cn,lastRenderedState:ne},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:cn,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function fd(e){var t=od(e).next.queue;Ul(e,t,{},Rt())}function Er(){return rt(Il)}function dd(){return Ke().memoizedState}function hd(){return Ke().memoizedState}function Vy(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=Rt();e=En(a);var i=zn(t,e,a);i!==null&&(wt(i,t,a),Al(i,t,a)),t={cache:Is()},e.payload=t;return}t=t.return}}function Xy(e,t,a){var i=Rt();a={lane:i,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},lu(e)?pd(t,a):(a=Gs(e,t,a,i),a!==null&&(wt(a,e,i),vd(a,t,i)))}function md(e,t,a){var i=Rt();Ul(e,t,a,i)}function Ul(e,t,a,i){var r={lane:i,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(lu(e))pd(t,r);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var d=t.lastRenderedState,p=c(d,a);if(r.hasEagerState=!0,r.eagerState=p,Tt(p,d))return Li(e,t,r,0),Ae===null&&ki(),!1}catch{}finally{}if(a=Gs(e,t,r,i),a!==null)return wt(a,e,i),vd(a,t,i),!0}return!1}function zr(e,t,a,i){if(i={lane:2,revertLane:nc(),action:i,hasEagerState:!1,eagerState:null,next:null},lu(e)){if(t)throw Error(s(479))}else t=Gs(e,a,i,2),t!==null&&wt(t,e,2)}function lu(e){var t=e.alternate;return e===oe||t!==null&&t===oe}function pd(e,t){Ba=Wi=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function vd(e,t,a){if((a&4194048)!==0){var i=t.lanes;i&=e.pendingLanes,a|=i,t.lanes=a,Eo(e,a)}}var iu={readContext:rt,use:eu,useCallback:Qe,useContext:Qe,useEffect:Qe,useImperativeHandle:Qe,useLayoutEffect:Qe,useInsertionEffect:Qe,useMemo:Qe,useReducer:Qe,useRef:Qe,useState:Qe,useDebugValue:Qe,useDeferredValue:Qe,useTransition:Qe,useSyncExternalStore:Qe,useId:Qe,useHostTransitionStatus:Qe,useFormState:Qe,useActionState:Qe,useOptimistic:Qe,useMemoCache:Qe,useCacheRefresh:Qe},yd={readContext:rt,use:eu,useCallback:function(e,t){return mt().memoizedState=[e,t===void 0?null:t],e},useContext:rt,useEffect:ed,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,au(4194308,4,ld.bind(null,t,e),a)},useLayoutEffect:function(e,t){return au(4194308,4,e,t)},useInsertionEffect:function(e,t){au(4,2,e,t)},useMemo:function(e,t){var a=mt();t=t===void 0?null:t;var i=e();if(ia){gn(!0);try{e()}finally{gn(!1)}}return a.memoizedState=[i,t],i},useReducer:function(e,t,a){var i=mt();if(a!==void 0){var r=a(t);if(ia){gn(!0);try{a(t)}finally{gn(!1)}}}else r=t;return i.memoizedState=i.baseState=r,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:r},i.queue=e,e=e.dispatch=Xy.bind(null,oe,e),[i.memoizedState,e]},useRef:function(e){var t=mt();return e={current:e},t.memoizedState=e},useState:function(e){e=gr(e);var t=e.queue,a=md.bind(null,oe,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:xr,useDeferredValue:function(e,t){var a=mt();return Sr(a,e,t)},useTransition:function(){var e=gr(!1);return e=cd.bind(null,oe,e.queue,!0,!1),mt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var i=oe,r=mt();if(xe){if(a===void 0)throw Error(s(407));a=a()}else{if(a=t(),Ae===null)throw Error(s(349));(ye&124)!==0||Bf(i,t,a)}r.memoizedState=a;var c={value:a,getSnapshot:t};return r.queue=c,ed(kf.bind(null,i,c,e),[e]),i.flags|=2048,ka(9,nu(),Hf.bind(null,i,c,a,t),null),a},useId:function(){var e=mt(),t=Ae.identifierPrefix;if(xe){var a=un,i=ln;a=(i&~(1<<32-zt(i)-1)).toString(32)+a,t="«"+t+"R"+a,a=Ii++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=ky++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Er,useFormState:Ff,useActionState:Ff,useOptimistic:function(e){var t=mt();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=zr.bind(null,oe,!0,a),a.dispatch=t,[e,t]},useMemoCache:pr,useCacheRefresh:function(){return mt().memoizedState=Vy.bind(null,oe)}},gd={readContext:rt,use:eu,useCallback:ud,useContext:rt,useEffect:td,useImperativeHandle:id,useInsertionEffect:nd,useLayoutEffect:ad,useMemo:sd,useReducer:tu,useRef:If,useState:function(){return tu(cn)},useDebugValue:xr,useDeferredValue:function(e,t){var a=Ke();return rd(a,Ee.memoizedState,e,t)},useTransition:function(){var e=tu(cn)[0],t=Ke().memoizedState;return[typeof e=="boolean"?e:Cl(e),t]},useSyncExternalStore:qf,useId:dd,useHostTransitionStatus:Er,useFormState:Jf,useActionState:Jf,useOptimistic:function(e,t){var a=Ke();return Gf(a,Ee,e,t)},useMemoCache:pr,useCacheRefresh:hd},Ky={readContext:rt,use:eu,useCallback:ud,useContext:rt,useEffect:td,useImperativeHandle:id,useInsertionEffect:nd,useLayoutEffect:ad,useMemo:sd,useReducer:yr,useRef:If,useState:function(){return yr(cn)},useDebugValue:xr,useDeferredValue:function(e,t){var a=Ke();return Ee===null?Sr(a,e,t):rd(a,Ee.memoizedState,e,t)},useTransition:function(){var e=yr(cn)[0],t=Ke().memoizedState;return[typeof e=="boolean"?e:Cl(e),t]},useSyncExternalStore:qf,useId:dd,useHostTransitionStatus:Er,useFormState:Wf,useActionState:Wf,useOptimistic:function(e,t){var a=Ke();return Ee!==null?Gf(a,Ee,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:pr,useCacheRefresh:hd},La=null,Zl=0;function uu(e){var t=Zl;return Zl+=1,La===null&&(La=[]),Af(La,e,t)}function ql(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function su(e,t){throw t.$$typeof===z?Error(s(525)):(e=Object.prototype.toString.call(t),Error(s(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function bd(e){var t=e._init;return t(e._payload)}function xd(e){function t(E,_){if(e){var T=E.deletions;T===null?(E.deletions=[_],E.flags|=16):T.push(_)}}function a(E,_){if(!e)return null;for(;_!==null;)t(E,_),_=_.sibling;return null}function i(E){for(var _=new Map;E!==null;)E.key!==null?_.set(E.key,E):_.set(E.index,E),E=E.sibling;return _}function r(E,_){return E=an(E,_),E.index=0,E.sibling=null,E}function c(E,_,T){return E.index=T,e?(T=E.alternate,T!==null?(T=T.index,T<_?(E.flags|=67108866,_):T):(E.flags|=67108866,_)):(E.flags|=1048576,_)}function d(E){return e&&E.alternate===null&&(E.flags|=67108866),E}function p(E,_,T,U){return _===null||_.tag!==6?(_=Vs(T,E.mode,U),_.return=E,_):(_=r(_,T),_.return=E,_)}function x(E,_,T,U){var $=T.type;return $===M?C(E,_,T.props.children,U,T.key):_!==null&&(_.elementType===$||typeof $=="object"&&$!==null&&$.$$typeof===J&&bd($)===_.type)?(_=r(_,T.props),ql(_,T),_.return=E,_):(_=Gi(T.type,T.key,T.props,null,E.mode,U),ql(_,T),_.return=E,_)}function O(E,_,T,U){return _===null||_.tag!==4||_.stateNode.containerInfo!==T.containerInfo||_.stateNode.implementation!==T.implementation?(_=Xs(T,E.mode,U),_.return=E,_):(_=r(_,T.children||[]),_.return=E,_)}function C(E,_,T,U,$){return _===null||_.tag!==7?(_=Pn(T,E.mode,U,$),_.return=E,_):(_=r(_,T),_.return=E,_)}function B(E,_,T){if(typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint")return _=Vs(""+_,E.mode,T),_.return=E,_;if(typeof _=="object"&&_!==null){switch(_.$$typeof){case w:return T=Gi(_.type,_.key,_.props,null,E.mode,T),ql(T,_),T.return=E,T;case k:return _=Xs(_,E.mode,T),_.return=E,_;case J:var U=_._init;return _=U(_._payload),B(E,_,T)}if(De(_)||le(_))return _=Pn(_,E.mode,T,null),_.return=E,_;if(typeof _.then=="function")return B(E,uu(_),T);if(_.$$typeof===Q)return B(E,Ki(E,_),T);su(E,_)}return null}function N(E,_,T,U){var $=_!==null?_.key:null;if(typeof T=="string"&&T!==""||typeof T=="number"||typeof T=="bigint")return $!==null?null:p(E,_,""+T,U);if(typeof T=="object"&&T!==null){switch(T.$$typeof){case w:return T.key===$?x(E,_,T,U):null;case k:return T.key===$?O(E,_,T,U):null;case J:return $=T._init,T=$(T._payload),N(E,_,T,U)}if(De(T)||le(T))return $!==null?null:C(E,_,T,U,null);if(typeof T.then=="function")return N(E,_,uu(T),U);if(T.$$typeof===Q)return N(E,_,Ki(E,T),U);su(E,T)}return null}function j(E,_,T,U,$){if(typeof U=="string"&&U!==""||typeof U=="number"||typeof U=="bigint")return E=E.get(T)||null,p(_,E,""+U,$);if(typeof U=="object"&&U!==null){switch(U.$$typeof){case w:return E=E.get(U.key===null?T:U.key)||null,x(_,E,U,$);case k:return E=E.get(U.key===null?T:U.key)||null,O(_,E,U,$);case J:var fe=U._init;return U=fe(U._payload),j(E,_,T,U,$)}if(De(U)||le(U))return E=E.get(T)||null,C(_,E,U,$,null);if(typeof U.then=="function")return j(E,_,T,uu(U),$);if(U.$$typeof===Q)return j(E,_,T,Ki(_,U),$);su(_,U)}return null}function ae(E,_,T,U){for(var $=null,fe=null,F=_,te=_=0,et=null;F!==null&&te<T.length;te++){F.index>te?(et=F,F=null):et=F.sibling;var be=N(E,F,T[te],U);if(be===null){F===null&&(F=et);break}e&&F&&be.alternate===null&&t(E,F),_=c(be,_,te),fe===null?$=be:fe.sibling=be,fe=be,F=et}if(te===T.length)return a(E,F),xe&&In(E,te),$;if(F===null){for(;te<T.length;te++)F=B(E,T[te],U),F!==null&&(_=c(F,_,te),fe===null?$=F:fe.sibling=F,fe=F);return xe&&In(E,te),$}for(F=i(F);te<T.length;te++)et=j(F,E,te,T[te],U),et!==null&&(e&&et.alternate!==null&&F.delete(et.key===null?te:et.key),_=c(et,_,te),fe===null?$=et:fe.sibling=et,fe=et);return e&&F.forEach(function(Ln){return t(E,Ln)}),xe&&In(E,te),$}function I(E,_,T,U){if(T==null)throw Error(s(151));for(var $=null,fe=null,F=_,te=_=0,et=null,be=T.next();F!==null&&!be.done;te++,be=T.next()){F.index>te?(et=F,F=null):et=F.sibling;var Ln=N(E,F,be.value,U);if(Ln===null){F===null&&(F=et);break}e&&F&&Ln.alternate===null&&t(E,F),_=c(Ln,_,te),fe===null?$=Ln:fe.sibling=Ln,fe=Ln,F=et}if(be.done)return a(E,F),xe&&In(E,te),$;if(F===null){for(;!be.done;te++,be=T.next())be=B(E,be.value,U),be!==null&&(_=c(be,_,te),fe===null?$=be:fe.sibling=be,fe=be);return xe&&In(E,te),$}for(F=i(F);!be.done;te++,be=T.next())be=j(F,E,te,be.value,U),be!==null&&(e&&be.alternate!==null&&F.delete(be.key===null?te:be.key),_=c(be,_,te),fe===null?$=be:fe.sibling=be,fe=be);return e&&F.forEach(function($g){return t(E,$g)}),xe&&In(E,te),$}function Te(E,_,T,U){if(typeof T=="object"&&T!==null&&T.type===M&&T.key===null&&(T=T.props.children),typeof T=="object"&&T!==null){switch(T.$$typeof){case w:e:{for(var $=T.key;_!==null;){if(_.key===$){if($=T.type,$===M){if(_.tag===7){a(E,_.sibling),U=r(_,T.props.children),U.return=E,E=U;break e}}else if(_.elementType===$||typeof $=="object"&&$!==null&&$.$$typeof===J&&bd($)===_.type){a(E,_.sibling),U=r(_,T.props),ql(U,T),U.return=E,E=U;break e}a(E,_);break}else t(E,_);_=_.sibling}T.type===M?(U=Pn(T.props.children,E.mode,U,T.key),U.return=E,E=U):(U=Gi(T.type,T.key,T.props,null,E.mode,U),ql(U,T),U.return=E,E=U)}return d(E);case k:e:{for($=T.key;_!==null;){if(_.key===$)if(_.tag===4&&_.stateNode.containerInfo===T.containerInfo&&_.stateNode.implementation===T.implementation){a(E,_.sibling),U=r(_,T.children||[]),U.return=E,E=U;break e}else{a(E,_);break}else t(E,_);_=_.sibling}U=Xs(T,E.mode,U),U.return=E,E=U}return d(E);case J:return $=T._init,T=$(T._payload),Te(E,_,T,U)}if(De(T))return ae(E,_,T,U);if(le(T)){if($=le(T),typeof $!="function")throw Error(s(150));return T=$.call(T),I(E,_,T,U)}if(typeof T.then=="function")return Te(E,_,uu(T),U);if(T.$$typeof===Q)return Te(E,_,Ki(E,T),U);su(E,T)}return typeof T=="string"&&T!==""||typeof T=="number"||typeof T=="bigint"?(T=""+T,_!==null&&_.tag===6?(a(E,_.sibling),U=r(_,T),U.return=E,E=U):(a(E,_),U=Vs(T,E.mode,U),U.return=E,E=U),d(E)):a(E,_)}return function(E,_,T,U){try{Zl=0;var $=Te(E,_,T,U);return La=null,$}catch(F){if(F===Nl||F===Fi)throw F;var fe=Ot(29,F,null,E.mode);return fe.lanes=U,fe.return=E,fe}finally{}}}var Qa=xd(!0),Sd=xd(!1),Bt=H(null),Ft=null;function On(e){var t=e.alternate;V(Je,Je.current&1),V(Bt,e),Ft===null&&(t===null||qa.current!==null||t.memoizedState!==null)&&(Ft=e)}function _d(e){if(e.tag===22){if(V(Je,Je.current),V(Bt,e),Ft===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Ft=e)}}else Nn()}function Nn(){V(Je,Je.current),V(Bt,Bt.current)}function on(e){X(Bt),Ft===e&&(Ft=null),X(Je)}var Je=H(0);function ru(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||mc(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Tr(e,t,a,i){t=e.memoizedState,a=a(i,t),a=a==null?t:b({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var Or={enqueueSetState:function(e,t,a){e=e._reactInternals;var i=Rt(),r=En(i);r.payload=t,a!=null&&(r.callback=a),t=zn(e,r,i),t!==null&&(wt(t,e,i),Al(t,e,i))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var i=Rt(),r=En(i);r.tag=1,r.payload=t,a!=null&&(r.callback=a),t=zn(e,r,i),t!==null&&(wt(t,e,i),Al(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=Rt(),i=En(a);i.tag=2,t!=null&&(i.callback=t),t=zn(e,i,a),t!==null&&(wt(t,e,a),Al(t,e,a))}};function Ed(e,t,a,i,r,c,d){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(i,c,d):t.prototype&&t.prototype.isPureReactComponent?!bl(a,i)||!bl(r,c):!0}function zd(e,t,a,i){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,i),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,i),t.state!==e&&Or.enqueueReplaceState(t,t.state,null)}function ua(e,t){var a=t;if("ref"in t){a={};for(var i in t)i!=="ref"&&(a[i]=t[i])}if(e=e.defaultProps){a===t&&(a=b({},a));for(var r in e)a[r]===void 0&&(a[r]=e[r])}return a}var cu=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Td(e){cu(e)}function Od(e){console.error(e)}function Nd(e){cu(e)}function ou(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(i){setTimeout(function(){throw i})}}function jd(e,t,a){try{var i=e.onCaughtError;i(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function Nr(e,t,a){return a=En(a),a.tag=3,a.payload={element:null},a.callback=function(){ou(e,t)},a}function Ad(e){return e=En(e),e.tag=3,e}function Rd(e,t,a,i){var r=a.type.getDerivedStateFromError;if(typeof r=="function"){var c=i.value;e.payload=function(){return r(c)},e.callback=function(){jd(t,a,i)}}var d=a.stateNode;d!==null&&typeof d.componentDidCatch=="function"&&(e.callback=function(){jd(t,a,i),typeof r!="function"&&(Cn===null?Cn=new Set([this]):Cn.add(this));var p=i.stack;this.componentDidCatch(i.value,{componentStack:p!==null?p:""})})}function $y(e,t,a,i,r){if(a.flags|=32768,i!==null&&typeof i=="object"&&typeof i.then=="function"){if(t=a.alternate,t!==null&&zl(t,a,r,!0),a=Bt.current,a!==null){switch(a.tag){case 13:return Ft===null?Pr():a.alternate===null&&Le===0&&(Le=3),a.flags&=-257,a.flags|=65536,a.lanes=r,i===nr?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([i]):t.add(i),Ir(e,i,r)),!1;case 22:return a.flags|=65536,i===nr?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([i])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([i]):a.add(i)),Ir(e,i,r)),!1}throw Error(s(435,a.tag))}return Ir(e,i,r),Pr(),!1}if(xe)return t=Bt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=r,i!==Fs&&(e=Error(s(422),{cause:i}),El(Mt(e,a)))):(i!==Fs&&(t=Error(s(423),{cause:i}),El(Mt(t,a))),e=e.current.alternate,e.flags|=65536,r&=-r,e.lanes|=r,i=Mt(i,a),r=Nr(e.stateNode,i,r),ir(e,r),Le!==4&&(Le=2)),!1;var c=Error(s(520),{cause:i});if(c=Mt(c,a),Yl===null?Yl=[c]:Yl.push(c),Le!==4&&(Le=2),t===null)return!0;i=Mt(i,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=r&-r,a.lanes|=e,e=Nr(a.stateNode,i,e),ir(a,e),!1;case 1:if(t=a.type,c=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(Cn===null||!Cn.has(c))))return a.flags|=65536,r&=-r,a.lanes|=r,r=Ad(r),Rd(r,e,a,i),ir(a,r),!1}a=a.return}while(a!==null);return!1}var wd=Error(s(461)),We=!1;function at(e,t,a,i){t.child=e===null?Sd(t,null,a,i):Qa(t,e.child,a,i)}function Dd(e,t,a,i,r){a=a.render;var c=t.ref;if("ref"in i){var d={};for(var p in i)p!=="ref"&&(d[p]=i[p])}else d=i;return aa(t),i=or(e,t,a,d,c,r),p=fr(),e!==null&&!We?(dr(e,t,r),fn(e,t,r)):(xe&&p&&Ks(t),t.flags|=1,at(e,t,i,r),t.child)}function Cd(e,t,a,i,r){if(e===null){var c=a.type;return typeof c=="function"&&!Ys(c)&&c.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=c,Md(e,t,c,i,r)):(e=Gi(a.type,null,i,t,t.mode,r),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!Ur(e,r)){var d=c.memoizedProps;if(a=a.compare,a=a!==null?a:bl,a(d,i)&&e.ref===t.ref)return fn(e,t,r)}return t.flags|=1,e=an(c,i),e.ref=t.ref,e.return=t,t.child=e}function Md(e,t,a,i,r){if(e!==null){var c=e.memoizedProps;if(bl(c,i)&&e.ref===t.ref)if(We=!1,t.pendingProps=i=c,Ur(e,r))(e.flags&131072)!==0&&(We=!0);else return t.lanes=e.lanes,fn(e,t,r)}return jr(e,t,a,i,r)}function Ud(e,t,a){var i=t.pendingProps,r=i.children,c=e!==null?e.memoizedState:null;if(i.mode==="hidden"){if((t.flags&128)!==0){if(i=c!==null?c.baseLanes|a:a,e!==null){for(r=t.child=e.child,c=0;r!==null;)c=c|r.lanes|r.childLanes,r=r.sibling;t.childLanes=c&~i}else t.childLanes=0,t.child=null;return Zd(e,t,i,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&$i(t,c!==null?c.cachePool:null),c!==null?Mf(t,c):sr(),_d(t);else return t.lanes=t.childLanes=536870912,Zd(e,t,c!==null?c.baseLanes|a:a,a)}else c!==null?($i(t,c.cachePool),Mf(t,c),Nn(),t.memoizedState=null):(e!==null&&$i(t,null),sr(),Nn());return at(e,t,r,a),t.child}function Zd(e,t,a,i){var r=tr();return r=r===null?null:{parent:Fe._currentValue,pool:r},t.memoizedState={baseLanes:a,cachePool:r},e!==null&&$i(t,null),sr(),_d(t),e!==null&&zl(e,t,i,!0),null}function fu(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(s(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function jr(e,t,a,i,r){return aa(t),a=or(e,t,a,i,void 0,r),i=fr(),e!==null&&!We?(dr(e,t,r),fn(e,t,r)):(xe&&i&&Ks(t),t.flags|=1,at(e,t,a,r),t.child)}function qd(e,t,a,i,r,c){return aa(t),t.updateQueue=null,a=Zf(t,i,a,r),Uf(e),i=fr(),e!==null&&!We?(dr(e,t,c),fn(e,t,c)):(xe&&i&&Ks(t),t.flags|=1,at(e,t,a,c),t.child)}function Bd(e,t,a,i,r){if(aa(t),t.stateNode===null){var c=Da,d=a.contextType;typeof d=="object"&&d!==null&&(c=rt(d)),c=new a(i,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=Or,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=i,c.state=t.memoizedState,c.refs={},ar(t),d=a.contextType,c.context=typeof d=="object"&&d!==null?rt(d):Da,c.state=t.memoizedState,d=a.getDerivedStateFromProps,typeof d=="function"&&(Tr(t,a,d,i),c.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(d=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),d!==c.state&&Or.enqueueReplaceState(c,c.state,null),wl(t,i,c,r),Rl(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),i=!0}else if(e===null){c=t.stateNode;var p=t.memoizedProps,x=ua(a,p);c.props=x;var O=c.context,C=a.contextType;d=Da,typeof C=="object"&&C!==null&&(d=rt(C));var B=a.getDerivedStateFromProps;C=typeof B=="function"||typeof c.getSnapshotBeforeUpdate=="function",p=t.pendingProps!==p,C||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(p||O!==d)&&zd(t,c,i,d),_n=!1;var N=t.memoizedState;c.state=N,wl(t,i,c,r),Rl(),O=t.memoizedState,p||N!==O||_n?(typeof B=="function"&&(Tr(t,a,B,i),O=t.memoizedState),(x=_n||Ed(t,a,x,i,N,O,d))?(C||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=i,t.memoizedState=O),c.props=i,c.state=O,c.context=d,i=x):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),i=!1)}else{c=t.stateNode,lr(e,t),d=t.memoizedProps,C=ua(a,d),c.props=C,B=t.pendingProps,N=c.context,O=a.contextType,x=Da,typeof O=="object"&&O!==null&&(x=rt(O)),p=a.getDerivedStateFromProps,(O=typeof p=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(d!==B||N!==x)&&zd(t,c,i,x),_n=!1,N=t.memoizedState,c.state=N,wl(t,i,c,r),Rl();var j=t.memoizedState;d!==B||N!==j||_n||e!==null&&e.dependencies!==null&&Xi(e.dependencies)?(typeof p=="function"&&(Tr(t,a,p,i),j=t.memoizedState),(C=_n||Ed(t,a,C,i,N,j,x)||e!==null&&e.dependencies!==null&&Xi(e.dependencies))?(O||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(i,j,x),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(i,j,x)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||d===e.memoizedProps&&N===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||d===e.memoizedProps&&N===e.memoizedState||(t.flags|=1024),t.memoizedProps=i,t.memoizedState=j),c.props=i,c.state=j,c.context=x,i=C):(typeof c.componentDidUpdate!="function"||d===e.memoizedProps&&N===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||d===e.memoizedProps&&N===e.memoizedState||(t.flags|=1024),i=!1)}return c=i,fu(e,t),i=(t.flags&128)!==0,c||i?(c=t.stateNode,a=i&&typeof a.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&i?(t.child=Qa(t,e.child,null,r),t.child=Qa(t,null,a,r)):at(e,t,a,r),t.memoizedState=c.state,e=t.child):e=fn(e,t,r),e}function Hd(e,t,a,i){return _l(),t.flags|=256,at(e,t,a,i),t.child}var Ar={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Rr(e){return{baseLanes:e,cachePool:Of()}}function wr(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=Ht),e}function kd(e,t,a){var i=t.pendingProps,r=!1,c=(t.flags&128)!==0,d;if((d=c)||(d=e!==null&&e.memoizedState===null?!1:(Je.current&2)!==0),d&&(r=!0,t.flags&=-129),d=(t.flags&32)!==0,t.flags&=-33,e===null){if(xe){if(r?On(t):Nn(),xe){var p=ke,x;if(x=p){e:{for(x=p,p=$t;x.nodeType!==8;){if(!p){p=null;break e}if(x=Vt(x.nextSibling),x===null){p=null;break e}}p=x}p!==null?(t.memoizedState={dehydrated:p,treeContext:Wn!==null?{id:ln,overflow:un}:null,retryLane:536870912,hydrationErrors:null},x=Ot(18,null,null,0),x.stateNode=p,x.return=t,t.child=x,ft=t,ke=null,x=!0):x=!1}x||ta(t)}if(p=t.memoizedState,p!==null&&(p=p.dehydrated,p!==null))return mc(p)?t.lanes=32:t.lanes=536870912,null;on(t)}return p=i.children,i=i.fallback,r?(Nn(),r=t.mode,p=du({mode:"hidden",children:p},r),i=Pn(i,r,a,null),p.return=t,i.return=t,p.sibling=i,t.child=p,r=t.child,r.memoizedState=Rr(a),r.childLanes=wr(e,d,a),t.memoizedState=Ar,i):(On(t),Dr(t,p))}if(x=e.memoizedState,x!==null&&(p=x.dehydrated,p!==null)){if(c)t.flags&256?(On(t),t.flags&=-257,t=Cr(e,t,a)):t.memoizedState!==null?(Nn(),t.child=e.child,t.flags|=128,t=null):(Nn(),r=i.fallback,p=t.mode,i=du({mode:"visible",children:i.children},p),r=Pn(r,p,a,null),r.flags|=2,i.return=t,r.return=t,i.sibling=r,t.child=i,Qa(t,e.child,null,a),i=t.child,i.memoizedState=Rr(a),i.childLanes=wr(e,d,a),t.memoizedState=Ar,t=r);else if(On(t),mc(p)){if(d=p.nextSibling&&p.nextSibling.dataset,d)var O=d.dgst;d=O,i=Error(s(419)),i.stack="",i.digest=d,El({value:i,source:null,stack:null}),t=Cr(e,t,a)}else if(We||zl(e,t,a,!1),d=(a&e.childLanes)!==0,We||d){if(d=Ae,d!==null&&(i=a&-a,i=(i&42)!==0?1:ps(i),i=(i&(d.suspendedLanes|a))!==0?0:i,i!==0&&i!==x.retryLane))throw x.retryLane=i,wa(e,i),wt(d,e,i),wd;p.data==="$?"||Pr(),t=Cr(e,t,a)}else p.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=x.treeContext,ke=Vt(p.nextSibling),ft=t,xe=!0,ea=null,$t=!1,e!==null&&(Zt[qt++]=ln,Zt[qt++]=un,Zt[qt++]=Wn,ln=e.id,un=e.overflow,Wn=t),t=Dr(t,i.children),t.flags|=4096);return t}return r?(Nn(),r=i.fallback,p=t.mode,x=e.child,O=x.sibling,i=an(x,{mode:"hidden",children:i.children}),i.subtreeFlags=x.subtreeFlags&65011712,O!==null?r=an(O,r):(r=Pn(r,p,a,null),r.flags|=2),r.return=t,i.return=t,i.sibling=r,t.child=i,i=r,r=t.child,p=e.child.memoizedState,p===null?p=Rr(a):(x=p.cachePool,x!==null?(O=Fe._currentValue,x=x.parent!==O?{parent:O,pool:O}:x):x=Of(),p={baseLanes:p.baseLanes|a,cachePool:x}),r.memoizedState=p,r.childLanes=wr(e,d,a),t.memoizedState=Ar,i):(On(t),a=e.child,e=a.sibling,a=an(a,{mode:"visible",children:i.children}),a.return=t,a.sibling=null,e!==null&&(d=t.deletions,d===null?(t.deletions=[e],t.flags|=16):d.push(e)),t.child=a,t.memoizedState=null,a)}function Dr(e,t){return t=du({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function du(e,t){return e=Ot(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Cr(e,t,a){return Qa(t,e.child,null,a),e=Dr(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Ld(e,t,a){e.lanes|=t;var i=e.alternate;i!==null&&(i.lanes|=t),Ps(e.return,t,a)}function Mr(e,t,a,i,r){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:i,tail:a,tailMode:r}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=i,c.tail=a,c.tailMode=r)}function Qd(e,t,a){var i=t.pendingProps,r=i.revealOrder,c=i.tail;if(at(e,t,i.children,a),i=Je.current,(i&2)!==0)i=i&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ld(e,a,t);else if(e.tag===19)Ld(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}i&=1}switch(V(Je,i),r){case"forwards":for(a=t.child,r=null;a!==null;)e=a.alternate,e!==null&&ru(e)===null&&(r=a),a=a.sibling;a=r,a===null?(r=t.child,t.child=null):(r=a.sibling,a.sibling=null),Mr(t,!1,r,a,c);break;case"backwards":for(a=null,r=t.child,t.child=null;r!==null;){if(e=r.alternate,e!==null&&ru(e)===null){t.child=r;break}e=r.sibling,r.sibling=a,a=r,r=e}Mr(t,!0,a,null,c);break;case"together":Mr(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function fn(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),Dn|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(zl(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(s(153));if(t.child!==null){for(e=t.child,a=an(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=an(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function Ur(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Xi(e)))}function Fy(e,t,a){switch(t.tag){case 3:Ce(t,t.stateNode.containerInfo),Sn(t,Fe,e.memoizedState.cache),_l();break;case 27:case 5:os(t);break;case 4:Ce(t,t.stateNode.containerInfo);break;case 10:Sn(t,t.type,t.memoizedProps.value);break;case 13:var i=t.memoizedState;if(i!==null)return i.dehydrated!==null?(On(t),t.flags|=128,null):(a&t.child.childLanes)!==0?kd(e,t,a):(On(t),e=fn(e,t,a),e!==null?e.sibling:null);On(t);break;case 19:var r=(e.flags&128)!==0;if(i=(a&t.childLanes)!==0,i||(zl(e,t,a,!1),i=(a&t.childLanes)!==0),r){if(i)return Qd(e,t,a);t.flags|=128}if(r=t.memoizedState,r!==null&&(r.rendering=null,r.tail=null,r.lastEffect=null),V(Je,Je.current),i)break;return null;case 22:case 23:return t.lanes=0,Ud(e,t,a);case 24:Sn(t,Fe,e.memoizedState.cache)}return fn(e,t,a)}function Gd(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)We=!0;else{if(!Ur(e,a)&&(t.flags&128)===0)return We=!1,Fy(e,t,a);We=(e.flags&131072)!==0}else We=!1,xe&&(t.flags&1048576)!==0&&bf(t,Vi,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var i=t.elementType,r=i._init;if(i=r(i._payload),t.type=i,typeof i=="function")Ys(i)?(e=ua(i,e),t.tag=1,t=Bd(null,t,i,e,a)):(t.tag=0,t=jr(null,t,i,e,a));else{if(i!=null){if(r=i.$$typeof,r===ee){t.tag=11,t=Dd(null,t,i,e,a);break e}else if(r===ue){t.tag=14,t=Cd(null,t,i,e,a);break e}}throw t=Qt(i)||i,Error(s(306,t,""))}}return t;case 0:return jr(e,t,t.type,t.pendingProps,a);case 1:return i=t.type,r=ua(i,t.pendingProps),Bd(e,t,i,r,a);case 3:e:{if(Ce(t,t.stateNode.containerInfo),e===null)throw Error(s(387));i=t.pendingProps;var c=t.memoizedState;r=c.element,lr(e,t),wl(t,i,null,a);var d=t.memoizedState;if(i=d.cache,Sn(t,Fe,i),i!==c.cache&&Ws(t,[Fe],a,!0),Rl(),i=d.element,c.isDehydrated)if(c={element:i,isDehydrated:!1,cache:d.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=Hd(e,t,i,a);break e}else if(i!==r){r=Mt(Error(s(424)),t),El(r),t=Hd(e,t,i,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(ke=Vt(e.firstChild),ft=t,xe=!0,ea=null,$t=!0,a=Sd(t,null,i,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(_l(),i===r){t=fn(e,t,a);break e}at(e,t,i,a)}t=t.child}return t;case 26:return fu(e,t),e===null?(a=Kh(t.type,null,t.pendingProps,null))?t.memoizedState=a:xe||(a=t.type,e=t.pendingProps,i=Ou(ie.current).createElement(a),i[st]=t,i[dt]=e,it(i,a,e),Pe(i),t.stateNode=i):t.memoizedState=Kh(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return os(t),e===null&&xe&&(i=t.stateNode=Yh(t.type,t.pendingProps,ie.current),ft=t,$t=!0,r=ke,Zn(t.type)?(pc=r,ke=Vt(i.firstChild)):ke=r),at(e,t,t.pendingProps.children,a),fu(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&xe&&((r=i=ke)&&(i=Eg(i,t.type,t.pendingProps,$t),i!==null?(t.stateNode=i,ft=t,ke=Vt(i.firstChild),$t=!1,r=!0):r=!1),r||ta(t)),os(t),r=t.type,c=t.pendingProps,d=e!==null?e.memoizedProps:null,i=c.children,fc(r,c)?i=null:d!==null&&fc(r,d)&&(t.flags|=32),t.memoizedState!==null&&(r=or(e,t,Ly,null,null,a),Il._currentValue=r),fu(e,t),at(e,t,i,a),t.child;case 6:return e===null&&xe&&((e=a=ke)&&(a=zg(a,t.pendingProps,$t),a!==null?(t.stateNode=a,ft=t,ke=null,e=!0):e=!1),e||ta(t)),null;case 13:return kd(e,t,a);case 4:return Ce(t,t.stateNode.containerInfo),i=t.pendingProps,e===null?t.child=Qa(t,null,i,a):at(e,t,i,a),t.child;case 11:return Dd(e,t,t.type,t.pendingProps,a);case 7:return at(e,t,t.pendingProps,a),t.child;case 8:return at(e,t,t.pendingProps.children,a),t.child;case 12:return at(e,t,t.pendingProps.children,a),t.child;case 10:return i=t.pendingProps,Sn(t,t.type,i.value),at(e,t,i.children,a),t.child;case 9:return r=t.type._context,i=t.pendingProps.children,aa(t),r=rt(r),i=i(r),t.flags|=1,at(e,t,i,a),t.child;case 14:return Cd(e,t,t.type,t.pendingProps,a);case 15:return Md(e,t,t.type,t.pendingProps,a);case 19:return Qd(e,t,a);case 31:return i=t.pendingProps,a=t.mode,i={mode:i.mode,children:i.children},e===null?(a=du(i,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=an(e.child,i),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return Ud(e,t,a);case 24:return aa(t),i=rt(Fe),e===null?(r=tr(),r===null&&(r=Ae,c=Is(),r.pooledCache=c,c.refCount++,c!==null&&(r.pooledCacheLanes|=a),r=c),t.memoizedState={parent:i,cache:r},ar(t),Sn(t,Fe,r)):((e.lanes&a)!==0&&(lr(e,t),wl(t,null,null,a),Rl()),r=e.memoizedState,c=t.memoizedState,r.parent!==i?(r={parent:i,cache:i},t.memoizedState=r,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=r),Sn(t,Fe,i)):(i=c.cache,Sn(t,Fe,i),i!==r.cache&&Ws(t,[Fe],a,!0))),at(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(s(156,t.tag))}function dn(e){e.flags|=4}function Yd(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Wh(t)){if(t=Bt.current,t!==null&&((ye&4194048)===ye?Ft!==null:(ye&62914560)!==ye&&(ye&536870912)===0||t!==Ft))throw jl=nr,Nf;e.flags|=8192}}function hu(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?So():536870912,e.lanes|=t,Xa|=t)}function Bl(e,t){if(!xe)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var i=null;a!==null;)a.alternate!==null&&(i=a),a=a.sibling;i===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:i.sibling=null}}function qe(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,i=0;if(t)for(var r=e.child;r!==null;)a|=r.lanes|r.childLanes,i|=r.subtreeFlags&65011712,i|=r.flags&65011712,r.return=e,r=r.sibling;else for(r=e.child;r!==null;)a|=r.lanes|r.childLanes,i|=r.subtreeFlags,i|=r.flags,r.return=e,r=r.sibling;return e.subtreeFlags|=i,e.childLanes=a,t}function Jy(e,t,a){var i=t.pendingProps;switch($s(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return qe(t),null;case 1:return qe(t),null;case 3:return a=t.stateNode,i=null,e!==null&&(i=e.memoizedState.cache),t.memoizedState.cache!==i&&(t.flags|=2048),rn(Fe),yn(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(Sl(t)?dn(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,_f())),qe(t),null;case 26:return a=t.memoizedState,e===null?(dn(t),a!==null?(qe(t),Yd(t,a)):(qe(t),t.flags&=-16777217)):a?a!==e.memoizedState?(dn(t),qe(t),Yd(t,a)):(qe(t),t.flags&=-16777217):(e.memoizedProps!==i&&dn(t),qe(t),t.flags&=-16777217),null;case 27:zi(t),a=ie.current;var r=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==i&&dn(t);else{if(!i){if(t.stateNode===null)throw Error(s(166));return qe(t),null}e=W.current,Sl(t)?xf(t):(e=Yh(r,i,a),t.stateNode=e,dn(t))}return qe(t),null;case 5:if(zi(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==i&&dn(t);else{if(!i){if(t.stateNode===null)throw Error(s(166));return qe(t),null}if(e=W.current,Sl(t))xf(t);else{switch(r=Ou(ie.current),e){case 1:e=r.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=r.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=r.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=r.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=r.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof i.is=="string"?r.createElement("select",{is:i.is}):r.createElement("select"),i.multiple?e.multiple=!0:i.size&&(e.size=i.size);break;default:e=typeof i.is=="string"?r.createElement(a,{is:i.is}):r.createElement(a)}}e[st]=t,e[dt]=i;e:for(r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.tag!==27&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break e;for(;r.sibling===null;){if(r.return===null||r.return===t)break e;r=r.return}r.sibling.return=r.return,r=r.sibling}t.stateNode=e;e:switch(it(e,a,i),a){case"button":case"input":case"select":case"textarea":e=!!i.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&dn(t)}}return qe(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==i&&dn(t);else{if(typeof i!="string"&&t.stateNode===null)throw Error(s(166));if(e=ie.current,Sl(t)){if(e=t.stateNode,a=t.memoizedProps,i=null,r=ft,r!==null)switch(r.tag){case 27:case 5:i=r.memoizedProps}e[st]=t,e=!!(e.nodeValue===a||i!==null&&i.suppressHydrationWarning===!0||qh(e.nodeValue,a)),e||ta(t)}else e=Ou(e).createTextNode(i),e[st]=t,t.stateNode=e}return qe(t),null;case 13:if(i=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(r=Sl(t),i!==null&&i.dehydrated!==null){if(e===null){if(!r)throw Error(s(318));if(r=t.memoizedState,r=r!==null?r.dehydrated:null,!r)throw Error(s(317));r[st]=t}else _l(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;qe(t),r=!1}else r=_f(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=r),r=!0;if(!r)return t.flags&256?(on(t),t):(on(t),null)}if(on(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=i!==null,e=e!==null&&e.memoizedState!==null,a){i=t.child,r=null,i.alternate!==null&&i.alternate.memoizedState!==null&&i.alternate.memoizedState.cachePool!==null&&(r=i.alternate.memoizedState.cachePool.pool);var c=null;i.memoizedState!==null&&i.memoizedState.cachePool!==null&&(c=i.memoizedState.cachePool.pool),c!==r&&(i.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),hu(t,t.updateQueue),qe(t),null;case 4:return yn(),e===null&&uc(t.stateNode.containerInfo),qe(t),null;case 10:return rn(t.type),qe(t),null;case 19:if(X(Je),r=t.memoizedState,r===null)return qe(t),null;if(i=(t.flags&128)!==0,c=r.rendering,c===null)if(i)Bl(r,!1);else{if(Le!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=ru(e),c!==null){for(t.flags|=128,Bl(r,!1),e=c.updateQueue,t.updateQueue=e,hu(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)gf(a,e),a=a.sibling;return V(Je,Je.current&1|2),t.child}e=e.sibling}r.tail!==null&&Kt()>vu&&(t.flags|=128,i=!0,Bl(r,!1),t.lanes=4194304)}else{if(!i)if(e=ru(c),e!==null){if(t.flags|=128,i=!0,e=e.updateQueue,t.updateQueue=e,hu(t,e),Bl(r,!0),r.tail===null&&r.tailMode==="hidden"&&!c.alternate&&!xe)return qe(t),null}else 2*Kt()-r.renderingStartTime>vu&&a!==536870912&&(t.flags|=128,i=!0,Bl(r,!1),t.lanes=4194304);r.isBackwards?(c.sibling=t.child,t.child=c):(e=r.last,e!==null?e.sibling=c:t.child=c,r.last=c)}return r.tail!==null?(t=r.tail,r.rendering=t,r.tail=t.sibling,r.renderingStartTime=Kt(),t.sibling=null,e=Je.current,V(Je,i?e&1|2:e&1),t):(qe(t),null);case 22:case 23:return on(t),rr(),i=t.memoizedState!==null,e!==null?e.memoizedState!==null!==i&&(t.flags|=8192):i&&(t.flags|=8192),i?(a&536870912)!==0&&(t.flags&128)===0&&(qe(t),t.subtreeFlags&6&&(t.flags|=8192)):qe(t),a=t.updateQueue,a!==null&&hu(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),i=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),i!==a&&(t.flags|=2048),e!==null&&X(la),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),rn(Fe),qe(t),null;case 25:return null;case 30:return null}throw Error(s(156,t.tag))}function Py(e,t){switch($s(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return rn(Fe),yn(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return zi(t),null;case 13:if(on(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(s(340));_l()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return X(Je),null;case 4:return yn(),null;case 10:return rn(t.type),null;case 22:case 23:return on(t),rr(),e!==null&&X(la),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return rn(Fe),null;case 25:return null;default:return null}}function Vd(e,t){switch($s(t),t.tag){case 3:rn(Fe),yn();break;case 26:case 27:case 5:zi(t);break;case 4:yn();break;case 13:on(t);break;case 19:X(Je);break;case 10:rn(t.type);break;case 22:case 23:on(t),rr(),e!==null&&X(la);break;case 24:rn(Fe)}}function Hl(e,t){try{var a=t.updateQueue,i=a!==null?a.lastEffect:null;if(i!==null){var r=i.next;a=r;do{if((a.tag&e)===e){i=void 0;var c=a.create,d=a.inst;i=c(),d.destroy=i}a=a.next}while(a!==r)}}catch(p){je(t,t.return,p)}}function jn(e,t,a){try{var i=t.updateQueue,r=i!==null?i.lastEffect:null;if(r!==null){var c=r.next;i=c;do{if((i.tag&e)===e){var d=i.inst,p=d.destroy;if(p!==void 0){d.destroy=void 0,r=t;var x=a,O=p;try{O()}catch(C){je(r,x,C)}}}i=i.next}while(i!==c)}}catch(C){je(t,t.return,C)}}function Xd(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{Cf(t,a)}catch(i){je(e,e.return,i)}}}function Kd(e,t,a){a.props=ua(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(i){je(e,t,i)}}function kl(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var i=e.stateNode;break;case 30:i=e.stateNode;break;default:i=e.stateNode}typeof a=="function"?e.refCleanup=a(i):a.current=i}}catch(r){je(e,t,r)}}function Jt(e,t){var a=e.ref,i=e.refCleanup;if(a!==null)if(typeof i=="function")try{i()}catch(r){je(e,t,r)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(r){je(e,t,r)}else a.current=null}function $d(e){var t=e.type,a=e.memoizedProps,i=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&i.focus();break e;case"img":a.src?i.src=a.src:a.srcSet&&(i.srcset=a.srcSet)}}catch(r){je(e,e.return,r)}}function Zr(e,t,a){try{var i=e.stateNode;gg(i,e.type,a,t),i[dt]=t}catch(r){je(e,e.return,r)}}function Fd(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Zn(e.type)||e.tag===4}function qr(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Fd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Zn(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Br(e,t,a){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=Tu));else if(i!==4&&(i===27&&Zn(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(Br(e,t,a),e=e.sibling;e!==null;)Br(e,t,a),e=e.sibling}function mu(e,t,a){var i=e.tag;if(i===5||i===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(i!==4&&(i===27&&Zn(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(mu(e,t,a),e=e.sibling;e!==null;)mu(e,t,a),e=e.sibling}function Jd(e){var t=e.stateNode,a=e.memoizedProps;try{for(var i=e.type,r=t.attributes;r.length;)t.removeAttributeNode(r[0]);it(t,i,a),t[st]=e,t[dt]=a}catch(c){je(e,e.return,c)}}var hn=!1,Ge=!1,Hr=!1,Pd=typeof WeakSet=="function"?WeakSet:Set,Ie=null;function Wy(e,t){if(e=e.containerInfo,cc=Du,e=rf(e),qs(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var i=a.getSelection&&a.getSelection();if(i&&i.rangeCount!==0){a=i.anchorNode;var r=i.anchorOffset,c=i.focusNode;i=i.focusOffset;try{a.nodeType,c.nodeType}catch{a=null;break e}var d=0,p=-1,x=-1,O=0,C=0,B=e,N=null;t:for(;;){for(var j;B!==a||r!==0&&B.nodeType!==3||(p=d+r),B!==c||i!==0&&B.nodeType!==3||(x=d+i),B.nodeType===3&&(d+=B.nodeValue.length),(j=B.firstChild)!==null;)N=B,B=j;for(;;){if(B===e)break t;if(N===a&&++O===r&&(p=d),N===c&&++C===i&&(x=d),(j=B.nextSibling)!==null)break;B=N,N=B.parentNode}B=j}a=p===-1||x===-1?null:{start:p,end:x}}else a=null}a=a||{start:0,end:0}}else a=null;for(oc={focusedElem:e,selectionRange:a},Du=!1,Ie=t;Ie!==null;)if(t=Ie,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Ie=e;else for(;Ie!==null;){switch(t=Ie,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,a=t,r=c.memoizedProps,c=c.memoizedState,i=a.stateNode;try{var ae=ua(a.type,r,a.elementType===a.type);e=i.getSnapshotBeforeUpdate(ae,c),i.__reactInternalSnapshotBeforeUpdate=e}catch(I){je(a,a.return,I)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)hc(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":hc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(s(163))}if(e=t.sibling,e!==null){e.return=t.return,Ie=e;break}Ie=t.return}}function Wd(e,t,a){var i=a.flags;switch(a.tag){case 0:case 11:case 15:An(e,a),i&4&&Hl(5,a);break;case 1:if(An(e,a),i&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(d){je(a,a.return,d)}else{var r=ua(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(r,t,e.__reactInternalSnapshotBeforeUpdate)}catch(d){je(a,a.return,d)}}i&64&&Xd(a),i&512&&kl(a,a.return);break;case 3:if(An(e,a),i&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{Cf(e,t)}catch(d){je(a,a.return,d)}}break;case 27:t===null&&i&4&&Jd(a);case 26:case 5:An(e,a),t===null&&i&4&&$d(a),i&512&&kl(a,a.return);break;case 12:An(e,a);break;case 13:An(e,a),i&4&&th(e,a),i&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=sg.bind(null,a),Tg(e,a))));break;case 22:if(i=a.memoizedState!==null||hn,!i){t=t!==null&&t.memoizedState!==null||Ge,r=hn;var c=Ge;hn=i,(Ge=t)&&!c?Rn(e,a,(a.subtreeFlags&8772)!==0):An(e,a),hn=r,Ge=c}break;case 30:break;default:An(e,a)}}function Id(e){var t=e.alternate;t!==null&&(e.alternate=null,Id(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&gs(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Me=null,pt=!1;function mn(e,t,a){for(a=a.child;a!==null;)eh(e,t,a),a=a.sibling}function eh(e,t,a){if(Et&&typeof Et.onCommitFiberUnmount=="function")try{Et.onCommitFiberUnmount(sl,a)}catch{}switch(a.tag){case 26:Ge||Jt(a,t),mn(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Ge||Jt(a,t);var i=Me,r=pt;Zn(a.type)&&(Me=a.stateNode,pt=!1),mn(e,t,a),Fl(a.stateNode),Me=i,pt=r;break;case 5:Ge||Jt(a,t);case 6:if(i=Me,r=pt,Me=null,mn(e,t,a),Me=i,pt=r,Me!==null)if(pt)try{(Me.nodeType===9?Me.body:Me.nodeName==="HTML"?Me.ownerDocument.body:Me).removeChild(a.stateNode)}catch(c){je(a,t,c)}else try{Me.removeChild(a.stateNode)}catch(c){je(a,t,c)}break;case 18:Me!==null&&(pt?(e=Me,Qh(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),ai(e)):Qh(Me,a.stateNode));break;case 4:i=Me,r=pt,Me=a.stateNode.containerInfo,pt=!0,mn(e,t,a),Me=i,pt=r;break;case 0:case 11:case 14:case 15:Ge||jn(2,a,t),Ge||jn(4,a,t),mn(e,t,a);break;case 1:Ge||(Jt(a,t),i=a.stateNode,typeof i.componentWillUnmount=="function"&&Kd(a,t,i)),mn(e,t,a);break;case 21:mn(e,t,a);break;case 22:Ge=(i=Ge)||a.memoizedState!==null,mn(e,t,a),Ge=i;break;default:mn(e,t,a)}}function th(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{ai(e)}catch(a){je(t,t.return,a)}}function Iy(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Pd),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Pd),t;default:throw Error(s(435,e.tag))}}function kr(e,t){var a=Iy(e);t.forEach(function(i){var r=rg.bind(null,e,i);a.has(i)||(a.add(i),i.then(r,r))})}function Nt(e,t){var a=t.deletions;if(a!==null)for(var i=0;i<a.length;i++){var r=a[i],c=e,d=t,p=d;e:for(;p!==null;){switch(p.tag){case 27:if(Zn(p.type)){Me=p.stateNode,pt=!1;break e}break;case 5:Me=p.stateNode,pt=!1;break e;case 3:case 4:Me=p.stateNode.containerInfo,pt=!0;break e}p=p.return}if(Me===null)throw Error(s(160));eh(c,d,r),Me=null,pt=!1,c=r.alternate,c!==null&&(c.return=null),r.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)nh(t,e),t=t.sibling}var Yt=null;function nh(e,t){var a=e.alternate,i=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Nt(t,e),jt(e),i&4&&(jn(3,e,e.return),Hl(3,e),jn(5,e,e.return));break;case 1:Nt(t,e),jt(e),i&512&&(Ge||a===null||Jt(a,a.return)),i&64&&hn&&(e=e.updateQueue,e!==null&&(i=e.callbacks,i!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?i:a.concat(i))));break;case 26:var r=Yt;if(Nt(t,e),jt(e),i&512&&(Ge||a===null||Jt(a,a.return)),i&4){var c=a!==null?a.memoizedState:null;if(i=e.memoizedState,a===null)if(i===null)if(e.stateNode===null){e:{i=e.type,a=e.memoizedProps,r=r.ownerDocument||r;t:switch(i){case"title":c=r.getElementsByTagName("title")[0],(!c||c[ol]||c[st]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=r.createElement(i),r.head.insertBefore(c,r.querySelector("head > title"))),it(c,i,a),c[st]=e,Pe(c),i=c;break e;case"link":var d=Jh("link","href",r).get(i+(a.href||""));if(d){for(var p=0;p<d.length;p++)if(c=d[p],c.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&c.getAttribute("rel")===(a.rel==null?null:a.rel)&&c.getAttribute("title")===(a.title==null?null:a.title)&&c.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){d.splice(p,1);break t}}c=r.createElement(i),it(c,i,a),r.head.appendChild(c);break;case"meta":if(d=Jh("meta","content",r).get(i+(a.content||""))){for(p=0;p<d.length;p++)if(c=d[p],c.getAttribute("content")===(a.content==null?null:""+a.content)&&c.getAttribute("name")===(a.name==null?null:a.name)&&c.getAttribute("property")===(a.property==null?null:a.property)&&c.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&c.getAttribute("charset")===(a.charSet==null?null:a.charSet)){d.splice(p,1);break t}}c=r.createElement(i),it(c,i,a),r.head.appendChild(c);break;default:throw Error(s(468,i))}c[st]=e,Pe(c),i=c}e.stateNode=i}else Ph(r,e.type,e.stateNode);else e.stateNode=Fh(r,i,e.memoizedProps);else c!==i?(c===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):c.count--,i===null?Ph(r,e.type,e.stateNode):Fh(r,i,e.memoizedProps)):i===null&&e.stateNode!==null&&Zr(e,e.memoizedProps,a.memoizedProps)}break;case 27:Nt(t,e),jt(e),i&512&&(Ge||a===null||Jt(a,a.return)),a!==null&&i&4&&Zr(e,e.memoizedProps,a.memoizedProps);break;case 5:if(Nt(t,e),jt(e),i&512&&(Ge||a===null||Jt(a,a.return)),e.flags&32){r=e.stateNode;try{za(r,"")}catch(j){je(e,e.return,j)}}i&4&&e.stateNode!=null&&(r=e.memoizedProps,Zr(e,r,a!==null?a.memoizedProps:r)),i&1024&&(Hr=!0);break;case 6:if(Nt(t,e),jt(e),i&4){if(e.stateNode===null)throw Error(s(162));i=e.memoizedProps,a=e.stateNode;try{a.nodeValue=i}catch(j){je(e,e.return,j)}}break;case 3:if(Au=null,r=Yt,Yt=Nu(t.containerInfo),Nt(t,e),Yt=r,jt(e),i&4&&a!==null&&a.memoizedState.isDehydrated)try{ai(t.containerInfo)}catch(j){je(e,e.return,j)}Hr&&(Hr=!1,ah(e));break;case 4:i=Yt,Yt=Nu(e.stateNode.containerInfo),Nt(t,e),jt(e),Yt=i;break;case 12:Nt(t,e),jt(e);break;case 13:Nt(t,e),jt(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(Xr=Kt()),i&4&&(i=e.updateQueue,i!==null&&(e.updateQueue=null,kr(e,i)));break;case 22:r=e.memoizedState!==null;var x=a!==null&&a.memoizedState!==null,O=hn,C=Ge;if(hn=O||r,Ge=C||x,Nt(t,e),Ge=C,hn=O,jt(e),i&8192)e:for(t=e.stateNode,t._visibility=r?t._visibility&-2:t._visibility|1,r&&(a===null||x||hn||Ge||sa(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){x=a=t;try{if(c=x.stateNode,r)d=c.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none";else{p=x.stateNode;var B=x.memoizedProps.style,N=B!=null&&B.hasOwnProperty("display")?B.display:null;p.style.display=N==null||typeof N=="boolean"?"":(""+N).trim()}}catch(j){je(x,x.return,j)}}}else if(t.tag===6){if(a===null){x=t;try{x.stateNode.nodeValue=r?"":x.memoizedProps}catch(j){je(x,x.return,j)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}i&4&&(i=e.updateQueue,i!==null&&(a=i.retryQueue,a!==null&&(i.retryQueue=null,kr(e,a))));break;case 19:Nt(t,e),jt(e),i&4&&(i=e.updateQueue,i!==null&&(e.updateQueue=null,kr(e,i)));break;case 30:break;case 21:break;default:Nt(t,e),jt(e)}}function jt(e){var t=e.flags;if(t&2){try{for(var a,i=e.return;i!==null;){if(Fd(i)){a=i;break}i=i.return}if(a==null)throw Error(s(160));switch(a.tag){case 27:var r=a.stateNode,c=qr(e);mu(e,c,r);break;case 5:var d=a.stateNode;a.flags&32&&(za(d,""),a.flags&=-33);var p=qr(e);mu(e,p,d);break;case 3:case 4:var x=a.stateNode.containerInfo,O=qr(e);Br(e,O,x);break;default:throw Error(s(161))}}catch(C){je(e,e.return,C)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function ah(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;ah(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function An(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Wd(e,t.alternate,t),t=t.sibling}function sa(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:jn(4,t,t.return),sa(t);break;case 1:Jt(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&Kd(t,t.return,a),sa(t);break;case 27:Fl(t.stateNode);case 26:case 5:Jt(t,t.return),sa(t);break;case 22:t.memoizedState===null&&sa(t);break;case 30:sa(t);break;default:sa(t)}e=e.sibling}}function Rn(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var i=t.alternate,r=e,c=t,d=c.flags;switch(c.tag){case 0:case 11:case 15:Rn(r,c,a),Hl(4,c);break;case 1:if(Rn(r,c,a),i=c,r=i.stateNode,typeof r.componentDidMount=="function")try{r.componentDidMount()}catch(O){je(i,i.return,O)}if(i=c,r=i.updateQueue,r!==null){var p=i.stateNode;try{var x=r.shared.hiddenCallbacks;if(x!==null)for(r.shared.hiddenCallbacks=null,r=0;r<x.length;r++)Df(x[r],p)}catch(O){je(i,i.return,O)}}a&&d&64&&Xd(c),kl(c,c.return);break;case 27:Jd(c);case 26:case 5:Rn(r,c,a),a&&i===null&&d&4&&$d(c),kl(c,c.return);break;case 12:Rn(r,c,a);break;case 13:Rn(r,c,a),a&&d&4&&th(r,c);break;case 22:c.memoizedState===null&&Rn(r,c,a),kl(c,c.return);break;case 30:break;default:Rn(r,c,a)}t=t.sibling}}function Lr(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&Tl(a))}function Qr(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Tl(e))}function Pt(e,t,a,i){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)lh(e,t,a,i),t=t.sibling}function lh(e,t,a,i){var r=t.flags;switch(t.tag){case 0:case 11:case 15:Pt(e,t,a,i),r&2048&&Hl(9,t);break;case 1:Pt(e,t,a,i);break;case 3:Pt(e,t,a,i),r&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Tl(e)));break;case 12:if(r&2048){Pt(e,t,a,i),e=t.stateNode;try{var c=t.memoizedProps,d=c.id,p=c.onPostCommit;typeof p=="function"&&p(d,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(x){je(t,t.return,x)}}else Pt(e,t,a,i);break;case 13:Pt(e,t,a,i);break;case 23:break;case 22:c=t.stateNode,d=t.alternate,t.memoizedState!==null?c._visibility&2?Pt(e,t,a,i):Ll(e,t):c._visibility&2?Pt(e,t,a,i):(c._visibility|=2,Ga(e,t,a,i,(t.subtreeFlags&10256)!==0)),r&2048&&Lr(d,t);break;case 24:Pt(e,t,a,i),r&2048&&Qr(t.alternate,t);break;default:Pt(e,t,a,i)}}function Ga(e,t,a,i,r){for(r=r&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,d=t,p=a,x=i,O=d.flags;switch(d.tag){case 0:case 11:case 15:Ga(c,d,p,x,r),Hl(8,d);break;case 23:break;case 22:var C=d.stateNode;d.memoizedState!==null?C._visibility&2?Ga(c,d,p,x,r):Ll(c,d):(C._visibility|=2,Ga(c,d,p,x,r)),r&&O&2048&&Lr(d.alternate,d);break;case 24:Ga(c,d,p,x,r),r&&O&2048&&Qr(d.alternate,d);break;default:Ga(c,d,p,x,r)}t=t.sibling}}function Ll(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,i=t,r=i.flags;switch(i.tag){case 22:Ll(a,i),r&2048&&Lr(i.alternate,i);break;case 24:Ll(a,i),r&2048&&Qr(i.alternate,i);break;default:Ll(a,i)}t=t.sibling}}var Ql=8192;function Ya(e){if(e.subtreeFlags&Ql)for(e=e.child;e!==null;)ih(e),e=e.sibling}function ih(e){switch(e.tag){case 26:Ya(e),e.flags&Ql&&e.memoizedState!==null&&Bg(Yt,e.memoizedState,e.memoizedProps);break;case 5:Ya(e);break;case 3:case 4:var t=Yt;Yt=Nu(e.stateNode.containerInfo),Ya(e),Yt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Ql,Ql=16777216,Ya(e),Ql=t):Ya(e));break;default:Ya(e)}}function uh(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Gl(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var i=t[a];Ie=i,rh(i,e)}uh(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)sh(e),e=e.sibling}function sh(e){switch(e.tag){case 0:case 11:case 15:Gl(e),e.flags&2048&&jn(9,e,e.return);break;case 3:Gl(e);break;case 12:Gl(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,pu(e)):Gl(e);break;default:Gl(e)}}function pu(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var i=t[a];Ie=i,rh(i,e)}uh(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:jn(8,t,t.return),pu(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,pu(t));break;default:pu(t)}e=e.sibling}}function rh(e,t){for(;Ie!==null;){var a=Ie;switch(a.tag){case 0:case 11:case 15:jn(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var i=a.memoizedState.cachePool.pool;i!=null&&i.refCount++}break;case 24:Tl(a.memoizedState.cache)}if(i=a.child,i!==null)i.return=a,Ie=i;else e:for(a=e;Ie!==null;){i=Ie;var r=i.sibling,c=i.return;if(Id(i),i===a){Ie=null;break e}if(r!==null){r.return=c,Ie=r;break e}Ie=c}}}var eg={getCacheForType:function(e){var t=rt(Fe),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},tg=typeof WeakMap=="function"?WeakMap:Map,Se=0,Ae=null,he=null,ye=0,_e=0,At=null,wn=!1,Va=!1,Gr=!1,pn=0,Le=0,Dn=0,ra=0,Yr=0,Ht=0,Xa=0,Yl=null,vt=null,Vr=!1,Xr=0,vu=1/0,yu=null,Cn=null,lt=0,Mn=null,Ka=null,$a=0,Kr=0,$r=null,ch=null,Vl=0,Fr=null;function Rt(){if((Se&2)!==0&&ye!==0)return ye&-ye;if(A.T!==null){var e=Ua;return e!==0?e:nc()}return zo()}function oh(){Ht===0&&(Ht=(ye&536870912)===0||xe?xo():536870912);var e=Bt.current;return e!==null&&(e.flags|=32),Ht}function wt(e,t,a){(e===Ae&&(_e===2||_e===9)||e.cancelPendingCommit!==null)&&(Fa(e,0),Un(e,ye,Ht,!1)),cl(e,a),((Se&2)===0||e!==Ae)&&(e===Ae&&((Se&2)===0&&(ra|=a),Le===4&&Un(e,ye,Ht,!1)),Wt(e))}function fh(e,t,a){if((Se&6)!==0)throw Error(s(327));var i=!a&&(t&124)===0&&(t&e.expiredLanes)===0||rl(e,t),r=i?lg(e,t):Wr(e,t,!0),c=i;do{if(r===0){Va&&!i&&Un(e,t,0,!1);break}else{if(a=e.current.alternate,c&&!ng(a)){r=Wr(e,t,!1),c=!1;continue}if(r===2){if(c=t,e.errorRecoveryDisabledLanes&c)var d=0;else d=e.pendingLanes&-536870913,d=d!==0?d:d&536870912?536870912:0;if(d!==0){t=d;e:{var p=e;r=Yl;var x=p.current.memoizedState.isDehydrated;if(x&&(Fa(p,d).flags|=256),d=Wr(p,d,!1),d!==2){if(Gr&&!x){p.errorRecoveryDisabledLanes|=c,ra|=c,r=4;break e}c=vt,vt=r,c!==null&&(vt===null?vt=c:vt.push.apply(vt,c))}r=d}if(c=!1,r!==2)continue}}if(r===1){Fa(e,0),Un(e,t,0,!0);break}e:{switch(i=e,c=r,c){case 0:case 1:throw Error(s(345));case 4:if((t&4194048)!==t)break;case 6:Un(i,t,Ht,!wn);break e;case 2:vt=null;break;case 3:case 5:break;default:throw Error(s(329))}if((t&62914560)===t&&(r=Xr+300-Kt(),10<r)){if(Un(i,t,Ht,!wn),ji(i,0,!0)!==0)break e;i.timeoutHandle=kh(dh.bind(null,i,a,vt,yu,Vr,t,Ht,ra,Xa,wn,c,2,-0,0),r);break e}dh(i,a,vt,yu,Vr,t,Ht,ra,Xa,wn,c,0,-0,0)}}break}while(!0);Wt(e)}function dh(e,t,a,i,r,c,d,p,x,O,C,B,N,j){if(e.timeoutHandle=-1,B=t.subtreeFlags,(B&8192||(B&16785408)===16785408)&&(Wl={stylesheets:null,count:0,unsuspend:qg},ih(t),B=Hg(),B!==null)){e.cancelPendingCommit=B(bh.bind(null,e,t,c,a,i,r,d,p,x,C,1,N,j)),Un(e,c,d,!O);return}bh(e,t,c,a,i,r,d,p,x)}function ng(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var i=0;i<a.length;i++){var r=a[i],c=r.getSnapshot;r=r.value;try{if(!Tt(c(),r))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Un(e,t,a,i){t&=~Yr,t&=~ra,e.suspendedLanes|=t,e.pingedLanes&=~t,i&&(e.warmLanes|=t),i=e.expirationTimes;for(var r=t;0<r;){var c=31-zt(r),d=1<<c;i[c]=-1,r&=~d}a!==0&&_o(e,a,t)}function gu(){return(Se&6)===0?(Xl(0),!1):!0}function Jr(){if(he!==null){if(_e===0)var e=he.return;else e=he,sn=na=null,hr(e),La=null,Zl=0,e=he;for(;e!==null;)Vd(e.alternate,e),e=e.return;he=null}}function Fa(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,xg(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),Jr(),Ae=e,he=a=an(e.current,null),ye=t,_e=0,At=null,wn=!1,Va=rl(e,t),Gr=!1,Xa=Ht=Yr=ra=Dn=Le=0,vt=Yl=null,Vr=!1,(t&8)!==0&&(t|=t&32);var i=e.entangledLanes;if(i!==0)for(e=e.entanglements,i&=t;0<i;){var r=31-zt(i),c=1<<r;t|=e[r],i&=~c}return pn=t,ki(),a}function hh(e,t){oe=null,A.H=iu,t===Nl||t===Fi?(t=Rf(),_e=3):t===Nf?(t=Rf(),_e=4):_e=t===wd?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,At=t,he===null&&(Le=1,ou(e,Mt(t,e.current)))}function mh(){var e=A.H;return A.H=iu,e===null?iu:e}function ph(){var e=A.A;return A.A=eg,e}function Pr(){Le=4,wn||(ye&4194048)!==ye&&Bt.current!==null||(Va=!0),(Dn&134217727)===0&&(ra&134217727)===0||Ae===null||Un(Ae,ye,Ht,!1)}function Wr(e,t,a){var i=Se;Se|=2;var r=mh(),c=ph();(Ae!==e||ye!==t)&&(yu=null,Fa(e,t)),t=!1;var d=Le;e:do try{if(_e!==0&&he!==null){var p=he,x=At;switch(_e){case 8:Jr(),d=6;break e;case 3:case 2:case 9:case 6:Bt.current===null&&(t=!0);var O=_e;if(_e=0,At=null,Ja(e,p,x,O),a&&Va){d=0;break e}break;default:O=_e,_e=0,At=null,Ja(e,p,x,O)}}ag(),d=Le;break}catch(C){hh(e,C)}while(!0);return t&&e.shellSuspendCounter++,sn=na=null,Se=i,A.H=r,A.A=c,he===null&&(Ae=null,ye=0,ki()),d}function ag(){for(;he!==null;)vh(he)}function lg(e,t){var a=Se;Se|=2;var i=mh(),r=ph();Ae!==e||ye!==t?(yu=null,vu=Kt()+500,Fa(e,t)):Va=rl(e,t);e:do try{if(_e!==0&&he!==null){t=he;var c=At;t:switch(_e){case 1:_e=0,At=null,Ja(e,t,c,1);break;case 2:case 9:if(jf(c)){_e=0,At=null,yh(t);break}t=function(){_e!==2&&_e!==9||Ae!==e||(_e=7),Wt(e)},c.then(t,t);break e;case 3:_e=7;break e;case 4:_e=5;break e;case 7:jf(c)?(_e=0,At=null,yh(t)):(_e=0,At=null,Ja(e,t,c,7));break;case 5:var d=null;switch(he.tag){case 26:d=he.memoizedState;case 5:case 27:var p=he;if(!d||Wh(d)){_e=0,At=null;var x=p.sibling;if(x!==null)he=x;else{var O=p.return;O!==null?(he=O,bu(O)):he=null}break t}}_e=0,At=null,Ja(e,t,c,5);break;case 6:_e=0,At=null,Ja(e,t,c,6);break;case 8:Jr(),Le=6;break e;default:throw Error(s(462))}}ig();break}catch(C){hh(e,C)}while(!0);return sn=na=null,A.H=i,A.A=r,Se=a,he!==null?0:(Ae=null,ye=0,ki(),Le)}function ig(){for(;he!==null&&!Nv();)vh(he)}function vh(e){var t=Gd(e.alternate,e,pn);e.memoizedProps=e.pendingProps,t===null?bu(e):he=t}function yh(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=qd(a,t,t.pendingProps,t.type,void 0,ye);break;case 11:t=qd(a,t,t.pendingProps,t.type.render,t.ref,ye);break;case 5:hr(t);default:Vd(a,t),t=he=gf(t,pn),t=Gd(a,t,pn)}e.memoizedProps=e.pendingProps,t===null?bu(e):he=t}function Ja(e,t,a,i){sn=na=null,hr(t),La=null,Zl=0;var r=t.return;try{if($y(e,r,t,a,ye)){Le=1,ou(e,Mt(a,e.current)),he=null;return}}catch(c){if(r!==null)throw he=r,c;Le=1,ou(e,Mt(a,e.current)),he=null;return}t.flags&32768?(xe||i===1?e=!0:Va||(ye&536870912)!==0?e=!1:(wn=e=!0,(i===2||i===9||i===3||i===6)&&(i=Bt.current,i!==null&&i.tag===13&&(i.flags|=16384))),gh(t,e)):bu(t)}function bu(e){var t=e;do{if((t.flags&32768)!==0){gh(t,wn);return}e=t.return;var a=Jy(t.alternate,t,pn);if(a!==null){he=a;return}if(t=t.sibling,t!==null){he=t;return}he=t=e}while(t!==null);Le===0&&(Le=5)}function gh(e,t){do{var a=Py(e.alternate,e);if(a!==null){a.flags&=32767,he=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){he=e;return}he=e=a}while(e!==null);Le=6,he=null}function bh(e,t,a,i,r,c,d,p,x){e.cancelPendingCommit=null;do xu();while(lt!==0);if((Se&6)!==0)throw Error(s(327));if(t!==null){if(t===e.current)throw Error(s(177));if(c=t.lanes|t.childLanes,c|=Qs,qv(e,a,c,d,p,x),e===Ae&&(he=Ae=null,ye=0),Ka=t,Mn=e,$a=a,Kr=c,$r=r,ch=i,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,cg(Ti,function(){return zh(),null})):(e.callbackNode=null,e.callbackPriority=0),i=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||i){i=A.T,A.T=null,r=Y.p,Y.p=2,d=Se,Se|=4;try{Wy(e,t,a)}finally{Se=d,Y.p=r,A.T=i}}lt=1,xh(),Sh(),_h()}}function xh(){if(lt===1){lt=0;var e=Mn,t=Ka,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=A.T,A.T=null;var i=Y.p;Y.p=2;var r=Se;Se|=4;try{nh(t,e);var c=oc,d=rf(e.containerInfo),p=c.focusedElem,x=c.selectionRange;if(d!==p&&p&&p.ownerDocument&&sf(p.ownerDocument.documentElement,p)){if(x!==null&&qs(p)){var O=x.start,C=x.end;if(C===void 0&&(C=O),"selectionStart"in p)p.selectionStart=O,p.selectionEnd=Math.min(C,p.value.length);else{var B=p.ownerDocument||document,N=B&&B.defaultView||window;if(N.getSelection){var j=N.getSelection(),ae=p.textContent.length,I=Math.min(x.start,ae),Te=x.end===void 0?I:Math.min(x.end,ae);!j.extend&&I>Te&&(d=Te,Te=I,I=d);var E=uf(p,I),_=uf(p,Te);if(E&&_&&(j.rangeCount!==1||j.anchorNode!==E.node||j.anchorOffset!==E.offset||j.focusNode!==_.node||j.focusOffset!==_.offset)){var T=B.createRange();T.setStart(E.node,E.offset),j.removeAllRanges(),I>Te?(j.addRange(T),j.extend(_.node,_.offset)):(T.setEnd(_.node,_.offset),j.addRange(T))}}}}for(B=[],j=p;j=j.parentNode;)j.nodeType===1&&B.push({element:j,left:j.scrollLeft,top:j.scrollTop});for(typeof p.focus=="function"&&p.focus(),p=0;p<B.length;p++){var U=B[p];U.element.scrollLeft=U.left,U.element.scrollTop=U.top}}Du=!!cc,oc=cc=null}finally{Se=r,Y.p=i,A.T=a}}e.current=t,lt=2}}function Sh(){if(lt===2){lt=0;var e=Mn,t=Ka,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=A.T,A.T=null;var i=Y.p;Y.p=2;var r=Se;Se|=4;try{Wd(e,t.alternate,t)}finally{Se=r,Y.p=i,A.T=a}}lt=3}}function _h(){if(lt===4||lt===3){lt=0,jv();var e=Mn,t=Ka,a=$a,i=ch;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?lt=5:(lt=0,Ka=Mn=null,Eh(e,e.pendingLanes));var r=e.pendingLanes;if(r===0&&(Cn=null),vs(a),t=t.stateNode,Et&&typeof Et.onCommitFiberRoot=="function")try{Et.onCommitFiberRoot(sl,t,void 0,(t.current.flags&128)===128)}catch{}if(i!==null){t=A.T,r=Y.p,Y.p=2,A.T=null;try{for(var c=e.onRecoverableError,d=0;d<i.length;d++){var p=i[d];c(p.value,{componentStack:p.stack})}}finally{A.T=t,Y.p=r}}($a&3)!==0&&xu(),Wt(e),r=e.pendingLanes,(a&4194090)!==0&&(r&42)!==0?e===Fr?Vl++:(Vl=0,Fr=e):Vl=0,Xl(0)}}function Eh(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Tl(t)))}function xu(e){return xh(),Sh(),_h(),zh()}function zh(){if(lt!==5)return!1;var e=Mn,t=Kr;Kr=0;var a=vs($a),i=A.T,r=Y.p;try{Y.p=32>a?32:a,A.T=null,a=$r,$r=null;var c=Mn,d=$a;if(lt=0,Ka=Mn=null,$a=0,(Se&6)!==0)throw Error(s(331));var p=Se;if(Se|=4,sh(c.current),lh(c,c.current,d,a),Se=p,Xl(0,!1),Et&&typeof Et.onPostCommitFiberRoot=="function")try{Et.onPostCommitFiberRoot(sl,c)}catch{}return!0}finally{Y.p=r,A.T=i,Eh(e,t)}}function Th(e,t,a){t=Mt(a,t),t=Nr(e.stateNode,t,2),e=zn(e,t,2),e!==null&&(cl(e,2),Wt(e))}function je(e,t,a){if(e.tag===3)Th(e,e,a);else for(;t!==null;){if(t.tag===3){Th(t,e,a);break}else if(t.tag===1){var i=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(Cn===null||!Cn.has(i))){e=Mt(a,e),a=Ad(2),i=zn(t,a,2),i!==null&&(Rd(a,i,t,e),cl(i,2),Wt(i));break}}t=t.return}}function Ir(e,t,a){var i=e.pingCache;if(i===null){i=e.pingCache=new tg;var r=new Set;i.set(t,r)}else r=i.get(t),r===void 0&&(r=new Set,i.set(t,r));r.has(a)||(Gr=!0,r.add(a),e=ug.bind(null,e,t,a),t.then(e,e))}function ug(e,t,a){var i=e.pingCache;i!==null&&i.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,Ae===e&&(ye&a)===a&&(Le===4||Le===3&&(ye&62914560)===ye&&300>Kt()-Xr?(Se&2)===0&&Fa(e,0):Yr|=a,Xa===ye&&(Xa=0)),Wt(e)}function Oh(e,t){t===0&&(t=So()),e=wa(e,t),e!==null&&(cl(e,t),Wt(e))}function sg(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),Oh(e,a)}function rg(e,t){var a=0;switch(e.tag){case 13:var i=e.stateNode,r=e.memoizedState;r!==null&&(a=r.retryLane);break;case 19:i=e.stateNode;break;case 22:i=e.stateNode._retryCache;break;default:throw Error(s(314))}i!==null&&i.delete(t),Oh(e,a)}function cg(e,t){return ds(e,t)}var Su=null,Pa=null,ec=!1,_u=!1,tc=!1,ca=0;function Wt(e){e!==Pa&&e.next===null&&(Pa===null?Su=Pa=e:Pa=Pa.next=e),_u=!0,ec||(ec=!0,fg())}function Xl(e,t){if(!tc&&_u){tc=!0;do for(var a=!1,i=Su;i!==null;){if(e!==0){var r=i.pendingLanes;if(r===0)var c=0;else{var d=i.suspendedLanes,p=i.pingedLanes;c=(1<<31-zt(42|e)+1)-1,c&=r&~(d&~p),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(a=!0,Rh(i,c))}else c=ye,c=ji(i,i===Ae?c:0,i.cancelPendingCommit!==null||i.timeoutHandle!==-1),(c&3)===0||rl(i,c)||(a=!0,Rh(i,c));i=i.next}while(a);tc=!1}}function og(){Nh()}function Nh(){_u=ec=!1;var e=0;ca!==0&&(bg()&&(e=ca),ca=0);for(var t=Kt(),a=null,i=Su;i!==null;){var r=i.next,c=jh(i,t);c===0?(i.next=null,a===null?Su=r:a.next=r,r===null&&(Pa=a)):(a=i,(e!==0||(c&3)!==0)&&(_u=!0)),i=r}Xl(e)}function jh(e,t){for(var a=e.suspendedLanes,i=e.pingedLanes,r=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var d=31-zt(c),p=1<<d,x=r[d];x===-1?((p&a)===0||(p&i)!==0)&&(r[d]=Zv(p,t)):x<=t&&(e.expiredLanes|=p),c&=~p}if(t=Ae,a=ye,a=ji(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),i=e.callbackNode,a===0||e===t&&(_e===2||_e===9)||e.cancelPendingCommit!==null)return i!==null&&i!==null&&hs(i),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||rl(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(i!==null&&hs(i),vs(a)){case 2:case 8:a=go;break;case 32:a=Ti;break;case 268435456:a=bo;break;default:a=Ti}return i=Ah.bind(null,e),a=ds(a,i),e.callbackPriority=t,e.callbackNode=a,t}return i!==null&&i!==null&&hs(i),e.callbackPriority=2,e.callbackNode=null,2}function Ah(e,t){if(lt!==0&&lt!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(xu()&&e.callbackNode!==a)return null;var i=ye;return i=ji(e,e===Ae?i:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),i===0?null:(fh(e,i,t),jh(e,Kt()),e.callbackNode!=null&&e.callbackNode===a?Ah.bind(null,e):null)}function Rh(e,t){if(xu())return null;fh(e,t,!0)}function fg(){Sg(function(){(Se&6)!==0?ds(yo,og):Nh()})}function nc(){return ca===0&&(ca=xo()),ca}function wh(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Ci(""+e)}function Dh(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function dg(e,t,a,i,r){if(t==="submit"&&a&&a.stateNode===r){var c=wh((r[dt]||null).action),d=i.submitter;d&&(t=(t=d[dt]||null)?wh(t.formAction):d.getAttribute("formAction"),t!==null&&(c=t,d=null));var p=new qi("action","action",null,i,r);e.push({event:p,listeners:[{instance:null,listener:function(){if(i.defaultPrevented){if(ca!==0){var x=d?Dh(r,d):new FormData(r);_r(a,{pending:!0,data:x,method:r.method,action:c},null,x)}}else typeof c=="function"&&(p.preventDefault(),x=d?Dh(r,d):new FormData(r),_r(a,{pending:!0,data:x,method:r.method,action:c},c,x))},currentTarget:r}]})}}for(var ac=0;ac<Ls.length;ac++){var lc=Ls[ac],hg=lc.toLowerCase(),mg=lc[0].toUpperCase()+lc.slice(1);Gt(hg,"on"+mg)}Gt(ff,"onAnimationEnd"),Gt(df,"onAnimationIteration"),Gt(hf,"onAnimationStart"),Gt("dblclick","onDoubleClick"),Gt("focusin","onFocus"),Gt("focusout","onBlur"),Gt(wy,"onTransitionRun"),Gt(Dy,"onTransitionStart"),Gt(Cy,"onTransitionCancel"),Gt(mf,"onTransitionEnd"),Sa("onMouseEnter",["mouseout","mouseover"]),Sa("onMouseLeave",["mouseout","mouseover"]),Sa("onPointerEnter",["pointerout","pointerover"]),Sa("onPointerLeave",["pointerout","pointerover"]),Kn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Kn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Kn("onBeforeInput",["compositionend","keypress","textInput","paste"]),Kn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Kn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Kn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Kl="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),pg=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Kl));function Ch(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var i=e[a],r=i.event;i=i.listeners;e:{var c=void 0;if(t)for(var d=i.length-1;0<=d;d--){var p=i[d],x=p.instance,O=p.currentTarget;if(p=p.listener,x!==c&&r.isPropagationStopped())break e;c=p,r.currentTarget=O;try{c(r)}catch(C){cu(C)}r.currentTarget=null,c=x}else for(d=0;d<i.length;d++){if(p=i[d],x=p.instance,O=p.currentTarget,p=p.listener,x!==c&&r.isPropagationStopped())break e;c=p,r.currentTarget=O;try{c(r)}catch(C){cu(C)}r.currentTarget=null,c=x}}}}function me(e,t){var a=t[ys];a===void 0&&(a=t[ys]=new Set);var i=e+"__bubble";a.has(i)||(Mh(t,e,2,!1),a.add(i))}function ic(e,t,a){var i=0;t&&(i|=4),Mh(a,e,i,t)}var Eu="_reactListening"+Math.random().toString(36).slice(2);function uc(e){if(!e[Eu]){e[Eu]=!0,Oo.forEach(function(a){a!=="selectionchange"&&(pg.has(a)||ic(a,!1,e),ic(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Eu]||(t[Eu]=!0,ic("selectionchange",!1,t))}}function Mh(e,t,a,i){switch(lm(t)){case 2:var r=Qg;break;case 8:r=Gg;break;default:r=xc}a=r.bind(null,t,a,e),r=void 0,!js||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(r=!0),i?r!==void 0?e.addEventListener(t,a,{capture:!0,passive:r}):e.addEventListener(t,a,!0):r!==void 0?e.addEventListener(t,a,{passive:r}):e.addEventListener(t,a,!1)}function sc(e,t,a,i,r){var c=i;if((t&1)===0&&(t&2)===0&&i!==null)e:for(;;){if(i===null)return;var d=i.tag;if(d===3||d===4){var p=i.stateNode.containerInfo;if(p===r)break;if(d===4)for(d=i.return;d!==null;){var x=d.tag;if((x===3||x===4)&&d.stateNode.containerInfo===r)return;d=d.return}for(;p!==null;){if(d=ga(p),d===null)return;if(x=d.tag,x===5||x===6||x===26||x===27){i=c=d;continue e}p=p.parentNode}}i=i.return}ko(function(){var O=c,C=Os(a),B=[];e:{var N=pf.get(e);if(N!==void 0){var j=qi,ae=e;switch(e){case"keypress":if(Ui(a)===0)break e;case"keydown":case"keyup":j=cy;break;case"focusin":ae="focus",j=Ds;break;case"focusout":ae="blur",j=Ds;break;case"beforeblur":case"afterblur":j=Ds;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":j=Go;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":j=Pv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":j=dy;break;case ff:case df:case hf:j=ey;break;case mf:j=my;break;case"scroll":case"scrollend":j=Fv;break;case"wheel":j=vy;break;case"copy":case"cut":case"paste":j=ny;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":j=Vo;break;case"toggle":case"beforetoggle":j=gy}var I=(t&4)!==0,Te=!I&&(e==="scroll"||e==="scrollend"),E=I?N!==null?N+"Capture":null:N;I=[];for(var _=O,T;_!==null;){var U=_;if(T=U.stateNode,U=U.tag,U!==5&&U!==26&&U!==27||T===null||E===null||(U=dl(_,E),U!=null&&I.push($l(_,U,T))),Te)break;_=_.return}0<I.length&&(N=new j(N,ae,null,a,C),B.push({event:N,listeners:I}))}}if((t&7)===0){e:{if(N=e==="mouseover"||e==="pointerover",j=e==="mouseout"||e==="pointerout",N&&a!==Ts&&(ae=a.relatedTarget||a.fromElement)&&(ga(ae)||ae[ya]))break e;if((j||N)&&(N=C.window===C?C:(N=C.ownerDocument)?N.defaultView||N.parentWindow:window,j?(ae=a.relatedTarget||a.toElement,j=O,ae=ae?ga(ae):null,ae!==null&&(Te=f(ae),I=ae.tag,ae!==Te||I!==5&&I!==27&&I!==6)&&(ae=null)):(j=null,ae=O),j!==ae)){if(I=Go,U="onMouseLeave",E="onMouseEnter",_="mouse",(e==="pointerout"||e==="pointerover")&&(I=Vo,U="onPointerLeave",E="onPointerEnter",_="pointer"),Te=j==null?N:fl(j),T=ae==null?N:fl(ae),N=new I(U,_+"leave",j,a,C),N.target=Te,N.relatedTarget=T,U=null,ga(C)===O&&(I=new I(E,_+"enter",ae,a,C),I.target=T,I.relatedTarget=Te,U=I),Te=U,j&&ae)t:{for(I=j,E=ae,_=0,T=I;T;T=Wa(T))_++;for(T=0,U=E;U;U=Wa(U))T++;for(;0<_-T;)I=Wa(I),_--;for(;0<T-_;)E=Wa(E),T--;for(;_--;){if(I===E||E!==null&&I===E.alternate)break t;I=Wa(I),E=Wa(E)}I=null}else I=null;j!==null&&Uh(B,N,j,I,!1),ae!==null&&Te!==null&&Uh(B,Te,ae,I,!0)}}e:{if(N=O?fl(O):window,j=N.nodeName&&N.nodeName.toLowerCase(),j==="select"||j==="input"&&N.type==="file")var $=Io;else if(Po(N))if(ef)$=jy;else{$=Oy;var fe=Ty}else j=N.nodeName,!j||j.toLowerCase()!=="input"||N.type!=="checkbox"&&N.type!=="radio"?O&&zs(O.elementType)&&($=Io):$=Ny;if($&&($=$(e,O))){Wo(B,$,a,C);break e}fe&&fe(e,N,O),e==="focusout"&&O&&N.type==="number"&&O.memoizedProps.value!=null&&Es(N,"number",N.value)}switch(fe=O?fl(O):window,e){case"focusin":(Po(fe)||fe.contentEditable==="true")&&(ja=fe,Bs=O,xl=null);break;case"focusout":xl=Bs=ja=null;break;case"mousedown":Hs=!0;break;case"contextmenu":case"mouseup":case"dragend":Hs=!1,cf(B,a,C);break;case"selectionchange":if(Ry)break;case"keydown":case"keyup":cf(B,a,C)}var F;if(Ms)e:{switch(e){case"compositionstart":var te="onCompositionStart";break e;case"compositionend":te="onCompositionEnd";break e;case"compositionupdate":te="onCompositionUpdate";break e}te=void 0}else Na?Fo(e,a)&&(te="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(te="onCompositionStart");te&&(Xo&&a.locale!=="ko"&&(Na||te!=="onCompositionStart"?te==="onCompositionEnd"&&Na&&(F=Lo()):(xn=C,As="value"in xn?xn.value:xn.textContent,Na=!0)),fe=zu(O,te),0<fe.length&&(te=new Yo(te,e,null,a,C),B.push({event:te,listeners:fe}),F?te.data=F:(F=Jo(a),F!==null&&(te.data=F)))),(F=xy?Sy(e,a):_y(e,a))&&(te=zu(O,"onBeforeInput"),0<te.length&&(fe=new Yo("onBeforeInput","beforeinput",null,a,C),B.push({event:fe,listeners:te}),fe.data=F)),dg(B,e,O,a,C)}Ch(B,t)})}function $l(e,t,a){return{instance:e,listener:t,currentTarget:a}}function zu(e,t){for(var a=t+"Capture",i=[];e!==null;){var r=e,c=r.stateNode;if(r=r.tag,r!==5&&r!==26&&r!==27||c===null||(r=dl(e,a),r!=null&&i.unshift($l(e,r,c)),r=dl(e,t),r!=null&&i.push($l(e,r,c))),e.tag===3)return i;e=e.return}return[]}function Wa(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Uh(e,t,a,i,r){for(var c=t._reactName,d=[];a!==null&&a!==i;){var p=a,x=p.alternate,O=p.stateNode;if(p=p.tag,x!==null&&x===i)break;p!==5&&p!==26&&p!==27||O===null||(x=O,r?(O=dl(a,c),O!=null&&d.unshift($l(a,O,x))):r||(O=dl(a,c),O!=null&&d.push($l(a,O,x)))),a=a.return}d.length!==0&&e.push({event:t,listeners:d})}var vg=/\r\n?/g,yg=/\u0000|\uFFFD/g;function Zh(e){return(typeof e=="string"?e:""+e).replace(vg,`
`).replace(yg,"")}function qh(e,t){return t=Zh(t),Zh(e)===t}function Tu(){}function ze(e,t,a,i,r,c){switch(a){case"children":typeof i=="string"?t==="body"||t==="textarea"&&i===""||za(e,i):(typeof i=="number"||typeof i=="bigint")&&t!=="body"&&za(e,""+i);break;case"className":Ri(e,"class",i);break;case"tabIndex":Ri(e,"tabindex",i);break;case"dir":case"role":case"viewBox":case"width":case"height":Ri(e,a,i);break;case"style":Bo(e,i,c);break;case"data":if(t!=="object"){Ri(e,"data",i);break}case"src":case"href":if(i===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(i==null||typeof i=="function"||typeof i=="symbol"||typeof i=="boolean"){e.removeAttribute(a);break}i=Ci(""+i),e.setAttribute(a,i);break;case"action":case"formAction":if(typeof i=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(a==="formAction"?(t!=="input"&&ze(e,t,"name",r.name,r,null),ze(e,t,"formEncType",r.formEncType,r,null),ze(e,t,"formMethod",r.formMethod,r,null),ze(e,t,"formTarget",r.formTarget,r,null)):(ze(e,t,"encType",r.encType,r,null),ze(e,t,"method",r.method,r,null),ze(e,t,"target",r.target,r,null)));if(i==null||typeof i=="symbol"||typeof i=="boolean"){e.removeAttribute(a);break}i=Ci(""+i),e.setAttribute(a,i);break;case"onClick":i!=null&&(e.onclick=Tu);break;case"onScroll":i!=null&&me("scroll",e);break;case"onScrollEnd":i!=null&&me("scrollend",e);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(s(61));if(a=i.__html,a!=null){if(r.children!=null)throw Error(s(60));e.innerHTML=a}}break;case"multiple":e.multiple=i&&typeof i!="function"&&typeof i!="symbol";break;case"muted":e.muted=i&&typeof i!="function"&&typeof i!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(i==null||typeof i=="function"||typeof i=="boolean"||typeof i=="symbol"){e.removeAttribute("xlink:href");break}a=Ci(""+i),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":i!=null&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(a,""+i):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":i&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":i===!0?e.setAttribute(a,""):i!==!1&&i!=null&&typeof i!="function"&&typeof i!="symbol"?e.setAttribute(a,i):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":i!=null&&typeof i!="function"&&typeof i!="symbol"&&!isNaN(i)&&1<=i?e.setAttribute(a,i):e.removeAttribute(a);break;case"rowSpan":case"start":i==null||typeof i=="function"||typeof i=="symbol"||isNaN(i)?e.removeAttribute(a):e.setAttribute(a,i);break;case"popover":me("beforetoggle",e),me("toggle",e),Ai(e,"popover",i);break;case"xlinkActuate":tn(e,"http://www.w3.org/1999/xlink","xlink:actuate",i);break;case"xlinkArcrole":tn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",i);break;case"xlinkRole":tn(e,"http://www.w3.org/1999/xlink","xlink:role",i);break;case"xlinkShow":tn(e,"http://www.w3.org/1999/xlink","xlink:show",i);break;case"xlinkTitle":tn(e,"http://www.w3.org/1999/xlink","xlink:title",i);break;case"xlinkType":tn(e,"http://www.w3.org/1999/xlink","xlink:type",i);break;case"xmlBase":tn(e,"http://www.w3.org/XML/1998/namespace","xml:base",i);break;case"xmlLang":tn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",i);break;case"xmlSpace":tn(e,"http://www.w3.org/XML/1998/namespace","xml:space",i);break;case"is":Ai(e,"is",i);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=Kv.get(a)||a,Ai(e,a,i))}}function rc(e,t,a,i,r,c){switch(a){case"style":Bo(e,i,c);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(s(61));if(a=i.__html,a!=null){if(r.children!=null)throw Error(s(60));e.innerHTML=a}}break;case"children":typeof i=="string"?za(e,i):(typeof i=="number"||typeof i=="bigint")&&za(e,""+i);break;case"onScroll":i!=null&&me("scroll",e);break;case"onScrollEnd":i!=null&&me("scrollend",e);break;case"onClick":i!=null&&(e.onclick=Tu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!No.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(r=a.endsWith("Capture"),t=a.slice(2,r?a.length-7:void 0),c=e[dt]||null,c=c!=null?c[a]:null,typeof c=="function"&&e.removeEventListener(t,c,r),typeof i=="function")){typeof c!="function"&&c!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,i,r);break e}a in e?e[a]=i:i===!0?e.setAttribute(a,""):Ai(e,a,i)}}}function it(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":me("error",e),me("load",e);var i=!1,r=!1,c;for(c in a)if(a.hasOwnProperty(c)){var d=a[c];if(d!=null)switch(c){case"src":i=!0;break;case"srcSet":r=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:ze(e,t,c,d,a,null)}}r&&ze(e,t,"srcSet",a.srcSet,a,null),i&&ze(e,t,"src",a.src,a,null);return;case"input":me("invalid",e);var p=c=d=r=null,x=null,O=null;for(i in a)if(a.hasOwnProperty(i)){var C=a[i];if(C!=null)switch(i){case"name":r=C;break;case"type":d=C;break;case"checked":x=C;break;case"defaultChecked":O=C;break;case"value":c=C;break;case"defaultValue":p=C;break;case"children":case"dangerouslySetInnerHTML":if(C!=null)throw Error(s(137,t));break;default:ze(e,t,i,C,a,null)}}Mo(e,c,p,x,O,d,r,!1),wi(e);return;case"select":me("invalid",e),i=d=c=null;for(r in a)if(a.hasOwnProperty(r)&&(p=a[r],p!=null))switch(r){case"value":c=p;break;case"defaultValue":d=p;break;case"multiple":i=p;default:ze(e,t,r,p,a,null)}t=c,a=d,e.multiple=!!i,t!=null?Ea(e,!!i,t,!1):a!=null&&Ea(e,!!i,a,!0);return;case"textarea":me("invalid",e),c=r=i=null;for(d in a)if(a.hasOwnProperty(d)&&(p=a[d],p!=null))switch(d){case"value":i=p;break;case"defaultValue":r=p;break;case"children":c=p;break;case"dangerouslySetInnerHTML":if(p!=null)throw Error(s(91));break;default:ze(e,t,d,p,a,null)}Zo(e,i,r,c),wi(e);return;case"option":for(x in a)if(a.hasOwnProperty(x)&&(i=a[x],i!=null))switch(x){case"selected":e.selected=i&&typeof i!="function"&&typeof i!="symbol";break;default:ze(e,t,x,i,a,null)}return;case"dialog":me("beforetoggle",e),me("toggle",e),me("cancel",e),me("close",e);break;case"iframe":case"object":me("load",e);break;case"video":case"audio":for(i=0;i<Kl.length;i++)me(Kl[i],e);break;case"image":me("error",e),me("load",e);break;case"details":me("toggle",e);break;case"embed":case"source":case"link":me("error",e),me("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(O in a)if(a.hasOwnProperty(O)&&(i=a[O],i!=null))switch(O){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:ze(e,t,O,i,a,null)}return;default:if(zs(t)){for(C in a)a.hasOwnProperty(C)&&(i=a[C],i!==void 0&&rc(e,t,C,i,a,void 0));return}}for(p in a)a.hasOwnProperty(p)&&(i=a[p],i!=null&&ze(e,t,p,i,a,null))}function gg(e,t,a,i){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var r=null,c=null,d=null,p=null,x=null,O=null,C=null;for(j in a){var B=a[j];if(a.hasOwnProperty(j)&&B!=null)switch(j){case"checked":break;case"value":break;case"defaultValue":x=B;default:i.hasOwnProperty(j)||ze(e,t,j,null,i,B)}}for(var N in i){var j=i[N];if(B=a[N],i.hasOwnProperty(N)&&(j!=null||B!=null))switch(N){case"type":c=j;break;case"name":r=j;break;case"checked":O=j;break;case"defaultChecked":C=j;break;case"value":d=j;break;case"defaultValue":p=j;break;case"children":case"dangerouslySetInnerHTML":if(j!=null)throw Error(s(137,t));break;default:j!==B&&ze(e,t,N,j,i,B)}}_s(e,d,p,x,O,C,c,r);return;case"select":j=d=p=N=null;for(c in a)if(x=a[c],a.hasOwnProperty(c)&&x!=null)switch(c){case"value":break;case"multiple":j=x;default:i.hasOwnProperty(c)||ze(e,t,c,null,i,x)}for(r in i)if(c=i[r],x=a[r],i.hasOwnProperty(r)&&(c!=null||x!=null))switch(r){case"value":N=c;break;case"defaultValue":p=c;break;case"multiple":d=c;default:c!==x&&ze(e,t,r,c,i,x)}t=p,a=d,i=j,N!=null?Ea(e,!!a,N,!1):!!i!=!!a&&(t!=null?Ea(e,!!a,t,!0):Ea(e,!!a,a?[]:"",!1));return;case"textarea":j=N=null;for(p in a)if(r=a[p],a.hasOwnProperty(p)&&r!=null&&!i.hasOwnProperty(p))switch(p){case"value":break;case"children":break;default:ze(e,t,p,null,i,r)}for(d in i)if(r=i[d],c=a[d],i.hasOwnProperty(d)&&(r!=null||c!=null))switch(d){case"value":N=r;break;case"defaultValue":j=r;break;case"children":break;case"dangerouslySetInnerHTML":if(r!=null)throw Error(s(91));break;default:r!==c&&ze(e,t,d,r,i,c)}Uo(e,N,j);return;case"option":for(var ae in a)if(N=a[ae],a.hasOwnProperty(ae)&&N!=null&&!i.hasOwnProperty(ae))switch(ae){case"selected":e.selected=!1;break;default:ze(e,t,ae,null,i,N)}for(x in i)if(N=i[x],j=a[x],i.hasOwnProperty(x)&&N!==j&&(N!=null||j!=null))switch(x){case"selected":e.selected=N&&typeof N!="function"&&typeof N!="symbol";break;default:ze(e,t,x,N,i,j)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var I in a)N=a[I],a.hasOwnProperty(I)&&N!=null&&!i.hasOwnProperty(I)&&ze(e,t,I,null,i,N);for(O in i)if(N=i[O],j=a[O],i.hasOwnProperty(O)&&N!==j&&(N!=null||j!=null))switch(O){case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(s(137,t));break;default:ze(e,t,O,N,i,j)}return;default:if(zs(t)){for(var Te in a)N=a[Te],a.hasOwnProperty(Te)&&N!==void 0&&!i.hasOwnProperty(Te)&&rc(e,t,Te,void 0,i,N);for(C in i)N=i[C],j=a[C],!i.hasOwnProperty(C)||N===j||N===void 0&&j===void 0||rc(e,t,C,N,i,j);return}}for(var E in a)N=a[E],a.hasOwnProperty(E)&&N!=null&&!i.hasOwnProperty(E)&&ze(e,t,E,null,i,N);for(B in i)N=i[B],j=a[B],!i.hasOwnProperty(B)||N===j||N==null&&j==null||ze(e,t,B,N,i,j)}var cc=null,oc=null;function Ou(e){return e.nodeType===9?e:e.ownerDocument}function Bh(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Hh(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function fc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var dc=null;function bg(){var e=window.event;return e&&e.type==="popstate"?e===dc?!1:(dc=e,!0):(dc=null,!1)}var kh=typeof setTimeout=="function"?setTimeout:void 0,xg=typeof clearTimeout=="function"?clearTimeout:void 0,Lh=typeof Promise=="function"?Promise:void 0,Sg=typeof queueMicrotask=="function"?queueMicrotask:typeof Lh<"u"?function(e){return Lh.resolve(null).then(e).catch(_g)}:kh;function _g(e){setTimeout(function(){throw e})}function Zn(e){return e==="head"}function Qh(e,t){var a=t,i=0,r=0;do{var c=a.nextSibling;if(e.removeChild(a),c&&c.nodeType===8)if(a=c.data,a==="/$"){if(0<i&&8>i){a=i;var d=e.ownerDocument;if(a&1&&Fl(d.documentElement),a&2&&Fl(d.body),a&4)for(a=d.head,Fl(a),d=a.firstChild;d;){var p=d.nextSibling,x=d.nodeName;d[ol]||x==="SCRIPT"||x==="STYLE"||x==="LINK"&&d.rel.toLowerCase()==="stylesheet"||a.removeChild(d),d=p}}if(r===0){e.removeChild(c),ai(t);return}r--}else a==="$"||a==="$?"||a==="$!"?r++:i=a.charCodeAt(0)-48;else i=0;a=c}while(a);ai(t)}function hc(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":hc(a),gs(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function Eg(e,t,a,i){for(;e.nodeType===1;){var r=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!i&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(i){if(!e[ol])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==r.rel||e.getAttribute("href")!==(r.href==null||r.href===""?null:r.href)||e.getAttribute("crossorigin")!==(r.crossOrigin==null?null:r.crossOrigin)||e.getAttribute("title")!==(r.title==null?null:r.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(r.src==null?null:r.src)||e.getAttribute("type")!==(r.type==null?null:r.type)||e.getAttribute("crossorigin")!==(r.crossOrigin==null?null:r.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=r.name==null?null:""+r.name;if(r.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=Vt(e.nextSibling),e===null)break}return null}function zg(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=Vt(e.nextSibling),e===null))return null;return e}function mc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function Tg(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var i=function(){t(),a.removeEventListener("DOMContentLoaded",i)};a.addEventListener("DOMContentLoaded",i),e._reactRetry=i}}function Vt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var pc=null;function Gh(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function Yh(e,t,a){switch(t=Ou(a),e){case"html":if(e=t.documentElement,!e)throw Error(s(452));return e;case"head":if(e=t.head,!e)throw Error(s(453));return e;case"body":if(e=t.body,!e)throw Error(s(454));return e;default:throw Error(s(451))}}function Fl(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);gs(e)}var kt=new Map,Vh=new Set;function Nu(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var vn=Y.d;Y.d={f:Og,r:Ng,D:jg,C:Ag,L:Rg,m:wg,X:Cg,S:Dg,M:Mg};function Og(){var e=vn.f(),t=gu();return e||t}function Ng(e){var t=ba(e);t!==null&&t.tag===5&&t.type==="form"?fd(t):vn.r(e)}var Ia=typeof document>"u"?null:document;function Xh(e,t,a){var i=Ia;if(i&&typeof t=="string"&&t){var r=Ct(t);r='link[rel="'+e+'"][href="'+r+'"]',typeof a=="string"&&(r+='[crossorigin="'+a+'"]'),Vh.has(r)||(Vh.add(r),e={rel:e,crossOrigin:a,href:t},i.querySelector(r)===null&&(t=i.createElement("link"),it(t,"link",e),Pe(t),i.head.appendChild(t)))}}function jg(e){vn.D(e),Xh("dns-prefetch",e,null)}function Ag(e,t){vn.C(e,t),Xh("preconnect",e,t)}function Rg(e,t,a){vn.L(e,t,a);var i=Ia;if(i&&e&&t){var r='link[rel="preload"][as="'+Ct(t)+'"]';t==="image"&&a&&a.imageSrcSet?(r+='[imagesrcset="'+Ct(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(r+='[imagesizes="'+Ct(a.imageSizes)+'"]')):r+='[href="'+Ct(e)+'"]';var c=r;switch(t){case"style":c=el(e);break;case"script":c=tl(e)}kt.has(c)||(e=b({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),kt.set(c,e),i.querySelector(r)!==null||t==="style"&&i.querySelector(Jl(c))||t==="script"&&i.querySelector(Pl(c))||(t=i.createElement("link"),it(t,"link",e),Pe(t),i.head.appendChild(t)))}}function wg(e,t){vn.m(e,t);var a=Ia;if(a&&e){var i=t&&typeof t.as=="string"?t.as:"script",r='link[rel="modulepreload"][as="'+Ct(i)+'"][href="'+Ct(e)+'"]',c=r;switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=tl(e)}if(!kt.has(c)&&(e=b({rel:"modulepreload",href:e},t),kt.set(c,e),a.querySelector(r)===null)){switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(Pl(c)))return}i=a.createElement("link"),it(i,"link",e),Pe(i),a.head.appendChild(i)}}}function Dg(e,t,a){vn.S(e,t,a);var i=Ia;if(i&&e){var r=xa(i).hoistableStyles,c=el(e);t=t||"default";var d=r.get(c);if(!d){var p={loading:0,preload:null};if(d=i.querySelector(Jl(c)))p.loading=5;else{e=b({rel:"stylesheet",href:e,"data-precedence":t},a),(a=kt.get(c))&&vc(e,a);var x=d=i.createElement("link");Pe(x),it(x,"link",e),x._p=new Promise(function(O,C){x.onload=O,x.onerror=C}),x.addEventListener("load",function(){p.loading|=1}),x.addEventListener("error",function(){p.loading|=2}),p.loading|=4,ju(d,t,i)}d={type:"stylesheet",instance:d,count:1,state:p},r.set(c,d)}}}function Cg(e,t){vn.X(e,t);var a=Ia;if(a&&e){var i=xa(a).hoistableScripts,r=tl(e),c=i.get(r);c||(c=a.querySelector(Pl(r)),c||(e=b({src:e,async:!0},t),(t=kt.get(r))&&yc(e,t),c=a.createElement("script"),Pe(c),it(c,"link",e),a.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},i.set(r,c))}}function Mg(e,t){vn.M(e,t);var a=Ia;if(a&&e){var i=xa(a).hoistableScripts,r=tl(e),c=i.get(r);c||(c=a.querySelector(Pl(r)),c||(e=b({src:e,async:!0,type:"module"},t),(t=kt.get(r))&&yc(e,t),c=a.createElement("script"),Pe(c),it(c,"link",e),a.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},i.set(r,c))}}function Kh(e,t,a,i){var r=(r=ie.current)?Nu(r):null;if(!r)throw Error(s(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=el(a.href),a=xa(r).hoistableStyles,i=a.get(t),i||(i={type:"style",instance:null,count:0,state:null},a.set(t,i)),i):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=el(a.href);var c=xa(r).hoistableStyles,d=c.get(e);if(d||(r=r.ownerDocument||r,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,d),(c=r.querySelector(Jl(e)))&&!c._p&&(d.instance=c,d.state.loading=5),kt.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},kt.set(e,a),c||Ug(r,e,a,d.state))),t&&i===null)throw Error(s(528,""));return d}if(t&&i!==null)throw Error(s(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=tl(a),a=xa(r).hoistableScripts,i=a.get(t),i||(i={type:"script",instance:null,count:0,state:null},a.set(t,i)),i):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,e))}}function el(e){return'href="'+Ct(e)+'"'}function Jl(e){return'link[rel="stylesheet"]['+e+"]"}function $h(e){return b({},e,{"data-precedence":e.precedence,precedence:null})}function Ug(e,t,a,i){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?i.loading=1:(t=e.createElement("link"),i.preload=t,t.addEventListener("load",function(){return i.loading|=1}),t.addEventListener("error",function(){return i.loading|=2}),it(t,"link",a),Pe(t),e.head.appendChild(t))}function tl(e){return'[src="'+Ct(e)+'"]'}function Pl(e){return"script[async]"+e}function Fh(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var i=e.querySelector('style[data-href~="'+Ct(a.href)+'"]');if(i)return t.instance=i,Pe(i),i;var r=b({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return i=(e.ownerDocument||e).createElement("style"),Pe(i),it(i,"style",r),ju(i,a.precedence,e),t.instance=i;case"stylesheet":r=el(a.href);var c=e.querySelector(Jl(r));if(c)return t.state.loading|=4,t.instance=c,Pe(c),c;i=$h(a),(r=kt.get(r))&&vc(i,r),c=(e.ownerDocument||e).createElement("link"),Pe(c);var d=c;return d._p=new Promise(function(p,x){d.onload=p,d.onerror=x}),it(c,"link",i),t.state.loading|=4,ju(c,a.precedence,e),t.instance=c;case"script":return c=tl(a.src),(r=e.querySelector(Pl(c)))?(t.instance=r,Pe(r),r):(i=a,(r=kt.get(c))&&(i=b({},a),yc(i,r)),e=e.ownerDocument||e,r=e.createElement("script"),Pe(r),it(r,"link",i),e.head.appendChild(r),t.instance=r);case"void":return null;default:throw Error(s(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(i=t.instance,t.state.loading|=4,ju(i,a.precedence,e));return t.instance}function ju(e,t,a){for(var i=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),r=i.length?i[i.length-1]:null,c=r,d=0;d<i.length;d++){var p=i[d];if(p.dataset.precedence===t)c=p;else if(c!==r)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function vc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function yc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Au=null;function Jh(e,t,a){if(Au===null){var i=new Map,r=Au=new Map;r.set(a,i)}else r=Au,i=r.get(a),i||(i=new Map,r.set(a,i));if(i.has(e))return i;for(i.set(e,null),a=a.getElementsByTagName(e),r=0;r<a.length;r++){var c=a[r];if(!(c[ol]||c[st]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var d=c.getAttribute(t)||"";d=e+d;var p=i.get(d);p?p.push(c):i.set(d,[c])}}return i}function Ph(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function Zg(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Wh(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Wl=null;function qg(){}function Bg(e,t,a){if(Wl===null)throw Error(s(475));var i=Wl;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var r=el(a.href),c=e.querySelector(Jl(r));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(i.count++,i=Ru.bind(i),e.then(i,i)),t.state.loading|=4,t.instance=c,Pe(c);return}c=e.ownerDocument||e,a=$h(a),(r=kt.get(r))&&vc(a,r),c=c.createElement("link"),Pe(c);var d=c;d._p=new Promise(function(p,x){d.onload=p,d.onerror=x}),it(c,"link",a),t.instance=c}i.stylesheets===null&&(i.stylesheets=new Map),i.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(i.count++,t=Ru.bind(i),e.addEventListener("load",t),e.addEventListener("error",t))}}function Hg(){if(Wl===null)throw Error(s(475));var e=Wl;return e.stylesheets&&e.count===0&&gc(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&gc(e,e.stylesheets),e.unsuspend){var i=e.unsuspend;e.unsuspend=null,i()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function Ru(){if(this.count--,this.count===0){if(this.stylesheets)gc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var wu=null;function gc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,wu=new Map,t.forEach(kg,e),wu=null,Ru.call(e))}function kg(e,t){if(!(t.state.loading&4)){var a=wu.get(e);if(a)var i=a.get(null);else{a=new Map,wu.set(e,a);for(var r=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<r.length;c++){var d=r[c];(d.nodeName==="LINK"||d.getAttribute("media")!=="not all")&&(a.set(d.dataset.precedence,d),i=d)}i&&a.set(null,i)}r=t.instance,d=r.getAttribute("data-precedence"),c=a.get(d)||i,c===i&&a.set(null,r),a.set(d,r),this.count++,i=Ru.bind(this),r.addEventListener("load",i),r.addEventListener("error",i),c?c.parentNode.insertBefore(r,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(r,e.firstChild)),t.state.loading|=4}}var Il={$$typeof:Q,Provider:null,Consumer:null,_currentValue:ne,_currentValue2:ne,_threadCount:0};function Lg(e,t,a,i,r,c,d,p){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=ms(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ms(0),this.hiddenUpdates=ms(null),this.identifierPrefix=i,this.onUncaughtError=r,this.onCaughtError=c,this.onRecoverableError=d,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=p,this.incompleteTransitions=new Map}function Ih(e,t,a,i,r,c,d,p,x,O,C,B){return e=new Lg(e,t,a,d,p,x,O,B),t=1,c===!0&&(t|=24),c=Ot(3,null,null,t),e.current=c,c.stateNode=e,t=Is(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:i,isDehydrated:a,cache:t},ar(c),e}function em(e){return e?(e=Da,e):Da}function tm(e,t,a,i,r,c){r=em(r),i.context===null?i.context=r:i.pendingContext=r,i=En(t),i.payload={element:a},c=c===void 0?null:c,c!==null&&(i.callback=c),a=zn(e,i,t),a!==null&&(wt(a,e,t),Al(a,e,t))}function nm(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function bc(e,t){nm(e,t),(e=e.alternate)&&nm(e,t)}function am(e){if(e.tag===13){var t=wa(e,67108864);t!==null&&wt(t,e,67108864),bc(e,67108864)}}var Du=!0;function Qg(e,t,a,i){var r=A.T;A.T=null;var c=Y.p;try{Y.p=2,xc(e,t,a,i)}finally{Y.p=c,A.T=r}}function Gg(e,t,a,i){var r=A.T;A.T=null;var c=Y.p;try{Y.p=8,xc(e,t,a,i)}finally{Y.p=c,A.T=r}}function xc(e,t,a,i){if(Du){var r=Sc(i);if(r===null)sc(e,t,i,Cu,a),im(e,i);else if(Vg(r,e,t,a,i))i.stopPropagation();else if(im(e,i),t&4&&-1<Yg.indexOf(e)){for(;r!==null;){var c=ba(r);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var d=Xn(c.pendingLanes);if(d!==0){var p=c;for(p.pendingLanes|=2,p.entangledLanes|=2;d;){var x=1<<31-zt(d);p.entanglements[1]|=x,d&=~x}Wt(c),(Se&6)===0&&(vu=Kt()+500,Xl(0))}}break;case 13:p=wa(c,2),p!==null&&wt(p,c,2),gu(),bc(c,2)}if(c=Sc(i),c===null&&sc(e,t,i,Cu,a),c===r)break;r=c}r!==null&&i.stopPropagation()}else sc(e,t,i,null,a)}}function Sc(e){return e=Os(e),_c(e)}var Cu=null;function _c(e){if(Cu=null,e=ga(e),e!==null){var t=f(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=h(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Cu=e,null}function lm(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Av()){case yo:return 2;case go:return 8;case Ti:case Rv:return 32;case bo:return 268435456;default:return 32}default:return 32}}var Ec=!1,qn=null,Bn=null,Hn=null,ei=new Map,ti=new Map,kn=[],Yg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function im(e,t){switch(e){case"focusin":case"focusout":qn=null;break;case"dragenter":case"dragleave":Bn=null;break;case"mouseover":case"mouseout":Hn=null;break;case"pointerover":case"pointerout":ei.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ti.delete(t.pointerId)}}function ni(e,t,a,i,r,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:a,eventSystemFlags:i,nativeEvent:c,targetContainers:[r]},t!==null&&(t=ba(t),t!==null&&am(t)),e):(e.eventSystemFlags|=i,t=e.targetContainers,r!==null&&t.indexOf(r)===-1&&t.push(r),e)}function Vg(e,t,a,i,r){switch(t){case"focusin":return qn=ni(qn,e,t,a,i,r),!0;case"dragenter":return Bn=ni(Bn,e,t,a,i,r),!0;case"mouseover":return Hn=ni(Hn,e,t,a,i,r),!0;case"pointerover":var c=r.pointerId;return ei.set(c,ni(ei.get(c)||null,e,t,a,i,r)),!0;case"gotpointercapture":return c=r.pointerId,ti.set(c,ni(ti.get(c)||null,e,t,a,i,r)),!0}return!1}function um(e){var t=ga(e.target);if(t!==null){var a=f(t);if(a!==null){if(t=a.tag,t===13){if(t=h(a),t!==null){e.blockedOn=t,Bv(e.priority,function(){if(a.tag===13){var i=Rt();i=ps(i);var r=wa(a,i);r!==null&&wt(r,a,i),bc(a,i)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Mu(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=Sc(e.nativeEvent);if(a===null){a=e.nativeEvent;var i=new a.constructor(a.type,a);Ts=i,a.target.dispatchEvent(i),Ts=null}else return t=ba(a),t!==null&&am(t),e.blockedOn=a,!1;t.shift()}return!0}function sm(e,t,a){Mu(e)&&a.delete(t)}function Xg(){Ec=!1,qn!==null&&Mu(qn)&&(qn=null),Bn!==null&&Mu(Bn)&&(Bn=null),Hn!==null&&Mu(Hn)&&(Hn=null),ei.forEach(sm),ti.forEach(sm)}function Uu(e,t){e.blockedOn===t&&(e.blockedOn=null,Ec||(Ec=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,Xg)))}var Zu=null;function rm(e){Zu!==e&&(Zu=e,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){Zu===e&&(Zu=null);for(var t=0;t<e.length;t+=3){var a=e[t],i=e[t+1],r=e[t+2];if(typeof i!="function"){if(_c(i||a)===null)continue;break}var c=ba(a);c!==null&&(e.splice(t,3),t-=3,_r(c,{pending:!0,data:r,method:a.method,action:i},i,r))}}))}function ai(e){function t(x){return Uu(x,e)}qn!==null&&Uu(qn,e),Bn!==null&&Uu(Bn,e),Hn!==null&&Uu(Hn,e),ei.forEach(t),ti.forEach(t);for(var a=0;a<kn.length;a++){var i=kn[a];i.blockedOn===e&&(i.blockedOn=null)}for(;0<kn.length&&(a=kn[0],a.blockedOn===null);)um(a),a.blockedOn===null&&kn.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(i=0;i<a.length;i+=3){var r=a[i],c=a[i+1],d=r[dt]||null;if(typeof c=="function")d||rm(a);else if(d){var p=null;if(c&&c.hasAttribute("formAction")){if(r=c,d=c[dt]||null)p=d.formAction;else if(_c(r)!==null)continue}else p=d.action;typeof p=="function"?a[i+1]=p:(a.splice(i,3),i-=3),rm(a)}}}function zc(e){this._internalRoot=e}qu.prototype.render=zc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(s(409));var a=t.current,i=Rt();tm(a,i,e,t,null,null)},qu.prototype.unmount=zc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;tm(e.current,2,null,e,null,null),gu(),t[ya]=null}};function qu(e){this._internalRoot=e}qu.prototype.unstable_scheduleHydration=function(e){if(e){var t=zo();e={blockedOn:null,target:e,priority:t};for(var a=0;a<kn.length&&t!==0&&t<kn[a].priority;a++);kn.splice(a,0,e),a===0&&um(e)}};var cm=l.version;if(cm!=="19.1.0")throw Error(s(527,cm,"19.1.0"));Y.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(s(188)):(e=Object.keys(e).join(","),Error(s(268,e)));return e=g(t),e=e!==null?v(e):null,e=e===null?null:e.stateNode,e};var Kg={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:A,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Bu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Bu.isDisabled&&Bu.supportsFiber)try{sl=Bu.inject(Kg),Et=Bu}catch{}}return ii.createRoot=function(e,t){if(!o(e))throw Error(s(299));var a=!1,i="",r=Td,c=Od,d=Nd,p=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(i=t.identifierPrefix),t.onUncaughtError!==void 0&&(r=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(d=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(p=t.unstable_transitionCallbacks)),t=Ih(e,1,!1,null,null,a,i,r,c,d,p,null),e[ya]=t.current,uc(e),new zc(t)},ii.hydrateRoot=function(e,t,a){if(!o(e))throw Error(s(299));var i=!1,r="",c=Td,d=Od,p=Nd,x=null,O=null;return a!=null&&(a.unstable_strictMode===!0&&(i=!0),a.identifierPrefix!==void 0&&(r=a.identifierPrefix),a.onUncaughtError!==void 0&&(c=a.onUncaughtError),a.onCaughtError!==void 0&&(d=a.onCaughtError),a.onRecoverableError!==void 0&&(p=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(x=a.unstable_transitionCallbacks),a.formState!==void 0&&(O=a.formState)),t=Ih(e,1,!0,t,a??null,i,r,c,d,p,x,O),t.context=em(null),a=t.current,i=Rt(),i=ps(i),r=En(i),r.callback=null,zn(a,r,i),a=i,t.current.lanes=a,cl(t,a),Wt(t),e[ya]=t.current,uc(e),new qu(t)},ii.version="19.1.0",ii}var xm;function l0(){if(xm)return Nc.exports;xm=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(l){console.error(l)}}return n(),Nc.exports=a0(),Nc.exports}var i0=l0(),vi=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(n){return this.listeners.add(n),this.onSubscribe(),()=>{this.listeners.delete(n),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},ha=typeof window>"u"||"Deno"in globalThis;function yt(){}function u0(n,l){return typeof n=="function"?n(l):n}function Bc(n){return typeof n=="number"&&n>=0&&n!==1/0}function mp(n,l){return Math.max(n+(l||0)-Date.now(),0)}function Qn(n,l){return typeof n=="function"?n(l):n}function Lt(n,l){return typeof n=="function"?n(l):n}function Sm(n,l){const{type:u="all",exact:s,fetchStatus:o,predicate:f,queryKey:h,stale:y}=n;if(h){if(s){if(l.queryHash!==no(h,l.options))return!1}else if(!fi(l.queryKey,h))return!1}if(u!=="all"){const g=l.isActive();if(u==="active"&&!g||u==="inactive"&&g)return!1}return!(typeof y=="boolean"&&l.isStale()!==y||o&&o!==l.state.fetchStatus||f&&!f(l))}function _m(n,l){const{exact:u,status:s,predicate:o,mutationKey:f}=n;if(f){if(!l.options.mutationKey)return!1;if(u){if(oi(l.options.mutationKey)!==oi(f))return!1}else if(!fi(l.options.mutationKey,f))return!1}return!(s&&l.state.status!==s||o&&!o(l))}function no(n,l){return(l?.queryKeyHashFn||oi)(n)}function oi(n){return JSON.stringify(n,(l,u)=>kc(u)?Object.keys(u).sort().reduce((s,o)=>(s[o]=u[o],s),{}):u)}function fi(n,l){return n===l?!0:typeof n!=typeof l?!1:n&&l&&typeof n=="object"&&typeof l=="object"?Object.keys(l).every(u=>fi(n[u],l[u])):!1}function pp(n,l){if(n===l)return n;const u=Em(n)&&Em(l);if(u||kc(n)&&kc(l)){const s=u?n:Object.keys(n),o=s.length,f=u?l:Object.keys(l),h=f.length,y=u?[]:{},g=new Set(s);let v=0;for(let b=0;b<h;b++){const z=u?b:f[b];(!u&&g.has(z)||u)&&n[z]===void 0&&l[z]===void 0?(y[z]=void 0,v++):(y[z]=pp(n[z],l[z]),y[z]===n[z]&&n[z]!==void 0&&v++)}return o===h&&v===o?n:y}return l}function Hc(n,l){if(!l||Object.keys(n).length!==Object.keys(l).length)return!1;for(const u in n)if(n[u]!==l[u])return!1;return!0}function Em(n){return Array.isArray(n)&&n.length===Object.keys(n).length}function kc(n){if(!zm(n))return!1;const l=n.constructor;if(l===void 0)return!0;const u=l.prototype;return!(!zm(u)||!u.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(n)!==Object.prototype)}function zm(n){return Object.prototype.toString.call(n)==="[object Object]"}function s0(n){return new Promise(l=>{setTimeout(l,n)})}function Lc(n,l,u){return typeof u.structuralSharing=="function"?u.structuralSharing(n,l):u.structuralSharing!==!1?pp(n,l):l}function r0(n,l,u=0){const s=[...n,l];return u&&s.length>u?s.slice(1):s}function c0(n,l,u=0){const s=[l,...n];return u&&s.length>u?s.slice(0,-1):s}var ao=Symbol();function vp(n,l){return!n.queryFn&&l?.initialPromise?()=>l.initialPromise:!n.queryFn||n.queryFn===ao?()=>Promise.reject(new Error(`Missing queryFn: '${n.queryHash}'`)):n.queryFn}function o0(n,l){return typeof n=="function"?n(...l):!!n}var f0=class extends vi{#t;#e;#n;constructor(){super(),this.#n=n=>{if(!ha&&window.addEventListener){const l=()=>n();return window.addEventListener("visibilitychange",l,!1),()=>{window.removeEventListener("visibilitychange",l)}}}}onSubscribe(){this.#e||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(n){this.#n=n,this.#e?.(),this.#e=n(l=>{typeof l=="boolean"?this.setFocused(l):this.onFocus()})}setFocused(n){this.#t!==n&&(this.#t=n,this.onFocus())}onFocus(){const n=this.isFocused();this.listeners.forEach(l=>{l(n)})}isFocused(){return typeof this.#t=="boolean"?this.#t:globalThis.document?.visibilityState!=="hidden"}},lo=new f0,d0=class extends vi{#t=!0;#e;#n;constructor(){super(),this.#n=n=>{if(!ha&&window.addEventListener){const l=()=>n(!0),u=()=>n(!1);return window.addEventListener("online",l,!1),window.addEventListener("offline",u,!1),()=>{window.removeEventListener("online",l),window.removeEventListener("offline",u)}}}}onSubscribe(){this.#e||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(n){this.#n=n,this.#e?.(),this.#e=n(this.setOnline.bind(this))}setOnline(n){this.#t!==n&&(this.#t=n,this.listeners.forEach(u=>{u(n)}))}isOnline(){return this.#t}},$u=new d0;function Qc(){let n,l;const u=new Promise((o,f)=>{n=o,l=f});u.status="pending",u.catch(()=>{});function s(o){Object.assign(u,o),delete u.resolve,delete u.reject}return u.resolve=o=>{s({status:"fulfilled",value:o}),n(o)},u.reject=o=>{s({status:"rejected",reason:o}),l(o)},u}function h0(n){return Math.min(1e3*2**n,3e4)}function yp(n){return(n??"online")==="online"?$u.isOnline():!0}var gp=class extends Error{constructor(n){super("CancelledError"),this.revert=n?.revert,this.silent=n?.silent}};function wc(n){return n instanceof gp}function bp(n){let l=!1,u=0,s=!1,o;const f=Qc(),h=D=>{s||(w(new gp(D)),n.abort?.())},y=()=>{l=!0},g=()=>{l=!1},v=()=>lo.isFocused()&&(n.networkMode==="always"||$u.isOnline())&&n.canRun(),b=()=>yp(n.networkMode)&&n.canRun(),z=D=>{s||(s=!0,n.onSuccess?.(D),o?.(),f.resolve(D))},w=D=>{s||(s=!0,n.onError?.(D),o?.(),f.reject(D))},k=()=>new Promise(D=>{o=Z=>{(s||v())&&D(Z)},n.onPause?.()}).then(()=>{o=void 0,s||n.onContinue?.()}),M=()=>{if(s)return;let D;const Z=u===0?n.initialPromise:void 0;try{D=Z??n.fn()}catch(L){D=Promise.reject(L)}Promise.resolve(D).then(z).catch(L=>{if(s)return;const G=n.retry??(ha?0:3),Q=n.retryDelay??h0,ee=typeof Q=="function"?Q(u,L):Q,K=G===!0||typeof G=="number"&&u<G||typeof G=="function"&&G(u,L);if(l||!K){w(L);return}u++,n.onFail?.(u,L),s0(ee).then(()=>v()?void 0:k()).then(()=>{l?w(L):M()})})};return{promise:f,cancel:h,continue:()=>(o?.(),f),cancelRetry:y,continueRetry:g,canStart:b,start:()=>(b()?M():k().then(M),f)}}var m0=n=>setTimeout(n,0);function p0(){let n=[],l=0,u=y=>{y()},s=y=>{y()},o=m0;const f=y=>{l?n.push(y):o(()=>{u(y)})},h=()=>{const y=n;n=[],y.length&&o(()=>{s(()=>{y.forEach(g=>{u(g)})})})};return{batch:y=>{let g;l++;try{g=y()}finally{l--,l||h()}return g},batchCalls:y=>(...g)=>{f(()=>{y(...g)})},schedule:f,setNotifyFunction:y=>{u=y},setBatchNotifyFunction:y=>{s=y},setScheduler:y=>{o=y}}}var ut=p0(),xp=class{#t;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Bc(this.gcTime)&&(this.#t=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(n){this.gcTime=Math.max(this.gcTime||0,n??(ha?1/0:300*1e3))}clearGcTimeout(){this.#t&&(clearTimeout(this.#t),this.#t=void 0)}},v0=class extends xp{#t;#e;#n;#a;#l;#s;#u;constructor(n){super(),this.#u=!1,this.#s=n.defaultOptions,this.setOptions(n.options),this.observers=[],this.#a=n.client,this.#n=this.#a.getQueryCache(),this.queryKey=n.queryKey,this.queryHash=n.queryHash,this.#t=y0(this.options),this.state=n.state??this.#t,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#l?.promise}setOptions(n){this.options={...this.#s,...n},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&this.#n.remove(this)}setData(n,l){const u=Lc(this.state.data,n,this.options);return this.#i({data:u,type:"success",dataUpdatedAt:l?.updatedAt,manual:l?.manual}),u}setState(n,l){this.#i({type:"setState",state:n,setStateOptions:l})}cancel(n){const l=this.#l?.promise;return this.#l?.cancel(n),l?l.then(yt).catch(yt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#t)}isActive(){return this.observers.some(n=>Lt(n.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===ao||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(n=>Qn(n.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(n=>n.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(n=0){return this.state.data===void 0?!0:n==="static"?!1:this.state.isInvalidated?!0:!mp(this.state.dataUpdatedAt,n)}onFocus(){this.observers.find(l=>l.shouldFetchOnWindowFocus())?.refetch({cancelRefetch:!1}),this.#l?.continue()}onOnline(){this.observers.find(l=>l.shouldFetchOnReconnect())?.refetch({cancelRefetch:!1}),this.#l?.continue()}addObserver(n){this.observers.includes(n)||(this.observers.push(n),this.clearGcTimeout(),this.#n.notify({type:"observerAdded",query:this,observer:n}))}removeObserver(n){this.observers.includes(n)&&(this.observers=this.observers.filter(l=>l!==n),this.observers.length||(this.#l&&(this.#u?this.#l.cancel({revert:!0}):this.#l.cancelRetry()),this.scheduleGc()),this.#n.notify({type:"observerRemoved",query:this,observer:n}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#i({type:"invalidate"})}fetch(n,l){if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&l?.cancelRefetch)this.cancel({silent:!0});else if(this.#l)return this.#l.continueRetry(),this.#l.promise}if(n&&this.setOptions(n),!this.options.queryFn){const g=this.observers.find(v=>v.options.queryFn);g&&this.setOptions(g.options)}const u=new AbortController,s=g=>{Object.defineProperty(g,"signal",{enumerable:!0,get:()=>(this.#u=!0,u.signal)})},o=()=>{const g=vp(this.options,l),b=(()=>{const z={client:this.#a,queryKey:this.queryKey,meta:this.meta};return s(z),z})();return this.#u=!1,this.options.persister?this.options.persister(g,b,this):g(b)},h=(()=>{const g={fetchOptions:l,options:this.options,queryKey:this.queryKey,client:this.#a,state:this.state,fetchFn:o};return s(g),g})();this.options.behavior?.onFetch(h,this),this.#e=this.state,(this.state.fetchStatus==="idle"||this.state.fetchMeta!==h.fetchOptions?.meta)&&this.#i({type:"fetch",meta:h.fetchOptions?.meta});const y=g=>{wc(g)&&g.silent||this.#i({type:"error",error:g}),wc(g)||(this.#n.config.onError?.(g,this),this.#n.config.onSettled?.(this.state.data,g,this)),this.scheduleGc()};return this.#l=bp({initialPromise:l?.initialPromise,fn:h.fetchFn,abort:u.abort.bind(u),onSuccess:g=>{if(g===void 0){y(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(g)}catch(v){y(v);return}this.#n.config.onSuccess?.(g,this),this.#n.config.onSettled?.(g,this.state.error,this),this.scheduleGc()},onError:y,onFail:(g,v)=>{this.#i({type:"failed",failureCount:g,error:v})},onPause:()=>{this.#i({type:"pause"})},onContinue:()=>{this.#i({type:"continue"})},retry:h.options.retry,retryDelay:h.options.retryDelay,networkMode:h.options.networkMode,canRun:()=>!0}),this.#l.start()}#i(n){const l=u=>{switch(n.type){case"failed":return{...u,fetchFailureCount:n.failureCount,fetchFailureReason:n.error};case"pause":return{...u,fetchStatus:"paused"};case"continue":return{...u,fetchStatus:"fetching"};case"fetch":return{...u,...Sp(u.data,this.options),fetchMeta:n.meta??null};case"success":return this.#e=void 0,{...u,data:n.data,dataUpdateCount:u.dataUpdateCount+1,dataUpdatedAt:n.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!n.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const s=n.error;return wc(s)&&s.revert&&this.#e?{...this.#e,fetchStatus:"idle"}:{...u,error:s,errorUpdateCount:u.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:u.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...u,isInvalidated:!0};case"setState":return{...u,...n.state}}};this.state=l(this.state),ut.batch(()=>{this.observers.forEach(u=>{u.onQueryUpdate()}),this.#n.notify({query:this,type:"updated",action:n})})}};function Sp(n,l){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:yp(l.networkMode)?"fetching":"paused",...n===void 0&&{error:null,status:"pending"}}}function y0(n){const l=typeof n.initialData=="function"?n.initialData():n.initialData,u=l!==void 0,s=u?typeof n.initialDataUpdatedAt=="function"?n.initialDataUpdatedAt():n.initialDataUpdatedAt:0;return{data:l,dataUpdateCount:0,dataUpdatedAt:u?s??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:u?"success":"pending",fetchStatus:"idle"}}var g0=class extends vi{constructor(n={}){super(),this.config=n,this.#t=new Map}#t;build(n,l,u){const s=l.queryKey,o=l.queryHash??no(s,l);let f=this.get(o);return f||(f=new v0({client:n,queryKey:s,queryHash:o,options:n.defaultQueryOptions(l),state:u,defaultOptions:n.getQueryDefaults(s)}),this.add(f)),f}add(n){this.#t.has(n.queryHash)||(this.#t.set(n.queryHash,n),this.notify({type:"added",query:n}))}remove(n){const l=this.#t.get(n.queryHash);l&&(n.destroy(),l===n&&this.#t.delete(n.queryHash),this.notify({type:"removed",query:n}))}clear(){ut.batch(()=>{this.getAll().forEach(n=>{this.remove(n)})})}get(n){return this.#t.get(n)}getAll(){return[...this.#t.values()]}find(n){const l={exact:!0,...n};return this.getAll().find(u=>Sm(l,u))}findAll(n={}){const l=this.getAll();return Object.keys(n).length>0?l.filter(u=>Sm(n,u)):l}notify(n){ut.batch(()=>{this.listeners.forEach(l=>{l(n)})})}onFocus(){ut.batch(()=>{this.getAll().forEach(n=>{n.onFocus()})})}onOnline(){ut.batch(()=>{this.getAll().forEach(n=>{n.onOnline()})})}},b0=class extends xp{#t;#e;#n;constructor(n){super(),this.mutationId=n.mutationId,this.#e=n.mutationCache,this.#t=[],this.state=n.state||x0(),this.setOptions(n.options),this.scheduleGc()}setOptions(n){this.options=n,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(n){this.#t.includes(n)||(this.#t.push(n),this.clearGcTimeout(),this.#e.notify({type:"observerAdded",mutation:this,observer:n}))}removeObserver(n){this.#t=this.#t.filter(l=>l!==n),this.scheduleGc(),this.#e.notify({type:"observerRemoved",mutation:this,observer:n})}optionalRemove(){this.#t.length||(this.state.status==="pending"?this.scheduleGc():this.#e.remove(this))}continue(){return this.#n?.continue()??this.execute(this.state.variables)}async execute(n){const l=()=>{this.#a({type:"continue"})};this.#n=bp({fn:()=>this.options.mutationFn?this.options.mutationFn(n):Promise.reject(new Error("No mutationFn found")),onFail:(o,f)=>{this.#a({type:"failed",failureCount:o,error:f})},onPause:()=>{this.#a({type:"pause"})},onContinue:l,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#e.canRun(this)});const u=this.state.status==="pending",s=!this.#n.canStart();try{if(u)l();else{this.#a({type:"pending",variables:n,isPaused:s}),await this.#e.config.onMutate?.(n,this);const f=await this.options.onMutate?.(n);f!==this.state.context&&this.#a({type:"pending",context:f,variables:n,isPaused:s})}const o=await this.#n.start();return await this.#e.config.onSuccess?.(o,n,this.state.context,this),await this.options.onSuccess?.(o,n,this.state.context),await this.#e.config.onSettled?.(o,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(o,null,n,this.state.context),this.#a({type:"success",data:o}),o}catch(o){try{throw await this.#e.config.onError?.(o,n,this.state.context,this),await this.options.onError?.(o,n,this.state.context),await this.#e.config.onSettled?.(void 0,o,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,o,n,this.state.context),o}finally{this.#a({type:"error",error:o})}}finally{this.#e.runNext(this)}}#a(n){const l=u=>{switch(n.type){case"failed":return{...u,failureCount:n.failureCount,failureReason:n.error};case"pause":return{...u,isPaused:!0};case"continue":return{...u,isPaused:!1};case"pending":return{...u,context:n.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:n.isPaused,status:"pending",variables:n.variables,submittedAt:Date.now()};case"success":return{...u,data:n.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...u,data:void 0,error:n.error,failureCount:u.failureCount+1,failureReason:n.error,isPaused:!1,status:"error"}}};this.state=l(this.state),ut.batch(()=>{this.#t.forEach(u=>{u.onMutationUpdate(n)}),this.#e.notify({mutation:this,type:"updated",action:n})})}};function x0(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var S0=class extends vi{constructor(n={}){super(),this.config=n,this.#t=new Set,this.#e=new Map,this.#n=0}#t;#e;#n;build(n,l,u){const s=new b0({mutationCache:this,mutationId:++this.#n,options:n.defaultMutationOptions(l),state:u});return this.add(s),s}add(n){this.#t.add(n);const l=Hu(n);if(typeof l=="string"){const u=this.#e.get(l);u?u.push(n):this.#e.set(l,[n])}this.notify({type:"added",mutation:n})}remove(n){if(this.#t.delete(n)){const l=Hu(n);if(typeof l=="string"){const u=this.#e.get(l);if(u)if(u.length>1){const s=u.indexOf(n);s!==-1&&u.splice(s,1)}else u[0]===n&&this.#e.delete(l)}}this.notify({type:"removed",mutation:n})}canRun(n){const l=Hu(n);if(typeof l=="string"){const s=this.#e.get(l)?.find(o=>o.state.status==="pending");return!s||s===n}else return!0}runNext(n){const l=Hu(n);return typeof l=="string"?this.#e.get(l)?.find(s=>s!==n&&s.state.isPaused)?.continue()??Promise.resolve():Promise.resolve()}clear(){ut.batch(()=>{this.#t.forEach(n=>{this.notify({type:"removed",mutation:n})}),this.#t.clear(),this.#e.clear()})}getAll(){return Array.from(this.#t)}find(n){const l={exact:!0,...n};return this.getAll().find(u=>_m(l,u))}findAll(n={}){return this.getAll().filter(l=>_m(n,l))}notify(n){ut.batch(()=>{this.listeners.forEach(l=>{l(n)})})}resumePausedMutations(){const n=this.getAll().filter(l=>l.state.isPaused);return ut.batch(()=>Promise.all(n.map(l=>l.continue().catch(yt))))}};function Hu(n){return n.options.scope?.id}function Tm(n){return{onFetch:(l,u)=>{const s=l.options,o=l.fetchOptions?.meta?.fetchMore?.direction,f=l.state.data?.pages||[],h=l.state.data?.pageParams||[];let y={pages:[],pageParams:[]},g=0;const v=async()=>{let b=!1;const z=M=>{Object.defineProperty(M,"signal",{enumerable:!0,get:()=>(l.signal.aborted?b=!0:l.signal.addEventListener("abort",()=>{b=!0}),l.signal)})},w=vp(l.options,l.fetchOptions),k=async(M,D,Z)=>{if(b)return Promise.reject();if(D==null&&M.pages.length)return Promise.resolve(M);const G=(()=>{const pe={client:l.client,queryKey:l.queryKey,pageParam:D,direction:Z?"backward":"forward",meta:l.options.meta};return z(pe),pe})(),Q=await w(G),{maxPages:ee}=l.options,K=Z?c0:r0;return{pages:K(M.pages,Q,ee),pageParams:K(M.pageParams,D,ee)}};if(o&&f.length){const M=o==="backward",D=M?_0:Om,Z={pages:f,pageParams:h},L=D(s,Z);y=await k(Z,L,M)}else{const M=n??f.length;do{const D=g===0?h[0]??s.initialPageParam:Om(s,y);if(g>0&&D==null)break;y=await k(y,D),g++}while(g<M)}return y};l.options.persister?l.fetchFn=()=>l.options.persister?.(v,{client:l.client,queryKey:l.queryKey,meta:l.options.meta,signal:l.signal},u):l.fetchFn=v}}}function Om(n,{pages:l,pageParams:u}){const s=l.length-1;return l.length>0?n.getNextPageParam(l[s],l,u[s],u):void 0}function _0(n,{pages:l,pageParams:u}){return l.length>0?n.getPreviousPageParam?.(l[0],l,u[0],u):void 0}var E0=class{#t;#e;#n;#a;#l;#s;#u;#i;constructor(n={}){this.#t=n.queryCache||new g0,this.#e=n.mutationCache||new S0,this.#n=n.defaultOptions||{},this.#a=new Map,this.#l=new Map,this.#s=0}mount(){this.#s++,this.#s===1&&(this.#u=lo.subscribe(async n=>{n&&(await this.resumePausedMutations(),this.#t.onFocus())}),this.#i=$u.subscribe(async n=>{n&&(await this.resumePausedMutations(),this.#t.onOnline())}))}unmount(){this.#s--,this.#s===0&&(this.#u?.(),this.#u=void 0,this.#i?.(),this.#i=void 0)}isFetching(n){return this.#t.findAll({...n,fetchStatus:"fetching"}).length}isMutating(n){return this.#e.findAll({...n,status:"pending"}).length}getQueryData(n){const l=this.defaultQueryOptions({queryKey:n});return this.#t.get(l.queryHash)?.state.data}ensureQueryData(n){const l=this.defaultQueryOptions(n),u=this.#t.build(this,l),s=u.state.data;return s===void 0?this.fetchQuery(n):(n.revalidateIfStale&&u.isStaleByTime(Qn(l.staleTime,u))&&this.prefetchQuery(l),Promise.resolve(s))}getQueriesData(n){return this.#t.findAll(n).map(({queryKey:l,state:u})=>{const s=u.data;return[l,s]})}setQueryData(n,l,u){const s=this.defaultQueryOptions({queryKey:n}),f=this.#t.get(s.queryHash)?.state.data,h=u0(l,f);if(h!==void 0)return this.#t.build(this,s).setData(h,{...u,manual:!0})}setQueriesData(n,l,u){return ut.batch(()=>this.#t.findAll(n).map(({queryKey:s})=>[s,this.setQueryData(s,l,u)]))}getQueryState(n){const l=this.defaultQueryOptions({queryKey:n});return this.#t.get(l.queryHash)?.state}removeQueries(n){const l=this.#t;ut.batch(()=>{l.findAll(n).forEach(u=>{l.remove(u)})})}resetQueries(n,l){const u=this.#t;return ut.batch(()=>(u.findAll(n).forEach(s=>{s.reset()}),this.refetchQueries({type:"active",...n},l)))}cancelQueries(n,l={}){const u={revert:!0,...l},s=ut.batch(()=>this.#t.findAll(n).map(o=>o.cancel(u)));return Promise.all(s).then(yt).catch(yt)}invalidateQueries(n,l={}){return ut.batch(()=>(this.#t.findAll(n).forEach(u=>{u.invalidate()}),n?.refetchType==="none"?Promise.resolve():this.refetchQueries({...n,type:n?.refetchType??n?.type??"active"},l)))}refetchQueries(n,l={}){const u={...l,cancelRefetch:l.cancelRefetch??!0},s=ut.batch(()=>this.#t.findAll(n).filter(o=>!o.isDisabled()&&!o.isStatic()).map(o=>{let f=o.fetch(void 0,u);return u.throwOnError||(f=f.catch(yt)),o.state.fetchStatus==="paused"?Promise.resolve():f}));return Promise.all(s).then(yt)}fetchQuery(n){const l=this.defaultQueryOptions(n);l.retry===void 0&&(l.retry=!1);const u=this.#t.build(this,l);return u.isStaleByTime(Qn(l.staleTime,u))?u.fetch(l):Promise.resolve(u.state.data)}prefetchQuery(n){return this.fetchQuery(n).then(yt).catch(yt)}fetchInfiniteQuery(n){return n.behavior=Tm(n.pages),this.fetchQuery(n)}prefetchInfiniteQuery(n){return this.fetchInfiniteQuery(n).then(yt).catch(yt)}ensureInfiniteQueryData(n){return n.behavior=Tm(n.pages),this.ensureQueryData(n)}resumePausedMutations(){return $u.isOnline()?this.#e.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#t}getMutationCache(){return this.#e}getDefaultOptions(){return this.#n}setDefaultOptions(n){this.#n=n}setQueryDefaults(n,l){this.#a.set(oi(n),{queryKey:n,defaultOptions:l})}getQueryDefaults(n){const l=[...this.#a.values()],u={};return l.forEach(s=>{fi(n,s.queryKey)&&Object.assign(u,s.defaultOptions)}),u}setMutationDefaults(n,l){this.#l.set(oi(n),{mutationKey:n,defaultOptions:l})}getMutationDefaults(n){const l=[...this.#l.values()],u={};return l.forEach(s=>{fi(n,s.mutationKey)&&Object.assign(u,s.defaultOptions)}),u}defaultQueryOptions(n){if(n._defaulted)return n;const l={...this.#n.queries,...this.getQueryDefaults(n.queryKey),...n,_defaulted:!0};return l.queryHash||(l.queryHash=no(l.queryKey,l)),l.refetchOnReconnect===void 0&&(l.refetchOnReconnect=l.networkMode!=="always"),l.throwOnError===void 0&&(l.throwOnError=!!l.suspense),!l.networkMode&&l.persister&&(l.networkMode="offlineFirst"),l.queryFn===ao&&(l.enabled=!1),l}defaultMutationOptions(n){return n?._defaulted?n:{...this.#n.mutations,...n?.mutationKey&&this.getMutationDefaults(n.mutationKey),...n,_defaulted:!0}}clear(){this.#t.clear(),this.#e.clear()}},z0=class extends vi{constructor(n,l){super(),this.options=l,this.#t=n,this.#i=null,this.#u=Qc(),this.options.experimental_prefetchInRender||this.#u.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(l)}#t;#e=void 0;#n=void 0;#a=void 0;#l;#s;#u;#i;#p;#d;#h;#c;#o;#r;#m=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(this.#e.addObserver(this),Nm(this.#e,this.options)?this.#f():this.updateResult(),this.#b())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return Gc(this.#e,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return Gc(this.#e,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#x(),this.#S(),this.#e.removeObserver(this)}setOptions(n){const l=this.options,u=this.#e;if(this.options=this.#t.defaultQueryOptions(n),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof Lt(this.options.enabled,this.#e)!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#_(),this.#e.setOptions(this.options),l._defaulted&&!Hc(this.options,l)&&this.#t.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#e,observer:this});const s=this.hasListeners();s&&jm(this.#e,u,this.options,l)&&this.#f(),this.updateResult(),s&&(this.#e!==u||Lt(this.options.enabled,this.#e)!==Lt(l.enabled,this.#e)||Qn(this.options.staleTime,this.#e)!==Qn(l.staleTime,this.#e))&&this.#v();const o=this.#y();s&&(this.#e!==u||Lt(this.options.enabled,this.#e)!==Lt(l.enabled,this.#e)||o!==this.#r)&&this.#g(o)}getOptimisticResult(n){const l=this.#t.getQueryCache().build(this.#t,n),u=this.createResult(l,n);return O0(this,u)&&(this.#a=u,this.#s=this.options,this.#l=this.#e.state),u}getCurrentResult(){return this.#a}trackResult(n,l){return new Proxy(n,{get:(u,s)=>(this.trackProp(s),l?.(s),Reflect.get(u,s))})}trackProp(n){this.#m.add(n)}getCurrentQuery(){return this.#e}refetch({...n}={}){return this.fetch({...n})}fetchOptimistic(n){const l=this.#t.defaultQueryOptions(n),u=this.#t.getQueryCache().build(this.#t,l);return u.fetch().then(()=>this.createResult(u,l))}fetch(n){return this.#f({...n,cancelRefetch:n.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#a))}#f(n){this.#_();let l=this.#e.fetch(this.options,n);return n?.throwOnError||(l=l.catch(yt)),l}#v(){this.#x();const n=Qn(this.options.staleTime,this.#e);if(ha||this.#a.isStale||!Bc(n))return;const u=mp(this.#a.dataUpdatedAt,n)+1;this.#c=setTimeout(()=>{this.#a.isStale||this.updateResult()},u)}#y(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(this.#e):this.options.refetchInterval)??!1}#g(n){this.#S(),this.#r=n,!(ha||Lt(this.options.enabled,this.#e)===!1||!Bc(this.#r)||this.#r===0)&&(this.#o=setInterval(()=>{(this.options.refetchIntervalInBackground||lo.isFocused())&&this.#f()},this.#r))}#b(){this.#v(),this.#g(this.#y())}#x(){this.#c&&(clearTimeout(this.#c),this.#c=void 0)}#S(){this.#o&&(clearInterval(this.#o),this.#o=void 0)}createResult(n,l){const u=this.#e,s=this.options,o=this.#a,f=this.#l,h=this.#s,g=n!==u?n.state:this.#n,{state:v}=n;let b={...v},z=!1,w;if(l._optimisticResults){const J=this.hasListeners(),Oe=!J&&Nm(n,l),tt=J&&jm(n,u,l,s);(Oe||tt)&&(b={...b,...Sp(v.data,n.options)}),l._optimisticResults==="isRestoring"&&(b.fetchStatus="idle")}let{error:k,errorUpdatedAt:M,status:D}=b;w=b.data;let Z=!1;if(l.placeholderData!==void 0&&w===void 0&&D==="pending"){let J;o?.isPlaceholderData&&l.placeholderData===h?.placeholderData?(J=o.data,Z=!0):J=typeof l.placeholderData=="function"?l.placeholderData(this.#h?.state.data,this.#h):l.placeholderData,J!==void 0&&(D="success",w=Lc(o?.data,J,l),z=!0)}if(l.select&&w!==void 0&&!Z)if(o&&w===f?.data&&l.select===this.#p)w=this.#d;else try{this.#p=l.select,w=l.select(w),w=Lc(o?.data,w,l),this.#d=w,this.#i=null}catch(J){this.#i=J}this.#i&&(k=this.#i,w=this.#d,M=Date.now(),D="error");const L=b.fetchStatus==="fetching",G=D==="pending",Q=D==="error",ee=G&&L,K=w!==void 0,ue={status:D,fetchStatus:b.fetchStatus,isPending:G,isSuccess:D==="success",isError:Q,isInitialLoading:ee,isLoading:ee,data:w,dataUpdatedAt:b.dataUpdatedAt,error:k,errorUpdatedAt:M,failureCount:b.fetchFailureCount,failureReason:b.fetchFailureReason,errorUpdateCount:b.errorUpdateCount,isFetched:b.dataUpdateCount>0||b.errorUpdateCount>0,isFetchedAfterMount:b.dataUpdateCount>g.dataUpdateCount||b.errorUpdateCount>g.errorUpdateCount,isFetching:L,isRefetching:L&&!G,isLoadingError:Q&&!K,isPaused:b.fetchStatus==="paused",isPlaceholderData:z,isRefetchError:Q&&K,isStale:io(n,l),refetch:this.refetch,promise:this.#u,isEnabled:Lt(l.enabled,n)!==!1};if(this.options.experimental_prefetchInRender){const J=$e=>{ue.status==="error"?$e.reject(ue.error):ue.data!==void 0&&$e.resolve(ue.data)},Oe=()=>{const $e=this.#u=ue.promise=Qc();J($e)},tt=this.#u;switch(tt.status){case"pending":n.queryHash===u.queryHash&&J(tt);break;case"fulfilled":(ue.status==="error"||ue.data!==tt.value)&&Oe();break;case"rejected":(ue.status!=="error"||ue.error!==tt.reason)&&Oe();break}}return ue}updateResult(){const n=this.#a,l=this.createResult(this.#e,this.options);if(this.#l=this.#e.state,this.#s=this.options,this.#l.data!==void 0&&(this.#h=this.#e),Hc(l,n))return;this.#a=l;const u=()=>{if(!n)return!0;const{notifyOnChangeProps:s}=this.options,o=typeof s=="function"?s():s;if(o==="all"||!o&&!this.#m.size)return!0;const f=new Set(o??this.#m);return this.options.throwOnError&&f.add("error"),Object.keys(this.#a).some(h=>{const y=h;return this.#a[y]!==n[y]&&f.has(y)})};this.#E({listeners:u()})}#_(){const n=this.#t.getQueryCache().build(this.#t,this.options);if(n===this.#e)return;const l=this.#e;this.#e=n,this.#n=n.state,this.hasListeners()&&(l?.removeObserver(this),n.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#b()}#E(n){ut.batch(()=>{n.listeners&&this.listeners.forEach(l=>{l(this.#a)}),this.#t.getQueryCache().notify({query:this.#e,type:"observerResultsUpdated"})})}};function T0(n,l){return Lt(l.enabled,n)!==!1&&n.state.data===void 0&&!(n.state.status==="error"&&l.retryOnMount===!1)}function Nm(n,l){return T0(n,l)||n.state.data!==void 0&&Gc(n,l,l.refetchOnMount)}function Gc(n,l,u){if(Lt(l.enabled,n)!==!1&&Qn(l.staleTime,n)!=="static"){const s=typeof u=="function"?u(n):u;return s==="always"||s!==!1&&io(n,l)}return!1}function jm(n,l,u,s){return(n!==l||Lt(s.enabled,n)===!1)&&(!u.suspense||n.state.status!=="error")&&io(n,u)}function io(n,l){return Lt(l.enabled,n)!==!1&&n.isStaleByTime(Qn(l.staleTime,n))}function O0(n,l){return!Hc(n.getCurrentResult(),l)}var _p=re.createContext(void 0),N0=n=>{const l=re.useContext(_p);if(!l)throw new Error("No QueryClient set, use QueryClientProvider to set one");return l},j0=({client:n,children:l})=>(re.useEffect(()=>(n.mount(),()=>{n.unmount()}),[n]),m.jsx(_p.Provider,{value:n,children:l})),Ep=re.createContext(!1),A0=()=>re.useContext(Ep);Ep.Provider;function R0(){let n=!1;return{clearReset:()=>{n=!1},reset:()=>{n=!0},isReset:()=>n}}var w0=re.createContext(R0()),D0=()=>re.useContext(w0),C0=(n,l)=>{(n.suspense||n.throwOnError||n.experimental_prefetchInRender)&&(l.isReset()||(n.retryOnMount=!1))},M0=n=>{re.useEffect(()=>{n.clearReset()},[n])},U0=({result:n,errorResetBoundary:l,throwOnError:u,query:s,suspense:o})=>n.isError&&!l.isReset()&&!n.isFetching&&s&&(o&&n.data===void 0||o0(u,[n.error,s])),Z0=n=>{if(n.suspense){const l=s=>s==="static"?s:Math.max(s??1e3,1e3),u=n.staleTime;n.staleTime=typeof u=="function"?(...s)=>l(u(...s)):l(u),typeof n.gcTime=="number"&&(n.gcTime=Math.max(n.gcTime,1e3))}},q0=(n,l)=>n.isLoading&&n.isFetching&&!l,B0=(n,l)=>n?.suspense&&l.isPending,Am=(n,l,u)=>l.fetchOptimistic(n).catch(()=>{u.clearReset()});function H0(n,l,u){const s=A0(),o=D0(),f=N0(),h=f.defaultQueryOptions(n);f.getDefaultOptions().queries?._experimental_beforeQuery?.(h),h._optimisticResults=s?"isRestoring":"optimistic",Z0(h),C0(h,o),M0(o);const y=!f.getQueryCache().get(h.queryHash),[g]=re.useState(()=>new l(f,h)),v=g.getOptimisticResult(h),b=!s&&n.subscribed!==!1;if(re.useSyncExternalStore(re.useCallback(z=>{const w=b?g.subscribe(ut.batchCalls(z)):yt;return g.updateResult(),w},[g,b]),()=>g.getCurrentResult(),()=>g.getCurrentResult()),re.useEffect(()=>{g.setOptions(h)},[h,g]),B0(h,v))throw Am(h,g,o);if(U0({result:v,errorResetBoundary:o,throwOnError:h.throwOnError,query:f.getQueryCache().get(h.queryHash),suspense:h.suspense}))throw v.error;return f.getDefaultOptions().queries?._experimental_afterQuery?.(h,v),h.experimental_prefetchInRender&&!ha&&q0(v,s)&&(y?Am(h,g,o):f.getQueryCache().get(h.queryHash)?.promise)?.catch(yt).finally(()=>{g.updateResult()}),h.notifyOnChangeProps?v:g.trackResult(v)}function uo(n,l){return H0(n,z0)}var k0=function(){return null};function Rm(n,l){if(typeof n=="function")return n(l);n!=null&&(n.current=l)}function L0(...n){return l=>{let u=!1;const s=n.map(o=>{const f=Rm(o,l);return!u&&typeof f=="function"&&(u=!0),f});if(u)return()=>{for(let o=0;o<s.length;o++){const f=s[o];typeof f=="function"?f():Rm(n[o],null)}}}}function Q0(n){const l=Y0(n),u=re.forwardRef((s,o)=>{const{children:f,...h}=s,y=re.Children.toArray(f),g=y.find(X0);if(g){const v=g.props.children,b=y.map(z=>z===g?re.Children.count(v)>1?re.Children.only(null):re.isValidElement(v)?v.props.children:null:z);return m.jsx(l,{...h,ref:o,children:re.isValidElement(v)?re.cloneElement(v,void 0,b):null})}return m.jsx(l,{...h,ref:o,children:f})});return u.displayName=`${n}.Slot`,u}var G0=Q0("Slot");function Y0(n){const l=re.forwardRef((u,s)=>{const{children:o,...f}=u;if(re.isValidElement(o)){const h=$0(o),y=K0(f,o.props);return o.type!==re.Fragment&&(y.ref=s?L0(s,h):h),re.cloneElement(o,y)}return re.Children.count(o)>1?re.Children.only(null):null});return l.displayName=`${n}.SlotClone`,l}var V0=Symbol("radix.slottable");function X0(n){return re.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===V0}function K0(n,l){const u={...l};for(const s in l){const o=n[s],f=l[s];/^on[A-Z]/.test(s)?o&&f?u[s]=(...y)=>{const g=f(...y);return o(...y),g}:o&&(u[s]=o):s==="style"?u[s]={...o,...f}:s==="className"&&(u[s]=[o,f].filter(Boolean).join(" "))}return{...n,...u}}function $0(n){let l=Object.getOwnPropertyDescriptor(n.props,"ref")?.get,u=l&&"isReactWarning"in l&&l.isReactWarning;return u?n.ref:(l=Object.getOwnPropertyDescriptor(n,"ref")?.get,u=l&&"isReactWarning"in l&&l.isReactWarning,u?n.props.ref:n.props.ref||n.ref)}function zp(n){var l,u,s="";if(typeof n=="string"||typeof n=="number")s+=n;else if(typeof n=="object")if(Array.isArray(n)){var o=n.length;for(l=0;l<o;l++)n[l]&&(u=zp(n[l]))&&(s&&(s+=" "),s+=u)}else for(u in n)n[u]&&(s&&(s+=" "),s+=u);return s}function Tp(){for(var n,l,u=0,s="",o=arguments.length;u<o;u++)(n=arguments[u])&&(l=zp(n))&&(s&&(s+=" "),s+=l);return s}const wm=n=>typeof n=="boolean"?`${n}`:n===0?"0":n,Dm=Tp,Iu=(n,l)=>u=>{var s;if(l?.variants==null)return Dm(n,u?.class,u?.className);const{variants:o,defaultVariants:f}=l,h=Object.keys(o).map(v=>{const b=u?.[v],z=f?.[v];if(b===null)return null;const w=wm(b)||wm(z);return o[v][w]}),y=u&&Object.entries(u).reduce((v,b)=>{let[z,w]=b;return w===void 0||(v[z]=w),v},{}),g=l==null||(s=l.compoundVariants)===null||s===void 0?void 0:s.reduce((v,b)=>{let{class:z,className:w,...k}=b;return Object.entries(k).every(M=>{let[D,Z]=M;return Array.isArray(Z)?Z.includes({...f,...y}[D]):{...f,...y}[D]===Z})?[...v,z,w]:v},[]);return Dm(n,h,g,u?.class,u?.className)};function gt(...n){return Tp(n)}const F0=Iu("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-smooth focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{primary:"bg-primary text-primary-foreground shadow hover:bg-primary/90",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{sm:"h-8 rounded-md px-3 text-xs",md:"h-9 px-4 py-2",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"primary",size:"md"}}),de=re.forwardRef(({className:n,variant:l,size:u,asChild:s=!1,loading:o=!1,disabled:f,children:h,...y},g)=>{const v=s?G0:"button";return m.jsxs(v,{className:gt(F0({variant:l,size:u,className:n})),ref:g,disabled:f||o,"aria-disabled":f||o,...y,children:[o&&m.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","aria-hidden":"true",children:[m.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),m.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),h]})});de.displayName="Button";const J0=Iu("flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-smooth",{variants:{variant:{default:"",search:"pl-10",error:"border-destructive focus-visible:ring-destructive"},size:{sm:"h-8 text-xs",md:"h-9",lg:"h-10 text-base"}},defaultVariants:{variant:"default",size:"md"}}),al=re.forwardRef(({className:n,variant:l,size:u,type:s="text",error:o,leftIcon:f,rightIcon:h,label:y,required:g,id:v,...b},z)=>{const w=v||re.useId(),k=`${w}-error`;return m.jsxs("div",{className:"space-y-2",children:[y&&m.jsxs("label",{htmlFor:w,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:[y,g&&m.jsx("span",{className:"text-destructive ml-1","aria-label":"required",children:"*"})]}),m.jsxs("div",{className:"relative",children:[f&&m.jsx("div",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground pointer-events-none",children:f}),m.jsx("input",{type:s,className:gt(J0({variant:o?"error":l,size:u,className:n}),f&&"pl-10",h&&"pr-10"),ref:z,id:w,"aria-invalid":o?"true":"false","aria-describedby":o?k:void 0,"aria-required":g,...b}),h&&m.jsx("div",{className:"absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground",children:h})]}),o&&m.jsx("p",{id:k,className:"text-sm text-destructive",role:"alert","aria-live":"polite",children:o})]})});al.displayName="Input";const P0=Iu("inline-flex items-center gap-1.5 rounded-full border px-2.5 py-0.5 text-xs font-medium transition-smooth focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-border bg-background text-foreground hover:bg-accent",primary:"border-primary/20 bg-primary/10 text-primary hover:bg-primary/20",secondary:"border-secondary bg-secondary text-secondary-foreground hover:bg-secondary/80",success:"border-green-200 bg-green-50 text-green-700 hover:bg-green-100",warning:"border-yellow-200 bg-yellow-50 text-yellow-700 hover:bg-yellow-100",error:"border-red-200 bg-red-50 text-red-700 hover:bg-red-100",outline:"border-border bg-transparent hover:bg-accent"},size:{sm:"text-xs px-2 py-0.5",md:"text-sm px-2.5 py-0.5",lg:"text-sm px-3 py-1"},clickable:{true:"cursor-pointer hover:shadow-sm",false:"cursor-default"}},defaultVariants:{variant:"default",size:"md",clickable:!1}}),Re=re.forwardRef(({className:n,variant:l,size:u,clickable:s,children:o,removable:f=!1,onRemove:h,icon:y,count:g,selected:v=!1,disabled:b=!1,onClick:z,onKeyDown:w,...k},M)=>{const D=Q=>{b||((Q.key==="Enter"||Q.key===" ")&&(Q.preventDefault(),z?.(Q)),(Q.key==="Delete"||Q.key==="Backspace")&&f&&h&&(Q.preventDefault(),h(Q)),w?.(Q))},Z=Q=>{Q.stopPropagation(),h?.(Q)},L=s||!!z,G=v;return m.jsxs("div",{ref:M,className:gt(P0({variant:G?"primary":l,size:u,clickable:L,className:n}),b&&"opacity-50 cursor-not-allowed",G&&"ring-2 ring-primary ring-offset-1"),onClick:b?void 0:z,onKeyDown:D,tabIndex:L&&!b?0:void 0,role:L?"button":void 0,"aria-pressed":G?"true":void 0,"aria-disabled":b,...k,children:[y&&m.jsx("span",{className:"flex-shrink-0","aria-hidden":"true",children:y}),m.jsx("span",{className:"truncate",children:o}),g!==void 0&&g>0&&m.jsx("span",{className:"flex-shrink-0 ml-1 px-1.5 py-0.5 text-xs bg-background/50 rounded-full","aria-label":`${g} items`,children:g}),f&&m.jsx("button",{type:"button",className:"flex-shrink-0 ml-1 p-0.5 rounded-full hover:bg-background/50 focus:outline-none focus:ring-1 focus:ring-ring transition-colors",onClick:Z,"aria-label":"Remove tag",tabIndex:-1,children:m.jsx("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:m.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})});Re.displayName="TagItem";const W0=Iu("group relative overflow-hidden rounded-lg border bg-card text-card-foreground shadow-sm transition-smooth hover:shadow-md focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2",{variants:{variant:{default:"border-border hover:border-border/80",selected:"border-primary ring-2 ring-primary ring-offset-2",error:"border-destructive bg-destructive/5"},size:{sm:"w-32 h-32",md:"w-40 h-40",lg:"w-48 h-48",xl:"w-56 h-56"},layout:{grid:"aspect-square",list:"aspect-auto flex-row h-20"}},defaultVariants:{variant:"default",size:"md",layout:"grid"}}),fa=re.forwardRef(({className:n,variant:l,size:u,layout:s,file:o,selected:f=!1,onSelect:h,onDoubleClick:y,showFileName:g=!0,showFileInfo:v=!1,showCheckbox:b=!1,loading:z=!1,error:w,onClick:k,onKeyDown:M,...D},Z)=>{const[L,G]=re.useState(!1),[Q,ee]=re.useState(!1),K=le=>{le.detail!==2&&(h?.(o.id,!f),k?.(le))},pe=le=>{le.preventDefault(),y?.(o)},ue=le=>{le.key==="Enter"?(le.preventDefault(),y?.(o)):le.key===" "&&(le.preventDefault(),h?.(o.id,!f)),M?.(le)},J=le=>{if(le===0)return"0 B";const nt=1024,Qt=["B","KB","MB","GB"],De=Math.floor(Math.log(le)/Math.log(nt));return parseFloat((le/Math.pow(nt,De)).toFixed(1))+" "+Qt[De]},Oe=le=>{const nt=le.toLowerCase();return nt.includes("image")?"🖼️":nt.includes("video")?"🎥":nt.includes("audio")?"🎵":nt.includes("pdf")?"📄":"📁"},tt=o.fileType.startsWith("image/"),$e=o.thumbnailPath||o.filePath;return m.jsxs("div",{ref:Z,className:gt(W0({variant:f?"selected":w?"error":l,size:s==="list"?void 0:u,layout:s,className:n})),onClick:K,onDoubleClick:pe,onKeyDown:ue,tabIndex:0,role:"button","aria-pressed":f,"aria-label":`File: ${o.fileName}`,...D,children:[b&&m.jsx("div",{className:"absolute top-2 left-2 z-10",children:m.jsx("input",{type:"checkbox",checked:f,onChange:()=>h?.(o.id,!f),className:"w-4 h-4 rounded border-border bg-background","aria-label":`Select ${o.fileName}`})}),m.jsxs("div",{className:gt("flex h-full",s==="list"?"flex-row items-center gap-3 p-3":"flex-col"),children:[m.jsxs("div",{className:gt("relative overflow-hidden bg-muted flex items-center justify-center",s==="list"?"w-14 h-14 rounded flex-shrink-0":"flex-1 w-full rounded-t-lg"),children:[z?m.jsx("div",{className:"flex items-center justify-center w-full h-full",children:m.jsx("div",{className:"w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin"})}):w||Q?m.jsxs("div",{className:"flex flex-col items-center justify-center text-muted-foreground",children:[m.jsx("span",{className:"text-2xl",children:"⚠️"}),m.jsx("span",{className:"text-xs mt-1",children:"Error"})]}):tt?m.jsxs(m.Fragment,{children:[m.jsx("img",{src:$e,alt:o.fileName,className:gt("object-cover transition-opacity duration-200","w-full h-full",L?"opacity-100":"opacity-0"),onLoad:()=>G(!0),onError:()=>ee(!0),loading:"lazy"}),!L&&m.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:m.jsx("div",{className:"w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"})})]}):m.jsxs("div",{className:"flex flex-col items-center justify-center text-muted-foreground",children:[m.jsx("span",{className:"text-2xl",children:Oe(o.fileType)}),m.jsx("span",{className:"text-xs mt-1 uppercase",children:o.fileType.split("/")[1]})]}),m.jsx("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors"})]}),(g||v)&&m.jsxs("div",{className:gt("flex flex-col",s==="list"?"flex-1 min-w-0":"p-2 pt-1"),children:[g&&m.jsx("p",{className:"text-sm font-medium truncate",title:o.fileName,children:o.fileName}),v&&m.jsxs("div",{className:"text-xs text-muted-foreground space-y-0.5",children:[m.jsx("p",{children:J(o.fileSize)}),o.width&&o.height&&m.jsxs("p",{children:[o.width," × ",o.height]})]})]})]}),f&&m.jsx("div",{className:"absolute top-2 right-2 w-5 h-5 bg-primary rounded-full flex items-center justify-center",children:m.jsx("svg",{className:"w-3 h-3 text-primary-foreground",fill:"currentColor",viewBox:"0 0 20 20",children:m.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]})});fa.displayName="FileCard";const Op=re.forwardRef(({catalogPanel:n,galleryPanel:l,workbenchPanel:u,infoPanel:s,showCatalogPanel:o=!0,showWorkbench:f=!1,showInfoPanel:h=!0,isFullscreenGallery:y=!1,catalogPanelWidth:g=280,infoPanelWidth:v=320,workbenchHeight:b=300,onLayoutChange:z,className:w,...k},M)=>{if(y)return m.jsx("div",{ref:M,className:gt("h-screen w-screen bg-background",w),...k,children:l});const D=[o?`${g}px`:"0px","1fr",h?`${v}px`:"0px"].join(" "),Z=f?`1fr ${b}px`:"1fr";return m.jsxs("div",{ref:M,className:gt("h-screen w-screen bg-background overflow-hidden","grid gap-0",w),style:{gridTemplateColumns:D,gridTemplateRows:Z,gridTemplateAreas:f?'"catalog gallery info" "catalog workbench info"':'"catalog gallery info"'},...k,children:[o&&m.jsx("div",{className:"panel-border panel-shadow bg-card overflow-hidden",style:{gridArea:"catalog"},children:n}),m.jsx("div",{className:"bg-background overflow-hidden relative",style:{gridArea:"gallery"},children:l}),f&&u&&m.jsx("div",{className:"panel-border panel-shadow bg-card overflow-hidden border-t",style:{gridArea:"workbench"},children:u}),h&&m.jsx("div",{className:"panel-border panel-shadow bg-card overflow-hidden",style:{gridArea:"info"},children:s}),o&&m.jsx("div",{className:"absolute top-0 bottom-0 w-1 bg-border hover:bg-primary cursor-col-resize z-10 transition-colors",style:{left:`${g}px`},onMouseDown:L=>{console.log("Resize catalog panel",L)}}),h&&m.jsx("div",{className:"absolute top-0 bottom-0 w-1 bg-border hover:bg-primary cursor-col-resize z-10 transition-colors",style:{right:`${v}px`},onMouseDown:L=>{console.log("Resize info panel",L)}}),f&&m.jsx("div",{className:"absolute left-0 right-0 h-1 bg-border hover:bg-primary cursor-row-resize z-10 transition-colors",style:{bottom:`${b}px`},onMouseDown:L=>{console.log("Resize workbench",L)}})]})});Op.displayName="MainLayout";const Np=re.forwardRef(({currentLibraryName:n="默认档案库",searchQuery:l="",onSearchChange:u,tagCategories:s=[],onTagClick:o,onCategoryToggle:f,className:h,...y},g)=>m.jsxs("div",{ref:g,className:gt("h-full flex flex-col bg-card",h),...y,children:[m.jsxs("div",{className:"flex-shrink-0 p-4 border-b border-border space-y-3",children:[m.jsxs("div",{className:"flex items-center justify-between",children:[m.jsx(de,{variant:"ghost",size:"sm",children:"⚙️ 首选项"}),m.jsx(de,{variant:"ghost",size:"sm",children:"🔍 全局视图"})]}),m.jsx("div",{className:"space-y-2",children:m.jsxs(de,{variant:"outline",className:"w-full justify-start",children:["📚 ",n,m.jsx("span",{className:"ml-auto",children:"▼"})]})}),m.jsxs("div",{className:"space-y-2",children:[m.jsx(al,{placeholder:"搜索标签...",value:l,onChange:v=>u?.(v.target.value),leftIcon:m.jsx("span",{children:"🔍"}),variant:"search"}),m.jsx(de,{variant:"outline",size:"sm",className:"w-full justify-start",children:"🔽 筛选器"})]})]}),m.jsx("div",{className:"flex-1 overflow-y-auto",children:m.jsxs("div",{className:"p-4 space-y-4",children:[m.jsxs("div",{className:"space-y-2",children:[m.jsx("h3",{className:"text-sm font-medium text-muted-foreground flex items-center gap-2",children:"⭐ 标签看板"}),m.jsxs("div",{className:"flex flex-wrap gap-1",children:[m.jsx(Re,{variant:"primary",size:"sm",children:"风景"}),m.jsx(Re,{variant:"secondary",size:"sm",children:"人像"}),m.jsx(Re,{variant:"success",size:"sm",children:"街拍"})]})]}),s.length>0?s.map(v=>m.jsxs("div",{className:"space-y-2",children:[m.jsxs("button",{className:"w-full flex items-center justify-between text-sm font-medium text-foreground hover:text-primary transition-colors",onClick:()=>f?.(v.id),children:[m.jsxs("span",{className:"flex items-center gap-2",children:[m.jsx("span",{children:v.icon}),m.jsx("span",{children:v.name}),m.jsxs("span",{className:"text-xs text-muted-foreground",children:["(",v.tags.length,")"]})]}),m.jsx("span",{className:"text-xs",children:v.expanded?"▼":"▶"})]}),v.expanded&&m.jsx("div",{className:"pl-4 space-y-1",children:v.tags.map(b=>m.jsx(Re,{variant:b.selected?"primary":"outline",size:"sm",count:b.count,clickable:!0,onClick:()=>o?.(v.id,b.id),className:"w-full justify-start",children:b.name},b.id))})]},v.id)):m.jsxs(m.Fragment,{children:[m.jsxs("div",{className:"space-y-2",children:[m.jsx("h3",{className:"text-sm font-medium text-foreground flex items-center gap-2",children:"👤 研究者标签"}),m.jsxs("div",{className:"space-y-1",children:[m.jsx(Re,{variant:"outline",size:"sm",count:24,clickable:!0,children:"摄影师：张三"}),m.jsx(Re,{variant:"outline",size:"sm",count:18,clickable:!0,children:"拍摄地点：北京"}),m.jsx(Re,{variant:"outline",size:"sm",count:12,clickable:!0,children:"拍摄年代：2024"})]})]}),m.jsxs("div",{className:"space-y-2",children:[m.jsx("h3",{className:"text-sm font-medium text-foreground flex items-center gap-2",children:"📊 元数据"}),m.jsxs("div",{className:"space-y-1",children:[m.jsx(Re,{variant:"outline",size:"sm",count:156,clickable:!0,children:"JPEG"}),m.jsx(Re,{variant:"outline",size:"sm",count:89,clickable:!0,children:"1920×1080"}),m.jsx(Re,{variant:"outline",size:"sm",count:67,clickable:!0,children:"Canon EOS R5"})]})]}),m.jsxs("div",{className:"space-y-2",children:[m.jsx("h3",{className:"text-sm font-medium text-foreground flex items-center gap-2",children:"🤖 计算机视觉"}),m.jsxs("div",{className:"space-y-1",children:[m.jsx(Re,{variant:"success",size:"sm",count:45,clickable:!0,children:"标准曝光"}),m.jsx(Re,{variant:"warning",size:"sm",count:23,clickable:!0,children:"轻微过曝"}),m.jsx(Re,{variant:"primary",size:"sm",count:78,clickable:!0,children:"高清晰度"})]})]}),m.jsxs("div",{className:"space-y-2",children:[m.jsx("h3",{className:"text-sm font-medium text-foreground flex items-center gap-2",children:"🎯 内容识别"}),m.jsxs("div",{className:"space-y-1",children:[m.jsx(Re,{variant:"outline",size:"sm",count:89,clickable:!0,children:"风景"}),m.jsx(Re,{variant:"outline",size:"sm",count:67,clickable:!0,children:"建筑"}),m.jsx(Re,{variant:"outline",size:"sm",count:45,clickable:!0,children:"人物"})]})]}),m.jsxs("div",{className:"space-y-2",children:[m.jsx("h3",{className:"text-sm font-medium text-foreground flex items-center gap-2",children:"🧠 语义识别"}),m.jsxs("div",{className:"space-y-1",children:[m.jsx(Re,{variant:"outline",size:"sm",count:34,clickable:!0,children:"城市夜景"}),m.jsx(Re,{variant:"outline",size:"sm",count:28,clickable:!0,children:"自然风光"}),m.jsx(Re,{variant:"outline",size:"sm",count:19,clickable:!0,children:"人文纪实"})]})]})]})]})}),m.jsx("div",{className:"flex-shrink-0 p-4 border-t border-border",children:m.jsx(de,{variant:"outline",size:"sm",className:"w-full",children:"🚫 屏蔽器"})})]}));Np.displayName="CatalogPanel";const jp=re.forwardRef(({files:n=[],selectedFileIds:l=[],layout:u="grid",zoomLevel:s=50,sortBy:o="name",sortOrder:f="asc",searchQuery:h="",showFileName:y=!0,showFileInfo:g=!1,loading:v=!1,onFileSelect:b,onFileDoubleClick:z,onSearchChange:w,onLayoutChange:k,onZoomChange:M,onSortChange:D,onFileUpload:Z,className:L,...G},Q)=>{const ee=J=>{J.preventDefault(),J.stopPropagation()},K=J=>{J.preventDefault(),J.stopPropagation();const Oe=J.dataTransfer.files;Oe.length>0&&Z?.(Oe)},ue=(()=>{const Oe=s/50;return Math.max(80,Math.min(300,120*Oe))})();return m.jsxs("div",{ref:Q,className:gt("h-full flex flex-col bg-background",L),onDragOver:ee,onDrop:K,...G,children:[m.jsxs("div",{className:"flex-shrink-0 p-4 border-b border-border space-y-3",children:[m.jsxs("div",{className:"flex items-center justify-between",children:[m.jsxs("div",{className:"flex items-center gap-2",children:[m.jsx("span",{className:"text-sm text-muted-foreground",children:"显示方式:"}),m.jsx(de,{variant:u==="grid"?"primary":"outline",size:"sm",onClick:()=>k?.("grid"),children:"🔲 网格"}),m.jsx(de,{variant:u==="list"?"primary":"outline",size:"sm",onClick:()=>k?.("list"),children:"📋 列表"})]}),m.jsxs("div",{className:"flex items-center gap-2",children:[m.jsx("span",{className:"text-sm text-muted-foreground",children:"缩放:"}),m.jsx("input",{type:"range",min:"20",max:"100",value:s,onChange:J=>M?.(Number(J.target.value)),className:"w-20"}),m.jsxs("span",{className:"text-xs text-muted-foreground w-8",children:[s,"%"]})]})]}),m.jsxs("div",{className:"flex items-center gap-4",children:[m.jsx("div",{className:"flex-1",children:m.jsx(al,{placeholder:"搜索图像...",value:h,onChange:J=>w?.(J.target.value),leftIcon:m.jsx("span",{children:"🔍"}),variant:"search"})}),m.jsxs("div",{className:"flex items-center gap-2",children:[m.jsxs(de,{variant:"outline",size:"sm",children:["📊 排序: ",o,m.jsx("span",{className:"ml-1",children:f==="asc"?"↑":"↓"})]}),m.jsx(de,{variant:"outline",size:"sm",children:"🔽 筛选"})]})]}),m.jsxs("div",{className:"flex items-center justify-between",children:[m.jsxs("div",{className:"text-sm text-muted-foreground",children:["共 ",n.length," 个文件",l.length>0&&m.jsxs("span",{className:"ml-2 text-primary",children:["已选择 ",l.length," 个"]})]}),m.jsx(de,{variant:"primary",size:"sm",children:"📁 上传图像"})]})]}),m.jsx("div",{className:"flex-1 overflow-auto",children:v?m.jsx("div",{className:"h-full flex items-center justify-center",children:m.jsxs("div",{className:"text-center space-y-4",children:[m.jsx("div",{className:"w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto"}),m.jsx("p",{className:"text-muted-foreground",children:"加载中..."})]})}):n.length===0?m.jsx("div",{className:"h-full flex items-center justify-center",children:m.jsxs("div",{className:"text-center space-y-4 max-w-md",children:[m.jsx("div",{className:"text-6xl",children:"📁"}),m.jsx("h3",{className:"text-lg font-medium",children:"暂无图像"}),m.jsx("p",{className:"text-muted-foreground",children:"拖拽图片到此处上传，或点击上传按钮选择文件"}),m.jsx(de,{variant:"primary",children:"📁 选择图像"})]})}):m.jsx("div",{className:"p-4",children:u==="grid"?m.jsx("div",{className:"grid gap-4",style:{gridTemplateColumns:`repeat(auto-fill, minmax(${ue}px, 1fr))`},children:n.map(J=>m.jsx(fa,{file:J,selected:l.includes(J.id),onSelect:b,onDoubleClick:z,showFileName:y,showFileInfo:g,layout:"grid",style:{width:`${ue}px`,height:`${ue}px`}},J.id))}):m.jsx("div",{className:"space-y-2",children:n.map(J=>m.jsx(fa,{file:J,selected:l.includes(J.id),onSelect:b,onDoubleClick:z,showFileName:y,showFileInfo:g,layout:"list",className:"w-full"},J.id))})})}),m.jsx("div",{className:"flex-shrink-0 p-2 border-t border-border bg-muted/30",children:m.jsxs("div",{className:"flex items-center justify-between text-xs text-muted-foreground",children:[m.jsxs("span",{children:[u==="grid"?"网格视图":"列表视图"," • 缩放 ",s,"%"]}),m.jsxs("span",{children:[n.length," 项"]})]})})]})});jp.displayName="GalleryPanel";const Ap=re.forwardRef(({activeWorkbench:n=null,clipboardFiles:l=[],clusterData:u,onWorkbenchChange:s,onClipboardFileMove:o,onClusterOperation:f,className:h,...y},g)=>m.jsxs("div",{ref:g,className:gt("h-full flex flex-col bg-card",h),...y,children:[m.jsx("div",{className:"flex-shrink-0 border-b border-border",children:m.jsxs("div",{className:"flex items-center",children:[m.jsx(de,{variant:n==="clipboard"?"primary":"ghost",size:"sm",className:"rounded-none border-r",onClick:()=>s?.("clipboard"),children:"📋 剪贴板"}),m.jsx(de,{variant:n==="cluster"?"primary":"ghost",size:"sm",className:"rounded-none border-r",onClick:()=>s?.("cluster"),children:"🔗 图像簇整理"}),m.jsx("div",{className:"flex-1"}),m.jsx(de,{variant:"ghost",size:"sm",className:"rounded-none",onClick:()=>s?.(null),children:"✕"})]})}),m.jsxs("div",{className:"flex-1 overflow-hidden",children:[n==="clipboard"&&m.jsx(I0,{files:l,onFileMove:o}),n==="cluster"&&m.jsx(eb,{clusterData:u,onClusterOperation:f}),!n&&m.jsx("div",{className:"h-full flex items-center justify-center",children:m.jsxs("div",{className:"text-center space-y-4",children:[m.jsx("div",{className:"text-4xl",children:"🛠️"}),m.jsx("h3",{className:"text-lg font-medium",children:"选择工作台"}),m.jsx("p",{className:"text-muted-foreground max-w-md",children:"选择一个工作台组件开始工作，或从画廊拖拽图片到此处"}),m.jsxs("div",{className:"flex gap-2 justify-center",children:[m.jsx(de,{variant:"outline",onClick:()=>s?.("clipboard"),children:"📋 剪贴板"}),m.jsx(de,{variant:"outline",onClick:()=>s?.("cluster"),children:"🔗 图像簇整理"})]})]})})]})]})),I0=({files:n=[],onFileMove:l})=>m.jsxs("div",{className:"h-full relative bg-background overflow-auto",children:[n.length===0?m.jsx("div",{className:"h-full flex items-center justify-center",children:m.jsxs("div",{className:"text-center space-y-4",children:[m.jsx("div",{className:"text-4xl",children:"📋"}),m.jsx("h3",{className:"text-lg font-medium",children:"剪贴板为空"}),m.jsx("p",{className:"text-muted-foreground",children:"从画廊拖拽图片到此处开始整理"})]})}):m.jsx("div",{className:"p-4 min-h-full",children:n.map(u=>m.jsx("div",{className:"absolute",style:{left:u.position?.x||0,top:u.position?.y||0},children:m.jsx(fa,{file:u,showFileName:!0,className:"cursor-move",onMouseDown:s=>{console.log("Start dragging file",u.id,s)}})},u.id))}),m.jsxs("div",{className:"absolute top-4 right-4 flex gap-2",children:[m.jsx(de,{variant:"outline",size:"sm",children:"🔄 自动排列"}),m.jsx(de,{variant:"outline",size:"sm",children:"🗑️ 清空"})]})]}),eb=({clusterData:n,onClusterOperation:l})=>n?m.jsxs("div",{className:"h-full p-4 space-y-6 overflow-auto",children:[m.jsxs("div",{className:"space-y-2",children:[m.jsx("h3",{className:"text-sm font-medium text-foreground",children:"主图"}),m.jsx("div",{className:"border-2 border-dashed border-primary/50 rounded-lg p-4 min-h-[200px] flex items-center justify-center",children:n.mainImage?m.jsx(fa,{file:n.mainImage,showFileName:!0,showFileInfo:!0,size:"lg"}):m.jsxs("div",{className:"text-center text-muted-foreground",children:[m.jsx("div",{className:"text-2xl mb-2",children:"🖼️"}),m.jsx("p",{children:"拖拽图片到此处设为主图"})]})})]}),m.jsxs("div",{className:"space-y-2",children:[m.jsxs("h3",{className:"text-sm font-medium text-foreground",children:["相似图 (",n.similarImages?.length||0,")"]}),m.jsx("div",{className:"border border-border rounded-lg p-4 min-h-[120px]",children:n.similarImages&&n.similarImages.length>0?m.jsx("div",{className:"grid grid-cols-4 gap-2",children:n.similarImages.map(u=>m.jsx(fa,{file:u,showFileName:!0,size:"sm"},u.id))}):m.jsx("div",{className:"h-full flex items-center justify-center text-muted-foreground",children:m.jsxs("div",{className:"text-center",children:[m.jsx("div",{className:"text-xl mb-1",children:"📸"}),m.jsx("p",{className:"text-sm",children:"拖拽相似图片到此处"})]})})})]}),m.jsxs("div",{className:"space-y-2",children:[m.jsxs("h3",{className:"text-sm font-medium text-foreground",children:["版本图 (",n.versionImages?.length||0,")"]}),m.jsx("div",{className:"border border-border rounded-lg p-4 min-h-[120px]",children:n.versionImages&&n.versionImages.length>0?m.jsx("div",{className:"grid grid-cols-4 gap-2",children:n.versionImages.map(u=>m.jsx(fa,{file:u,showFileName:!0,size:"sm"},u.id))}):m.jsx("div",{className:"h-full flex items-center justify-center text-muted-foreground",children:m.jsxs("div",{className:"text-center",children:[m.jsx("div",{className:"text-xl mb-1",children:"🔄"}),m.jsx("p",{className:"text-sm",children:"拖拽版本图片到此处"})]})})})]}),m.jsxs("div",{className:"flex gap-2 pt-4 border-t border-border",children:[m.jsx(de,{variant:"destructive",size:"sm",children:"🗑️ 解散图像簇"}),m.jsx(de,{variant:"outline",size:"sm",children:"💾 保存更改"})]})]}):m.jsx("div",{className:"h-full flex items-center justify-center",children:m.jsxs("div",{className:"text-center space-y-4",children:[m.jsx("div",{className:"text-4xl",children:"🔗"}),m.jsx("h3",{className:"text-lg font-medium",children:"未选择图像簇"}),m.jsx("p",{className:"text-muted-foreground",children:"在画廊中双击图像簇卡片开始整理"})]})});Ap.displayName="WorkbenchPanel";const Rp=re.forwardRef(({selectedFile:n,selectedCount:l=0,activePanels:u=["metadata"],onPanelToggle:s,onTagOperation:o,className:f,...h},y)=>m.jsxs("div",{ref:y,className:gt("h-full flex flex-col bg-card",f),...h,children:[m.jsx("div",{className:"flex-shrink-0 border-b border-border",children:m.jsxs("div",{className:"flex flex-wrap",children:[m.jsx(de,{variant:u.includes("metadata")?"primary":"ghost",size:"sm",className:"rounded-none border-r text-xs",onClick:()=>s?.("metadata"),children:"📊 元数据"}),m.jsx(de,{variant:u.includes("rules")?"primary":"ghost",size:"sm",className:"rounded-none border-r text-xs",onClick:()=>s?.("rules"),children:"👤 规则"}),m.jsx(de,{variant:u.includes("cv")?"primary":"ghost",size:"sm",className:"rounded-none border-r text-xs",onClick:()=>s?.("cv"),children:"🤖 视觉"}),m.jsx(de,{variant:u.includes("ai")?"primary":"ghost",size:"sm",className:"rounded-none text-xs",onClick:()=>s?.("ai"),children:"🧠 AI"})]})}),m.jsx("div",{className:"flex-1 overflow-y-auto",children:n?m.jsxs("div",{className:"space-y-4",children:[u.includes("metadata")&&m.jsx(tb,{file:n}),u.includes("rules")&&m.jsx(nb,{file:n,onTagOperation:o}),u.includes("cv")&&m.jsx(ab,{file:n}),u.includes("ai")&&m.jsx(lb,{file:n})]}):m.jsx("div",{className:"h-full flex items-center justify-center",children:m.jsxs("div",{className:"text-center space-y-4",children:[m.jsx("div",{className:"text-4xl",children:"📄"}),m.jsx("h3",{className:"text-lg font-medium",children:"未选择文件"}),m.jsx("p",{className:"text-muted-foreground",children:l>0?`已选择 ${l} 个文件`:"在画廊中选择文件查看详细信息"})]})})})]})),tb=({file:n})=>{if(!n)return null;const l=u=>{if(u===0)return"0 B";const s=1024,o=["B","KB","MB","GB"],f=Math.floor(Math.log(u)/Math.log(s));return parseFloat((u/Math.pow(s,f)).toFixed(1))+" "+o[f]};return m.jsxs("div",{className:"p-4 space-y-4",children:[m.jsx("h3",{className:"text-sm font-medium text-foreground border-b pb-2",children:"📊 文件信息"}),m.jsx("div",{className:"flex justify-center",children:m.jsx("img",{src:n.thumbnailPath||n.filePath,alt:n.fileName,className:"w-32 h-32 object-cover rounded-lg border"})}),m.jsxs("div",{className:"space-y-2 text-sm",children:[m.jsxs("div",{className:"flex justify-between",children:[m.jsx("span",{className:"text-muted-foreground",children:"文件名:"}),m.jsx("span",{className:"font-medium truncate ml-2",children:n.fileName})]}),m.jsxs("div",{className:"flex justify-between",children:[m.jsx("span",{className:"text-muted-foreground",children:"大小:"}),m.jsx("span",{children:l(n.fileSize)})]}),m.jsxs("div",{className:"flex justify-between",children:[m.jsx("span",{className:"text-muted-foreground",children:"类型:"}),m.jsx("span",{children:n.fileType})]}),n.width&&n.height&&m.jsxs("div",{className:"flex justify-between",children:[m.jsx("span",{className:"text-muted-foreground",children:"尺寸:"}),m.jsxs("span",{children:[n.width," × ",n.height]})]}),n.createdAt&&m.jsxs("div",{className:"flex justify-between",children:[m.jsx("span",{className:"text-muted-foreground",children:"创建时间:"}),m.jsx("span",{children:new Date(n.createdAt).toLocaleDateString()})]}),m.jsxs("div",{className:"flex justify-between",children:[m.jsx("span",{className:"text-muted-foreground",children:"路径:"}),m.jsx("span",{className:"text-xs truncate ml-2",title:n.filePath,children:n.filePath})]})]}),n.tags?.metadata&&Object.keys(n.tags.metadata).length>0&&m.jsxs("div",{className:"space-y-2",children:[m.jsx("h4",{className:"text-xs font-medium text-muted-foreground",children:"元数据标签"}),m.jsx("div",{className:"flex flex-wrap gap-1",children:Object.entries(n.tags.metadata).map(([u,s])=>m.jsxs(Re,{variant:"outline",size:"sm",children:[u,": ",s]},u))})]}),m.jsxs("div",{className:"flex gap-2 pt-2 border-t",children:[m.jsx(de,{variant:"outline",size:"sm",className:"flex-1",children:"📁 打开位置"}),m.jsx(de,{variant:"outline",size:"sm",className:"flex-1",children:"📋 复制路径"})]})]})},nb=({file:n,onTagOperation:l})=>{const[u,s]=re.useState("");return m.jsxs("div",{className:"p-4 space-y-4",children:[m.jsx("h3",{className:"text-sm font-medium text-foreground border-b pb-2",children:"👤 研究者标签规则"}),m.jsxs("div",{className:"space-y-3",children:[m.jsx("h4",{className:"text-xs font-medium text-muted-foreground",children:"文件名生成标签"}),m.jsxs("div",{className:"p-3 bg-muted/30 rounded-lg space-y-2",children:[m.jsxs("div",{className:"text-xs text-muted-foreground",children:["当前文件名: ",n?.fileName]}),m.jsx(al,{placeholder:"输入标签模式，如：摄影师_年代_编号",size:"sm"}),m.jsx(de,{variant:"primary",size:"sm",className:"w-full",children:"🏷️ 应用规则"})]})]}),m.jsxs("div",{className:"space-y-3",children:[m.jsx("h4",{className:"text-xs font-medium text-muted-foreground",children:"手动添加标签"}),m.jsxs("div",{className:"flex gap-2",children:[m.jsx(al,{placeholder:"输入标签名称",value:u,onChange:o=>s(o.target.value),size:"sm",className:"flex-1"}),m.jsx(de,{variant:"primary",size:"sm",onClick:()=>{u.trim()&&(l?.("add","user",u.trim()),s(""))},children:"➕"})]})]}),n?.tags?.user&&n.tags.user.length>0&&m.jsxs("div",{className:"space-y-2",children:[m.jsx("h4",{className:"text-xs font-medium text-muted-foreground",children:"用户标签"}),m.jsx("div",{className:"flex flex-wrap gap-1",children:n.tags.user.map((o,f)=>m.jsx(Re,{variant:"secondary",size:"sm",removable:!0,onRemove:()=>l?.("remove","user",o),children:o},f))})]})]})},ab=({file:n})=>m.jsxs("div",{className:"p-4 space-y-4",children:[m.jsx("h3",{className:"text-sm font-medium text-foreground border-b pb-2",children:"🤖 计算机视觉分析"}),m.jsxs("div",{className:"space-y-2",children:[m.jsx("h4",{className:"text-xs font-medium text-muted-foreground",children:"质量评分"}),m.jsxs("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[m.jsxs("div",{className:"p-2 bg-muted/30 rounded",children:[m.jsx("div",{className:"text-muted-foreground",children:"清晰度"}),m.jsx("div",{className:"font-medium",children:"85/100"})]}),m.jsxs("div",{className:"p-2 bg-muted/30 rounded",children:[m.jsx("div",{className:"text-muted-foreground",children:"曝光"}),m.jsx("div",{className:"font-medium",children:"92/100"})]}),m.jsxs("div",{className:"p-2 bg-muted/30 rounded",children:[m.jsx("div",{className:"text-muted-foreground",children:"色彩"}),m.jsx("div",{className:"font-medium",children:"78/100"})]}),m.jsxs("div",{className:"p-2 bg-muted/30 rounded",children:[m.jsx("div",{className:"text-muted-foreground",children:"构图"}),m.jsx("div",{className:"font-medium",children:"88/100"})]})]})]}),m.jsxs("div",{className:"space-y-2",children:[m.jsx("h4",{className:"text-xs font-medium text-muted-foreground",children:"视觉映射配置"}),m.jsx(de,{variant:"outline",size:"sm",className:"w-full",children:"⚙️ 配置映射规则"})]}),m.jsxs("div",{className:"space-y-2",children:[m.jsx("h4",{className:"text-xs font-medium text-muted-foreground",children:"生成标签"}),m.jsxs("div",{className:"flex flex-wrap gap-1",children:[m.jsx(Re,{variant:"success",size:"sm",children:"标准曝光"}),m.jsx(Re,{variant:"primary",size:"sm",children:"高清晰度"}),m.jsx(Re,{variant:"warning",size:"sm",children:"色彩偏暖"})]})]})]}),lb=({file:n})=>m.jsxs("div",{className:"p-4 space-y-4",children:[m.jsx("h3",{className:"text-sm font-medium text-foreground border-b pb-2",children:"🧠 AI分析"}),m.jsxs("div",{className:"space-y-2",children:[m.jsx("h4",{className:"text-xs font-medium text-muted-foreground",children:"模型配置"}),m.jsxs("div",{className:"space-y-2",children:[m.jsx(al,{placeholder:"API Key",size:"sm",type:"password"}),m.jsxs("select",{className:"w-full p-2 text-xs border border-border rounded",children:[m.jsx("option",{children:"GPT-4 Vision"}),m.jsx("option",{children:"Claude Vision"}),m.jsx("option",{children:"Gemini Pro Vision"})]})]})]}),m.jsxs("div",{className:"space-y-2",children:[m.jsx("h4",{className:"text-xs font-medium text-muted-foreground",children:"图像描述"}),m.jsx("div",{className:"p-3 bg-muted/30 rounded-lg text-xs",children:"这是一张城市夜景照片，展现了现代建筑群在夜晚的璀璨灯光。画面构图均衡，色彩层次丰富，具有很强的视觉冲击力。"})]}),n?.tags?.ai&&n.tags.ai.length>0&&m.jsxs("div",{className:"space-y-2",children:[m.jsx("h4",{className:"text-xs font-medium text-muted-foreground",children:"AI标签"}),m.jsx("div",{className:"flex flex-wrap gap-1",children:n.tags.ai.map((l,u)=>m.jsx(Re,{variant:"primary",size:"sm",children:l},u))})]}),m.jsxs("div",{className:"space-y-2",children:[m.jsx(de,{variant:"primary",size:"sm",className:"w-full",children:"🔄 重新分析"}),m.jsx(de,{variant:"outline",size:"sm",className:"w-full",children:"💾 保存结果"})]})]});Rp.displayName="InfoPanel";const Cm=n=>{let l;const u=new Set,s=(v,b)=>{const z=typeof v=="function"?v(l):v;if(!Object.is(z,l)){const w=l;l=b??(typeof z!="object"||z===null)?z:Object.assign({},l,z),u.forEach(k=>k(l,w))}},o=()=>l,y={setState:s,getState:o,getInitialState:()=>g,subscribe:v=>(u.add(v),()=>u.delete(v))},g=l=n(s,o,y);return y},ib=n=>n?Cm(n):Cm,ub=n=>n;function sb(n,l=ub){const u=mm.useSyncExternalStore(n.subscribe,()=>l(n.getState()),()=>l(n.getInitialState()));return mm.useDebugValue(u),u}const rb=n=>{const l=ib(n),u=s=>sb(l,s);return Object.assign(u,l),u},cb=n=>rb,Mm={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1},di=new Map,ku=n=>{const l=di.get(n);return l?Object.fromEntries(Object.entries(l.stores).map(([u,s])=>[u,s.getState()])):{}},ob=(n,l,u)=>{if(n===void 0)return{type:"untracked",connection:l.connect(u)};const s=di.get(u.name);if(s)return{type:"tracked",store:n,...s};const o={connection:l.connect(u),stores:{}};return di.set(u.name,o),{type:"tracked",store:n,...o}},fb=(n,l)=>{if(l===void 0)return;const u=di.get(n);u&&(delete u.stores[l],Object.keys(u.stores).length===0&&di.delete(n))},db=n=>{var l,u;if(!n)return;const s=n.split(`
`),o=s.findIndex(h=>h.includes("api.setState"));if(o<0)return;const f=((l=s[o+1])==null?void 0:l.trim())||"";return(u=/.+ (.+) .+/.exec(f))==null?void 0:u[1]},hb=(n,l={})=>(u,s,o)=>{const{enabled:f,anonymousActionType:h,store:y,...g}=l;let v;try{v=(f??(Mm?"production":void 0)!=="production")&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!v)return n(u,s,o);const{connection:b,...z}=ob(y,v,g);let w=!0;o.setState=(D,Z,L)=>{const G=u(D,Z);if(!w)return G;const Q=L===void 0?{type:h||db(new Error().stack)||"anonymous"}:typeof L=="string"?{type:L}:L;return y===void 0?(b?.send(Q,s()),G):(b?.send({...Q,type:`${y}/${Q.type}`},{...ku(g.name),[y]:o.getState()}),G)},o.devtools={cleanup:()=>{b&&typeof b.unsubscribe=="function"&&b.unsubscribe(),fb(g.name,y)}};const k=(...D)=>{const Z=w;w=!1,u(...D),w=Z},M=n(o.setState,s,o);if(z.type==="untracked"?b?.init(M):(z.stores[z.store]=o,b?.init(Object.fromEntries(Object.entries(z.stores).map(([D,Z])=>[D,D===z.store?M:Z.getState()])))),o.dispatchFromDevtools&&typeof o.dispatch=="function"){let D=!1;const Z=o.dispatch;o.dispatch=(...L)=>{(Mm?"production":void 0)!=="production"&&L[0].type==="__setState"&&!D&&(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),D=!0),Z(...L)}}return b.subscribe(D=>{var Z;switch(D.type){case"ACTION":if(typeof D.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return Dc(D.payload,L=>{if(L.type==="__setState"){if(y===void 0){k(L.state);return}Object.keys(L.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const G=L.state[y];if(G==null)return;JSON.stringify(o.getState())!==JSON.stringify(G)&&k(G);return}o.dispatchFromDevtools&&typeof o.dispatch=="function"&&o.dispatch(L)});case"DISPATCH":switch(D.payload.type){case"RESET":return k(M),y===void 0?b?.init(o.getState()):b?.init(ku(g.name));case"COMMIT":if(y===void 0){b?.init(o.getState());return}return b?.init(ku(g.name));case"ROLLBACK":return Dc(D.state,L=>{if(y===void 0){k(L),b?.init(o.getState());return}k(L[y]),b?.init(ku(g.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return Dc(D.state,L=>{if(y===void 0){k(L);return}JSON.stringify(o.getState())!==JSON.stringify(L[y])&&k(L[y])});case"IMPORT_STATE":{const{nextLiftedState:L}=D.payload,G=(Z=L.computedStates.slice(-1)[0])==null?void 0:Z.state;if(!G)return;k(y===void 0?G:G[y]),b?.send(null,L);return}case"PAUSE_RECORDING":return w=!w}return}}),M},mb=hb,Dc=(n,l)=>{let u;try{u=JSON.parse(n)}catch(s){console.error("[zustand devtools middleware] Could not parse the received json",s)}u!==void 0&&l(u)},Um={showCatalogPanel:!0,showInfoPanel:!0,showWorkbench:!1,isFullscreenGallery:!1,catalogPanelWidth:280,infoPanelWidth:320,workbenchHeight:300,selectedCaseId:null,selectedFileIds:[],searchQuery:"",activeFilters:{},galleryLayout:"grid",galleryZoomLevel:50,gallerySortBy:"name",gallerySortOrder:"asc",showFileName:!0,showFileInfo:!1,activeWorkbench:null,clipboardFiles:[],activePanels:["metadata"]},yi=cb()(mb((n,l)=>({...Um,toggleCatalogPanel:()=>n(u=>({showCatalogPanel:!u.showCatalogPanel})),toggleInfoPanel:()=>n(u=>({showInfoPanel:!u.showInfoPanel})),toggleWorkbench:()=>n(u=>({showWorkbench:!u.showWorkbench,activeWorkbench:u.showWorkbench?null:"clipboard"})),toggleFullscreenGallery:()=>n(u=>({isFullscreenGallery:!u.isFullscreenGallery})),setCatalogPanelWidth:u=>n({catalogPanelWidth:u}),setInfoPanelWidth:u=>n({infoPanelWidth:u}),setWorkbenchHeight:u=>n({workbenchHeight:u}),setSelectedCase:u=>n({selectedCaseId:u}),setSelectedFiles:u=>n({selectedFileIds:u}),addSelectedFile:u=>n(s=>({selectedFileIds:s.selectedFileIds.includes(u)?s.selectedFileIds:[...s.selectedFileIds,u]})),removeSelectedFile:u=>n(s=>({selectedFileIds:s.selectedFileIds.filter(o=>o!==u)})),toggleFileSelection:u=>{const{selectedFileIds:s}=l();s.includes(u)?l().removeSelectedFile(u):l().addSelectedFile(u)},clearSelection:()=>n({selectedFileIds:[]}),setSearchQuery:u=>n({searchQuery:u}),setFilter:(u,s)=>n(o=>({activeFilters:{...o.activeFilters,[u]:s}})),removeFilter:u=>n(s=>{const{[u]:o,...f}=s.activeFilters;return{activeFilters:f}}),clearFilters:()=>n({activeFilters:{}}),setGalleryLayout:u=>n({galleryLayout:u}),setGalleryZoom:u=>n({galleryZoomLevel:u}),setGallerySort:(u,s)=>n({gallerySortBy:u,gallerySortOrder:s}),toggleShowFileName:()=>n(u=>({showFileName:!u.showFileName})),toggleShowFileInfo:()=>n(u=>({showFileInfo:!u.showFileInfo})),setActiveWorkbench:u=>n({activeWorkbench:u}),addToClipboard:(u,s={x:0,y:0})=>n(o=>({clipboardFiles:o.clipboardFiles.find(f=>f.id===u)?o.clipboardFiles:[...o.clipboardFiles,{id:u,position:s}]})),removeFromClipboard:u=>n(s=>({clipboardFiles:s.clipboardFiles.filter(o=>o.id!==u)})),updateClipboardPosition:(u,s)=>n(o=>({clipboardFiles:o.clipboardFiles.map(f=>f.id===u?{...f,position:s}:f)})),clearClipboard:()=>n({clipboardFiles:[]}),toggleInfoPanelSection:u=>n(s=>({activePanels:s.activePanels.includes(u)?s.activePanels.filter(o=>o!==u):[...s.activePanels,u]})),resetUI:()=>n(Um)}),{name:"mizzy-star-ui-store"})),pb=()=>yi(n=>({showCatalogPanel:n.showCatalogPanel,showInfoPanel:n.showInfoPanel,showWorkbench:n.showWorkbench,isFullscreenGallery:n.isFullscreenGallery})),vb=()=>yi(n=>({selectedCaseId:n.selectedCaseId,selectedFileIds:n.selectedFileIds})),yb=()=>yi(n=>({layout:n.galleryLayout,zoomLevel:n.galleryZoomLevel,sortBy:n.gallerySortBy,sortOrder:n.gallerySortOrder,showFileName:n.showFileName,showFileInfo:n.showFileInfo})),gb=()=>yi(n=>({searchQuery:n.searchQuery,activeFilters:n.activeFilters}));function wp(n,l){return function(){return n.apply(l,arguments)}}const{toString:bb}=Object.prototype,{getPrototypeOf:so}=Object,{iterator:es,toStringTag:Dp}=Symbol,ts=(n=>l=>{const u=bb.call(l);return n[u]||(n[u]=u.slice(8,-1).toLowerCase())})(Object.create(null)),Xt=n=>(n=n.toLowerCase(),l=>ts(l)===n),ns=n=>l=>typeof l===n,{isArray:ll}=Array,hi=ns("undefined");function gi(n){return n!==null&&!hi(n)&&n.constructor!==null&&!hi(n.constructor)&&bt(n.constructor.isBuffer)&&n.constructor.isBuffer(n)}const Cp=Xt("ArrayBuffer");function xb(n){let l;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?l=ArrayBuffer.isView(n):l=n&&n.buffer&&Cp(n.buffer),l}const Sb=ns("string"),bt=ns("function"),Mp=ns("number"),bi=n=>n!==null&&typeof n=="object",_b=n=>n===!0||n===!1,Vu=n=>{if(ts(n)!=="object")return!1;const l=so(n);return(l===null||l===Object.prototype||Object.getPrototypeOf(l)===null)&&!(Dp in n)&&!(es in n)},Eb=n=>{if(!bi(n)||gi(n))return!1;try{return Object.keys(n).length===0&&Object.getPrototypeOf(n)===Object.prototype}catch{return!1}},zb=Xt("Date"),Tb=Xt("File"),Ob=Xt("Blob"),Nb=Xt("FileList"),jb=n=>bi(n)&&bt(n.pipe),Ab=n=>{let l;return n&&(typeof FormData=="function"&&n instanceof FormData||bt(n.append)&&((l=ts(n))==="formdata"||l==="object"&&bt(n.toString)&&n.toString()==="[object FormData]"))},Rb=Xt("URLSearchParams"),[wb,Db,Cb,Mb]=["ReadableStream","Request","Response","Headers"].map(Xt),Ub=n=>n.trim?n.trim():n.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function xi(n,l,{allOwnKeys:u=!1}={}){if(n===null||typeof n>"u")return;let s,o;if(typeof n!="object"&&(n=[n]),ll(n))for(s=0,o=n.length;s<o;s++)l.call(null,n[s],s,n);else{if(gi(n))return;const f=u?Object.getOwnPropertyNames(n):Object.keys(n),h=f.length;let y;for(s=0;s<h;s++)y=f[s],l.call(null,n[y],y,n)}}function Up(n,l){if(gi(n))return null;l=l.toLowerCase();const u=Object.keys(n);let s=u.length,o;for(;s-- >0;)if(o=u[s],l===o.toLowerCase())return o;return null}const oa=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Zp=n=>!hi(n)&&n!==oa;function Yc(){const{caseless:n}=Zp(this)&&this||{},l={},u=(s,o)=>{const f=n&&Up(l,o)||o;Vu(l[f])&&Vu(s)?l[f]=Yc(l[f],s):Vu(s)?l[f]=Yc({},s):ll(s)?l[f]=s.slice():l[f]=s};for(let s=0,o=arguments.length;s<o;s++)arguments[s]&&xi(arguments[s],u);return l}const Zb=(n,l,u,{allOwnKeys:s}={})=>(xi(l,(o,f)=>{u&&bt(o)?n[f]=wp(o,u):n[f]=o},{allOwnKeys:s}),n),qb=n=>(n.charCodeAt(0)===65279&&(n=n.slice(1)),n),Bb=(n,l,u,s)=>{n.prototype=Object.create(l.prototype,s),n.prototype.constructor=n,Object.defineProperty(n,"super",{value:l.prototype}),u&&Object.assign(n.prototype,u)},Hb=(n,l,u,s)=>{let o,f,h;const y={};if(l=l||{},n==null)return l;do{for(o=Object.getOwnPropertyNames(n),f=o.length;f-- >0;)h=o[f],(!s||s(h,n,l))&&!y[h]&&(l[h]=n[h],y[h]=!0);n=u!==!1&&so(n)}while(n&&(!u||u(n,l))&&n!==Object.prototype);return l},kb=(n,l,u)=>{n=String(n),(u===void 0||u>n.length)&&(u=n.length),u-=l.length;const s=n.indexOf(l,u);return s!==-1&&s===u},Lb=n=>{if(!n)return null;if(ll(n))return n;let l=n.length;if(!Mp(l))return null;const u=new Array(l);for(;l-- >0;)u[l]=n[l];return u},Qb=(n=>l=>n&&l instanceof n)(typeof Uint8Array<"u"&&so(Uint8Array)),Gb=(n,l)=>{const s=(n&&n[es]).call(n);let o;for(;(o=s.next())&&!o.done;){const f=o.value;l.call(n,f[0],f[1])}},Yb=(n,l)=>{let u;const s=[];for(;(u=n.exec(l))!==null;)s.push(u);return s},Vb=Xt("HTMLFormElement"),Xb=n=>n.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(u,s,o){return s.toUpperCase()+o}),Zm=(({hasOwnProperty:n})=>(l,u)=>n.call(l,u))(Object.prototype),Kb=Xt("RegExp"),qp=(n,l)=>{const u=Object.getOwnPropertyDescriptors(n),s={};xi(u,(o,f)=>{let h;(h=l(o,f,n))!==!1&&(s[f]=h||o)}),Object.defineProperties(n,s)},$b=n=>{qp(n,(l,u)=>{if(bt(n)&&["arguments","caller","callee"].indexOf(u)!==-1)return!1;const s=n[u];if(bt(s)){if(l.enumerable=!1,"writable"in l){l.writable=!1;return}l.set||(l.set=()=>{throw Error("Can not rewrite read-only method '"+u+"'")})}})},Fb=(n,l)=>{const u={},s=o=>{o.forEach(f=>{u[f]=!0})};return ll(n)?s(n):s(String(n).split(l)),u},Jb=()=>{},Pb=(n,l)=>n!=null&&Number.isFinite(n=+n)?n:l;function Wb(n){return!!(n&&bt(n.append)&&n[Dp]==="FormData"&&n[es])}const Ib=n=>{const l=new Array(10),u=(s,o)=>{if(bi(s)){if(l.indexOf(s)>=0)return;if(gi(s))return s;if(!("toJSON"in s)){l[o]=s;const f=ll(s)?[]:{};return xi(s,(h,y)=>{const g=u(h,o+1);!hi(g)&&(f[y]=g)}),l[o]=void 0,f}}return s};return u(n,0)},e1=Xt("AsyncFunction"),t1=n=>n&&(bi(n)||bt(n))&&bt(n.then)&&bt(n.catch),Bp=((n,l)=>n?setImmediate:l?((u,s)=>(oa.addEventListener("message",({source:o,data:f})=>{o===oa&&f===u&&s.length&&s.shift()()},!1),o=>{s.push(o),oa.postMessage(u,"*")}))(`axios@${Math.random()}`,[]):u=>setTimeout(u))(typeof setImmediate=="function",bt(oa.postMessage)),n1=typeof queueMicrotask<"u"?queueMicrotask.bind(oa):typeof process<"u"&&process.nextTick||Bp,a1=n=>n!=null&&bt(n[es]),R={isArray:ll,isArrayBuffer:Cp,isBuffer:gi,isFormData:Ab,isArrayBufferView:xb,isString:Sb,isNumber:Mp,isBoolean:_b,isObject:bi,isPlainObject:Vu,isEmptyObject:Eb,isReadableStream:wb,isRequest:Db,isResponse:Cb,isHeaders:Mb,isUndefined:hi,isDate:zb,isFile:Tb,isBlob:Ob,isRegExp:Kb,isFunction:bt,isStream:jb,isURLSearchParams:Rb,isTypedArray:Qb,isFileList:Nb,forEach:xi,merge:Yc,extend:Zb,trim:Ub,stripBOM:qb,inherits:Bb,toFlatObject:Hb,kindOf:ts,kindOfTest:Xt,endsWith:kb,toArray:Lb,forEachEntry:Gb,matchAll:Yb,isHTMLForm:Vb,hasOwnProperty:Zm,hasOwnProp:Zm,reduceDescriptors:qp,freezeMethods:$b,toObjectSet:Fb,toCamelCase:Xb,noop:Jb,toFiniteNumber:Pb,findKey:Up,global:oa,isContextDefined:Zp,isSpecCompliantForm:Wb,toJSONObject:Ib,isAsyncFn:e1,isThenable:t1,setImmediate:Bp,asap:n1,isIterable:a1};function se(n,l,u,s,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=n,this.name="AxiosError",l&&(this.code=l),u&&(this.config=u),s&&(this.request=s),o&&(this.response=o,this.status=o.status?o.status:null)}R.inherits(se,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:R.toJSONObject(this.config),code:this.code,status:this.status}}});const Hp=se.prototype,kp={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(n=>{kp[n]={value:n}});Object.defineProperties(se,kp);Object.defineProperty(Hp,"isAxiosError",{value:!0});se.from=(n,l,u,s,o,f)=>{const h=Object.create(Hp);return R.toFlatObject(n,h,function(g){return g!==Error.prototype},y=>y!=="isAxiosError"),se.call(h,n.message,l,u,s,o),h.cause=n,h.name=n.name,f&&Object.assign(h,f),h};const l1=null;function Vc(n){return R.isPlainObject(n)||R.isArray(n)}function Lp(n){return R.endsWith(n,"[]")?n.slice(0,-2):n}function qm(n,l,u){return n?n.concat(l).map(function(o,f){return o=Lp(o),!u&&f?"["+o+"]":o}).join(u?".":""):l}function i1(n){return R.isArray(n)&&!n.some(Vc)}const u1=R.toFlatObject(R,{},null,function(l){return/^is[A-Z]/.test(l)});function as(n,l,u){if(!R.isObject(n))throw new TypeError("target must be an object");l=l||new FormData,u=R.toFlatObject(u,{metaTokens:!0,dots:!1,indexes:!1},!1,function(D,Z){return!R.isUndefined(Z[D])});const s=u.metaTokens,o=u.visitor||b,f=u.dots,h=u.indexes,g=(u.Blob||typeof Blob<"u"&&Blob)&&R.isSpecCompliantForm(l);if(!R.isFunction(o))throw new TypeError("visitor must be a function");function v(M){if(M===null)return"";if(R.isDate(M))return M.toISOString();if(R.isBoolean(M))return M.toString();if(!g&&R.isBlob(M))throw new se("Blob is not supported. Use a Buffer instead.");return R.isArrayBuffer(M)||R.isTypedArray(M)?g&&typeof Blob=="function"?new Blob([M]):Buffer.from(M):M}function b(M,D,Z){let L=M;if(M&&!Z&&typeof M=="object"){if(R.endsWith(D,"{}"))D=s?D:D.slice(0,-2),M=JSON.stringify(M);else if(R.isArray(M)&&i1(M)||(R.isFileList(M)||R.endsWith(D,"[]"))&&(L=R.toArray(M)))return D=Lp(D),L.forEach(function(Q,ee){!(R.isUndefined(Q)||Q===null)&&l.append(h===!0?qm([D],ee,f):h===null?D:D+"[]",v(Q))}),!1}return Vc(M)?!0:(l.append(qm(Z,D,f),v(M)),!1)}const z=[],w=Object.assign(u1,{defaultVisitor:b,convertValue:v,isVisitable:Vc});function k(M,D){if(!R.isUndefined(M)){if(z.indexOf(M)!==-1)throw Error("Circular reference detected in "+D.join("."));z.push(M),R.forEach(M,function(L,G){(!(R.isUndefined(L)||L===null)&&o.call(l,L,R.isString(G)?G.trim():G,D,w))===!0&&k(L,D?D.concat(G):[G])}),z.pop()}}if(!R.isObject(n))throw new TypeError("data must be an object");return k(n),l}function Bm(n){const l={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(n).replace(/[!'()~]|%20|%00/g,function(s){return l[s]})}function ro(n,l){this._pairs=[],n&&as(n,this,l)}const Qp=ro.prototype;Qp.append=function(l,u){this._pairs.push([l,u])};Qp.toString=function(l){const u=l?function(s){return l.call(this,s,Bm)}:Bm;return this._pairs.map(function(o){return u(o[0])+"="+u(o[1])},"").join("&")};function s1(n){return encodeURIComponent(n).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Gp(n,l,u){if(!l)return n;const s=u&&u.encode||s1;R.isFunction(u)&&(u={serialize:u});const o=u&&u.serialize;let f;if(o?f=o(l,u):f=R.isURLSearchParams(l)?l.toString():new ro(l,u).toString(s),f){const h=n.indexOf("#");h!==-1&&(n=n.slice(0,h)),n+=(n.indexOf("?")===-1?"?":"&")+f}return n}class Hm{constructor(){this.handlers=[]}use(l,u,s){return this.handlers.push({fulfilled:l,rejected:u,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(l){this.handlers[l]&&(this.handlers[l]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(l){R.forEach(this.handlers,function(s){s!==null&&l(s)})}}const Yp={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},r1=typeof URLSearchParams<"u"?URLSearchParams:ro,c1=typeof FormData<"u"?FormData:null,o1=typeof Blob<"u"?Blob:null,f1={isBrowser:!0,classes:{URLSearchParams:r1,FormData:c1,Blob:o1},protocols:["http","https","file","blob","url","data"]},co=typeof window<"u"&&typeof document<"u",Xc=typeof navigator=="object"&&navigator||void 0,d1=co&&(!Xc||["ReactNative","NativeScript","NS"].indexOf(Xc.product)<0),h1=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",m1=co&&window.location.href||"http://localhost",p1=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:co,hasStandardBrowserEnv:d1,hasStandardBrowserWebWorkerEnv:h1,navigator:Xc,origin:m1},Symbol.toStringTag,{value:"Module"})),ot={...p1,...f1};function v1(n,l){return as(n,new ot.classes.URLSearchParams,{visitor:function(u,s,o,f){return ot.isNode&&R.isBuffer(u)?(this.append(s,u.toString("base64")),!1):f.defaultVisitor.apply(this,arguments)},...l})}function y1(n){return R.matchAll(/\w+|\[(\w*)]/g,n).map(l=>l[0]==="[]"?"":l[1]||l[0])}function g1(n){const l={},u=Object.keys(n);let s;const o=u.length;let f;for(s=0;s<o;s++)f=u[s],l[f]=n[f];return l}function Vp(n){function l(u,s,o,f){let h=u[f++];if(h==="__proto__")return!0;const y=Number.isFinite(+h),g=f>=u.length;return h=!h&&R.isArray(o)?o.length:h,g?(R.hasOwnProp(o,h)?o[h]=[o[h],s]:o[h]=s,!y):((!o[h]||!R.isObject(o[h]))&&(o[h]=[]),l(u,s,o[h],f)&&R.isArray(o[h])&&(o[h]=g1(o[h])),!y)}if(R.isFormData(n)&&R.isFunction(n.entries)){const u={};return R.forEachEntry(n,(s,o)=>{l(y1(s),o,u,0)}),u}return null}function b1(n,l,u){if(R.isString(n))try{return(l||JSON.parse)(n),R.trim(n)}catch(s){if(s.name!=="SyntaxError")throw s}return(u||JSON.stringify)(n)}const Si={transitional:Yp,adapter:["xhr","http","fetch"],transformRequest:[function(l,u){const s=u.getContentType()||"",o=s.indexOf("application/json")>-1,f=R.isObject(l);if(f&&R.isHTMLForm(l)&&(l=new FormData(l)),R.isFormData(l))return o?JSON.stringify(Vp(l)):l;if(R.isArrayBuffer(l)||R.isBuffer(l)||R.isStream(l)||R.isFile(l)||R.isBlob(l)||R.isReadableStream(l))return l;if(R.isArrayBufferView(l))return l.buffer;if(R.isURLSearchParams(l))return u.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),l.toString();let y;if(f){if(s.indexOf("application/x-www-form-urlencoded")>-1)return v1(l,this.formSerializer).toString();if((y=R.isFileList(l))||s.indexOf("multipart/form-data")>-1){const g=this.env&&this.env.FormData;return as(y?{"files[]":l}:l,g&&new g,this.formSerializer)}}return f||o?(u.setContentType("application/json",!1),b1(l)):l}],transformResponse:[function(l){const u=this.transitional||Si.transitional,s=u&&u.forcedJSONParsing,o=this.responseType==="json";if(R.isResponse(l)||R.isReadableStream(l))return l;if(l&&R.isString(l)&&(s&&!this.responseType||o)){const h=!(u&&u.silentJSONParsing)&&o;try{return JSON.parse(l)}catch(y){if(h)throw y.name==="SyntaxError"?se.from(y,se.ERR_BAD_RESPONSE,this,null,this.response):y}}return l}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ot.classes.FormData,Blob:ot.classes.Blob},validateStatus:function(l){return l>=200&&l<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};R.forEach(["delete","get","head","post","put","patch"],n=>{Si.headers[n]={}});const x1=R.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),S1=n=>{const l={};let u,s,o;return n&&n.split(`
`).forEach(function(h){o=h.indexOf(":"),u=h.substring(0,o).trim().toLowerCase(),s=h.substring(o+1).trim(),!(!u||l[u]&&x1[u])&&(u==="set-cookie"?l[u]?l[u].push(s):l[u]=[s]:l[u]=l[u]?l[u]+", "+s:s)}),l},km=Symbol("internals");function ui(n){return n&&String(n).trim().toLowerCase()}function Xu(n){return n===!1||n==null?n:R.isArray(n)?n.map(Xu):String(n)}function _1(n){const l=Object.create(null),u=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=u.exec(n);)l[s[1]]=s[2];return l}const E1=n=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(n.trim());function Cc(n,l,u,s,o){if(R.isFunction(s))return s.call(this,l,u);if(o&&(l=u),!!R.isString(l)){if(R.isString(s))return l.indexOf(s)!==-1;if(R.isRegExp(s))return s.test(l)}}function z1(n){return n.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(l,u,s)=>u.toUpperCase()+s)}function T1(n,l){const u=R.toCamelCase(" "+l);["get","set","has"].forEach(s=>{Object.defineProperty(n,s+u,{value:function(o,f,h){return this[s].call(this,l,o,f,h)},configurable:!0})})}let xt=class{constructor(l){l&&this.set(l)}set(l,u,s){const o=this;function f(y,g,v){const b=ui(g);if(!b)throw new Error("header name must be a non-empty string");const z=R.findKey(o,b);(!z||o[z]===void 0||v===!0||v===void 0&&o[z]!==!1)&&(o[z||g]=Xu(y))}const h=(y,g)=>R.forEach(y,(v,b)=>f(v,b,g));if(R.isPlainObject(l)||l instanceof this.constructor)h(l,u);else if(R.isString(l)&&(l=l.trim())&&!E1(l))h(S1(l),u);else if(R.isObject(l)&&R.isIterable(l)){let y={},g,v;for(const b of l){if(!R.isArray(b))throw TypeError("Object iterator must return a key-value pair");y[v=b[0]]=(g=y[v])?R.isArray(g)?[...g,b[1]]:[g,b[1]]:b[1]}h(y,u)}else l!=null&&f(u,l,s);return this}get(l,u){if(l=ui(l),l){const s=R.findKey(this,l);if(s){const o=this[s];if(!u)return o;if(u===!0)return _1(o);if(R.isFunction(u))return u.call(this,o,s);if(R.isRegExp(u))return u.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(l,u){if(l=ui(l),l){const s=R.findKey(this,l);return!!(s&&this[s]!==void 0&&(!u||Cc(this,this[s],s,u)))}return!1}delete(l,u){const s=this;let o=!1;function f(h){if(h=ui(h),h){const y=R.findKey(s,h);y&&(!u||Cc(s,s[y],y,u))&&(delete s[y],o=!0)}}return R.isArray(l)?l.forEach(f):f(l),o}clear(l){const u=Object.keys(this);let s=u.length,o=!1;for(;s--;){const f=u[s];(!l||Cc(this,this[f],f,l,!0))&&(delete this[f],o=!0)}return o}normalize(l){const u=this,s={};return R.forEach(this,(o,f)=>{const h=R.findKey(s,f);if(h){u[h]=Xu(o),delete u[f];return}const y=l?z1(f):String(f).trim();y!==f&&delete u[f],u[y]=Xu(o),s[y]=!0}),this}concat(...l){return this.constructor.concat(this,...l)}toJSON(l){const u=Object.create(null);return R.forEach(this,(s,o)=>{s!=null&&s!==!1&&(u[o]=l&&R.isArray(s)?s.join(", "):s)}),u}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([l,u])=>l+": "+u).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(l){return l instanceof this?l:new this(l)}static concat(l,...u){const s=new this(l);return u.forEach(o=>s.set(o)),s}static accessor(l){const s=(this[km]=this[km]={accessors:{}}).accessors,o=this.prototype;function f(h){const y=ui(h);s[y]||(T1(o,h),s[y]=!0)}return R.isArray(l)?l.forEach(f):f(l),this}};xt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);R.reduceDescriptors(xt.prototype,({value:n},l)=>{let u=l[0].toUpperCase()+l.slice(1);return{get:()=>n,set(s){this[u]=s}}});R.freezeMethods(xt);function Mc(n,l){const u=this||Si,s=l||u,o=xt.from(s.headers);let f=s.data;return R.forEach(n,function(y){f=y.call(u,f,o.normalize(),l?l.status:void 0)}),o.normalize(),f}function Xp(n){return!!(n&&n.__CANCEL__)}function il(n,l,u){se.call(this,n??"canceled",se.ERR_CANCELED,l,u),this.name="CanceledError"}R.inherits(il,se,{__CANCEL__:!0});function Kp(n,l,u){const s=u.config.validateStatus;!u.status||!s||s(u.status)?n(u):l(new se("Request failed with status code "+u.status,[se.ERR_BAD_REQUEST,se.ERR_BAD_RESPONSE][Math.floor(u.status/100)-4],u.config,u.request,u))}function O1(n){const l=/^([-+\w]{1,25})(:?\/\/|:)/.exec(n);return l&&l[1]||""}function N1(n,l){n=n||10;const u=new Array(n),s=new Array(n);let o=0,f=0,h;return l=l!==void 0?l:1e3,function(g){const v=Date.now(),b=s[f];h||(h=v),u[o]=g,s[o]=v;let z=f,w=0;for(;z!==o;)w+=u[z++],z=z%n;if(o=(o+1)%n,o===f&&(f=(f+1)%n),v-h<l)return;const k=b&&v-b;return k?Math.round(w*1e3/k):void 0}}function j1(n,l){let u=0,s=1e3/l,o,f;const h=(v,b=Date.now())=>{u=b,o=null,f&&(clearTimeout(f),f=null),n(...v)};return[(...v)=>{const b=Date.now(),z=b-u;z>=s?h(v,b):(o=v,f||(f=setTimeout(()=>{f=null,h(o)},s-z)))},()=>o&&h(o)]}const Fu=(n,l,u=3)=>{let s=0;const o=N1(50,250);return j1(f=>{const h=f.loaded,y=f.lengthComputable?f.total:void 0,g=h-s,v=o(g),b=h<=y;s=h;const z={loaded:h,total:y,progress:y?h/y:void 0,bytes:g,rate:v||void 0,estimated:v&&y&&b?(y-h)/v:void 0,event:f,lengthComputable:y!=null,[l?"download":"upload"]:!0};n(z)},u)},Lm=(n,l)=>{const u=n!=null;return[s=>l[0]({lengthComputable:u,total:n,loaded:s}),l[1]]},Qm=n=>(...l)=>R.asap(()=>n(...l)),A1=ot.hasStandardBrowserEnv?((n,l)=>u=>(u=new URL(u,ot.origin),n.protocol===u.protocol&&n.host===u.host&&(l||n.port===u.port)))(new URL(ot.origin),ot.navigator&&/(msie|trident)/i.test(ot.navigator.userAgent)):()=>!0,R1=ot.hasStandardBrowserEnv?{write(n,l,u,s,o,f){const h=[n+"="+encodeURIComponent(l)];R.isNumber(u)&&h.push("expires="+new Date(u).toGMTString()),R.isString(s)&&h.push("path="+s),R.isString(o)&&h.push("domain="+o),f===!0&&h.push("secure"),document.cookie=h.join("; ")},read(n){const l=document.cookie.match(new RegExp("(^|;\\s*)("+n+")=([^;]*)"));return l?decodeURIComponent(l[3]):null},remove(n){this.write(n,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function w1(n){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(n)}function D1(n,l){return l?n.replace(/\/?\/$/,"")+"/"+l.replace(/^\/+/,""):n}function $p(n,l,u){let s=!w1(l);return n&&(s||u==!1)?D1(n,l):l}const Gm=n=>n instanceof xt?{...n}:n;function ma(n,l){l=l||{};const u={};function s(v,b,z,w){return R.isPlainObject(v)&&R.isPlainObject(b)?R.merge.call({caseless:w},v,b):R.isPlainObject(b)?R.merge({},b):R.isArray(b)?b.slice():b}function o(v,b,z,w){if(R.isUndefined(b)){if(!R.isUndefined(v))return s(void 0,v,z,w)}else return s(v,b,z,w)}function f(v,b){if(!R.isUndefined(b))return s(void 0,b)}function h(v,b){if(R.isUndefined(b)){if(!R.isUndefined(v))return s(void 0,v)}else return s(void 0,b)}function y(v,b,z){if(z in l)return s(v,b);if(z in n)return s(void 0,v)}const g={url:f,method:f,data:f,baseURL:h,transformRequest:h,transformResponse:h,paramsSerializer:h,timeout:h,timeoutMessage:h,withCredentials:h,withXSRFToken:h,adapter:h,responseType:h,xsrfCookieName:h,xsrfHeaderName:h,onUploadProgress:h,onDownloadProgress:h,decompress:h,maxContentLength:h,maxBodyLength:h,beforeRedirect:h,transport:h,httpAgent:h,httpsAgent:h,cancelToken:h,socketPath:h,responseEncoding:h,validateStatus:y,headers:(v,b,z)=>o(Gm(v),Gm(b),z,!0)};return R.forEach(Object.keys({...n,...l}),function(b){const z=g[b]||o,w=z(n[b],l[b],b);R.isUndefined(w)&&z!==y||(u[b]=w)}),u}const Fp=n=>{const l=ma({},n);let{data:u,withXSRFToken:s,xsrfHeaderName:o,xsrfCookieName:f,headers:h,auth:y}=l;l.headers=h=xt.from(h),l.url=Gp($p(l.baseURL,l.url,l.allowAbsoluteUrls),n.params,n.paramsSerializer),y&&h.set("Authorization","Basic "+btoa((y.username||"")+":"+(y.password?unescape(encodeURIComponent(y.password)):"")));let g;if(R.isFormData(u)){if(ot.hasStandardBrowserEnv||ot.hasStandardBrowserWebWorkerEnv)h.setContentType(void 0);else if((g=h.getContentType())!==!1){const[v,...b]=g?g.split(";").map(z=>z.trim()).filter(Boolean):[];h.setContentType([v||"multipart/form-data",...b].join("; "))}}if(ot.hasStandardBrowserEnv&&(s&&R.isFunction(s)&&(s=s(l)),s||s!==!1&&A1(l.url))){const v=o&&f&&R1.read(f);v&&h.set(o,v)}return l},C1=typeof XMLHttpRequest<"u",M1=C1&&function(n){return new Promise(function(u,s){const o=Fp(n);let f=o.data;const h=xt.from(o.headers).normalize();let{responseType:y,onUploadProgress:g,onDownloadProgress:v}=o,b,z,w,k,M;function D(){k&&k(),M&&M(),o.cancelToken&&o.cancelToken.unsubscribe(b),o.signal&&o.signal.removeEventListener("abort",b)}let Z=new XMLHttpRequest;Z.open(o.method.toUpperCase(),o.url,!0),Z.timeout=o.timeout;function L(){if(!Z)return;const Q=xt.from("getAllResponseHeaders"in Z&&Z.getAllResponseHeaders()),K={data:!y||y==="text"||y==="json"?Z.responseText:Z.response,status:Z.status,statusText:Z.statusText,headers:Q,config:n,request:Z};Kp(function(ue){u(ue),D()},function(ue){s(ue),D()},K),Z=null}"onloadend"in Z?Z.onloadend=L:Z.onreadystatechange=function(){!Z||Z.readyState!==4||Z.status===0&&!(Z.responseURL&&Z.responseURL.indexOf("file:")===0)||setTimeout(L)},Z.onabort=function(){Z&&(s(new se("Request aborted",se.ECONNABORTED,n,Z)),Z=null)},Z.onerror=function(){s(new se("Network Error",se.ERR_NETWORK,n,Z)),Z=null},Z.ontimeout=function(){let ee=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const K=o.transitional||Yp;o.timeoutErrorMessage&&(ee=o.timeoutErrorMessage),s(new se(ee,K.clarifyTimeoutError?se.ETIMEDOUT:se.ECONNABORTED,n,Z)),Z=null},f===void 0&&h.setContentType(null),"setRequestHeader"in Z&&R.forEach(h.toJSON(),function(ee,K){Z.setRequestHeader(K,ee)}),R.isUndefined(o.withCredentials)||(Z.withCredentials=!!o.withCredentials),y&&y!=="json"&&(Z.responseType=o.responseType),v&&([w,M]=Fu(v,!0),Z.addEventListener("progress",w)),g&&Z.upload&&([z,k]=Fu(g),Z.upload.addEventListener("progress",z),Z.upload.addEventListener("loadend",k)),(o.cancelToken||o.signal)&&(b=Q=>{Z&&(s(!Q||Q.type?new il(null,n,Z):Q),Z.abort(),Z=null)},o.cancelToken&&o.cancelToken.subscribe(b),o.signal&&(o.signal.aborted?b():o.signal.addEventListener("abort",b)));const G=O1(o.url);if(G&&ot.protocols.indexOf(G)===-1){s(new se("Unsupported protocol "+G+":",se.ERR_BAD_REQUEST,n));return}Z.send(f||null)})},U1=(n,l)=>{const{length:u}=n=n?n.filter(Boolean):[];if(l||u){let s=new AbortController,o;const f=function(v){if(!o){o=!0,y();const b=v instanceof Error?v:this.reason;s.abort(b instanceof se?b:new il(b instanceof Error?b.message:b))}};let h=l&&setTimeout(()=>{h=null,f(new se(`timeout ${l} of ms exceeded`,se.ETIMEDOUT))},l);const y=()=>{n&&(h&&clearTimeout(h),h=null,n.forEach(v=>{v.unsubscribe?v.unsubscribe(f):v.removeEventListener("abort",f)}),n=null)};n.forEach(v=>v.addEventListener("abort",f));const{signal:g}=s;return g.unsubscribe=()=>R.asap(y),g}},Z1=function*(n,l){let u=n.byteLength;if(u<l){yield n;return}let s=0,o;for(;s<u;)o=s+l,yield n.slice(s,o),s=o},q1=async function*(n,l){for await(const u of B1(n))yield*Z1(u,l)},B1=async function*(n){if(n[Symbol.asyncIterator]){yield*n;return}const l=n.getReader();try{for(;;){const{done:u,value:s}=await l.read();if(u)break;yield s}}finally{await l.cancel()}},Ym=(n,l,u,s)=>{const o=q1(n,l);let f=0,h,y=g=>{h||(h=!0,s&&s(g))};return new ReadableStream({async pull(g){try{const{done:v,value:b}=await o.next();if(v){y(),g.close();return}let z=b.byteLength;if(u){let w=f+=z;u(w)}g.enqueue(new Uint8Array(b))}catch(v){throw y(v),v}},cancel(g){return y(g),o.return()}},{highWaterMark:2})},ls=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Jp=ls&&typeof ReadableStream=="function",H1=ls&&(typeof TextEncoder=="function"?(n=>l=>n.encode(l))(new TextEncoder):async n=>new Uint8Array(await new Response(n).arrayBuffer())),Pp=(n,...l)=>{try{return!!n(...l)}catch{return!1}},k1=Jp&&Pp(()=>{let n=!1;const l=new Request(ot.origin,{body:new ReadableStream,method:"POST",get duplex(){return n=!0,"half"}}).headers.has("Content-Type");return n&&!l}),Vm=64*1024,Kc=Jp&&Pp(()=>R.isReadableStream(new Response("").body)),Ju={stream:Kc&&(n=>n.body)};ls&&(n=>{["text","arrayBuffer","blob","formData","stream"].forEach(l=>{!Ju[l]&&(Ju[l]=R.isFunction(n[l])?u=>u[l]():(u,s)=>{throw new se(`Response type '${l}' is not supported`,se.ERR_NOT_SUPPORT,s)})})})(new Response);const L1=async n=>{if(n==null)return 0;if(R.isBlob(n))return n.size;if(R.isSpecCompliantForm(n))return(await new Request(ot.origin,{method:"POST",body:n}).arrayBuffer()).byteLength;if(R.isArrayBufferView(n)||R.isArrayBuffer(n))return n.byteLength;if(R.isURLSearchParams(n)&&(n=n+""),R.isString(n))return(await H1(n)).byteLength},Q1=async(n,l)=>{const u=R.toFiniteNumber(n.getContentLength());return u??L1(l)},G1=ls&&(async n=>{let{url:l,method:u,data:s,signal:o,cancelToken:f,timeout:h,onDownloadProgress:y,onUploadProgress:g,responseType:v,headers:b,withCredentials:z="same-origin",fetchOptions:w}=Fp(n);v=v?(v+"").toLowerCase():"text";let k=U1([o,f&&f.toAbortSignal()],h),M;const D=k&&k.unsubscribe&&(()=>{k.unsubscribe()});let Z;try{if(g&&k1&&u!=="get"&&u!=="head"&&(Z=await Q1(b,s))!==0){let K=new Request(l,{method:"POST",body:s,duplex:"half"}),pe;if(R.isFormData(s)&&(pe=K.headers.get("content-type"))&&b.setContentType(pe),K.body){const[ue,J]=Lm(Z,Fu(Qm(g)));s=Ym(K.body,Vm,ue,J)}}R.isString(z)||(z=z?"include":"omit");const L="credentials"in Request.prototype;M=new Request(l,{...w,signal:k,method:u.toUpperCase(),headers:b.normalize().toJSON(),body:s,duplex:"half",credentials:L?z:void 0});let G=await fetch(M,w);const Q=Kc&&(v==="stream"||v==="response");if(Kc&&(y||Q&&D)){const K={};["status","statusText","headers"].forEach(Oe=>{K[Oe]=G[Oe]});const pe=R.toFiniteNumber(G.headers.get("content-length")),[ue,J]=y&&Lm(pe,Fu(Qm(y),!0))||[];G=new Response(Ym(G.body,Vm,ue,()=>{J&&J(),D&&D()}),K)}v=v||"text";let ee=await Ju[R.findKey(Ju,v)||"text"](G,n);return!Q&&D&&D(),await new Promise((K,pe)=>{Kp(K,pe,{data:ee,headers:xt.from(G.headers),status:G.status,statusText:G.statusText,config:n,request:M})})}catch(L){throw D&&D(),L&&L.name==="TypeError"&&/Load failed|fetch/i.test(L.message)?Object.assign(new se("Network Error",se.ERR_NETWORK,n,M),{cause:L.cause||L}):se.from(L,L&&L.code,n,M)}}),$c={http:l1,xhr:M1,fetch:G1};R.forEach($c,(n,l)=>{if(n){try{Object.defineProperty(n,"name",{value:l})}catch{}Object.defineProperty(n,"adapterName",{value:l})}});const Xm=n=>`- ${n}`,Y1=n=>R.isFunction(n)||n===null||n===!1,Wp={getAdapter:n=>{n=R.isArray(n)?n:[n];const{length:l}=n;let u,s;const o={};for(let f=0;f<l;f++){u=n[f];let h;if(s=u,!Y1(u)&&(s=$c[(h=String(u)).toLowerCase()],s===void 0))throw new se(`Unknown adapter '${h}'`);if(s)break;o[h||"#"+f]=s}if(!s){const f=Object.entries(o).map(([y,g])=>`adapter ${y} `+(g===!1?"is not supported by the environment":"is not available in the build"));let h=l?f.length>1?`since :
`+f.map(Xm).join(`
`):" "+Xm(f[0]):"as no adapter specified";throw new se("There is no suitable adapter to dispatch the request "+h,"ERR_NOT_SUPPORT")}return s},adapters:$c};function Uc(n){if(n.cancelToken&&n.cancelToken.throwIfRequested(),n.signal&&n.signal.aborted)throw new il(null,n)}function Km(n){return Uc(n),n.headers=xt.from(n.headers),n.data=Mc.call(n,n.transformRequest),["post","put","patch"].indexOf(n.method)!==-1&&n.headers.setContentType("application/x-www-form-urlencoded",!1),Wp.getAdapter(n.adapter||Si.adapter)(n).then(function(s){return Uc(n),s.data=Mc.call(n,n.transformResponse,s),s.headers=xt.from(s.headers),s},function(s){return Xp(s)||(Uc(n),s&&s.response&&(s.response.data=Mc.call(n,n.transformResponse,s.response),s.response.headers=xt.from(s.response.headers))),Promise.reject(s)})}const Ip="1.11.0",is={};["object","boolean","number","function","string","symbol"].forEach((n,l)=>{is[n]=function(s){return typeof s===n||"a"+(l<1?"n ":" ")+n}});const $m={};is.transitional=function(l,u,s){function o(f,h){return"[Axios v"+Ip+"] Transitional option '"+f+"'"+h+(s?". "+s:"")}return(f,h,y)=>{if(l===!1)throw new se(o(h," has been removed"+(u?" in "+u:"")),se.ERR_DEPRECATED);return u&&!$m[h]&&($m[h]=!0,console.warn(o(h," has been deprecated since v"+u+" and will be removed in the near future"))),l?l(f,h,y):!0}};is.spelling=function(l){return(u,s)=>(console.warn(`${s} is likely a misspelling of ${l}`),!0)};function V1(n,l,u){if(typeof n!="object")throw new se("options must be an object",se.ERR_BAD_OPTION_VALUE);const s=Object.keys(n);let o=s.length;for(;o-- >0;){const f=s[o],h=l[f];if(h){const y=n[f],g=y===void 0||h(y,f,n);if(g!==!0)throw new se("option "+f+" must be "+g,se.ERR_BAD_OPTION_VALUE);continue}if(u!==!0)throw new se("Unknown option "+f,se.ERR_BAD_OPTION)}}const Ku={assertOptions:V1,validators:is},It=Ku.validators;let da=class{constructor(l){this.defaults=l||{},this.interceptors={request:new Hm,response:new Hm}}async request(l,u){try{return await this._request(l,u)}catch(s){if(s instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const f=o.stack?o.stack.replace(/^.+\n/,""):"";try{s.stack?f&&!String(s.stack).endsWith(f.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+f):s.stack=f}catch{}}throw s}}_request(l,u){typeof l=="string"?(u=u||{},u.url=l):u=l||{},u=ma(this.defaults,u);const{transitional:s,paramsSerializer:o,headers:f}=u;s!==void 0&&Ku.assertOptions(s,{silentJSONParsing:It.transitional(It.boolean),forcedJSONParsing:It.transitional(It.boolean),clarifyTimeoutError:It.transitional(It.boolean)},!1),o!=null&&(R.isFunction(o)?u.paramsSerializer={serialize:o}:Ku.assertOptions(o,{encode:It.function,serialize:It.function},!0)),u.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?u.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:u.allowAbsoluteUrls=!0),Ku.assertOptions(u,{baseUrl:It.spelling("baseURL"),withXsrfToken:It.spelling("withXSRFToken")},!0),u.method=(u.method||this.defaults.method||"get").toLowerCase();let h=f&&R.merge(f.common,f[u.method]);f&&R.forEach(["delete","get","head","post","put","patch","common"],M=>{delete f[M]}),u.headers=xt.concat(h,f);const y=[];let g=!0;this.interceptors.request.forEach(function(D){typeof D.runWhen=="function"&&D.runWhen(u)===!1||(g=g&&D.synchronous,y.unshift(D.fulfilled,D.rejected))});const v=[];this.interceptors.response.forEach(function(D){v.push(D.fulfilled,D.rejected)});let b,z=0,w;if(!g){const M=[Km.bind(this),void 0];for(M.unshift(...y),M.push(...v),w=M.length,b=Promise.resolve(u);z<w;)b=b.then(M[z++],M[z++]);return b}w=y.length;let k=u;for(z=0;z<w;){const M=y[z++],D=y[z++];try{k=M(k)}catch(Z){D.call(this,Z);break}}try{b=Km.call(this,k)}catch(M){return Promise.reject(M)}for(z=0,w=v.length;z<w;)b=b.then(v[z++],v[z++]);return b}getUri(l){l=ma(this.defaults,l);const u=$p(l.baseURL,l.url,l.allowAbsoluteUrls);return Gp(u,l.params,l.paramsSerializer)}};R.forEach(["delete","get","head","options"],function(l){da.prototype[l]=function(u,s){return this.request(ma(s||{},{method:l,url:u,data:(s||{}).data}))}});R.forEach(["post","put","patch"],function(l){function u(s){return function(f,h,y){return this.request(ma(y||{},{method:l,headers:s?{"Content-Type":"multipart/form-data"}:{},url:f,data:h}))}}da.prototype[l]=u(),da.prototype[l+"Form"]=u(!0)});let X1=class ev{constructor(l){if(typeof l!="function")throw new TypeError("executor must be a function.");let u;this.promise=new Promise(function(f){u=f});const s=this;this.promise.then(o=>{if(!s._listeners)return;let f=s._listeners.length;for(;f-- >0;)s._listeners[f](o);s._listeners=null}),this.promise.then=o=>{let f;const h=new Promise(y=>{s.subscribe(y),f=y}).then(o);return h.cancel=function(){s.unsubscribe(f)},h},l(function(f,h,y){s.reason||(s.reason=new il(f,h,y),u(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(l){if(this.reason){l(this.reason);return}this._listeners?this._listeners.push(l):this._listeners=[l]}unsubscribe(l){if(!this._listeners)return;const u=this._listeners.indexOf(l);u!==-1&&this._listeners.splice(u,1)}toAbortSignal(){const l=new AbortController,u=s=>{l.abort(s)};return this.subscribe(u),l.signal.unsubscribe=()=>this.unsubscribe(u),l.signal}static source(){let l;return{token:new ev(function(o){l=o}),cancel:l}}};function K1(n){return function(u){return n.apply(null,u)}}function $1(n){return R.isObject(n)&&n.isAxiosError===!0}const Fc={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Fc).forEach(([n,l])=>{Fc[l]=n});function tv(n){const l=new da(n),u=wp(da.prototype.request,l);return R.extend(u,da.prototype,l,{allOwnKeys:!0}),R.extend(u,l,null,{allOwnKeys:!0}),u.create=function(o){return tv(ma(n,o))},u}const Ye=tv(Si);Ye.Axios=da;Ye.CanceledError=il;Ye.CancelToken=X1;Ye.isCancel=Xp;Ye.VERSION=Ip;Ye.toFormData=as;Ye.AxiosError=se;Ye.Cancel=Ye.CanceledError;Ye.all=function(l){return Promise.all(l)};Ye.spread=K1;Ye.isAxiosError=$1;Ye.mergeConfig=ma;Ye.AxiosHeaders=xt;Ye.formToJSON=n=>Vp(R.isHTMLForm(n)?new FormData(n):n);Ye.getAdapter=Wp.getAdapter;Ye.HttpStatusCode=Fc;Ye.default=Ye;const{Axios:nE,AxiosError:aE,CanceledError:lE,isCancel:iE,CancelToken:uE,VERSION:sE,all:rE,Cancel:cE,isAxiosError:oE,spread:fE,toFormData:dE,AxiosHeaders:hE,HttpStatusCode:mE,formToJSON:pE,getAdapter:vE,mergeConfig:yE}=Ye;function q(n,l,u){function s(y,g){var v;Object.defineProperty(y,"_zod",{value:y._zod??{},enumerable:!1}),(v=y._zod).traits??(v.traits=new Set),y._zod.traits.add(n),l(y,g);for(const b in h.prototype)b in y||Object.defineProperty(y,b,{value:h.prototype[b].bind(y)});y._zod.constr=h,y._zod.def=g}const o=u?.Parent??Object;class f extends o{}Object.defineProperty(f,"name",{value:n});function h(y){var g;const v=u?.Parent?new f:this;s(v,y),(g=v._zod).deferred??(g.deferred=[]);for(const b of v._zod.deferred)b();return v}return Object.defineProperty(h,"init",{value:s}),Object.defineProperty(h,Symbol.hasInstance,{value:y=>u?.Parent&&y instanceof u.Parent?!0:y?._zod?.traits?.has(n)}),Object.defineProperty(h,"name",{value:n}),h}class mi extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}const nv={};function Gn(n){return nv}function F1(n){const l=Object.values(n).filter(s=>typeof s=="number");return Object.entries(n).filter(([s,o])=>l.indexOf(+s)===-1).map(([s,o])=>o)}function Jc(n,l){return typeof l=="bigint"?l.toString():l}function av(n){return{get value(){{const l=n();return Object.defineProperty(this,"value",{value:l}),l}}}}function oo(n){return n==null}function fo(n){const l=n.startsWith("^")?1:0,u=n.endsWith("$")?n.length-1:n.length;return n.slice(l,u)}function J1(n,l){const u=(n.toString().split(".")[1]||"").length,s=(l.toString().split(".")[1]||"").length,o=u>s?u:s,f=Number.parseInt(n.toFixed(o).replace(".","")),h=Number.parseInt(l.toFixed(o).replace(".",""));return f%h/10**o}function we(n,l,u){Object.defineProperty(n,l,{get(){{const s=u();return n[l]=s,s}},set(s){Object.defineProperty(n,l,{value:s})},configurable:!0})}function pa(n,l,u){Object.defineProperty(n,l,{value:u,writable:!0,enumerable:!0,configurable:!0})}function ul(...n){const l={};for(const u of n){const s=Object.getOwnPropertyDescriptors(u);Object.assign(l,s)}return Object.defineProperties({},l)}function Fm(n){return JSON.stringify(n)}const lv="captureStackTrace"in Error?Error.captureStackTrace:(...n)=>{};function Pc(n){return typeof n=="object"&&n!==null&&!Array.isArray(n)}const P1=av(()=>{if(typeof navigator<"u"&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{const n=Function;return new n(""),!0}catch{return!1}});function Pu(n){if(Pc(n)===!1)return!1;const l=n.constructor;if(l===void 0)return!0;const u=l.prototype;return!(Pc(u)===!1||Object.prototype.hasOwnProperty.call(u,"isPrototypeOf")===!1)}const W1=new Set(["string","number","symbol"]);function us(n){return n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function va(n,l,u){const s=new n._zod.constr(l??n._zod.def);return(!l||u?.parent)&&(s._zod.parent=n),s}function P(n){const l=n;if(!l)return{};if(typeof l=="string")return{error:()=>l};if(l?.message!==void 0){if(l?.error!==void 0)throw new Error("Cannot specify both `message` and `error` params");l.error=l.message}return delete l.message,typeof l.error=="string"?{...l,error:()=>l.error}:l}function I1(n){return Object.keys(n).filter(l=>n[l]._zod.optin==="optional"&&n[l]._zod.optout==="optional")}const ex={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-2147483648,2147483647],uint32:[0,4294967295],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]};function tx(n,l){const u=n._zod.def,s=ul(n._zod.def,{get shape(){const o={};for(const f in l){if(!(f in u.shape))throw new Error(`Unrecognized key: "${f}"`);l[f]&&(o[f]=u.shape[f])}return pa(this,"shape",o),o},checks:[]});return va(n,s)}function nx(n,l){const u=n._zod.def,s=ul(n._zod.def,{get shape(){const o={...n._zod.def.shape};for(const f in l){if(!(f in u.shape))throw new Error(`Unrecognized key: "${f}"`);l[f]&&delete o[f]}return pa(this,"shape",o),o},checks:[]});return va(n,s)}function ax(n,l){if(!Pu(l))throw new Error("Invalid input to extend: expected a plain object");const u=ul(n._zod.def,{get shape(){const s={...n._zod.def.shape,...l};return pa(this,"shape",s),s},checks:[]});return va(n,u)}function lx(n,l){const u=ul(n._zod.def,{get shape(){const s={...n._zod.def.shape,...l._zod.def.shape};return pa(this,"shape",s),s},get catchall(){return l._zod.def.catchall},checks:[]});return va(n,u)}function ix(n,l,u){const s=ul(l._zod.def,{get shape(){const o=l._zod.def.shape,f={...o};if(u)for(const h in u){if(!(h in o))throw new Error(`Unrecognized key: "${h}"`);u[h]&&(f[h]=n?new n({type:"optional",innerType:o[h]}):o[h])}else for(const h in o)f[h]=n?new n({type:"optional",innerType:o[h]}):o[h];return pa(this,"shape",f),f},checks:[]});return va(l,s)}function ux(n,l,u){const s=ul(l._zod.def,{get shape(){const o=l._zod.def.shape,f={...o};if(u)for(const h in u){if(!(h in f))throw new Error(`Unrecognized key: "${h}"`);u[h]&&(f[h]=new n({type:"nonoptional",innerType:o[h]}))}else for(const h in o)f[h]=new n({type:"nonoptional",innerType:o[h]});return pa(this,"shape",f),f},checks:[]});return va(l,s)}function ri(n,l=0){for(let u=l;u<n.issues.length;u++)if(n.issues[u]?.continue!==!0)return!0;return!1}function nl(n,l){return l.map(u=>{var s;return(s=u).path??(s.path=[]),u.path.unshift(n),u})}function Lu(n){return typeof n=="string"?n:n?.message}function Yn(n,l,u){const s={...n,path:n.path??[]};if(!n.message){const o=Lu(n.inst?._zod.def?.error?.(n))??Lu(l?.error?.(n))??Lu(u.customError?.(n))??Lu(u.localeError?.(n))??"Invalid input";s.message=o}return delete s.inst,delete s.continue,l?.reportInput||delete s.input,s}function ho(n){return Array.isArray(n)?"array":typeof n=="string"?"string":"unknown"}function pi(...n){const[l,u,s]=n;return typeof l=="string"?{message:l,code:"custom",input:u,inst:s}:{...l}}const iv=(n,l)=>{n.name="$ZodError",Object.defineProperty(n,"_zod",{value:n._zod,enumerable:!1}),Object.defineProperty(n,"issues",{value:l,enumerable:!1}),n.message=JSON.stringify(l,Jc,2),Object.defineProperty(n,"toString",{value:()=>n.message,enumerable:!1})},uv=q("$ZodError",iv),sv=q("$ZodError",iv,{Parent:Error});function sx(n,l=u=>u.message){const u={},s=[];for(const o of n.issues)o.path.length>0?(u[o.path[0]]=u[o.path[0]]||[],u[o.path[0]].push(l(o))):s.push(l(o));return{formErrors:s,fieldErrors:u}}function rx(n,l){const u=l||function(f){return f.message},s={_errors:[]},o=f=>{for(const h of f.issues)if(h.code==="invalid_union"&&h.errors.length)h.errors.map(y=>o({issues:y}));else if(h.code==="invalid_key")o({issues:h.issues});else if(h.code==="invalid_element")o({issues:h.issues});else if(h.path.length===0)s._errors.push(u(h));else{let y=s,g=0;for(;g<h.path.length;){const v=h.path[g];g===h.path.length-1?(y[v]=y[v]||{_errors:[]},y[v]._errors.push(u(h))):y[v]=y[v]||{_errors:[]},y=y[v],g++}}};return o(n),s}const cx=n=>(l,u,s,o)=>{const f=s?Object.assign(s,{async:!1}):{async:!1},h=l._zod.run({value:u,issues:[]},f);if(h instanceof Promise)throw new mi;if(h.issues.length){const y=new(o?.Err??n)(h.issues.map(g=>Yn(g,f,Gn())));throw lv(y,o?.callee),y}return h.value},ox=n=>async(l,u,s,o)=>{const f=s?Object.assign(s,{async:!0}):{async:!0};let h=l._zod.run({value:u,issues:[]},f);if(h instanceof Promise&&(h=await h),h.issues.length){const y=new(o?.Err??n)(h.issues.map(g=>Yn(g,f,Gn())));throw lv(y,o?.callee),y}return h.value},rv=n=>(l,u,s)=>{const o=s?{...s,async:!1}:{async:!1},f=l._zod.run({value:u,issues:[]},o);if(f instanceof Promise)throw new mi;return f.issues.length?{success:!1,error:new(n??uv)(f.issues.map(h=>Yn(h,o,Gn())))}:{success:!0,data:f.value}},fx=rv(sv),cv=n=>async(l,u,s)=>{const o=s?Object.assign(s,{async:!0}):{async:!0};let f=l._zod.run({value:u,issues:[]},o);return f instanceof Promise&&(f=await f),f.issues.length?{success:!1,error:new n(f.issues.map(h=>Yn(h,o,Gn())))}:{success:!0,data:f.value}},dx=cv(sv),hx=/^[cC][^\s-]{8,}$/,mx=/^[0-9a-z]+$/,px=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,vx=/^[0-9a-vA-V]{20}$/,yx=/^[A-Za-z0-9]{27}$/,gx=/^[a-zA-Z0-9_-]{21}$/,bx=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,xx=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,Jm=n=>n?new RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${n}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/,Sx=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,_x="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";function Ex(){return new RegExp(_x,"u")}const zx=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Tx=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,Ox=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,Nx=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,jx=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,ov=/^[A-Za-z0-9_-]*$/,Ax=/^([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+$/,Rx=/^\+(?:[0-9]){6,14}[0-9]$/,fv="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",wx=new RegExp(`^${fv}$`);function dv(n){const l="(?:[01]\\d|2[0-3]):[0-5]\\d";return typeof n.precision=="number"?n.precision===-1?`${l}`:n.precision===0?`${l}:[0-5]\\d`:`${l}:[0-5]\\d\\.\\d{${n.precision}}`:`${l}(?::[0-5]\\d(?:\\.\\d+)?)?`}function Dx(n){return new RegExp(`^${dv(n)}$`)}function Cx(n){const l=dv({precision:n.precision}),u=["Z"];n.local&&u.push(""),n.offset&&u.push("([+-]\\d{2}:\\d{2})");const s=`${l}(?:${u.join("|")})`;return new RegExp(`^${fv}T(?:${s})$`)}const Mx=n=>{const l=n?`[\\s\\S]{${n?.minimum??0},${n?.maximum??""}}`:"[\\s\\S]*";return new RegExp(`^${l}$`)},Ux=/^\d+$/,Zx=/^-?\d+(?:\.\d+)?/i,qx=/true|false/i,Bx=/^[^A-Z]*$/,Hx=/^[^a-z]*$/,St=q("$ZodCheck",(n,l)=>{var u;n._zod??(n._zod={}),n._zod.def=l,(u=n._zod).onattach??(u.onattach=[])}),hv={number:"number",bigint:"bigint",object:"date"},mv=q("$ZodCheckLessThan",(n,l)=>{St.init(n,l);const u=hv[typeof l.value];n._zod.onattach.push(s=>{const o=s._zod.bag,f=(l.inclusive?o.maximum:o.exclusiveMaximum)??Number.POSITIVE_INFINITY;l.value<f&&(l.inclusive?o.maximum=l.value:o.exclusiveMaximum=l.value)}),n._zod.check=s=>{(l.inclusive?s.value<=l.value:s.value<l.value)||s.issues.push({origin:u,code:"too_big",maximum:l.value,input:s.value,inclusive:l.inclusive,inst:n,continue:!l.abort})}}),pv=q("$ZodCheckGreaterThan",(n,l)=>{St.init(n,l);const u=hv[typeof l.value];n._zod.onattach.push(s=>{const o=s._zod.bag,f=(l.inclusive?o.minimum:o.exclusiveMinimum)??Number.NEGATIVE_INFINITY;l.value>f&&(l.inclusive?o.minimum=l.value:o.exclusiveMinimum=l.value)}),n._zod.check=s=>{(l.inclusive?s.value>=l.value:s.value>l.value)||s.issues.push({origin:u,code:"too_small",minimum:l.value,input:s.value,inclusive:l.inclusive,inst:n,continue:!l.abort})}}),kx=q("$ZodCheckMultipleOf",(n,l)=>{St.init(n,l),n._zod.onattach.push(u=>{var s;(s=u._zod.bag).multipleOf??(s.multipleOf=l.value)}),n._zod.check=u=>{if(typeof u.value!=typeof l.value)throw new Error("Cannot mix number and bigint in multiple_of check.");(typeof u.value=="bigint"?u.value%l.value===BigInt(0):J1(u.value,l.value)===0)||u.issues.push({origin:typeof u.value,code:"not_multiple_of",divisor:l.value,input:u.value,inst:n,continue:!l.abort})}}),Lx=q("$ZodCheckNumberFormat",(n,l)=>{St.init(n,l),l.format=l.format||"float64";const u=l.format?.includes("int"),s=u?"int":"number",[o,f]=ex[l.format];n._zod.onattach.push(h=>{const y=h._zod.bag;y.format=l.format,y.minimum=o,y.maximum=f,u&&(y.pattern=Ux)}),n._zod.check=h=>{const y=h.value;if(u){if(!Number.isInteger(y)){h.issues.push({expected:s,format:l.format,code:"invalid_type",input:y,inst:n});return}if(!Number.isSafeInteger(y)){y>0?h.issues.push({input:y,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:n,origin:s,continue:!l.abort}):h.issues.push({input:y,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:n,origin:s,continue:!l.abort});return}}y<o&&h.issues.push({origin:"number",input:y,code:"too_small",minimum:o,inclusive:!0,inst:n,continue:!l.abort}),y>f&&h.issues.push({origin:"number",input:y,code:"too_big",maximum:f,inst:n})}}),Qx=q("$ZodCheckMaxLength",(n,l)=>{var u;St.init(n,l),(u=n._zod.def).when??(u.when=s=>{const o=s.value;return!oo(o)&&o.length!==void 0}),n._zod.onattach.push(s=>{const o=s._zod.bag.maximum??Number.POSITIVE_INFINITY;l.maximum<o&&(s._zod.bag.maximum=l.maximum)}),n._zod.check=s=>{const o=s.value;if(o.length<=l.maximum)return;const h=ho(o);s.issues.push({origin:h,code:"too_big",maximum:l.maximum,inclusive:!0,input:o,inst:n,continue:!l.abort})}}),Gx=q("$ZodCheckMinLength",(n,l)=>{var u;St.init(n,l),(u=n._zod.def).when??(u.when=s=>{const o=s.value;return!oo(o)&&o.length!==void 0}),n._zod.onattach.push(s=>{const o=s._zod.bag.minimum??Number.NEGATIVE_INFINITY;l.minimum>o&&(s._zod.bag.minimum=l.minimum)}),n._zod.check=s=>{const o=s.value;if(o.length>=l.minimum)return;const h=ho(o);s.issues.push({origin:h,code:"too_small",minimum:l.minimum,inclusive:!0,input:o,inst:n,continue:!l.abort})}}),Yx=q("$ZodCheckLengthEquals",(n,l)=>{var u;St.init(n,l),(u=n._zod.def).when??(u.when=s=>{const o=s.value;return!oo(o)&&o.length!==void 0}),n._zod.onattach.push(s=>{const o=s._zod.bag;o.minimum=l.length,o.maximum=l.length,o.length=l.length}),n._zod.check=s=>{const o=s.value,f=o.length;if(f===l.length)return;const h=ho(o),y=f>l.length;s.issues.push({origin:h,...y?{code:"too_big",maximum:l.length}:{code:"too_small",minimum:l.length},inclusive:!0,exact:!0,input:s.value,inst:n,continue:!l.abort})}}),ss=q("$ZodCheckStringFormat",(n,l)=>{var u,s;St.init(n,l),n._zod.onattach.push(o=>{const f=o._zod.bag;f.format=l.format,l.pattern&&(f.patterns??(f.patterns=new Set),f.patterns.add(l.pattern))}),l.pattern?(u=n._zod).check??(u.check=o=>{l.pattern.lastIndex=0,!l.pattern.test(o.value)&&o.issues.push({origin:"string",code:"invalid_format",format:l.format,input:o.value,...l.pattern?{pattern:l.pattern.toString()}:{},inst:n,continue:!l.abort})}):(s=n._zod).check??(s.check=()=>{})}),Vx=q("$ZodCheckRegex",(n,l)=>{ss.init(n,l),n._zod.check=u=>{l.pattern.lastIndex=0,!l.pattern.test(u.value)&&u.issues.push({origin:"string",code:"invalid_format",format:"regex",input:u.value,pattern:l.pattern.toString(),inst:n,continue:!l.abort})}}),Xx=q("$ZodCheckLowerCase",(n,l)=>{l.pattern??(l.pattern=Bx),ss.init(n,l)}),Kx=q("$ZodCheckUpperCase",(n,l)=>{l.pattern??(l.pattern=Hx),ss.init(n,l)}),$x=q("$ZodCheckIncludes",(n,l)=>{St.init(n,l);const u=us(l.includes),s=new RegExp(typeof l.position=="number"?`^.{${l.position}}${u}`:u);l.pattern=s,n._zod.onattach.push(o=>{const f=o._zod.bag;f.patterns??(f.patterns=new Set),f.patterns.add(s)}),n._zod.check=o=>{o.value.includes(l.includes,l.position)||o.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:l.includes,input:o.value,inst:n,continue:!l.abort})}}),Fx=q("$ZodCheckStartsWith",(n,l)=>{St.init(n,l);const u=new RegExp(`^${us(l.prefix)}.*`);l.pattern??(l.pattern=u),n._zod.onattach.push(s=>{const o=s._zod.bag;o.patterns??(o.patterns=new Set),o.patterns.add(u)}),n._zod.check=s=>{s.value.startsWith(l.prefix)||s.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:l.prefix,input:s.value,inst:n,continue:!l.abort})}}),Jx=q("$ZodCheckEndsWith",(n,l)=>{St.init(n,l);const u=new RegExp(`.*${us(l.suffix)}$`);l.pattern??(l.pattern=u),n._zod.onattach.push(s=>{const o=s._zod.bag;o.patterns??(o.patterns=new Set),o.patterns.add(u)}),n._zod.check=s=>{s.value.endsWith(l.suffix)||s.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:l.suffix,input:s.value,inst:n,continue:!l.abort})}}),Px=q("$ZodCheckOverwrite",(n,l)=>{St.init(n,l),n._zod.check=u=>{u.value=l.tx(u.value)}});class Wx{constructor(l=[]){this.content=[],this.indent=0,this&&(this.args=l)}indented(l){this.indent+=1,l(this),this.indent-=1}write(l){if(typeof l=="function"){l(this,{execution:"sync"}),l(this,{execution:"async"});return}const s=l.split(`
`).filter(h=>h),o=Math.min(...s.map(h=>h.length-h.trimStart().length)),f=s.map(h=>h.slice(o)).map(h=>" ".repeat(this.indent*2)+h);for(const h of f)this.content.push(h)}compile(){const l=Function,u=this?.args,o=[...(this?.content??[""]).map(f=>`  ${f}`)];return new l(...u,o.join(`
`))}}const Ix={major:4,minor:0,patch:10},Be=q("$ZodType",(n,l)=>{var u;n??(n={}),n._zod.def=l,n._zod.bag=n._zod.bag||{},n._zod.version=Ix;const s=[...n._zod.def.checks??[]];n._zod.traits.has("$ZodCheck")&&s.unshift(n);for(const o of s)for(const f of o._zod.onattach)f(n);if(s.length===0)(u=n._zod).deferred??(u.deferred=[]),n._zod.deferred?.push(()=>{n._zod.run=n._zod.parse});else{const o=(f,h,y)=>{let g=ri(f),v;for(const b of h){if(b._zod.def.when){if(!b._zod.def.when(f))continue}else if(g)continue;const z=f.issues.length,w=b._zod.check(f);if(w instanceof Promise&&y?.async===!1)throw new mi;if(v||w instanceof Promise)v=(v??Promise.resolve()).then(async()=>{await w,f.issues.length!==z&&(g||(g=ri(f,z)))});else{if(f.issues.length===z)continue;g||(g=ri(f,z))}}return v?v.then(()=>f):f};n._zod.run=(f,h)=>{const y=n._zod.parse(f,h);if(y instanceof Promise){if(h.async===!1)throw new mi;return y.then(g=>o(g,s,h))}return o(y,s,h)}}n["~standard"]={validate:o=>{try{const f=fx(n,o);return f.success?{value:f.data}:{issues:f.error?.issues}}catch{return dx(n,o).then(h=>h.success?{value:h.data}:{issues:h.error?.issues})}},vendor:"zod",version:1}}),mo=q("$ZodString",(n,l)=>{Be.init(n,l),n._zod.pattern=[...n?._zod.bag?.patterns??[]].pop()??Mx(n._zod.bag),n._zod.parse=(u,s)=>{if(l.coerce)try{u.value=String(u.value)}catch{}return typeof u.value=="string"||u.issues.push({expected:"string",code:"invalid_type",input:u.value,inst:n}),u}}),Ze=q("$ZodStringFormat",(n,l)=>{ss.init(n,l),mo.init(n,l)}),eS=q("$ZodGUID",(n,l)=>{l.pattern??(l.pattern=xx),Ze.init(n,l)}),tS=q("$ZodUUID",(n,l)=>{if(l.version){const s={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[l.version];if(s===void 0)throw new Error(`Invalid UUID version: "${l.version}"`);l.pattern??(l.pattern=Jm(s))}else l.pattern??(l.pattern=Jm());Ze.init(n,l)}),nS=q("$ZodEmail",(n,l)=>{l.pattern??(l.pattern=Sx),Ze.init(n,l)}),aS=q("$ZodURL",(n,l)=>{Ze.init(n,l),n._zod.check=u=>{try{const s=u.value.trim(),o=new URL(s);l.hostname&&(l.hostname.lastIndex=0,l.hostname.test(o.hostname)||u.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:Ax.source,input:u.value,inst:n,continue:!l.abort})),l.protocol&&(l.protocol.lastIndex=0,l.protocol.test(o.protocol.endsWith(":")?o.protocol.slice(0,-1):o.protocol)||u.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:l.protocol.source,input:u.value,inst:n,continue:!l.abort})),l.normalize?u.value=o.href:u.value=s;return}catch{u.issues.push({code:"invalid_format",format:"url",input:u.value,inst:n,continue:!l.abort})}}}),lS=q("$ZodEmoji",(n,l)=>{l.pattern??(l.pattern=Ex()),Ze.init(n,l)}),iS=q("$ZodNanoID",(n,l)=>{l.pattern??(l.pattern=gx),Ze.init(n,l)}),uS=q("$ZodCUID",(n,l)=>{l.pattern??(l.pattern=hx),Ze.init(n,l)}),sS=q("$ZodCUID2",(n,l)=>{l.pattern??(l.pattern=mx),Ze.init(n,l)}),rS=q("$ZodULID",(n,l)=>{l.pattern??(l.pattern=px),Ze.init(n,l)}),cS=q("$ZodXID",(n,l)=>{l.pattern??(l.pattern=vx),Ze.init(n,l)}),oS=q("$ZodKSUID",(n,l)=>{l.pattern??(l.pattern=yx),Ze.init(n,l)}),fS=q("$ZodISODateTime",(n,l)=>{l.pattern??(l.pattern=Cx(l)),Ze.init(n,l)}),dS=q("$ZodISODate",(n,l)=>{l.pattern??(l.pattern=wx),Ze.init(n,l)}),hS=q("$ZodISOTime",(n,l)=>{l.pattern??(l.pattern=Dx(l)),Ze.init(n,l)}),mS=q("$ZodISODuration",(n,l)=>{l.pattern??(l.pattern=bx),Ze.init(n,l)}),pS=q("$ZodIPv4",(n,l)=>{l.pattern??(l.pattern=zx),Ze.init(n,l),n._zod.onattach.push(u=>{const s=u._zod.bag;s.format="ipv4"})}),vS=q("$ZodIPv6",(n,l)=>{l.pattern??(l.pattern=Tx),Ze.init(n,l),n._zod.onattach.push(u=>{const s=u._zod.bag;s.format="ipv6"}),n._zod.check=u=>{try{new URL(`http://[${u.value}]`)}catch{u.issues.push({code:"invalid_format",format:"ipv6",input:u.value,inst:n,continue:!l.abort})}}}),yS=q("$ZodCIDRv4",(n,l)=>{l.pattern??(l.pattern=Ox),Ze.init(n,l)}),gS=q("$ZodCIDRv6",(n,l)=>{l.pattern??(l.pattern=Nx),Ze.init(n,l),n._zod.check=u=>{const[s,o]=u.value.split("/");try{if(!o)throw new Error;const f=Number(o);if(`${f}`!==o)throw new Error;if(f<0||f>128)throw new Error;new URL(`http://[${s}]`)}catch{u.issues.push({code:"invalid_format",format:"cidrv6",input:u.value,inst:n,continue:!l.abort})}}});function vv(n){if(n==="")return!0;if(n.length%4!==0)return!1;try{return atob(n),!0}catch{return!1}}const bS=q("$ZodBase64",(n,l)=>{l.pattern??(l.pattern=jx),Ze.init(n,l),n._zod.onattach.push(u=>{u._zod.bag.contentEncoding="base64"}),n._zod.check=u=>{vv(u.value)||u.issues.push({code:"invalid_format",format:"base64",input:u.value,inst:n,continue:!l.abort})}});function xS(n){if(!ov.test(n))return!1;const l=n.replace(/[-_]/g,s=>s==="-"?"+":"/"),u=l.padEnd(Math.ceil(l.length/4)*4,"=");return vv(u)}const SS=q("$ZodBase64URL",(n,l)=>{l.pattern??(l.pattern=ov),Ze.init(n,l),n._zod.onattach.push(u=>{u._zod.bag.contentEncoding="base64url"}),n._zod.check=u=>{xS(u.value)||u.issues.push({code:"invalid_format",format:"base64url",input:u.value,inst:n,continue:!l.abort})}}),_S=q("$ZodE164",(n,l)=>{l.pattern??(l.pattern=Rx),Ze.init(n,l)});function ES(n,l=null){try{const u=n.split(".");if(u.length!==3)return!1;const[s]=u;if(!s)return!1;const o=JSON.parse(atob(s));return!("typ"in o&&o?.typ!=="JWT"||!o.alg||l&&(!("alg"in o)||o.alg!==l))}catch{return!1}}const zS=q("$ZodJWT",(n,l)=>{Ze.init(n,l),n._zod.check=u=>{ES(u.value,l.alg)||u.issues.push({code:"invalid_format",format:"jwt",input:u.value,inst:n,continue:!l.abort})}}),yv=q("$ZodNumber",(n,l)=>{Be.init(n,l),n._zod.pattern=n._zod.bag.pattern??Zx,n._zod.parse=(u,s)=>{if(l.coerce)try{u.value=Number(u.value)}catch{}const o=u.value;if(typeof o=="number"&&!Number.isNaN(o)&&Number.isFinite(o))return u;const f=typeof o=="number"?Number.isNaN(o)?"NaN":Number.isFinite(o)?void 0:"Infinity":void 0;return u.issues.push({expected:"number",code:"invalid_type",input:o,inst:n,...f?{received:f}:{}}),u}}),TS=q("$ZodNumber",(n,l)=>{Lx.init(n,l),yv.init(n,l)}),OS=q("$ZodBoolean",(n,l)=>{Be.init(n,l),n._zod.pattern=qx,n._zod.parse=(u,s)=>{if(l.coerce)try{u.value=!!u.value}catch{}const o=u.value;return typeof o=="boolean"||u.issues.push({expected:"boolean",code:"invalid_type",input:o,inst:n}),u}}),NS=q("$ZodUnknown",(n,l)=>{Be.init(n,l),n._zod.parse=u=>u}),jS=q("$ZodNever",(n,l)=>{Be.init(n,l),n._zod.parse=(u,s)=>(u.issues.push({expected:"never",code:"invalid_type",input:u.value,inst:n}),u)});function Pm(n,l,u){n.issues.length&&l.issues.push(...nl(u,n.issues)),l.value[u]=n.value}const AS=q("$ZodArray",(n,l)=>{Be.init(n,l),n._zod.parse=(u,s)=>{const o=u.value;if(!Array.isArray(o))return u.issues.push({expected:"array",code:"invalid_type",input:o,inst:n}),u;u.value=Array(o.length);const f=[];for(let h=0;h<o.length;h++){const y=o[h],g=l.element._zod.run({value:y,issues:[]},s);g instanceof Promise?f.push(g.then(v=>Pm(v,u,h))):Pm(g,u,h)}return f.length?Promise.all(f).then(()=>u):u}});function Qu(n,l,u,s){n.issues.length&&l.issues.push(...nl(u,n.issues)),n.value===void 0?u in s&&(l.value[u]=void 0):l.value[u]=n.value}const RS=q("$ZodObject",(n,l)=>{Be.init(n,l);const u=av(()=>{const z=Object.keys(l.shape);for(const k of z)if(!(l.shape[k]instanceof Be))throw new Error(`Invalid element at key "${k}": expected a Zod schema`);const w=I1(l.shape);return{shape:l.shape,keys:z,keySet:new Set(z),numKeys:z.length,optionalKeys:new Set(w)}});we(n._zod,"propValues",()=>{const z=l.shape,w={};for(const k in z){const M=z[k]._zod;if(M.values){w[k]??(w[k]=new Set);for(const D of M.values)w[k].add(D)}}return w});const s=z=>{const w=new Wx(["shape","payload","ctx"]),k=u.value,M=G=>{const Q=Fm(G);return`shape[${Q}]._zod.run({ value: input[${Q}], issues: [] }, ctx)`};w.write("const input = payload.value;");const D=Object.create(null);let Z=0;for(const G of k.keys)D[G]=`key_${Z++}`;w.write("const newResult = {}");for(const G of k.keys){const Q=D[G],ee=Fm(G);w.write(`const ${Q} = ${M(G)};`),w.write(`
        if (${Q}.issues.length) {
          payload.issues = payload.issues.concat(${Q}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${ee}, ...iss.path] : [${ee}]
          })));
        }
        
        if (${Q}.value === undefined) {
          if (${ee} in input) {
            newResult[${ee}] = undefined;
          }
        } else {
          newResult[${ee}] = ${Q}.value;
        }
      `)}w.write("payload.value = newResult;"),w.write("return payload;");const L=w.compile();return(G,Q)=>L(z,G,Q)};let o;const f=Pc,h=!nv.jitless,g=h&&P1.value,v=l.catchall;let b;n._zod.parse=(z,w)=>{b??(b=u.value);const k=z.value;if(!f(k))return z.issues.push({expected:"object",code:"invalid_type",input:k,inst:n}),z;const M=[];if(h&&g&&w?.async===!1&&w.jitless!==!0)o||(o=s(l.shape)),z=o(z,w);else{z.value={};const Q=b.shape;for(const ee of b.keys){const pe=Q[ee]._zod.run({value:k[ee],issues:[]},w);pe instanceof Promise?M.push(pe.then(ue=>Qu(ue,z,ee,k))):Qu(pe,z,ee,k)}}if(!v)return M.length?Promise.all(M).then(()=>z):z;const D=[],Z=b.keySet,L=v._zod,G=L.def.type;for(const Q of Object.keys(k)){if(Z.has(Q))continue;if(G==="never"){D.push(Q);continue}const ee=L.run({value:k[Q],issues:[]},w);ee instanceof Promise?M.push(ee.then(K=>Qu(K,z,Q,k))):Qu(ee,z,Q,k)}return D.length&&z.issues.push({code:"unrecognized_keys",keys:D,input:k,inst:n}),M.length?Promise.all(M).then(()=>z):z}});function Wm(n,l,u,s){for(const f of n)if(f.issues.length===0)return l.value=f.value,l;const o=n.filter(f=>!ri(f));return o.length===1?(l.value=o[0].value,o[0]):(l.issues.push({code:"invalid_union",input:l.value,inst:u,errors:n.map(f=>f.issues.map(h=>Yn(h,s,Gn())))}),l)}const wS=q("$ZodUnion",(n,l)=>{Be.init(n,l),we(n._zod,"optin",()=>l.options.some(u=>u._zod.optin==="optional")?"optional":void 0),we(n._zod,"optout",()=>l.options.some(u=>u._zod.optout==="optional")?"optional":void 0),we(n._zod,"values",()=>{if(l.options.every(u=>u._zod.values))return new Set(l.options.flatMap(u=>Array.from(u._zod.values)))}),we(n._zod,"pattern",()=>{if(l.options.every(u=>u._zod.pattern)){const u=l.options.map(s=>s._zod.pattern);return new RegExp(`^(${u.map(s=>fo(s.source)).join("|")})$`)}}),n._zod.parse=(u,s)=>{let o=!1;const f=[];for(const h of l.options){const y=h._zod.run({value:u.value,issues:[]},s);if(y instanceof Promise)f.push(y),o=!0;else{if(y.issues.length===0)return y;f.push(y)}}return o?Promise.all(f).then(h=>Wm(h,u,n,s)):Wm(f,u,n,s)}}),DS=q("$ZodIntersection",(n,l)=>{Be.init(n,l),n._zod.parse=(u,s)=>{const o=u.value,f=l.left._zod.run({value:o,issues:[]},s),h=l.right._zod.run({value:o,issues:[]},s);return f instanceof Promise||h instanceof Promise?Promise.all([f,h]).then(([g,v])=>Im(u,g,v)):Im(u,f,h)}});function Wc(n,l){if(n===l)return{valid:!0,data:n};if(n instanceof Date&&l instanceof Date&&+n==+l)return{valid:!0,data:n};if(Pu(n)&&Pu(l)){const u=Object.keys(l),s=Object.keys(n).filter(f=>u.indexOf(f)!==-1),o={...n,...l};for(const f of s){const h=Wc(n[f],l[f]);if(!h.valid)return{valid:!1,mergeErrorPath:[f,...h.mergeErrorPath]};o[f]=h.data}return{valid:!0,data:o}}if(Array.isArray(n)&&Array.isArray(l)){if(n.length!==l.length)return{valid:!1,mergeErrorPath:[]};const u=[];for(let s=0;s<n.length;s++){const o=n[s],f=l[s],h=Wc(o,f);if(!h.valid)return{valid:!1,mergeErrorPath:[s,...h.mergeErrorPath]};u.push(h.data)}return{valid:!0,data:u}}return{valid:!1,mergeErrorPath:[]}}function Im(n,l,u){if(l.issues.length&&n.issues.push(...l.issues),u.issues.length&&n.issues.push(...u.issues),ri(n))return n;const s=Wc(l.value,u.value);if(!s.valid)throw new Error(`Unmergable intersection. Error path: ${JSON.stringify(s.mergeErrorPath)}`);return n.value=s.data,n}const CS=q("$ZodRecord",(n,l)=>{Be.init(n,l),n._zod.parse=(u,s)=>{const o=u.value;if(!Pu(o))return u.issues.push({expected:"record",code:"invalid_type",input:o,inst:n}),u;const f=[];if(l.keyType._zod.values){const h=l.keyType._zod.values;u.value={};for(const g of h)if(typeof g=="string"||typeof g=="number"||typeof g=="symbol"){const v=l.valueType._zod.run({value:o[g],issues:[]},s);v instanceof Promise?f.push(v.then(b=>{b.issues.length&&u.issues.push(...nl(g,b.issues)),u.value[g]=b.value})):(v.issues.length&&u.issues.push(...nl(g,v.issues)),u.value[g]=v.value)}let y;for(const g in o)h.has(g)||(y=y??[],y.push(g));y&&y.length>0&&u.issues.push({code:"unrecognized_keys",input:o,inst:n,keys:y})}else{u.value={};for(const h of Reflect.ownKeys(o)){if(h==="__proto__")continue;const y=l.keyType._zod.run({value:h,issues:[]},s);if(y instanceof Promise)throw new Error("Async schemas not supported in object keys currently");if(y.issues.length){u.issues.push({origin:"record",code:"invalid_key",issues:y.issues.map(v=>Yn(v,s,Gn())),input:h,path:[h],inst:n}),u.value[y.value]=y.value;continue}const g=l.valueType._zod.run({value:o[h],issues:[]},s);g instanceof Promise?f.push(g.then(v=>{v.issues.length&&u.issues.push(...nl(h,v.issues)),u.value[y.value]=v.value})):(g.issues.length&&u.issues.push(...nl(h,g.issues)),u.value[y.value]=g.value)}}return f.length?Promise.all(f).then(()=>u):u}}),MS=q("$ZodEnum",(n,l)=>{Be.init(n,l);const u=F1(l.entries),s=new Set(u);n._zod.values=s,n._zod.pattern=new RegExp(`^(${u.filter(o=>W1.has(typeof o)).map(o=>typeof o=="string"?us(o):o.toString()).join("|")})$`),n._zod.parse=(o,f)=>{const h=o.value;return s.has(h)||o.issues.push({code:"invalid_value",values:u,input:h,inst:n}),o}}),US=q("$ZodTransform",(n,l)=>{Be.init(n,l),n._zod.parse=(u,s)=>{const o=l.transform(u.value,u);if(s.async)return(o instanceof Promise?o:Promise.resolve(o)).then(h=>(u.value=h,u));if(o instanceof Promise)throw new mi;return u.value=o,u}}),ZS=q("$ZodOptional",(n,l)=>{Be.init(n,l),n._zod.optin="optional",n._zod.optout="optional",we(n._zod,"values",()=>l.innerType._zod.values?new Set([...l.innerType._zod.values,void 0]):void 0),we(n._zod,"pattern",()=>{const u=l.innerType._zod.pattern;return u?new RegExp(`^(${fo(u.source)})?$`):void 0}),n._zod.parse=(u,s)=>l.innerType._zod.optin==="optional"?l.innerType._zod.run(u,s):u.value===void 0?u:l.innerType._zod.run(u,s)}),qS=q("$ZodNullable",(n,l)=>{Be.init(n,l),we(n._zod,"optin",()=>l.innerType._zod.optin),we(n._zod,"optout",()=>l.innerType._zod.optout),we(n._zod,"pattern",()=>{const u=l.innerType._zod.pattern;return u?new RegExp(`^(${fo(u.source)}|null)$`):void 0}),we(n._zod,"values",()=>l.innerType._zod.values?new Set([...l.innerType._zod.values,null]):void 0),n._zod.parse=(u,s)=>u.value===null?u:l.innerType._zod.run(u,s)}),BS=q("$ZodDefault",(n,l)=>{Be.init(n,l),n._zod.optin="optional",we(n._zod,"values",()=>l.innerType._zod.values),n._zod.parse=(u,s)=>{if(u.value===void 0)return u.value=l.defaultValue,u;const o=l.innerType._zod.run(u,s);return o instanceof Promise?o.then(f=>ep(f,l)):ep(o,l)}});function ep(n,l){return n.value===void 0&&(n.value=l.defaultValue),n}const HS=q("$ZodPrefault",(n,l)=>{Be.init(n,l),n._zod.optin="optional",we(n._zod,"values",()=>l.innerType._zod.values),n._zod.parse=(u,s)=>(u.value===void 0&&(u.value=l.defaultValue),l.innerType._zod.run(u,s))}),kS=q("$ZodNonOptional",(n,l)=>{Be.init(n,l),we(n._zod,"values",()=>{const u=l.innerType._zod.values;return u?new Set([...u].filter(s=>s!==void 0)):void 0}),n._zod.parse=(u,s)=>{const o=l.innerType._zod.run(u,s);return o instanceof Promise?o.then(f=>tp(f,n)):tp(o,n)}});function tp(n,l){return!n.issues.length&&n.value===void 0&&n.issues.push({code:"invalid_type",expected:"nonoptional",input:n.value,inst:l}),n}const LS=q("$ZodCatch",(n,l)=>{Be.init(n,l),we(n._zod,"optin",()=>l.innerType._zod.optin),we(n._zod,"optout",()=>l.innerType._zod.optout),we(n._zod,"values",()=>l.innerType._zod.values),n._zod.parse=(u,s)=>{const o=l.innerType._zod.run(u,s);return o instanceof Promise?o.then(f=>(u.value=f.value,f.issues.length&&(u.value=l.catchValue({...u,error:{issues:f.issues.map(h=>Yn(h,s,Gn()))},input:u.value}),u.issues=[]),u)):(u.value=o.value,o.issues.length&&(u.value=l.catchValue({...u,error:{issues:o.issues.map(f=>Yn(f,s,Gn()))},input:u.value}),u.issues=[]),u)}}),QS=q("$ZodPipe",(n,l)=>{Be.init(n,l),we(n._zod,"values",()=>l.in._zod.values),we(n._zod,"optin",()=>l.in._zod.optin),we(n._zod,"optout",()=>l.out._zod.optout),we(n._zod,"propValues",()=>l.in._zod.propValues),n._zod.parse=(u,s)=>{const o=l.in._zod.run(u,s);return o instanceof Promise?o.then(f=>np(f,l,s)):np(o,l,s)}});function np(n,l,u){return n.issues.length?n:l.out._zod.run({value:n.value,issues:n.issues},u)}const GS=q("$ZodReadonly",(n,l)=>{Be.init(n,l),we(n._zod,"propValues",()=>l.innerType._zod.propValues),we(n._zod,"values",()=>l.innerType._zod.values),we(n._zod,"optin",()=>l.innerType._zod.optin),we(n._zod,"optout",()=>l.innerType._zod.optout),n._zod.parse=(u,s)=>{const o=l.innerType._zod.run(u,s);return o instanceof Promise?o.then(ap):ap(o)}});function ap(n){return n.value=Object.freeze(n.value),n}const YS=q("$ZodCustom",(n,l)=>{St.init(n,l),Be.init(n,l),n._zod.parse=(u,s)=>u,n._zod.check=u=>{const s=u.value,o=l.fn(s);if(o instanceof Promise)return o.then(f=>lp(f,u,s,n));lp(o,u,s,n)}});function lp(n,l,u,s){if(!n){const o={code:"custom",input:u,inst:s,path:[...s._zod.def.path??[]],continue:!s._zod.def.abort};s._zod.def.params&&(o.params=s._zod.def.params),l.issues.push(pi(o))}}class VS{constructor(){this._map=new Map,this._idmap=new Map}add(l,...u){const s=u[0];if(this._map.set(l,s),s&&typeof s=="object"&&"id"in s){if(this._idmap.has(s.id))throw new Error(`ID ${s.id} already exists in the registry`);this._idmap.set(s.id,l)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(l){const u=this._map.get(l);return u&&typeof u=="object"&&"id"in u&&this._idmap.delete(u.id),this._map.delete(l),this}get(l){const u=l._zod.parent;if(u){const s={...this.get(u)??{}};delete s.id;const o={...s,...this._map.get(l)};return Object.keys(o).length?o:void 0}return this._map.get(l)}has(l){return this._map.has(l)}}function XS(){return new VS}const Gu=XS();function KS(n,l){return new n({type:"string",...P(l)})}function $S(n,l){return new n({type:"string",format:"email",check:"string_format",abort:!1,...P(l)})}function ip(n,l){return new n({type:"string",format:"guid",check:"string_format",abort:!1,...P(l)})}function FS(n,l){return new n({type:"string",format:"uuid",check:"string_format",abort:!1,...P(l)})}function JS(n,l){return new n({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...P(l)})}function PS(n,l){return new n({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...P(l)})}function WS(n,l){return new n({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...P(l)})}function IS(n,l){return new n({type:"string",format:"url",check:"string_format",abort:!1,...P(l)})}function e_(n,l){return new n({type:"string",format:"emoji",check:"string_format",abort:!1,...P(l)})}function t_(n,l){return new n({type:"string",format:"nanoid",check:"string_format",abort:!1,...P(l)})}function n_(n,l){return new n({type:"string",format:"cuid",check:"string_format",abort:!1,...P(l)})}function a_(n,l){return new n({type:"string",format:"cuid2",check:"string_format",abort:!1,...P(l)})}function l_(n,l){return new n({type:"string",format:"ulid",check:"string_format",abort:!1,...P(l)})}function i_(n,l){return new n({type:"string",format:"xid",check:"string_format",abort:!1,...P(l)})}function u_(n,l){return new n({type:"string",format:"ksuid",check:"string_format",abort:!1,...P(l)})}function s_(n,l){return new n({type:"string",format:"ipv4",check:"string_format",abort:!1,...P(l)})}function r_(n,l){return new n({type:"string",format:"ipv6",check:"string_format",abort:!1,...P(l)})}function c_(n,l){return new n({type:"string",format:"cidrv4",check:"string_format",abort:!1,...P(l)})}function o_(n,l){return new n({type:"string",format:"cidrv6",check:"string_format",abort:!1,...P(l)})}function f_(n,l){return new n({type:"string",format:"base64",check:"string_format",abort:!1,...P(l)})}function d_(n,l){return new n({type:"string",format:"base64url",check:"string_format",abort:!1,...P(l)})}function h_(n,l){return new n({type:"string",format:"e164",check:"string_format",abort:!1,...P(l)})}function m_(n,l){return new n({type:"string",format:"jwt",check:"string_format",abort:!1,...P(l)})}function p_(n,l){return new n({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...P(l)})}function v_(n,l){return new n({type:"string",format:"date",check:"string_format",...P(l)})}function y_(n,l){return new n({type:"string",format:"time",check:"string_format",precision:null,...P(l)})}function g_(n,l){return new n({type:"string",format:"duration",check:"string_format",...P(l)})}function b_(n,l){return new n({type:"number",checks:[],...P(l)})}function x_(n,l){return new n({type:"number",check:"number_format",abort:!1,format:"safeint",...P(l)})}function S_(n,l){return new n({type:"boolean",...P(l)})}function __(n){return new n({type:"unknown"})}function E_(n,l){return new n({type:"never",...P(l)})}function up(n,l){return new mv({check:"less_than",...P(l),value:n,inclusive:!1})}function Zc(n,l){return new mv({check:"less_than",...P(l),value:n,inclusive:!0})}function sp(n,l){return new pv({check:"greater_than",...P(l),value:n,inclusive:!1})}function qc(n,l){return new pv({check:"greater_than",...P(l),value:n,inclusive:!0})}function rp(n,l){return new kx({check:"multiple_of",...P(l),value:n})}function gv(n,l){return new Qx({check:"max_length",...P(l),maximum:n})}function Wu(n,l){return new Gx({check:"min_length",...P(l),minimum:n})}function bv(n,l){return new Yx({check:"length_equals",...P(l),length:n})}function z_(n,l){return new Vx({check:"string_format",format:"regex",...P(l),pattern:n})}function T_(n){return new Xx({check:"string_format",format:"lowercase",...P(n)})}function O_(n){return new Kx({check:"string_format",format:"uppercase",...P(n)})}function N_(n,l){return new $x({check:"string_format",format:"includes",...P(l),includes:n})}function j_(n,l){return new Fx({check:"string_format",format:"starts_with",...P(l),prefix:n})}function A_(n,l){return new Jx({check:"string_format",format:"ends_with",...P(l),suffix:n})}function _i(n){return new Px({check:"overwrite",tx:n})}function R_(n){return _i(l=>l.normalize(n))}function w_(){return _i(n=>n.trim())}function D_(){return _i(n=>n.toLowerCase())}function C_(){return _i(n=>n.toUpperCase())}function M_(n,l,u){return new n({type:"array",element:l,...P(u)})}function U_(n,l,u){return new n({type:"custom",check:"custom",fn:l,...P(u)})}const Z_=q("ZodISODateTime",(n,l)=>{fS.init(n,l),He.init(n,l)});function q_(n){return p_(Z_,n)}const B_=q("ZodISODate",(n,l)=>{dS.init(n,l),He.init(n,l)});function H_(n){return v_(B_,n)}const k_=q("ZodISOTime",(n,l)=>{hS.init(n,l),He.init(n,l)});function L_(n){return y_(k_,n)}const Q_=q("ZodISODuration",(n,l)=>{mS.init(n,l),He.init(n,l)});function G_(n){return g_(Q_,n)}const Y_=(n,l)=>{uv.init(n,l),n.name="ZodError",Object.defineProperties(n,{format:{value:u=>rx(n,u)},flatten:{value:u=>sx(n,u)},addIssue:{value:u=>{n.issues.push(u),n.message=JSON.stringify(n.issues,Jc,2)}},addIssues:{value:u=>{n.issues.push(...u),n.message=JSON.stringify(n.issues,Jc,2)}},isEmpty:{get(){return n.issues.length===0}}})},rs=q("ZodError",Y_,{Parent:Error}),V_=cx(rs),X_=ox(rs),K_=rv(rs),$_=cv(rs),Ve=q("ZodType",(n,l)=>(Be.init(n,l),n.def=l,Object.defineProperty(n,"_def",{value:l}),n.check=(...u)=>n.clone({...l,checks:[...l.checks??[],...u.map(s=>typeof s=="function"?{_zod:{check:s,def:{check:"custom"},onattach:[]}}:s)]}),n.clone=(u,s)=>va(n,u,s),n.brand=()=>n,n.register=(u,s)=>(u.add(n,s),n),n.parse=(u,s)=>V_(n,u,s,{callee:n.parse}),n.safeParse=(u,s)=>K_(n,u,s),n.parseAsync=async(u,s)=>X_(n,u,s,{callee:n.parseAsync}),n.safeParseAsync=async(u,s)=>$_(n,u,s),n.spa=n.safeParseAsync,n.refine=(u,s)=>n.check(H2(u,s)),n.superRefine=u=>n.check(k2(u)),n.overwrite=u=>n.check(_i(u)),n.optional=()=>fp(n),n.nullable=()=>dp(n),n.nullish=()=>fp(dp(n)),n.nonoptional=u=>w2(n,u),n.array=()=>Vn(n),n.or=u=>_v([n,u]),n.and=u=>_2(n,u),n.transform=u=>hp(n,T2(u)),n.default=u=>j2(n,u),n.prefault=u=>R2(n,u),n.catch=u=>C2(n,u),n.pipe=u=>hp(n,u),n.readonly=()=>Z2(n),n.describe=u=>{const s=n.clone();return Gu.add(s,{description:u}),s},Object.defineProperty(n,"description",{get(){return Gu.get(n)?.description},configurable:!0}),n.meta=(...u)=>{if(u.length===0)return Gu.get(n);const s=n.clone();return Gu.add(s,u[0]),s},n.isOptional=()=>n.safeParse(void 0).success,n.isNullable=()=>n.safeParse(null).success,n)),xv=q("_ZodString",(n,l)=>{mo.init(n,l),Ve.init(n,l);const u=n._zod.bag;n.format=u.format??null,n.minLength=u.minimum??null,n.maxLength=u.maximum??null,n.regex=(...s)=>n.check(z_(...s)),n.includes=(...s)=>n.check(N_(...s)),n.startsWith=(...s)=>n.check(j_(...s)),n.endsWith=(...s)=>n.check(A_(...s)),n.min=(...s)=>n.check(Wu(...s)),n.max=(...s)=>n.check(gv(...s)),n.length=(...s)=>n.check(bv(...s)),n.nonempty=(...s)=>n.check(Wu(1,...s)),n.lowercase=s=>n.check(T_(s)),n.uppercase=s=>n.check(O_(s)),n.trim=()=>n.check(w_()),n.normalize=(...s)=>n.check(R_(...s)),n.toLowerCase=()=>n.check(D_()),n.toUpperCase=()=>n.check(C_())}),F_=q("ZodString",(n,l)=>{mo.init(n,l),xv.init(n,l),n.email=u=>n.check($S(J_,u)),n.url=u=>n.check(IS(P_,u)),n.jwt=u=>n.check(m_(d2,u)),n.emoji=u=>n.check(e_(W_,u)),n.guid=u=>n.check(ip(cp,u)),n.uuid=u=>n.check(FS(Yu,u)),n.uuidv4=u=>n.check(JS(Yu,u)),n.uuidv6=u=>n.check(PS(Yu,u)),n.uuidv7=u=>n.check(WS(Yu,u)),n.nanoid=u=>n.check(t_(I_,u)),n.guid=u=>n.check(ip(cp,u)),n.cuid=u=>n.check(n_(e2,u)),n.cuid2=u=>n.check(a_(t2,u)),n.ulid=u=>n.check(l_(n2,u)),n.base64=u=>n.check(f_(c2,u)),n.base64url=u=>n.check(d_(o2,u)),n.xid=u=>n.check(i_(a2,u)),n.ksuid=u=>n.check(u_(l2,u)),n.ipv4=u=>n.check(s_(i2,u)),n.ipv6=u=>n.check(r_(u2,u)),n.cidrv4=u=>n.check(c_(s2,u)),n.cidrv6=u=>n.check(o_(r2,u)),n.e164=u=>n.check(h_(f2,u)),n.datetime=u=>n.check(q_(u)),n.date=u=>n.check(H_(u)),n.time=u=>n.check(L_(u)),n.duration=u=>n.check(G_(u))});function ve(n){return KS(F_,n)}const He=q("ZodStringFormat",(n,l)=>{Ze.init(n,l),xv.init(n,l)}),J_=q("ZodEmail",(n,l)=>{nS.init(n,l),He.init(n,l)}),cp=q("ZodGUID",(n,l)=>{eS.init(n,l),He.init(n,l)}),Yu=q("ZodUUID",(n,l)=>{tS.init(n,l),He.init(n,l)}),P_=q("ZodURL",(n,l)=>{aS.init(n,l),He.init(n,l)}),W_=q("ZodEmoji",(n,l)=>{lS.init(n,l),He.init(n,l)}),I_=q("ZodNanoID",(n,l)=>{iS.init(n,l),He.init(n,l)}),e2=q("ZodCUID",(n,l)=>{uS.init(n,l),He.init(n,l)}),t2=q("ZodCUID2",(n,l)=>{sS.init(n,l),He.init(n,l)}),n2=q("ZodULID",(n,l)=>{rS.init(n,l),He.init(n,l)}),a2=q("ZodXID",(n,l)=>{cS.init(n,l),He.init(n,l)}),l2=q("ZodKSUID",(n,l)=>{oS.init(n,l),He.init(n,l)}),i2=q("ZodIPv4",(n,l)=>{pS.init(n,l),He.init(n,l)}),u2=q("ZodIPv6",(n,l)=>{vS.init(n,l),He.init(n,l)}),s2=q("ZodCIDRv4",(n,l)=>{yS.init(n,l),He.init(n,l)}),r2=q("ZodCIDRv6",(n,l)=>{gS.init(n,l),He.init(n,l)}),c2=q("ZodBase64",(n,l)=>{bS.init(n,l),He.init(n,l)}),o2=q("ZodBase64URL",(n,l)=>{SS.init(n,l),He.init(n,l)}),f2=q("ZodE164",(n,l)=>{_S.init(n,l),He.init(n,l)}),d2=q("ZodJWT",(n,l)=>{zS.init(n,l),He.init(n,l)}),Sv=q("ZodNumber",(n,l)=>{yv.init(n,l),Ve.init(n,l),n.gt=(s,o)=>n.check(sp(s,o)),n.gte=(s,o)=>n.check(qc(s,o)),n.min=(s,o)=>n.check(qc(s,o)),n.lt=(s,o)=>n.check(up(s,o)),n.lte=(s,o)=>n.check(Zc(s,o)),n.max=(s,o)=>n.check(Zc(s,o)),n.int=s=>n.check(op(s)),n.safe=s=>n.check(op(s)),n.positive=s=>n.check(sp(0,s)),n.nonnegative=s=>n.check(qc(0,s)),n.negative=s=>n.check(up(0,s)),n.nonpositive=s=>n.check(Zc(0,s)),n.multipleOf=(s,o)=>n.check(rp(s,o)),n.step=(s,o)=>n.check(rp(s,o)),n.finite=()=>n;const u=n._zod.bag;n.minValue=Math.max(u.minimum??Number.NEGATIVE_INFINITY,u.exclusiveMinimum??Number.NEGATIVE_INFINITY)??null,n.maxValue=Math.min(u.maximum??Number.POSITIVE_INFINITY,u.exclusiveMaximum??Number.POSITIVE_INFINITY)??null,n.isInt=(u.format??"").includes("int")||Number.isSafeInteger(u.multipleOf??.5),n.isFinite=!0,n.format=u.format??null});function Ue(n){return b_(Sv,n)}const h2=q("ZodNumberFormat",(n,l)=>{TS.init(n,l),Sv.init(n,l)});function op(n){return x_(h2,n)}const m2=q("ZodBoolean",(n,l)=>{OS.init(n,l),Ve.init(n,l)});function Ic(n){return S_(m2,n)}const p2=q("ZodUnknown",(n,l)=>{NS.init(n,l),Ve.init(n,l)});function ci(){return __(p2)}const v2=q("ZodNever",(n,l)=>{jS.init(n,l),Ve.init(n,l)});function y2(n){return E_(v2,n)}const g2=q("ZodArray",(n,l)=>{AS.init(n,l),Ve.init(n,l),n.element=l.element,n.min=(u,s)=>n.check(Wu(u,s)),n.nonempty=u=>n.check(Wu(1,u)),n.max=(u,s)=>n.check(gv(u,s)),n.length=(u,s)=>n.check(bv(u,s)),n.unwrap=()=>n.element});function Vn(n,l){return M_(g2,n,l)}const b2=q("ZodObject",(n,l)=>{RS.init(n,l),Ve.init(n,l),we(n,"shape",()=>l.shape),n.keyof=()=>cs(Object.keys(n._zod.def.shape)),n.catchall=u=>n.clone({...n._zod.def,catchall:u}),n.passthrough=()=>n.clone({...n._zod.def,catchall:ci()}),n.loose=()=>n.clone({...n._zod.def,catchall:ci()}),n.strict=()=>n.clone({...n._zod.def,catchall:y2()}),n.strip=()=>n.clone({...n._zod.def,catchall:void 0}),n.extend=u=>ax(n,u),n.merge=u=>lx(n,u),n.pick=u=>tx(n,u),n.omit=u=>nx(n,u),n.partial=(...u)=>ix(Ev,n,u[0]),n.required=(...u)=>ux(zv,n,u[0])});function en(n,l){const u={type:"object",get shape(){return pa(this,"shape",{...n}),this.shape},...P(l)};return new b2(u)}const x2=q("ZodUnion",(n,l)=>{wS.init(n,l),Ve.init(n,l),n.options=l.options});function _v(n,l){return new x2({type:"union",options:n,...P(l)})}const S2=q("ZodIntersection",(n,l)=>{DS.init(n,l),Ve.init(n,l)});function _2(n,l){return new S2({type:"intersection",left:n,right:l})}const E2=q("ZodRecord",(n,l)=>{CS.init(n,l),Ve.init(n,l),n.keyType=l.keyType,n.valueType=l.valueType});function si(n,l,u){return new E2({type:"record",keyType:n,valueType:l,...P(u)})}const eo=q("ZodEnum",(n,l)=>{MS.init(n,l),Ve.init(n,l),n.enum=l.entries,n.options=Object.values(l.entries);const u=new Set(Object.keys(l.entries));n.extract=(s,o)=>{const f={};for(const h of s)if(u.has(h))f[h]=l.entries[h];else throw new Error(`Key ${h} not found in enum`);return new eo({...l,checks:[],...P(o),entries:f})},n.exclude=(s,o)=>{const f={...l.entries};for(const h of s)if(u.has(h))delete f[h];else throw new Error(`Key ${h} not found in enum`);return new eo({...l,checks:[],...P(o),entries:f})}});function cs(n,l){const u=Array.isArray(n)?Object.fromEntries(n.map(s=>[s,s])):n;return new eo({type:"enum",entries:u,...P(l)})}const z2=q("ZodTransform",(n,l)=>{US.init(n,l),Ve.init(n,l),n._zod.parse=(u,s)=>{u.addIssue=f=>{if(typeof f=="string")u.issues.push(pi(f,u.value,l));else{const h=f;h.fatal&&(h.continue=!1),h.code??(h.code="custom"),h.input??(h.input=u.value),h.inst??(h.inst=n),u.issues.push(pi(h))}};const o=l.transform(u.value,u);return o instanceof Promise?o.then(f=>(u.value=f,u)):(u.value=o,u)}});function T2(n){return new z2({type:"transform",transform:n})}const Ev=q("ZodOptional",(n,l)=>{ZS.init(n,l),Ve.init(n,l),n.unwrap=()=>n._zod.def.innerType});function fp(n){return new Ev({type:"optional",innerType:n})}const O2=q("ZodNullable",(n,l)=>{qS.init(n,l),Ve.init(n,l),n.unwrap=()=>n._zod.def.innerType});function dp(n){return new O2({type:"nullable",innerType:n})}const N2=q("ZodDefault",(n,l)=>{BS.init(n,l),Ve.init(n,l),n.unwrap=()=>n._zod.def.innerType,n.removeDefault=n.unwrap});function j2(n,l){return new N2({type:"default",innerType:n,get defaultValue(){return typeof l=="function"?l():l}})}const A2=q("ZodPrefault",(n,l)=>{HS.init(n,l),Ve.init(n,l),n.unwrap=()=>n._zod.def.innerType});function R2(n,l){return new A2({type:"prefault",innerType:n,get defaultValue(){return typeof l=="function"?l():l}})}const zv=q("ZodNonOptional",(n,l)=>{kS.init(n,l),Ve.init(n,l),n.unwrap=()=>n._zod.def.innerType});function w2(n,l){return new zv({type:"nonoptional",innerType:n,...P(l)})}const D2=q("ZodCatch",(n,l)=>{LS.init(n,l),Ve.init(n,l),n.unwrap=()=>n._zod.def.innerType,n.removeCatch=n.unwrap});function C2(n,l){return new D2({type:"catch",innerType:n,catchValue:typeof l=="function"?l:()=>l})}const M2=q("ZodPipe",(n,l)=>{QS.init(n,l),Ve.init(n,l),n.in=l.in,n.out=l.out});function hp(n,l){return new M2({type:"pipe",in:n,out:l})}const U2=q("ZodReadonly",(n,l)=>{GS.init(n,l),Ve.init(n,l),n.unwrap=()=>n._zod.def.innerType});function Z2(n){return new U2({type:"readonly",innerType:n})}const q2=q("ZodCustom",(n,l)=>{YS.init(n,l),Ve.init(n,l)});function B2(n){const l=new St({check:"custom"});return l._zod.check=n,l}function H2(n,l={}){return U_(q2,n,l)}function k2(n){const l=B2(u=>(u.addIssue=s=>{if(typeof s=="string")u.issues.push(pi(s,u.value,l._zod.def));else{const o=s;o.fatal&&(o.continue=!1),o.code??(o.code="custom"),o.input??(o.input=u.value),o.inst??(o.inst=l),o.continue??(o.continue=!l._zod.def.abort),u.issues.push(pi(o))}},n(u.value,u)));return l}const Tv=n=>en({data:n,message:ve().optional(),status:cs(["success","error"])}),L2=n=>en({data:Vn(n),total:Ue(),page:Ue(),limit:Ue(),hasNext:Ic(),hasPrev:Ic()}),Q2=en({id:Ue(),case_name:ve(),description:ve().nullable(),created_at:ve(),status:cs(["active","deleted","permanently_deleted"]),deleted_at:ve().nullable(),db_path:ve()}),G2=en({properties:si(ve(),ci()),tags:en({metadata:si(ve(),ve()),cv:si(ve(),ci()),user:Vn(ve()),ai:Vn(ve()),custom:si(ve(),ve())}).and(si(ve(),ci()))}),Y2=en({id:Ue(),case_id:Ue(),file_name:ve(),file_path:ve(),file_type:ve(),file_size:Ue(),width:Ue().nullable(),height:Ue().nullable(),created_at:ve(),taken_at:ve().nullable(),thumbnail_small_path:ve().nullable(),tags:G2.nullable(),quality_score:Ue().nullable(),sharpness:Ue().nullable(),brightness:Ue().nullable(),dynamic_range:Ue().nullable(),num_faces:Ue().nullable(),face_sharpness:Ue().nullable(),face_quality:Ue().nullable(),cluster_id:ve().nullable(),phash:ve().nullable()});en({displayName:ve(),icon:ve(),handler:ve(),titleTemplate:ve(),priority:Ue(),editable:Ic()});const V2=en({key:ve(),value:_v([ve(),Ue()]),count:Ue(),fileIds:Vn(Ue()),category:ve()}),Ov=en({case_id:Ue().optional(),file_type:ve().optional(),tags:Vn(ve()).optional(),search:ve().optional(),page:Ue().default(1),limit:Ue().default(50)});Ov.partial().transform(n=>({page:1,limit:50,...n}));en({file_id:Ue(),category:ve(),tag:ve(),operation:cs(["add","remove"])});const Ei=Ye.create({baseURL:"/api",timeout:1e4,headers:{"Content-Type":"application/json"}});Ei.interceptors.request.use(n=>n,n=>Promise.reject(n));Ei.interceptors.response.use(n=>n,n=>(console.error("API Error:",n),Promise.reject(n)));function po(n,l){try{return l.parse(n)}catch(u){throw console.error("API Response Validation Error:",u),new Error("Invalid API response format")}}async function X2(){const n=await Ei.get("/cases"),l=Tv(Vn(Q2));return po(n.data,l).data}async function K2(n={}){const l=Ov.parse({page:1,limit:50,...n}),u=await Ei.get("/files",{params:l}),s=L2(Y2),o=po(u.data,s);return{files:o.data,total:o.total,hasNext:o.hasNext,hasPrev:o.hasPrev}}async function $2(n){const l=await Ei.get("/tags",{params:n?{case_id:n}:{}}),u=Tv(Vn(V2));return po(l.data,u).data}const vo={cases:["cases"],case:n=>["cases",n],files:["files"],filesList:n=>["files","list",n],file:n=>["files",n],tags:["tags"],tagsList:n=>["tags","list",n],searchFiles:(n,l)=>["search","files",n,l],searchTags:(n,l)=>["search","tags",n,l]};function F2(){return uo({queryKey:vo.cases,queryFn:X2,staleTime:300*1e3})}function J2(n={}){return uo({queryKey:vo.filesList(n),queryFn:()=>K2(n),staleTime:120*1e3})}function P2(n){return uo({queryKey:vo.tagsList(n),queryFn:()=>$2(n),staleTime:300*1e3})}function W2(){const n=pb(),l=vb(),u=yb(),s=gb(),{toggleCatalogPanel:o,toggleInfoPanel:f,toggleWorkbench:h,toggleFullscreenGallery:y,setSelectedCase:g,toggleFileSelection:v,setSearchQuery:b,setActiveWorkbench:z,catalogPanelWidth:w,infoPanelWidth:k,workbenchHeight:M,activeWorkbench:D}=yi(),{data:Z,isLoading:L,error:G}=F2(),Q=l.selectedCaseId||Z?.[0]?.id||null,{data:ee,isLoading:K,error:pe}=J2({case_id:Q||void 0,search:s.searchQuery||void 0}),{isLoading:ue,error:J}=P2(Q||void 0);re.useEffect(()=>{Z&&Z.length>0&&!l.selectedCaseId&&g(Z[0].id)},[Z,l.selectedCaseId,g]);const Oe=L||K||ue,tt=G||pe||J,$e=ee?.files||[],le=$e.find(A=>l.selectedFileIds.includes(A.id)),nt=(A,Y)=>{v(A)},Qt=A=>{console.log("预览文件:",A.fileName)},De=A=>{switch(A){case"catalog":o();break;case"workbench":h(),n.showWorkbench||z("clipboard");break;case"info":f();break;case"fullscreen":y();break}};return tt?m.jsx("div",{className:"h-screen w-screen flex items-center justify-center",children:m.jsxs("div",{className:"text-center space-y-4",children:[m.jsx("div",{className:"text-6xl",children:"⚠️"}),m.jsx("h1",{className:"text-2xl font-bold",children:"数据加载失败"}),m.jsx("p",{className:"text-muted-foreground",children:G?.message||pe?.message||J?.message||"未知错误"}),m.jsx(de,{onClick:()=>window.location.reload(),children:"🔄 重新加载"})]})}):m.jsxs("div",{className:"h-screen w-screen",children:[!n.isFullscreenGallery&&m.jsx("div",{className:"absolute top-4 left-1/2 transform -translate-x-1/2 z-50 bg-card border rounded-lg p-2 shadow-lg",children:m.jsxs("div",{className:"flex gap-2",children:[m.jsx(de,{variant:n.showCatalogPanel?"primary":"outline",size:"sm",onClick:()=>De("catalog"),children:"📚 目录栏"}),m.jsx(de,{variant:n.showWorkbench?"primary":"outline",size:"sm",onClick:()=>De("workbench"),children:"🛠️ 工作台"}),m.jsx(de,{variant:n.showInfoPanel?"primary":"outline",size:"sm",onClick:()=>De("info"),children:"📄 信息栏"}),m.jsx(de,{variant:"ghost",size:"sm",onClick:()=>De("fullscreen"),children:"🔍 全屏"}),Oe&&m.jsxs("div",{className:"flex items-center gap-2 px-2",children:[m.jsx("div",{className:"w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"}),m.jsx("span",{className:"text-sm text-muted-foreground",children:"加载中..."})]})]})}),m.jsx(Op,{catalogPanel:m.jsx(Np,{currentLibraryName:Z?.find(A=>A.id===Q)?.case_name||"加载中...",searchQuery:s.searchQuery,onSearchChange:b,tagCategories:[]}),galleryPanel:m.jsx(jp,{files:$e.map(A=>({id:A.id,fileName:A.file_name,filePath:A.file_path,fileType:A.file_type,fileSize:A.file_size,width:A.width||void 0,height:A.height||void 0,thumbnailPath:A.thumbnail_small_path||void 0})),selectedFileIds:l.selectedFileIds,layout:u.layout,zoomLevel:u.zoomLevel,searchQuery:s.searchQuery,showFileName:u.showFileName,showFileInfo:u.showFileInfo,loading:K,onFileSelect:nt,onFileDoubleClick:Qt,onSearchChange:b}),workbenchPanel:m.jsx(Ap,{activeWorkbench:D,onWorkbenchChange:z}),infoPanel:m.jsx(Rp,{selectedFile:le?{id:le.id,fileName:le.file_name,filePath:le.file_path,fileType:le.file_type,fileSize:le.file_size,width:le.width||void 0,height:le.height||void 0,thumbnailPath:le.thumbnail_small_path||void 0,createdAt:le.created_at,tags:le.tags?.tags||void 0}:void 0,selectedCount:l.selectedFileIds.length}),showCatalogPanel:n.showCatalogPanel,showWorkbench:n.showWorkbench,showInfoPanel:n.showInfoPanel,isFullscreenGallery:n.isFullscreenGallery,catalogPanelWidth:w,infoPanelWidth:k,workbenchHeight:M}),n.isFullscreenGallery&&m.jsx(de,{variant:"primary",size:"sm",className:"absolute top-4 right-4 z-50",onClick:()=>y(),children:"✕ 退出全屏"})]})}const I2=new E0({defaultOptions:{queries:{staleTime:300*1e3,gcTime:600*1e3,retry:3,retryDelay:n=>Math.min(1e3*2**n,3e4)},mutations:{retry:1}}});i0.createRoot(document.getElementById("root")).render(m.jsx(re.StrictMode,{children:m.jsxs(j0,{client:I2,children:[m.jsx(W2,{}),m.jsx(k0,{initialIsOpen:!1})]})}));
