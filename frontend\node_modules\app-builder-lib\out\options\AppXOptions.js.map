{"version": 3, "file": "AppXOptions.js", "sourceRoot": "", "sources": ["../../src/options/AppXOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["import { TargetSpecificOptions } from \"../core\"\n\nexport interface AppXOptions extends TargetSpecificOptions {\n  /**\n   * The application id. Defaults to `identityName`. Can’t start with numbers.\n   */\n  readonly applicationId?: string\n\n  /**\n   * The background color of the app tile. See [Visual Elements](https://msdn.microsoft.com/en-us/library/windows/apps/br211471.aspx).\n   * @default #464646\n   */\n  readonly backgroundColor?: string | null\n\n  /**\n   * A friendly name that can be displayed to users. Corresponds to [Properties.DisplayName](https://msdn.microsoft.com/en-us/library/windows/apps/br211432.aspx).\n   * Defaults to the application product name.\n   */\n  readonly displayName?: string | null\n\n  /**\n   * The name. Corresponds to [Identity.Name](https://msdn.microsoft.com/en-us/library/windows/apps/br211441.aspx). Defaults to the [application name](/configuration/configuration#Metadata-name).\n   */\n  readonly identityName?: string | null\n\n  /**\n   * The Windows Store publisher. Not used if AppX is build for testing. See [AppX Package Code Signing](#appx-package-code-signing) below.\n   */\n  readonly publisher?: string | null\n\n  /**\n   * A friendly name for the publisher that can be displayed to users. Corresponds to [Properties.PublisherDisplayName](https://msdn.microsoft.com/en-us/library/windows/apps/br211460.aspx).\n   * Defaults to company name from the application metadata.\n   */\n  readonly publisherDisplayName?: string | null\n\n  /**\n   * The list of [supported languages](https://docs.microsoft.com/en-us/windows/uwp/globalizing/manage-language-and-region#specify-the-supported-languages-in-the-apps-manifest) that will be listed in the Windows Store.\n   * The first entry (index 0) will be the default language.\n   * Defaults to en-US if omitted.\n   */\n  readonly languages?: Array<string> | string | null\n\n  /**\n   * Whether to add auto launch extension. Defaults to `true` if [electron-winstore-auto-launch](https://github.com/felixrieseberg/electron-winstore-auto-launch) in the dependencies.\n   */\n  readonly addAutoLaunchExtension?: boolean\n\n  /**\n   * Relative path to custom extensions xml to be included in an `appmanifest.xml`.\n   */\n  readonly customExtensionsPath?: string\n\n  /**\n   * Whether to overlay the app's name on top of tile images on the Start screen. Defaults to `false`. (https://docs.microsoft.com/en-us/uwp/schemas/appxpackage/uapmanifestschema/element-uap-shownameontiles) in the dependencies.\n   * @default false\n   */\n  readonly showNameOnTiles?: boolean\n\n  /**\n   * @private\n   * @default false\n   */\n  readonly electronUpdaterAware?: boolean\n\n  /**\n   * Whether to set build number. See https://github.com/electron-userland/electron-builder/issues/3875\n   * @default false\n   */\n  readonly setBuildNumber?: boolean\n\n  /** @private */\n  readonly makeappxArgs?: Array<string> | null\n}\n"]}