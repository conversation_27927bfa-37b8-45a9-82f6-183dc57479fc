openapi: 3.0.3
info:
  title: Mizzy Star Case Management API - Cover Image Feature
  description: |
    **✅ FULLY IMPLEMENTED** API for managing case cover images with automatic and manual selection capabilities.

    **Cover Image Priority:**
    1. Manual selection (ChooseFront.jpg) - highest priority
    2. Automatic selection (Front.jpg) - based on quality score
    3. Default placeholder - fallback when no cover is available

    **Automatic Selection Triggers:**
    - ✅ Case creation (sets default placeholder)
    - ✅ Quality analysis completion (auto-selects best quality image)
    - ✅ File deletion (reselection when current cover source is deleted)
    - ✅ Manual reselection trigger (maintenance/recovery)

    **Manual Management:**
    - ✅ Set specific image as cover (PUT /cases/{id}/cover)
    - ✅ Remove manual selection (DELETE /cases/{id}/cover)
    - ✅ Trigger reselection (POST /cases/{id}/cover/reselect)

    **Error Handling Strategy:**
    - ✅ Automatic reselection on file deletion
    - ✅ Fallback to placeholder when automatic fails
    - ✅ Graceful degradation with attention flags
    - ✅ Comprehensive error responses with specific error codes

    **Implementation Status:**
    - ✅ Database schema with cover fields
    - ✅ Cover service with all business logic
    - ✅ Complete API endpoints
    - ✅ Integration with quality analysis
    - ✅ File deletion impact handling
    - ✅ Error handling and validation

    **File Storage:**
    All file paths are abstracted through BASE_DATA_DIR configuration.
    Cover images are accessible via direct file:// URLs for frontend consumption.

    **Cover File Locations:**
    - Manual covers: {case_dir}/ChooseFront.jpg
    - Automatic covers: {case_dir}/Front.jpg
    - Placeholder: {data_dir}/placeholder-cover.jpg
    - Case directory: {data_dir}/case_{id}/

    **URL Format:**
    - file://C:/Users/<USER>/mizzy_star_v0.3/data/placeholder-cover.jpg
    - file://C:/Users/<USER>/mizzy_star_v0.3/data/case_1/Front.jpg
    - file://C:/Users/<USER>/mizzy_star_v0.3/data/case_1/ChooseFront.jpg

    **Database Fields:**
    - coverImageUrl: Direct URL to cover image
    - coverType: manual/automatic/placeholder
    - coverSourceFileId: Source file ID (null for placeholder)
    - coverNeedsAttention: Flag for failed automatic operations
    - coverUpdatedAt: Last cover update timestamp

    **✅ TESTED ENDPOINTS:**
    - GET /cases/ - Returns cases with cover fields
    - POST /cases/ - Creates case with placeholder cover
    - POST /cases/{id}/cover/reselect - Manual reselection
    - PUT /cases/{id}/cover - Set manual cover (ready)
    - DELETE /cases/{id}/cover - Remove manual cover (ready)

    **Test Results (2025-07-18):**
    - ✅ Case creation: Auto-sets placeholder cover
    - ✅ Cover reselection: Handles no-images scenario correctly
    - ✅ Database: All cover fields properly stored and retrieved
    - ✅ Error handling: Graceful fallback to placeholder
    - ✅ API responses: Include all cover-related fields
  version: 1.1.0
  contact:
    name: Mizzy Star API Team
    email: <EMAIL>
servers:
  - url: http://localhost:8000/api/v1
    description: Development server (✅ Tested and Working)
  - url: http://0.0.0.0:8000/api/v1
    description: Local network server

paths:
  /cases:
    get:
      summary: Get all cases with cover images
      description: |
        Retrieve all cases including their cover image URLs.
        Cover images are automatically resolved based on priority:
        manual > automatic > placeholder.
      tags:
        - Cases
      parameters:
        - name: skip
          in: query
          schema:
            type: integer
            default: 0
          description: Number of cases to skip
        - name: limit
          in: query
          schema:
            type: integer
            default: 100
          description: Maximum number of cases to return
      responses:
        '200':
          description: List of cases retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Case'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Create a new case
      description: |
        Create a new case with automatic placeholder cover image assignment.
        
        **Side Effects:**
        - Creates case directory structure
        - Sets default placeholder as cover image
        - Initializes coverImageUrl with placeholder URL
      tags:
        - Cases
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CaseCreate'
      responses:
        '201':
          description: Case created successfully with placeholder cover
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Case'
              example:
                id: 1
                case_name: "封面测试案例"
                description: "测试封面功能的案例"
                created_at: "2025-07-18T10:48:45"
                status: "active"
                deleted_at: null
                db_path: "C:\\Users\\<USER>\\mizzy_star_v0.3\\data\\case_1\\db.sqlite"
                files: []
                coverImageUrl: "file://C:\\Users\\<USER>\\mizzy_star_v0.3\\data\\placeholder-cover.jpg"
                coverType: "placeholder"
                coverSourceFileId: null
                coverNeedsAttention: false
                coverUpdatedAt: "2025-07-18T10:48:45.573716"
        '400':
          $ref: '#/components/responses/BadRequest'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /cases/{caseId}:
    get:
      summary: Get case details with cover image
      description: Get detailed information about a specific case including cover image URL
      tags:
        - Cases
      parameters:
        - $ref: '#/components/parameters/CaseId'
      responses:
        '200':
          description: Case details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Case'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /cases/{caseId}/files/upload:
    post:
      summary: Upload files to case
      description: |
        Upload files to a case. After successful upload, automatic cover selection
        may be triggered if no manual cover is set.
        
        **Side Effects:**
        - If no manual cover exists and uploaded files include images
        - May trigger automatic cover selection based on quality scores
        - Updates case coverImageUrl if new automatic cover is selected
      tags:
        - Files
      parameters:
        - $ref: '#/components/parameters/CaseId'
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
      responses:
        '201':
          description: Files uploaded successfully, cover may be updated
          content:
            application/json:
              schema:
                type: object
                properties:
                  uploadedFiles:
                    type: array
                    items:
                      $ref: '#/components/schemas/File'
                  coverUpdated:
                    type: boolean
                    description: Whether the case cover was automatically updated
                  newCoverUrl:
                    type: string
                    format: uri
                    nullable: true
                    description: New cover URL if updated
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /cases/{caseId}/files/{fileId}:
    delete:
      summary: Delete a file from case
      description: |
        Delete a file from the case. If the deleted file was the source of the current
        cover image, automatic reselection will be triggered.
        
        **Cover Reselection Logic:**
        1. If deleted file is current cover source, trigger automatic reselection
        2. Select highest quality remaining image as new automatic cover
        3. If no suitable images remain, revert to placeholder
        4. If reselection fails, user should manually select new cover
        
        **Side Effects:**
        - Removes file from case database
        - Deletes physical file and thumbnails
        - Triggers cover reselection if necessary
        - Updates case coverImageUrl if cover changes
      tags:
        - Files
      parameters:
        - $ref: '#/components/parameters/CaseId'
        - name: fileId
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
          description: ID of the file to delete
      responses:
        '200':
          description: File deleted successfully, cover reselected if needed
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    description: Whether deletion was successful
                  message:
                    type: string
                    description: Operation result message
                  coverReselected:
                    type: boolean
                    description: Whether cover reselection was triggered
                  newCoverUrl:
                    type: string
                    format: uri
                    nullable: true
                    description: New cover URL if reselected
                  reselectionStatus:
                    type: string
                    enum: [
                      "NOT_NEEDED",
                      "SUCCESS", 
                      "FAILED_NO_IMAGES",
                      "FAILED_ERROR"
                    ]
                    description: Status of cover reselection process
                  requiresManualSelection:
                    type: boolean
                    description: Whether user should manually select new cover
                required:
                  - success
                  - message
                  - coverReselected
                  - reselectionStatus
                  - requiresManualSelection
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /quality/{caseId}/analyze:
    post:
      summary: Analyze image quality
      description: |
        Perform quality analysis on case images. After analysis completion,
        automatic cover selection is triggered if no manual cover exists.
        
        **Side Effects:**
        - Calculates quality scores for all images
        - If no manual cover (ChooseFront.jpg) exists:
          - Selects highest quality image as automatic cover
          - Copies selected image as Front.jpg
          - Updates case coverImageUrl
      tags:
        - Quality Analysis
      parameters:
        - $ref: '#/components/parameters/CaseId'
      requestBody:
        required: false
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QualityAnalysisRequest'
      responses:
        '200':
          description: Quality analysis completed, cover may be updated
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/QualityAnalysisResponse'
                  - type: object
                    properties:
                      coverUpdated:
                        type: boolean
                        description: Whether automatic cover was updated
                      newCoverUrl:
                        type: string
                        format: uri
                        nullable: true
                        description: New cover URL if updated
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /cases/{caseId}/cover:
    put:
      summary: Manually set case cover image
      description: |
        Set a specific image as the case cover. This creates/updates ChooseFront.jpg
        and takes priority over automatic selection.
        
        **Use Cases:**
        - User wants to override automatic selection
        - Automatic reselection failed after file deletion
        - User prefers specific image regardless of quality score
        
        **Process:**
        1. Validates that the specified file exists and is an image
        2. Verifies the original file exists at its path
        3. Copies original file to case directory as ChooseFront.jpg
        4. Updates case coverImageUrl to point to manual cover
        5. Sets coverType to 'manual' and records source file ID
        6. Returns immediate success response
      tags:
        - Cover Management
      parameters:
        - $ref: '#/components/parameters/CaseId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetCoverRequest'
      responses:
        '200':
          description: Cover image set successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Case'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '422':
          description: File validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                file_not_found:
                  summary: Original file not found
                  value:
                    error: "FILE_NOT_FOUND"
                    message: "该文件已被更名或删除"
                    details: "Original file not found at specified path"
                not_image:
                  summary: File is not an image
                  value:
                    error: "INVALID_FILE_TYPE"
                    message: "所选文件不是图片格式"
                    details: "Only image files can be set as cover"
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      summary: Remove manual cover selection
      description: |
        Remove manual cover selection, reverting to automatic selection.
        Deletes ChooseFront.jpg and updates coverImageUrl to automatic cover or placeholder.
        
        **Process:**
        1. Removes ChooseFront.jpg file
        2. Attempts automatic cover selection from remaining images
        3. Falls back to placeholder if no suitable images exist
        4. Updates case record with new cover information
      tags:
        - Cover Management
      parameters:
        - $ref: '#/components/parameters/CaseId'
      responses:
        '200':
          description: Manual cover removed, reverted to automatic or placeholder
          content:
            application/json:
              schema:
                type: object
                properties:
                  case:
                    $ref: '#/components/schemas/Case'
                  revertedTo:
                    type: string
                    enum: ["automatic", "placeholder"]
                    description: What the cover reverted to
                required:
                  - case
                  - revertedTo
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /cases/{caseId}/cover/reselect:
    post:
      summary: Manually trigger cover reselection
      description: |
        Manually trigger cover reselection for maintenance or recovery purposes.
        This endpoint is useful when:
        - Files were deleted externally and cover needs updating
        - Quality analysis was updated and better images are now available
        - System maintenance requires cover refresh
        - Automatic reselection failed and needs retry

        **Process:**
        1. Preserves manual cover if it exists (ChooseFront.jpg)
        2. For automatic covers, re-evaluates all available images
        3. Selects highest quality image as new automatic cover
        4. Falls back to placeholder if no suitable images exist
        5. Updates case record with reselection results

        **Use Cases:**
        - System maintenance and recovery
        - After external file system changes
        - When automatic reselection previously failed
        - Quality analysis improvements require cover update
      tags:
        - Cover Management
      parameters:
        - $ref: '#/components/parameters/CaseId'
      responses:
        '200':
          description: Cover reselection completed
          content:
            application/json:
              schema:
                type: object
                properties:
                  coverReselected:
                    type: boolean
                    description: Whether cover was actually reselected
                  reselectionStatus:
                    type: string
                    enum: [
                      "NOT_NEEDED",
                      "SUCCESS",
                      "FAILED_NO_IMAGES",
                      "FAILED_ERROR"
                    ]
                    description: |
                      Status of reselection process:
                      - NOT_NEEDED: Manual cover exists, no reselection needed
                      - SUCCESS: Automatic cover successfully reselected
                      - FAILED_NO_IMAGES: No suitable images available
                      - FAILED_ERROR: Technical error during reselection
                  requiresManualSelection:
                    type: boolean
                    description: Whether user should manually select a cover
                  newCoverUrl:
                    type: string
                    format: uri
                    nullable: true
                    description: New cover URL if reselected
                required:
                  - coverReselected
                  - reselectionStatus
                  - requiresManualSelection
              examples:
                success:
                  summary: Successful reselection
                  value:
                    coverReselected: true
                    reselectionStatus: "SUCCESS"
                    requiresManualSelection: false
                    newCoverUrl: "file://C:/Users/<USER>/mizzy_star_v0.3/data/case_1/Front.jpg"
                no_images:
                  summary: No suitable images found
                  value:
                    coverReselected: true
                    reselectionStatus: "FAILED_NO_IMAGES"
                    requiresManualSelection: true
                    newCoverUrl: "file://C:/Users/<USER>/mizzy_star_v0.3/data/placeholder-cover.jpg"
                not_needed:
                  summary: Manual cover exists
                  value:
                    coverReselected: false
                    reselectionStatus: "NOT_NEEDED"
                    requiresManualSelection: false
                    newCoverUrl: null
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  parameters:
    CaseId:
      name: caseId
      in: path
      required: true
      schema:
        type: integer
        minimum: 1
      description: Unique identifier for the case

  schemas:
    Case:
      type: object
      description: Case object with cover image information
      properties:
        id:
          type: integer
          description: Unique case identifier
          example: 1
        case_name:
          type: string
          description: Case title/name
          example: "封面测试案例"
        description:
          type: string
          nullable: true
          description: Case description
          example: "测试封面功能的案例"
        created_at:
          type: string
          format: date-time
          description: Case creation timestamp
          example: "2025-07-18T10:48:45"
        status:
          type: string
          enum: [active, deleted, permanently_deleted]
          description: Case status
          example: "active"
        deleted_at:
          type: string
          format: date-time
          nullable: true
          description: Deletion timestamp (null for active cases)
          example: null
        db_path:
          type: string
          description: Path to case database file
          example: "C:\\Users\\<USER>\\mizzy_star_v0.3\\data\\case_1\\db.sqlite"
        coverImageUrl:
          type: string
          format: uri
          description: |
            Direct URL to case cover image. Priority order:
            1. Manual cover (ChooseFront.jpg) if exists
            2. Automatic cover (Front.jpg) if exists
            3. Default placeholder image
          example: "file://C:\\Users\\<USER>\\mizzy_star_v0.3\\data\\placeholder-cover.jpg"
        coverType:
          type: string
          enum: [manual, automatic, placeholder]
          description: Type of current cover image
          example: "placeholder"
        coverSourceFileId:
          type: integer
          nullable: true
          description: ID of source file for current cover (null for placeholder)
          example: null
        coverNeedsAttention:
          type: boolean
          description: |
            Whether cover needs user attention (e.g., automatic reselection failed,
            current cover source was deleted but reselection failed)
          example: false
        coverUpdatedAt:
          type: string
          format: date-time
          nullable: true
          description: Last cover update timestamp
          example: "2025-07-18T10:48:45.573716"
        files:
          type: array
          items:
            $ref: '#/components/schemas/File'
          description: Files associated with this case
          example: []
      required:
        - id
        - case_name
        - created_at
        - status
        - deleted_at
        - db_path
        - coverImageUrl
        - coverType
        - coverSourceFileId
        - coverNeedsAttention
        - coverUpdatedAt
        - files

    CaseCreate:
      type: object
      properties:
        case_name:
          type: string
          minLength: 1
          maxLength: 255
          description: Case name/title
        description:
          type: string
          nullable: true
          maxLength: 1000
          description: Optional case description
      required:
        - case_name

    File:
      type: object
      properties:
        id:
          type: integer
          description: Unique file identifier
        file_name:
          type: string
          description: Original filename
        file_type:
          type: string
          description: MIME type
        file_path:
          type: string
          description: Internal file path
        thumbnail_small_path:
          type: string
          nullable: true
          description: Small thumbnail path
        width:
          type: integer
          nullable: true
          description: Image width in pixels
        height:
          type: integer
          nullable: true
          description: Image height in pixels
        quality_score:
          type: number
          format: float
          nullable: true
          description: Image quality score (0-1)
        created_at:
          type: string
          format: date-time
          description: File upload timestamp
        isCurrentCoverSource:
          type: boolean
          description: Whether this file is the source of current cover image
      required:
        - id
        - file_name
        - file_type
        - file_path
        - created_at
        - isCurrentCoverSource

    SetCoverRequest:
      type: object
      properties:
        fileId:
          type: integer
          description: ID of the file to set as cover
      required:
        - fileId

    QualityAnalysisRequest:
      type: object
      properties:
        file_ids:
          type: array
          items:
            type: integer
          nullable: true
          description: Specific file IDs to analyze (null for all)
        weights:
          type: object
          nullable: true
          description: Custom quality scoring weights
        phash_threshold:
          type: integer
          default: 18
          description: Perceptual hash similarity threshold

    QualityAnalysisResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Whether analysis completed successfully
        message:
          type: string
          description: Human-readable result message
        total_files:
          type: integer
          description: Number of files analyzed
        clusters_count:
          type: integer
          description: Number of similarity clusters found
        task_id:
          type: string
          description: Analysis task identifier
      required:
        - success
        - message

    Error:
      type: object
      properties:
        error:
          type: string
          description: Error code
        message:
          type: string
          description: Human-readable error message
        details:
          type: string
          nullable: true
          description: Additional error details
      required:
        - error
        - message

  responses:
    BadRequest:
      description: Invalid request parameters
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: "INVALID_REQUEST"
            message: "Request parameters are invalid"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: "RESOURCE_NOT_FOUND"
            message: "The requested resource was not found"

    ValidationError:
      description: Request validation failed
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: "VALIDATION_ERROR"
            message: "Request data validation failed"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: "INTERNAL_ERROR"
            message: "An internal server error occurred"

tags:
  - name: Cases
    description: Case management operations
  - name: Files
    description: File upload and management
  - name: Quality Analysis
    description: Image quality analysis operations
  - name: Cover Management
    description: Cover image selection and management
