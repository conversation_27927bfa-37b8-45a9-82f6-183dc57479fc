#!/usr/bin/env python3
"""
生产环境配置
优化异步API在生产环境中的性能和稳定性
"""

import os
from typing import Dict, Any
from dataclasses import dataclass
from enum import Enum


class DeploymentEnvironment(Enum):
    """部署环境类型"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


@dataclass
class DatabaseConfig:
    """数据库配置"""
    # 连接池配置
    pool_size: int = 20
    max_overflow: int = 30
    pool_timeout: int = 30
    pool_recycle: int = 3600
    pool_pre_ping: bool = True
    
    # 异步配置
    async_pool_size: int = 25
    async_max_overflow: int = 35
    async_pool_timeout: int = 30
    async_pool_recycle: int = 3600
    
    # 连接配置
    connect_timeout: int = 10
    command_timeout: int = 60
    server_settings: Dict[str, str] = None
    
    def __post_init__(self):
        if self.server_settings is None:
            self.server_settings = {
                'application_name': 'mizzy_star_async_api',
                'timezone': 'UTC',
                'statement_timeout': '60s',
                'idle_in_transaction_session_timeout': '300s'
            }


@dataclass
class ServerConfig:
    """服务器配置"""
    # 基本配置
    host: str = "0.0.0.0"
    port: int = 8000
    workers: int = 4
    worker_class: str = "uvicorn.workers.UvicornWorker"
    
    # 性能配置
    worker_connections: int = 1000
    max_requests: int = 10000
    max_requests_jitter: int = 1000
    timeout: int = 30
    keepalive: int = 5
    
    # 日志配置
    log_level: str = "info"
    access_log: bool = True
    error_log: str = "/var/log/mizzy_star/error.log"
    access_log_file: str = "/var/log/mizzy_star/access.log"
    
    # SSL配置 (生产环境)
    ssl_keyfile: str = None
    ssl_certfile: str = None
    ssl_ca_certs: str = None


@dataclass
class CacheConfig:
    """缓存配置"""
    # Redis配置
    redis_url: str = "redis://localhost:6379/0"
    redis_pool_size: int = 20
    redis_timeout: int = 5
    
    # 缓存策略
    default_ttl: int = 3600  # 1小时
    case_list_ttl: int = 300  # 5分钟
    case_detail_ttl: int = 1800  # 30分钟
    system_status_ttl: int = 60  # 1分钟
    
    # 缓存键前缀
    key_prefix: str = "mizzy_star"


@dataclass
class MonitoringConfig:
    """监控配置"""
    # Prometheus配置
    enable_metrics: bool = True
    metrics_port: int = 9090
    metrics_path: str = "/metrics"
    
    # 健康检查
    health_check_interval: int = 30
    health_check_timeout: int = 10
    
    # 性能监控
    enable_performance_tracking: bool = True
    slow_query_threshold: float = 1.0  # 秒
    
    # 告警配置
    alert_webhook_url: str = None
    alert_email: str = None


@dataclass
class SecurityConfig:
    """安全配置"""
    # CORS配置
    cors_origins: list = None
    cors_methods: list = None
    cors_headers: list = None
    
    # 限流配置
    rate_limit_enabled: bool = True
    rate_limit_requests: int = 1000
    rate_limit_window: int = 3600  # 1小时
    
    # API密钥
    api_key_required: bool = False
    api_keys: list = None
    
    def __post_init__(self):
        if self.cors_origins is None:
            self.cors_origins = ["*"]  # 生产环境应该限制
        if self.cors_methods is None:
            self.cors_methods = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
        if self.cors_headers is None:
            self.cors_headers = ["*"]


class ProductionConfig:
    """生产环境配置管理器"""
    
    def __init__(self, environment: DeploymentEnvironment = DeploymentEnvironment.PRODUCTION):
        self.environment = environment
        self.database = self._get_database_config()
        self.server = self._get_server_config()
        self.cache = self._get_cache_config()
        self.monitoring = self._get_monitoring_config()
        self.security = self._get_security_config()
    
    def _get_database_config(self) -> DatabaseConfig:
        """获取数据库配置"""
        if self.environment == DeploymentEnvironment.PRODUCTION:
            return DatabaseConfig(
                pool_size=30,
                max_overflow=50,
                async_pool_size=35,
                async_max_overflow=55,
                pool_timeout=60,
                connect_timeout=15,
                command_timeout=120
            )
        elif self.environment == DeploymentEnvironment.STAGING:
            return DatabaseConfig(
                pool_size=20,
                max_overflow=30,
                async_pool_size=25,
                async_max_overflow=35
            )
        else:  # DEVELOPMENT
            return DatabaseConfig(
                pool_size=10,
                max_overflow=15,
                async_pool_size=15,
                async_max_overflow=20
            )
    
    def _get_server_config(self) -> ServerConfig:
        """获取服务器配置"""
        if self.environment == DeploymentEnvironment.PRODUCTION:
            return ServerConfig(
                workers=8,
                worker_connections=2000,
                max_requests=50000,
                timeout=60,
                log_level="warning"
            )
        elif self.environment == DeploymentEnvironment.STAGING:
            return ServerConfig(
                workers=4,
                worker_connections=1000,
                max_requests=20000,
                log_level="info"
            )
        else:  # DEVELOPMENT
            return ServerConfig(
                workers=1,
                worker_connections=100,
                max_requests=1000,
                log_level="debug"
            )
    
    def _get_cache_config(self) -> CacheConfig:
        """获取缓存配置"""
        redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
        
        if self.environment == DeploymentEnvironment.PRODUCTION:
            return CacheConfig(
                redis_url=redis_url,
                redis_pool_size=50,
                default_ttl=7200,  # 2小时
                case_list_ttl=600,  # 10分钟
                case_detail_ttl=3600  # 1小时
            )
        else:
            return CacheConfig(redis_url=redis_url)
    
    def _get_monitoring_config(self) -> MonitoringConfig:
        """获取监控配置"""
        return MonitoringConfig(
            enable_metrics=self.environment != DeploymentEnvironment.DEVELOPMENT,
            enable_performance_tracking=True,
            slow_query_threshold=2.0 if self.environment == DeploymentEnvironment.PRODUCTION else 1.0
        )
    
    def _get_security_config(self) -> SecurityConfig:
        """获取安全配置"""
        if self.environment == DeploymentEnvironment.PRODUCTION:
            return SecurityConfig(
                cors_origins=os.getenv('CORS_ORIGINS', '').split(','),
                rate_limit_enabled=True,
                rate_limit_requests=5000,
                api_key_required=True
            )
        else:
            return SecurityConfig()
    
    def get_uvicorn_config(self) -> Dict[str, Any]:
        """获取Uvicorn配置"""
        config = {
            "host": self.server.host,
            "port": self.server.port,
            "log_level": self.server.log_level,
            "access_log": self.server.access_log,
            "timeout_keep_alive": self.server.keepalive,
            "limit_concurrency": self.server.worker_connections,
            "limit_max_requests": self.server.max_requests
        }
        
        # SSL配置
        if self.server.ssl_certfile and self.server.ssl_keyfile:
            config.update({
                "ssl_certfile": self.server.ssl_certfile,
                "ssl_keyfile": self.server.ssl_keyfile
            })
            if self.server.ssl_ca_certs:
                config["ssl_ca_certs"] = self.server.ssl_ca_certs
        
        return config
    
    def get_gunicorn_config(self) -> Dict[str, Any]:
        """获取Gunicorn配置"""
        return {
            "bind": f"{self.server.host}:{self.server.port}",
            "workers": self.server.workers,
            "worker_class": self.server.worker_class,
            "worker_connections": self.server.worker_connections,
            "max_requests": self.server.max_requests,
            "max_requests_jitter": self.server.max_requests_jitter,
            "timeout": self.server.timeout,
            "keepalive": self.server.keepalive,
            "loglevel": self.server.log_level,
            "accesslog": self.server.access_log_file if self.server.access_log else None,
            "errorlog": self.server.error_log,
            "preload_app": True,
            "enable_stdio_inheritance": True
        }
    
    def get_database_url_config(self) -> Dict[str, Any]:
        """获取数据库URL配置参数"""
        return {
            "pool_size": self.database.pool_size,
            "max_overflow": self.database.max_overflow,
            "pool_timeout": self.database.pool_timeout,
            "pool_recycle": self.database.pool_recycle,
            "pool_pre_ping": self.database.pool_pre_ping
        }
    
    def get_async_database_config(self) -> Dict[str, Any]:
        """获取异步数据库配置"""
        return {
            "pool_size": self.database.async_pool_size,
            "max_overflow": self.database.async_max_overflow,
            "pool_timeout": self.database.async_pool_timeout,
            "pool_recycle": self.database.async_pool_recycle,
            "connect_timeout": self.database.connect_timeout,
            "command_timeout": self.database.command_timeout,
            "server_settings": self.database.server_settings
        }


# 创建全局配置实例
def get_production_config(env: str = None) -> ProductionConfig:
    """获取生产环境配置"""
    if env is None:
        env = os.getenv('DEPLOYMENT_ENV', 'development')
    
    try:
        environment = DeploymentEnvironment(env.lower())
    except ValueError:
        environment = DeploymentEnvironment.DEVELOPMENT
    
    return ProductionConfig(environment)


# 默认配置实例
production_config = get_production_config()


# 导出配置供其他模块使用
__all__ = [
    'DeploymentEnvironment',
    'DatabaseConfig',
    'ServerConfig',
    'CacheConfig',
    'MonitoringConfig',
    'SecurityConfig',
    'ProductionConfig',
    'get_production_config',
    'production_config'
]
