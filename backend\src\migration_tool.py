#!/usr/bin/env python3
"""
PostgreSQL数据库管理工具
支持PostgreSQL数据库结构验证和管理
"""

import argparse
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.orm import sessionmaker
import json

from database_config import db_config, DatabaseType
from database_manager import db_manager
from models import Base, Case, File, CustomTag, TagCache
import models

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseMigrationTool:
    """数据库迁移工具"""
    
    def __init__(self):
        self.source_engines = {}
        self.target_engines = {}
    
    def create_source_engine(self, db_path: str):
        """创建源数据库引擎（已弃用 - PostgreSQL模式）"""
        # PostgreSQL模式下不再需要SQLite源引擎
        raise NotImplementedError("PostgreSQL模式下不支持SQLite源引擎")
    
    def create_target_engine(self, case_id: Optional[str] = None):
        """创建目标数据库引擎（PostgreSQL）"""
        if case_id:
            url = db_config.get_case_database_url(case_id)
            key = f"case_{case_id}"
        else:
            url = db_config.get_master_database_url()
            key = "master"
        
        if key not in self.target_engines:
            engine_kwargs = db_config.get_engine_kwargs(DatabaseType.POSTGRESQL)
            self.target_engines[key] = create_engine(url, **engine_kwargs)
        
        return self.target_engines[key]
    
    def verify_database_structure(self, engine, db_type: str = "unknown") -> Dict[str, Any]:
        """验证数据库结构"""
        inspector = inspect(engine)
        
        tables = inspector.get_table_names()
        indexes = []
        for table in tables:
            table_indexes = inspector.get_indexes(table)
            indexes.extend([f"{table}.{idx['name']}" for idx in table_indexes])
        
        result = {
            'database_type': db_type,
            'tables': tables,
            'table_count': len(tables),
            'indexes': indexes,
            'index_count': len(indexes)
        }
        
        logger.info(f"{db_type} 数据库结构:")
        logger.info(f"  表数量: {len(tables)}")
        logger.info(f"  索引数量: {len(indexes)}")
        logger.info(f"  表列表: {', '.join(tables)}")
        
        return result
    
    def migrate_master_database(self, source_db_path: str) -> bool:
        """迁移主数据库（已弃用 - PostgreSQL模式）"""
        logger.warning("PostgreSQL模式下不支持SQLite数据库迁移")
        raise NotImplementedError("PostgreSQL模式下不支持SQLite数据库迁移")
            
            # 创建目标数据库表结构
            master_tables = [
                models.Case.__table__,
                models.SystemConfig.__table__,
                models.CaseProcessingRule.__table__
            ]
            Base.metadata.create_all(bind=target_engine, tables=master_tables)
            
            # 创建会话
            SourceSession = sessionmaker(bind=source_engine)
            TargetSession = sessionmaker(bind=target_engine)
            
            with SourceSession() as source_session, TargetSession() as target_session:
                # 迁移案例数据
                cases = source_session.query(Case).all()
                for case in cases:
                    # 检查目标数据库中是否已存在
                    existing = target_session.query(Case).filter(Case.id == case.id).first()
                    if not existing:
                        # 创建新的案例对象，避免会话冲突
                        new_case = Case(
                            id=case.id,
                            name=case.name,
                            description=case.description,
                            status=case.status,
                            created_at=case.created_at,
                            updated_at=case.updated_at,
                            deleted_at=case.deleted_at,
                            # PostgreSQL模式下不使用db_path
                            cover_image_url=case.cover_image_url,
                            cover_type=case.cover_type
                        )
                        target_session.add(new_case)
                
                target_session.commit()
                logger.info(f"主数据库迁移完成，迁移了 {len(cases)} 个案例")
                return True
                
        except Exception as e:
            logger.error(f"主数据库迁移失败: {e}")
            return False
    
    def migrate_case_database(self, case_id: str, source_db_path: str) -> bool:
        """迁移案例数据库（已弃用 - PostgreSQL模式）"""
        logger.warning("PostgreSQL模式下不支持SQLite案例数据库迁移")
        raise NotImplementedError("PostgreSQL模式下不支持SQLite案例数据库迁移")
            
            # 创建目标数据库表结构
            case_tables = [
                models.File.__table__,
                models.CustomTag.__table__,
                models.FileCustomTag.__table__,
                models.TagCache.__table__,
                models.DeletedFile.__table__
            ]
            Base.metadata.create_all(bind=target_engine, tables=case_tables)
            
            # 创建会话
            SourceSession = sessionmaker(bind=source_engine)
            TargetSession = sessionmaker(bind=target_engine)
            
            with SourceSession() as source_session, TargetSession() as target_session:
                # 迁移文件数据
                files = source_session.query(File).all()
                migrated_files = 0
                
                for file in files:
                    existing = target_session.query(File).filter(File.id == file.id).first()
                    if not existing:
                        # 处理tags字段，确保JSON格式正确
                        tags_data = file.tags
                        if isinstance(tags_data, str):
                            try:
                                tags_data = json.loads(tags_data)
                            except json.JSONDecodeError:
                                tags_data = None
                        
                        new_file = File(
                            id=file.id,
                            file_name=file.file_name,
                            file_type=file.file_type,
                            file_path=file.file_path,
                            thumbnail_small_path=file.thumbnail_small_path,
                            width=file.width,
                            height=file.height,
                            created_at=file.created_at,
                            taken_at=file.taken_at,
                            quality_score=file.quality_score,
                            sharpness=file.sharpness,
                            brightness=file.brightness,
                            dynamic_range=file.dynamic_range,
                            num_faces=file.num_faces,
                            face_sharpness=file.face_sharpness,
                            face_quality=file.face_quality,
                            cluster_id=file.cluster_id,
                            phash=file.phash,
                            group_id=file.group_id,
                            frame_number=file.frame_number,
                            tags=tags_data
                        )
                        target_session.add(new_file)
                        migrated_files += 1
                
                # 迁移自定义标签
                custom_tags = source_session.query(CustomTag).all()
                for tag in custom_tags:
                    existing = target_session.query(CustomTag).filter(CustomTag.id == tag.id).first()
                    if not existing:
                        new_tag = CustomTag(
                            id=tag.id,
                            tag_name=tag.tag_name,
                            tag_color=tag.tag_color,
                            display_order=tag.display_order,
                            created_at=tag.created_at,
                            updated_at=tag.updated_at
                        )
                        target_session.add(new_tag)
                
                target_session.commit()
                logger.info(f"案例数据库迁移完成: case_{case_id}，迁移了 {migrated_files} 个文件")
                return True
                
        except Exception as e:
            logger.error(f"案例数据库迁移失败 case_{case_id}: {e}")
            return False
    
    def auto_discover_and_migrate(self, data_dir: str) -> bool:
        """自动发现并迁移所有数据库"""
        logger.info("开始自动发现和迁移数据库...")
        
        data_path = Path(data_dir)
        if not data_path.exists():
            logger.error(f"数据目录不存在: {data_dir}")
            return False
        
        success_count = 0
        total_count = 0
        
        # PostgreSQL模式下不支持SQLite数据库迁移
        logger.warning("PostgreSQL模式下不支持SQLite数据库迁移")
        logger.info("请使用 migrate_to_postgresql.py 脚本进行数据迁移")
        
        logger.info(f"迁移完成: {success_count}/{total_count} 个数据库迁移成功")
        return success_count == total_count
    
    def cleanup(self):
        """清理资源"""
        for engine in self.source_engines.values():
            engine.dispose()
        for engine in self.target_engines.values():
            engine.dispose()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据库迁移工具')
    parser.add_argument('--action', choices=['migrate', 'verify', 'auto'], 
                       default='verify', help='操作类型')
    parser.add_argument('--data-dir', default='../../data', 
                       help='数据目录路径')
    parser.add_argument('--source-db', help='源数据库路径')
    parser.add_argument('--case-id', help='案例ID（用于案例数据库迁移）')
    
    args = parser.parse_args()
    
    tool = DatabaseMigrationTool()
    
    try:
        if args.action == 'verify':
            # 验证数据库结构
            logger.info("验证数据库结构...")
            
            # 验证PostgreSQL连接
            if db_config.is_postgresql_enabled():
                if db_config.validate_postgresql_connection():
                    logger.info("✅ PostgreSQL连接验证成功")
                else:
                    logger.error("❌ PostgreSQL连接验证失败")
                    return
            
            # 验证主数据库结构
            master_engine = db_manager.master_engine
            tool.verify_database_structure(master_engine, "主数据库")
            
        elif args.action == 'migrate':
            if not args.source_db:
                logger.error("迁移操作需要指定源数据库路径")
                return
            
            if args.case_id:
                # 迁移案例数据库
                success = tool.migrate_case_database(args.case_id, args.source_db)
            else:
                # 迁移主数据库
                success = tool.migrate_master_database(args.source_db)
            
            if success:
                logger.info("✅ 迁移成功")
            else:
                logger.error("❌ 迁移失败")
        
        elif args.action == 'auto':
            # 自动迁移
            success = tool.auto_discover_and_migrate(args.data_dir)
            if success:
                logger.info("✅ 自动迁移成功")
            else:
                logger.error("❌ 自动迁移失败")
    
    finally:
        tool.cleanup()

if __name__ == "__main__":
    main()
