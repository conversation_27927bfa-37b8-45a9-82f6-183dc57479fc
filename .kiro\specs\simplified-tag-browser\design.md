# 精简标签浏览器设计文档

## 概述

本设计文档描述了精简标签浏览器的技术实现方案。系统专注于提供直观的双面板浏览体验：左侧展示标签层级结构，右侧展示对应的图片画廊，去除复杂的管理功能，确保简洁高效的用户体验。

## 架构设计

### 系统架构图

```
精简标签浏览器架构
├── 后端API层
│   ├── 标签数据API
│   ├── 图片获取API
│   └── 标签搜索API
├── 数据存储层
│   ├── 标签缓存文件
│   ├── 图片索引
│   └── 删除记录表
├── 前端界面层
│   ├── 标签树面板
│   ├── 图片画廊面板
│   └── 分栏布局组件
└── 交互逻辑层
    ├── 标签选择逻辑
    ├── 图片过滤逻辑
    └── 搜索过滤逻辑
```

## 组件和接口

### 1. 后端API设计

#### 标签数据API
```python
# 获取案例标签树（精简版）
GET /api/v1/cases/{case_id}/tags/tree-simple
Response: {
    "metadata": [
        {
            "name": "project",
            "values": [
                {"value": "春季拍摄", "count": 5},
                {"value": "夏季拍摄", "count": 3}
            ]
        },
        {
            "name": "camera",
            "values": [
                {"value": "Canon EOS R5", "count": 8}
            ]
        }
    ],
    "cv": [
        {
            "name": "objects",
            "values": [
                {"value": "person", "count": 12},
                {"value": "car", "count": 6}
            ]
        }
    ],
    "user": [...],
    "ai": [...]
}

# 根据标签获取图片列表
GET /api/v1/cases/{case_id}/files/by-tag?category={category}&name={name}&value={value}
Response: {
    "files": [
        {
            "id": 1,
            "name": "IMG_001.jpg",
            "path": "/data/case_18/IMG_001.jpg",
            "thumbnail": "/data/case_18/thumbnails/IMG_001_thumb.jpg",
            "size": 2048576,
            "created_at": "2024-01-15T10:30:00Z"
        }
    ],
    "total": 5,
    "tag_info": {
        "category": "metadata",
        "name": "project", 
        "value": "春季拍摄"
    }
}

# 搜索标签
GET /api/v1/cases/{case_id}/tags/search?q={query}
Response: {
    "results": [
        {
            "category": "metadata",
            "name": "project",
            "value": "春季拍摄",
            "count": 5,
            "match_type": "value"  // name, value, both
        }
    ]
}
```

### 2. 前端组件设计

#### 主要组件结构
```javascript
// 主容器组件
class TagBrowserPage {
    constructor() {
        this.state = {
            tagTree: {},
            selectedTag: null,
            currentFiles: [],
            searchQuery: '',
            leftPanelWidth: 350,
            isLoading: false
        };
    }
}

// 标签树组件
class TagTreePanel {
    render() {
        return `
            <div class="tag-tree-panel">
                <div class="search-box">
                    <input type="text" placeholder="搜索标签..." />
                </div>
                <div class="tag-tree">
                    ${this.renderTagCategories()}
                </div>
            </div>
        `;
    }
    
    renderTagCategories() {
        // 渲染标签分类和层级结构
    }
}

// 图片画廊组件
class ImageGalleryPanel {
    render() {
        return `
            <div class="gallery-panel">
                <div class="gallery-header">
                    <h3>${this.getGalleryTitle()}</h3>
                    <span class="file-count">${this.state.currentFiles.length} 张图片</span>
                </div>
                <div class="image-grid">
                    ${this.renderImageGrid()}
                </div>
            </div>
        `;
    }
}

// 分栏布局组件
class ResizablePanels {
    constructor(leftPanel, rightPanel) {
        this.leftPanel = leftPanel;
        this.rightPanel = rightPanel;
        this.minLeftWidth = 250;
        this.maxLeftWidth = 500;
    }
    
    initResize() {
        // 初始化拖拽调整功能
    }
}
```

## 数据模型

### 1. 标签数据结构
```javascript
// 标签树数据结构
const tagTreeStructure = {
    metadata: [
        {
            name: "project",
            displayName: "项目",
            values: [
                { value: "春季拍摄", count: 5 },
                { value: "夏季拍摄", count: 3 }
            ]
        }
    ],
    cv: [...],
    user: [...],
    ai: [...]
};

// 选中标签数据结构
const selectedTag = {
    category: "metadata",
    name: "project",
    value: "春季拍摄",
    displayPath: "元数据 > 项目 > 春季拍摄"
};
```

### 2. 图片数据结构
```javascript
// 图片文件数据结构
const imageFile = {
    id: 1,
    name: "IMG_001.jpg",
    path: "/data/case_18/IMG_001.jpg",
    thumbnail: "/data/case_18/thumbnails/IMG_001_thumb.jpg",
    size: 2048576,
    dimensions: { width: 1920, height: 1080 },
    created_at: "2024-01-15T10:30:00Z"
};
```

## 错误处理

### 1. 后端错误处理
```python
class TagBrowserError(Exception):
    """标签浏览器相关错误"""
    pass

@app.exception_handler(TagBrowserError)
async def tag_browser_error_handler(request: Request, exc: TagBrowserError):
    return JSONResponse(
        status_code=400,
        content={
            "error": "TAG_BROWSER_ERROR",
            "message": str(exc),
            "timestamp": datetime.utcnow().isoformat()
        }
    )

# 标签数据获取错误处理
async def get_tag_tree_safe(case_id: int):
    try:
        return await get_tag_tree(case_id)
    except FileNotFoundError:
        # 返回空的标签树结构
        return create_empty_tag_tree()
    except Exception as e:
        logger.error(f"获取标签树失败: {e}")
        raise TagBrowserError("无法加载标签数据")
```

### 2. 前端错误处理
```javascript
class TagBrowserErrorHandler {
    static handleApiError(error) {
        const errorMessages = {
            'TAG_BROWSER_ERROR': '标签数据加载失败',
            'NETWORK_ERROR': '网络连接失败',
            'FILE_NOT_FOUND': '图片文件不存在'
        };
        
        const message = errorMessages[error.code] || '操作失败，请重试';
        this.showErrorMessage(message);
    }
    
    static showErrorMessage(message) {
        // 显示错误提示
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        document.body.appendChild(errorDiv);
        
        setTimeout(() => {
            document.body.removeChild(errorDiv);
        }, 3000);
    }
}
```

## 测试策略

### 1. 后端测试
```python
# 标签API测试
class TestTagBrowserAPI:
    def test_get_tag_tree_simple(self):
        # 测试获取标签树
        pass
    
    def test_get_files_by_tag(self):
        # 测试根据标签获取文件
        pass
    
    def test_search_tags(self):
        # 测试标签搜索
        pass
    
    def test_empty_case_handling(self):
        # 测试空案例处理
        pass
```

### 2. 前端测试
```javascript
// 组件测试
describe('TagTreePanel', () => {
    test('should render tag categories', () => {
        // 测试标签分类渲染
    });
    
    test('should handle tag selection', () => {
        // 测试标签选择
    });
    
    test('should filter tags on search', () => {
        // 测试搜索过滤
    });
});

describe('ImageGalleryPanel', () => {
    test('should render image grid', () => {
        // 测试图片网格渲染
    });
    
    test('should handle empty state', () => {
        // 测试空状态处理
    });
});
```

## 性能优化

### 1. 数据加载优化
- 标签树数据缓存
- 图片懒加载
- 缩略图预加载
- API响应缓存

### 2. UI渲染优化
- 虚拟滚动（大量图片）
- 防抖搜索
- 组件懒渲染
- 图片占位符

### 3. 内存管理
- 及时清理未使用的图片引用
- 限制同时加载的图片数量
- 定期清理缓存数据

## 响应式设计

### 1. 断点设计
```css
/* 桌面端 */
@media (min-width: 1024px) {
    .tag-browser-layout {
        display: flex;
        flex-direction: row;
    }
    
    .tag-tree-panel {
        width: 350px;
        min-width: 250px;
        max-width: 500px;
    }
}

/* 平板端 */
@media (max-width: 1023px) and (min-width: 768px) {
    .tag-browser-layout {
        display: flex;
        flex-direction: column;
    }
    
    .tag-tree-panel {
        height: 40vh;
        width: 100%;
    }
}

/* 移动端 */
@media (max-width: 767px) {
    .tag-tree-panel {
        position: fixed;
        top: 0;
        left: -100%;
        width: 80vw;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .tag-tree-panel.open {
        left: 0;
    }
}
```

### 2. 触摸交互
```javascript
// 移动端触摸支持
class TouchHandler {
    constructor() {
        this.startX = 0;
        this.startY = 0;
    }
    
    handleTouchStart(e) {
        this.startX = e.touches[0].clientX;
        this.startY = e.touches[0].clientY;
    }
    
    handleTouchEnd(e) {
        const endX = e.changedTouches[0].clientX;
        const endY = e.changedTouches[0].clientY;
        
        // 检测滑动手势
        const deltaX = endX - this.startX;
        const deltaY = endY - this.startY;
        
        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
            if (deltaX > 0) {
                // 右滑 - 显示标签面板
                this.showTagPanel();
            } else {
                // 左滑 - 隐藏标签面板
                this.hideTagPanel();
            }
        }
    }
}
```

这个设计专注于简洁的双面板浏览体验，去除了复杂的管理功能，确保用户能够快速浏览标签和对应的图片内容。