# src/routers/search_v2.py
"""
高性能搜索API v2
集成PostgreSQL优化、缓存机制、查询优化器的统一搜索接口
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field
import logging
from datetime import datetime

from ..database_manager import get_master_db
from ..services.postgresql_search import PostgreSQLSearchService
from ..services.query_optimizer import QueryOptimizer
from ..services.search_cache import get_search_cache, cached_search
from ..crud.case_crud import get_case

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v2/search", tags=["搜索v2"])

# Pydantic模型
class SearchRequest(BaseModel):
    """搜索请求模型"""
    text_query: Optional[str] = Field(None, description="文本搜索查询")
    metadata_filters: Optional[Dict[str, Any]] = Field(None, description="元数据过滤条件")
    user_tags: Optional[List[str]] = Field(None, description="用户标签过滤")
    ai_tags: Optional[List[str]] = Field(None, description="AI标签过滤")
    quality_range: Optional[List[float]] = Field(None, description="质量分数范围 [min, max]")
    date_range: Optional[List[str]] = Field(None, description="日期范围 ['2023-01-01', '2023-12-31']")
    limit: int = Field(50, ge=1, le=500, description="返回结果数量限制")
    offset: int = Field(0, ge=0, description="偏移量")
    sort_by: str = Field("quality_desc", description="排序方式: quality_desc, date_desc, relevance")
    enable_cache: bool = Field(True, description="是否启用缓存")

class SearchResponse(BaseModel):
    """搜索响应模型"""
    files: List[Dict[str, Any]]
    total_count: int
    execution_time_ms: float
    cache_hit: bool
    statistics: Optional[Dict[str, Any]] = None
    optimization_suggestions: Optional[List[str]] = None

@router.post("/{case_id}/advanced")
async def advanced_search(
    case_id: int,
    search_request: SearchRequest,
    db: Session = Depends(get_master_db)
) -> SearchResponse:
    """
    高级搜索接口
    
    支持复杂的多条件搜索，自动优化查询性能
    """
    start_time = datetime.now()
    
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")
        
        # 创建搜索服务
        search_service = PostgreSQLSearchService(case_id)
        cache = get_search_cache()
        
        # 构建缓存键
        cache_key_params = {
            'case_id': case_id,
            **search_request.dict(exclude={'enable_cache'})
        }
        
        cache_hit = False
        result = None
        
        # 尝试从缓存获取结果
        if search_request.enable_cache:
            result = cache.get('advanced_search', cache_key_params)
            if result:
                cache_hit = True
                logger.info(f"缓存命中: 案例 {case_id} 高级搜索")
        
        # 如果缓存未命中，执行搜索
        if not result:
            search_params = search_request.dict()
            result = search_service.complex_search(search_params)
            
            # 缓存结果
            if search_request.enable_cache and result:
                cache.set('advanced_search', cache_key_params, result)
        
        # 计算执行时间
        execution_time = (datetime.now() - start_time).total_seconds() * 1000
        
        return SearchResponse(
            files=result.get('files', []),
            total_count=result.get('total_count', 0),
            execution_time_ms=execution_time,
            cache_hit=cache_hit,
            statistics=result.get('statistics'),
            optimization_suggestions=[]
        )
        
    except Exception as e:
        logger.error(f"高级搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.get("/{case_id}/fulltext")
async def fulltext_search(
    case_id: int,
    q: str = Query(..., description="搜索查询"),
    limit: int = Query(50, ge=1, le=200, description="返回结果数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    enable_cache: bool = Query(True, description="是否启用缓存"),
    db: Session = Depends(get_master_db)
) -> SearchResponse:
    """
    全文搜索接口
    
    使用PostgreSQL全文搜索
    """
    start_time = datetime.now()
    
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")
        
        # 创建搜索服务
        search_service = PostgreSQLSearchService(case_id)
        cache = get_search_cache()
        
        cache_key_params = {
            'case_id': case_id,
            'query': q,
            'limit': limit,
            'offset': offset
        }
        
        cache_hit = False
        files = []
        
        # 尝试从缓存获取结果
        if enable_cache:
            cached_files = cache.get('fulltext_search', cache_key_params)
            if cached_files:
                files = cached_files
                cache_hit = True
                logger.info(f"缓存命中: 案例 {case_id} 全文搜索")
        
        # 如果缓存未命中，执行搜索
        if not files:
            files = search_service.fulltext_search(q, limit, offset)
            
            # 缓存结果
            if enable_cache and files:
                cache.set('fulltext_search', cache_key_params, files)
        
        # 计算执行时间
        execution_time = (datetime.now() - start_time).total_seconds() * 1000
        
        return SearchResponse(
            files=files,
            total_count=len(files),
            execution_time_ms=execution_time,
            cache_hit=cache_hit
        )
        
    except Exception as e:
        logger.error(f"全文搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.get("/{case_id}/by-tags")
async def search_by_tags(
    case_id: int,
    metadata_filters: Optional[str] = Query(None, description="元数据过滤条件 (JSON字符串)"),
    user_tags: Optional[str] = Query(None, description="用户标签 (逗号分隔)"),
    ai_tags: Optional[str] = Query(None, description="AI标签 (逗号分隔)"),
    quality_min: Optional[float] = Query(None, ge=0, le=100, description="最小质量分数"),
    quality_max: Optional[float] = Query(None, ge=0, le=100, description="最大质量分数"),
    limit: int = Query(50, ge=1, le=200, description="返回结果数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    enable_cache: bool = Query(True, description="是否启用缓存"),
    db: Session = Depends(get_master_db)
) -> SearchResponse:
    """
    基于标签的搜索接口
    
    使用JSONB优化的标签查询
    """
    start_time = datetime.now()
    
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")
        
        # 解析参数
        import json
        
        parsed_metadata_filters = None
        if metadata_filters:
            try:
                parsed_metadata_filters = json.loads(metadata_filters)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="元数据过滤条件格式错误")
        
        parsed_user_tags = None
        if user_tags:
            parsed_user_tags = [tag.strip() for tag in user_tags.split(',') if tag.strip()]
        
        parsed_ai_tags = None
        if ai_tags:
            parsed_ai_tags = [tag.strip() for tag in ai_tags.split(',') if tag.strip()]
        
        # 创建搜索服务
        search_service = PostgreSQLSearchService(case_id)
        cache = get_search_cache()
        
        cache_key_params = {
            'case_id': case_id,
            'metadata_filters': parsed_metadata_filters,
            'user_tags': parsed_user_tags,
            'ai_tags': parsed_ai_tags,
            'quality_min': quality_min,
            'quality_max': quality_max,
            'limit': limit,
            'offset': offset
        }
        
        cache_hit = False
        files = []
        
        # 尝试从缓存获取结果
        if enable_cache:
            cached_files = cache.get('jsonb_search', cache_key_params)
            if cached_files:
                files = cached_files
                cache_hit = True
                logger.info(f"缓存命中: 案例 {case_id} 标签搜索")
        
        # 如果缓存未命中，执行搜索
        if not files:
            files = search_service.search_files_by_jsonb_tags(
                metadata_filters=parsed_metadata_filters,
                user_tags=parsed_user_tags,
                ai_tags=parsed_ai_tags,
                quality_min=quality_min,
                quality_max=quality_max,
                limit=limit,
                offset=offset
            )
            
            # 缓存结果
            if enable_cache and files:
                cache.set('jsonb_search', cache_key_params, files)
        
        # 计算执行时间
        execution_time = (datetime.now() - start_time).total_seconds() * 1000
        
        return SearchResponse(
            files=files,
            total_count=len(files),
            execution_time_ms=execution_time,
            cache_hit=cache_hit
        )
        
    except Exception as e:
        logger.error(f"标签搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.get("/{case_id}/performance")
async def get_search_performance(
    case_id: int,
    hours: int = Query(24, ge=1, le=168, description="统计时间范围（小时）"),
    db: Session = Depends(get_master_db)
) -> Dict[str, Any]:
    """
    获取搜索性能统计
    """
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")
        
        # 获取查询优化器
        optimizer = QueryOptimizer(str(case_id))
        performance_summary = optimizer.get_performance_summary(hours)
        
        # 获取缓存统计
        cache = get_search_cache()
        cache_stats = cache.get_stats()
        
        # 获取索引推荐
        index_recommendations = optimizer.get_index_recommendations()
        
        return {
            'case_id': case_id,
            'time_range_hours': hours,
            'query_performance': performance_summary,
            'cache_performance': cache_stats,
            'index_recommendations': [
                {
                    'table': rec.table_name,
                    'column': rec.column_expression,
                    'type': rec.index_type,
                    'benefit': rec.estimated_benefit,
                    'reason': rec.reason
                }
                for rec in index_recommendations
            ]
        }
        
    except Exception as e:
        logger.error(f"获取搜索性能统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取性能统计失败: {str(e)}")

@router.post("/{case_id}/cache/clear")
async def clear_search_cache(
    case_id: int,
    db: Session = Depends(get_master_db)
) -> Dict[str, str]:
    """
    清理搜索缓存
    """
    try:
        # 验证案例存在
        case = get_case(db, case_id)
        if not case:
            raise HTTPException(status_code=404, detail="案例不存在")
        
        # 清理缓存
        cache = get_search_cache()
        cache.cache.invalidate_by_case(str(case_id))
        
        return {"message": f"案例 {case_id} 的搜索缓存已清理"}
        
    except Exception as e:
        logger.error(f"清理搜索缓存失败: {e}")
        raise HTTPException(status_code=500, detail=f"清理缓存失败: {str(e)}")

@router.get("/cache/stats")
async def get_cache_stats() -> Dict[str, Any]:
    """
    获取全局缓存统计信息
    """
    try:
        cache = get_search_cache()
        stats = cache.get_stats()
        cache_info = cache.cache.get_cache_info()
        
        return {
            'global_stats': stats,
            'cache_entries': cache_info[:20]  # 只返回前20个条目
        }
        
    except Exception as e:
        logger.error(f"获取缓存统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取缓存统计失败: {str(e)}")

@router.post("/cache/clear-all")
async def clear_all_cache() -> Dict[str, str]:
    """
    清理所有搜索缓存
    """
    try:
        cache = get_search_cache()
        cache.cache.clear()
        
        return {"message": "所有搜索缓存已清理"}
        
    except Exception as e:
        logger.error(f"清理所有缓存失败: {e}")
        raise HTTPException(status_code=500, detail=f"清理缓存失败: {str(e)}")
