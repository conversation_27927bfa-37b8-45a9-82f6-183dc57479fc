# src/models.py
from sqlalchemy import (
    Column, <PERSON>Key, Integer, String, DateTime, Float, func, Enum as SQLEnum, <PERSON>ole<PERSON>, Text
)
from sqlalchemy.types import TypeDecorator
from sqlalchemy.orm import relationship, declarative_base
from sqlalchemy.dialects.postgresql import JSONB
import enum
import json

# 导入pgvector，用于向量存储
try:
    from pgvector.sqlalchemy import Vector
    VECTOR_AVAILABLE = True
except ImportError:
    # 如果pgvector不可用，创建一个备用的Vector类型，使用TEXT存储
    from sqlalchemy import TEXT

    class Vector(TypeDecorator):
        impl = TEXT
        cache_ok = True

        def __init__(self, dimensions=None):
            self.dimensions = dimensions
            super().__init__()

        def load_dialect_impl(self, dialect):
            # 对于所有数据库都使用TEXT类型
            return dialect.type_descriptor(TEXT())

    VECTOR_AVAILABLE = False

# 创建Base类
Base = declarative_base()

# 定义案例状态枚举
class CaseStatus(enum.Enum):
    ACTIVE = "active"           # 正常状态
    DELETED = "deleted"         # 已删除（在回收站中）
    PERMANENTLY_DELETED = "permanently_deleted"  # 彻底删除

# 定义封面类型枚举
class CoverType(enum.Enum):
    MANUAL = "manual"           # 手动选择的封面
    AUTOMATIC = "automatic"     # 自动选择的封面
    PLACEHOLDER = "placeholder" # 占位图

# 定义规则类型枚举
class RuleType(enum.Enum):
    FILENAME_PARSING = "FILENAME_PARSING"           # 文件名解析规则
    DATE_TAGGING_FORMAT = "DATE_TAGGING_FORMAT"     # 日期标签格式化规则



# --- Model for the MASTER database ---
class Case(Base):
    __tablename__ = "cases"
    __table_args__ = {'extend_existing': True, 'autoload_with': None}

    id = Column(Integer, primary_key=True, index=True)
    case_name = Column(String, nullable=False, index=True)
    description = Column(String, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    status = Column(
        SQLEnum(
            CaseStatus,
            values_callable=lambda enum_cls: [e.value for e in enum_cls],  # 使用枚举的value值存储
            native_enum=True  # PostgreSQL支持原生枚举
        ),
        default=CaseStatus.ACTIVE,
        nullable=False,
        index=True
    )

    # 新增：删除时间（用于回收站排序和自动清理）
    deleted_at = Column(DateTime(timezone=True), nullable=True)

    # 封面图相关字段
    cover_image_url = Column(String(500), nullable=True, comment="封面图URL")
    cover_type = Column(
        SQLEnum(
            CoverType,
            values_callable=lambda enum_cls: [e.value for e in enum_cls],
            native_enum=True  # PostgreSQL支持原生枚举
        ),
        default=CoverType.PLACEHOLDER,
        nullable=False,
        comment="封面类型"
    )
    cover_source_file_id = Column(Integer, nullable=True, comment="封面源文件ID")
    cover_needs_attention = Column(Boolean, default=False, nullable=False, comment="封面是否需要用户关注")
    cover_updated_at = Column(DateTime(timezone=True), nullable=True, comment="封面更新时间")

    # PostgreSQL单一数据库架构 - 建立Case和File的关系
    files = relationship("File", back_populates="case", cascade="all, delete-orphan")

class File(Base):
    __tablename__ = "files"

    id = Column(Integer, primary_key=True, index=True)
    # PostgreSQL模式下需要case_id字段来关联案例
    case_id = Column(Integer, ForeignKey("cases.id"), nullable=False, index=True, comment="案例ID")
    file_name = Column(String, index=True)
    file_type = Column(String)
    file_path = Column(String)
    thumbnail_small_path = Column(String, nullable=True)
    width = Column(Integer, nullable=True)
    height = Column(Integer, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    taken_at = Column(DateTime(timezone=True), nullable=True)
    
    # 图像质量分析字段
    quality_score = Column(Float, nullable=True, comment="综合质量分数")
    sharpness = Column(Float, nullable=True, comment="清晰度分数")
    brightness = Column(Float, nullable=True, comment="亮度值")
    dynamic_range = Column(Float, nullable=True, comment="动态范围")
    num_faces = Column(Integer, nullable=True, comment="人脸数量")
    face_sharpness = Column(Float, nullable=True, comment="人脸清晰度")
    face_quality = Column(Float, nullable=True, comment="人脸质量分数")
    cluster_id = Column(Integer, nullable=True, comment="相似性聚类ID")
    phash = Column(String, nullable=True, comment="感知哈希值")
    group_id = Column(String, nullable=True, comment="文件名解析的组ID")
    frame_number = Column(Integer, nullable=True, comment="帧号")

    # 标签系统字段 - 使用PostgreSQL JSONB
    tags = Column(JSONB, nullable=True, comment="文件标签的复杂对象结构，包含properties和tags")

    # 向量字段 - 用于AI搜索和相似性匹配
    vector_image = Column(Vector(512), nullable=True, comment="图像向量表示，用于相似性搜索")
    vector_text = Column(Vector(512), nullable=True, comment="文本向量表示，用于语义搜索")

    # PostgreSQL单一数据库架构 - 建立Case和File的关系
    case = relationship("Case", back_populates="files")

class SystemConfig(Base):
    __tablename__ = "system_config"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    config_key = Column(String(100), unique=True, nullable=False, index=True, comment="配置键")
    config_value = Column(String, nullable=False, comment="配置值")
    description = Column(String, nullable=True, comment="配置描述")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

class CaseProcessingRule(Base):
    __tablename__ = "case_processing_rules"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True, comment="规则唯一标识符")
    case_id = Column(Integer, ForeignKey("cases.id"), nullable=False, index=True, comment="关联的案例ID")
    rule_type = Column(
        SQLEnum(
            RuleType,
            values_callable=lambda enum_cls: [e.value for e in enum_cls],
            native_enum=True  # PostgreSQL支持原生枚举
        ),
        nullable=False,
        index=True,
        comment="规则类型"
    )
    rule_config = Column(JSONB, nullable=False, comment="规则配置的JSON对象")
    is_active = Column(Boolean, default=True, nullable=False, index=True, comment="规则是否激活")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="规则创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="规则最后更新时间")

    # 建立与Case的关系
    case = relationship("Case", backref="processing_rules")

class TagCache(Base):
    """标签缓存表"""
    __tablename__ = "tag_cache"

    id = Column(Integer, primary_key=True, index=True)
    case_id = Column(Integer, ForeignKey("cases.id"), nullable=False, index=True, comment="案例ID")
    tag_category = Column(String(50), nullable=False, index=True, comment="标签类别: metadata, cv, user, ai, custom")
    tag_name = Column(String(100), nullable=False, index=True, comment="标签名称")
    tag_value = Column(String(500), nullable=False, index=True, comment="标签值")
    file_ids = Column(JSONB, nullable=False, comment="包含该标签的文件ID列表")
    file_count = Column(Integer, default=0, nullable=False, comment="文件数量")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 创建复合索引以提高查询性能
    __table_args__ = (
        {'postgresql_using': 'btree'},
    )

    # 关联到案例
    case = relationship("Case", backref="tag_cache")

class DeletedFiles(Base):
    """删除文件记录表"""
    __tablename__ = "deleted_files"

    id = Column(Integer, primary_key=True, index=True)
    case_id = Column(Integer, ForeignKey("cases.id"), nullable=False, index=True, comment="案例ID")
    file_id = Column(Integer, nullable=False, index=True, comment="被删除的文件ID")
    file_name = Column(String(255), nullable=False, comment="文件名")
    deleted_at = Column(DateTime(timezone=True), server_default=func.now(), comment="删除时间")
    is_permanent = Column(Boolean, default=False, nullable=False, comment="是否永久删除")
    
    # 关联到案例
    case = relationship("Case", backref="deleted_files")

class CustomTags(Base):
    """自定义标签表"""
    __tablename__ = "custom_tags"

    id = Column(Integer, primary_key=True, index=True)
    case_id = Column(Integer, ForeignKey("cases.id"), nullable=False, index=True, comment="案例ID")
    tag_name = Column(String(100), nullable=False, comment="自定义标签名称")
    tag_color = Column(String(7), default="#3B82F6", nullable=False, comment="标签颜色(十六进制)")
    display_order = Column(Integer, default=0, nullable=False, comment="显示顺序")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关联到案例
    case = relationship("Case", backref="custom_tags")
    
    # 创建复合唯一索引，防止同一案例中有重复标签名
    __table_args__ = (
        {'postgresql_using': 'btree'},
    )

class FileCustomTags(Base):
    """文件自定义标签关联表"""
    __tablename__ = "file_custom_tags"

    id = Column(Integer, primary_key=True, index=True)
    file_id = Column(Integer, ForeignKey("files.id"), nullable=False, index=True, comment="文件ID")
    custom_tag_id = Column(Integer, ForeignKey("custom_tags.id"), nullable=False, index=True, comment="自定义标签ID")
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 创建复合唯一索引，防止重复关联
    __table_args__ = (
        {'postgresql_using': 'btree'},
    )
    
    # 关联关系
    file = relationship("File", backref="custom_tag_links")
    custom_tag = relationship("CustomTags", backref="file_links")

