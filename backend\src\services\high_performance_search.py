"""
高性能标签检索服务
支持10,000+文件的实时标签检索
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Union, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import json

from sqlalchemy.orm import Session
from sqlalchemy import text, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession

from ..database import get_independent_db
from ..models import File, TagCache
from ..services.search_cache import SearchCache
from ..services.tag_cache_manager import TagCacheManager

logger = logging.getLogger(__name__)

@dataclass
class SearchQuery:
    """搜索查询参数"""
    case_id: int
    user_tags: Optional[List[str]] = None
    custom_tags: Optional[Dict[str, str]] = None
    ai_tags: Optional[List[str]] = None
    operator: str = 'AND'  # 'AND' 或 'OR'
    quality_min: Optional[float] = None
    date_range: Optional[Tuple[datetime, datetime]] = None
    limit: int = 100
    offset: int = 0
    sort_by: str = 'relevance'  # 'relevance', 'date', 'quality', 'name'
    sort_order: str = 'desc'

@dataclass
class SearchResult:
    """搜索结果"""
    file_id: int
    file_name: str
    file_path: str
    thumbnail_path: Optional[str]
    tag_match_count: int
    relevance_score: float
    quality_score: Optional[float]
    created_at: datetime
    tags: Dict[str, Any]

class HighPerformanceSearchService:
    """高性能标签检索服务"""

    def __init__(self):
        self.cache_manager = SearchCache()
        # TagCacheManager需要case_id，在需要时创建

    async def search_files(self, query: SearchQuery) -> Tuple[List[SearchResult], int]:
        """
        高性能文件搜索

        Args:
            query: 搜索查询参数

        Returns:
            Tuple[搜索结果列表, 总数量]
        """
        # 1. 检查缓存
        cache_params = self._query_to_cache_params(query)
        cached_result = self.cache_manager.get("file_search", cache_params)

        if cached_result:
            logger.info(f"从缓存返回搜索结果")
            return cached_result['results'], cached_result['total']

        # 2. 执行搜索
        with get_independent_db() as db:
            results, total = await self._execute_search(db, query)

        # 3. 缓存结果
        cache_data = {
            'results': results,
            'total': total,
            'query': query.__dict__,
            'timestamp': datetime.now().isoformat()
        }
        self.cache_manager.set("file_search", cache_params, cache_data, ttl_seconds=300)

        return results, total

    async def _execute_search(self, db: Session, query: SearchQuery) -> Tuple[List[SearchResult], int]:
        """执行实际的数据库搜索"""

        # 构建基础查询
        base_query = db.query(File).filter(File.case_id == query.case_id)

        # 添加标签过滤条件 - 使用GIN索引优化的查询
        tag_conditions = []

        if query.user_tags:
            for tag in query.user_tags:
                # 使用GIN索引的 @> 操作符进行包含查询
                condition = File.tags['tags']['user'].op('@>')([tag])
                tag_conditions.append(condition)

        if query.custom_tags:
            for tag_name, tag_value in query.custom_tags.items():
                # 使用GIN索引的 @> 操作符查询自定义标签
                condition = File.tags['tags']['custom'].op('@>')({tag_name: tag_value})
                tag_conditions.append(condition)

        if query.ai_tags:
            for tag in query.ai_tags:
                # 使用GIN索引的 @> 操作符进行包含查询
                condition = File.tags['tags']['ai'].op('@>')([tag])
                tag_conditions.append(condition)

        # 应用标签条件
        if tag_conditions:
            if query.operator == 'AND':
                base_query = base_query.filter(and_(*tag_conditions))
            else:  # OR
                base_query = base_query.filter(or_(*tag_conditions))

        # 添加质量过滤
        if query.quality_min is not None:
            base_query = base_query.filter(File.quality_score >= query.quality_min)

        # 添加日期范围过滤
        if query.date_range:
            start_date, end_date = query.date_range
            base_query = base_query.filter(
                and_(
                    File.created_at >= start_date,
                    File.created_at <= end_date
                )
            )

        # 获取总数 - 优化：只在需要时计算总数
        # 对于性能优化，我们可以先获取结果，然后估算总数
        # 或者使用更高效的计数查询

        # 应用排序
        if query.sort_by == 'date':
            order_col = File.created_at
        elif query.sort_by == 'quality':
            order_col = File.quality_score
        elif query.sort_by == 'name':
            order_col = File.file_name
        else:  # relevance - 使用复合排序
            order_col = File.quality_score

        if query.sort_order == 'desc':
            base_query = base_query.order_by(order_col.desc())
        else:
            base_query = base_query.order_by(order_col.asc())

        # 应用分页
        files = base_query.offset(query.offset).limit(query.limit).all()

        # 转换为搜索结果 - 性能优化：简化计算
        results = []
        for file in files:
            # 暂时简化计算以提高性能
            match_count = 0
            relevance_score = 1.0

            result = SearchResult(
                file_id=file.id,
                file_name=file.file_name,
                file_path=file.file_path,
                thumbnail_path=file.thumbnail_small_path,
                tag_match_count=match_count,
                relevance_score=relevance_score,
                quality_score=file.quality_score,
                created_at=file.created_at,
                tags=file.tags or {}
            )
            results.append(result)

        # 如果按相关性排序，重新排序结果
        if query.sort_by == 'relevance':
            results.sort(key=lambda x: x.relevance_score, reverse=(query.sort_order == 'desc'))

        # 优化：智能计算总数
        if len(files) < query.limit:
            # 如果返回的结果少于限制，说明这就是全部结果
            total = query.offset + len(files)
        else:
            # 如果返回了完整的limit数量，我们需要计算总数
            # 使用更快的计数查询（只查询ID）
            count_query = db.query(File.id).filter(File.case_id == query.case_id)

            # 应用相同的过滤条件
            if tag_conditions:
                if query.operator == 'AND':
                    count_query = count_query.filter(and_(*tag_conditions))
                else:
                    count_query = count_query.filter(or_(*tag_conditions))

            if query.quality_min is not None:
                count_query = count_query.filter(File.quality_score >= query.quality_min)

            if query.date_range:
                start_date, end_date = query.date_range
                count_query = count_query.filter(
                    and_(
                        File.created_at >= start_date,
                        File.created_at <= end_date
                    )
                )

            total = count_query.count()

        return results, total

    def _calculate_match_count(self, file: File, query: SearchQuery) -> int:
        """计算标签匹配数量"""
        match_count = 0

        if not file.tags:
            return 0

        file_tags = file.tags.get('tags', {})

        # 用户标签匹配
        if query.user_tags and 'user' in file_tags:
            user_tags = file_tags['user'] or []
            for tag in query.user_tags:
                if tag in user_tags:
                    match_count += 1

        # 自定义标签匹配
        if query.custom_tags and 'custom' in file_tags:
            custom_tags = file_tags['custom'] or {}
            for tag_name, tag_value in query.custom_tags.items():
                if custom_tags.get(tag_name) == tag_value:
                    match_count += 1

        # AI标签匹配
        if query.ai_tags and 'ai' in file_tags:
            ai_tags = file_tags['ai'] or []
            for tag in query.ai_tags:
                if tag in ai_tags:
                    match_count += 1

        return match_count

    def _calculate_relevance_score(self, file: File, query: SearchQuery, match_count: int) -> float:
        """计算相关性分数"""
        score = 0.0

        # 基础匹配分数
        score += match_count * 10

        # 质量分数权重
        if file.quality_score:
            score += file.quality_score * 0.3

        # 时间新鲜度权重（越新越高分）
        if file.created_at:
            try:
                # 处理时区问题
                now = datetime.now()
                if file.created_at.tzinfo is not None:
                    # 如果文件时间有时区信息，转换为UTC
                    import pytz
                    now = now.replace(tzinfo=pytz.UTC)
                elif now.tzinfo is not None:
                    # 如果当前时间有时区信息，移除时区
                    now = now.replace(tzinfo=None)

                days_old = (now - file.created_at).days
                freshness_score = max(0, 100 - days_old * 0.5)
                score += freshness_score * 0.1
            except Exception as e:
                # 如果时间计算失败，跳过时间权重
                logger.debug(f"时间计算失败: {e}")
                pass

        # 标签完整度权重
        if file.tags:
            tag_categories = file.tags.get('tags', {})
            completeness = len([k for k, v in tag_categories.items() if v])
            score += completeness * 2

        return round(score, 2)

    def _query_to_cache_params(self, query: SearchQuery) -> Dict[str, Any]:
        """转换查询为缓存参数"""
        return {
            'case_id': query.case_id,
            'user_tags': sorted(query.user_tags) if query.user_tags else None,
            'custom_tags': dict(sorted(query.custom_tags.items())) if query.custom_tags else None,
            'ai_tags': sorted(query.ai_tags) if query.ai_tags else None,
            'operator': query.operator,
            'quality_min': query.quality_min,
            'date_range': [d.isoformat() for d in query.date_range] if query.date_range else None,
            'limit': query.limit,
            'offset': query.offset,
            'sort_by': query.sort_by,
            'sort_order': query.sort_order
        }

    async def get_tag_suggestions(
        self,
        case_id: int,
        query: str,
        category: Optional[str] = None,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """获取标签建议"""

        # 检查缓存
        cache_params = {
            'case_id': case_id,
            'query': query,
            'category': category,
            'limit': limit
        }
        cached_result = self.cache_manager.get("tag_suggestions", cache_params)

        if cached_result:
            return cached_result

        # 从数据库获取建议
        with get_independent_db() as db:
            suggestions = self._get_tag_suggestions_from_db(db, case_id, query, category, limit)

        # 缓存结果
        self.cache_manager.set("tag_suggestions", cache_params, suggestions, ttl_seconds=600)

        return suggestions

    def _get_tag_suggestions_from_db(
        self,
        db: Session,
        case_id: int,
        query: str,
        category: Optional[str],
        limit: int
    ) -> List[Dict[str, Any]]:
        """从数据库获取标签建议"""

        # 构建查询
        base_query = db.query(TagCache).filter(TagCache.case_id == case_id)

        if category:
            base_query = base_query.filter(TagCache.tag_category == category)

        # 模糊搜索
        search_condition = or_(
            TagCache.tag_name.ilike(f'%{query}%'),
            TagCache.tag_value.ilike(f'%{query}%')
        )
        base_query = base_query.filter(search_condition)

        # 按使用频率排序
        results = base_query.order_by(TagCache.file_count.desc()).limit(limit).all()

        suggestions = []
        for result in results:
            # 计算相关性分数
            relevance = 0
            if result.tag_name.lower().startswith(query.lower()):
                relevance += 100
            elif result.tag_value.lower().startswith(query.lower()):
                relevance += 90
            elif query.lower() in result.tag_name.lower():
                relevance += 70
            elif query.lower() in result.tag_value.lower():
                relevance += 60

            # 添加使用频率权重
            relevance += min(result.file_count * 2, 50)

            suggestions.append({
                'tag_name': result.tag_name,
                'tag_value': result.tag_value,
                'tag_category': result.tag_category,
                'file_count': result.file_count,
                'relevance_score': relevance
            })

        # 按相关性排序
        suggestions.sort(key=lambda x: x['relevance_score'], reverse=True)

        return suggestions

    async def invalidate_cache(self, case_id: int):
        """使缓存失效"""
        # 清除搜索缓存
        self.cache_manager.invalidate_pattern("file_search")
        self.cache_manager.invalidate_pattern("tag_suggestions")

        logger.info(f"已清除案例 {case_id} 的搜索缓存")

# 全局实例
search_service = HighPerformanceSearchService()
