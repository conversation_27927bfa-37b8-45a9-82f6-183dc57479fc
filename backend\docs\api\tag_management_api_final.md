# 标签管理系统 API 最终规范文档

## 🎯 项目概述

迷星 Mizzy Star 标签管理系统后端API已完成开发，提供了完整的标签组织、管理和应用功能。系统支持多种标签类型、自定义标签、批量操作和灵活的查询方式。

## ✅ 已完成功能

### 1. 核心API端点
- ✅ **标签树获取** - `GET /api/v1/cases/{case_id}/tags/tree`
- ✅ **标签搜索** - `GET /api/v1/cases/{case_id}/tags/search`
- ✅ **自定义标签管理** - `POST/PUT/DELETE /api/v1/cases/{case_id}/tags/custom`
- ✅ **标签统计** - `GET /api/v1/cases/{case_id}/tags/stats`
- ✅ **数据库迁移** - `POST /api/v1/cases/{case_id}/tags/migrate`

### 2. 数据库结构
- ✅ **标签缓存表** (tag_cache) - 提高查询性能
- ✅ **删除文件记录表** (deleted_files) - 支持软删除
- ✅ **自定义标签表** (custom_tags) - 用户自定义标签
- ✅ **文件标签关联表** (file_custom_tags) - 多对多关系

### 3. 核心功能
- ✅ **标签树构建** - 按类别组织的层级结构
- ✅ **标签搜索** - 支持关键词和类别筛选
- ✅ **自定义标签** - 创建、编辑、删除用户标签
- ✅ **数据迁移** - 自动创建必要的数据库表结构

## 📊 API 测试结果

### 标签树构建测试
```json
{
  "properties": {
    "filename": {
      "test1.jpg": {"count": 1, "file_ids": [1]}
    },
    "qualityScore": {
      "0.85": {"count": 1, "file_ids": [1]}
    }
  },
  "tags": {
    "metadata": {
      "camera": {
        "Nikon D850": {"count": 1, "file_ids": [1]}
      },
      "project": {
        "春季拍摄": {"count": 2, "file_ids": [1, 2]}
      }
    },
    "cv": {
      "objects": {
        "person": {"count": 2, "file_ids": [1, 2]}
      }
    },
    "user": {
      "重要": {"count": 2, "file_ids": [1, 2]}
    },
    "ai": {
      "风景": {"count": 1, "file_ids": [1]}
    }
  },
  "custom": [
    {
      "id": 1,
      "name": "重要",
      "color": "#EF4444",
      "count": 2,
      "file_ids": [1, 2]
    }
  ]
}
```

### 真实数据库测试
- ✅ 成功解析现有标签数据
- ✅ 正确处理双重JSON编码问题
- ✅ 构建完整标签树结构
- ✅ 自定义标签功能正常

## 🔧 前端开发指南

### 基础配置
```javascript
const API_BASE_URL = 'http://localhost:8001/api/v1';

// 获取标签树
async function getTagTree(caseId, includeEmpty = false) {
  const response = await fetch(
    `${API_BASE_URL}/cases/${caseId}/tags/tree?include_empty=${includeEmpty}`
  );
  return await response.json();
}

// 搜索标签
async function searchTags(caseId, query, category = null) {
  const params = new URLSearchParams({ q: query });
  if (category) params.append('category', category);
  
  const response = await fetch(
    `${API_BASE_URL}/cases/${caseId}/tags/search?${params}`
  );
  return await response.json();
}

// 创建自定义标签
async function createCustomTag(caseId, name, color = '#3B82F6') {
  const response = await fetch(`${API_BASE_URL}/cases/${caseId}/tags/custom`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ name, color })
  });
  return await response.json();
}
```

### 数据结构说明

#### 标签树结构
```typescript
interface TagTreeResponse {
  properties: {
    [key: string]: {
      [value: string]: {
        count: number;
        file_ids: number[];
      }
    }
  };
  tags: {
    metadata: TagCategory;
    cv: TagCategory;
    user: TagCategory;
    ai: TagCategory;
  };
  custom: CustomTag[];
}

interface CustomTag {
  id: number;
  name: string;
  color: string;
  count: number;
  file_ids: number[];
}
```

#### 搜索结果结构
```typescript
interface TagSearchResponse {
  results: TagSearchResult[];
}

interface TagSearchResult {
  category: string;
  name: string;
  value: string;
  count: number;
  file_ids: number[];
  color?: string; // 仅自定义标签有此字段
}
```

### 推荐的前端实现步骤

1. **首先确保数据库迁移**
   ```javascript
   // 在应用启动时检查并执行迁移
   await fetch(`${API_BASE_URL}/cases/${caseId}/tags/migrate`, {
     method: 'POST'
   });
   ```

2. **获取标签树数据**
   ```javascript
   const tagTree = await getTagTree(caseId);
   // 渲染标签管理面板
   ```

3. **实现搜索功能**
   ```javascript
   const searchResults = await searchTags(caseId, searchQuery);
   // 更新搜索结果显示
   ```

4. **实现自定义标签管理**
   ```javascript
   // 创建标签
   const newTag = await createCustomTag(caseId, tagName, tagColor);
   
   // 为文件添加标签
   await fetch(`${API_BASE_URL}/cases/${caseId}/tags/files/${fileId}/custom`, {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify({ tag_ids: [newTag.id] })
   });
   ```

## 🚀 部署说明

### 数据库迁移
在使用标签管理功能前，需要为现有案例执行数据库迁移：

```python
# 使用Python脚本执行迁移
python test_migration.py

# 或通过API执行迁移
POST /api/v1/cases/{case_id}/tags/migrate
```

### 性能优化建议
1. **缓存策略** - 标签树数据可以在前端缓存，定期刷新
2. **分页加载** - 大量标签时使用虚拟滚动
3. **防抖搜索** - 搜索输入使用防抖机制
4. **批量操作** - 多文件操作使用批量API

## 📋 API 端点完整列表

| 方法 | 端点 | 功能 | 状态 |
|------|------|------|------|
| GET | `/cases/{case_id}/tags/tree` | 获取标签树结构 | ✅ |
| GET | `/cases/{case_id}/tags/search` | 搜索标签 | ✅ |
| POST | `/cases/{case_id}/tags/custom` | 创建自定义标签 | ✅ |
| PUT | `/cases/{case_id}/tags/custom/{tag_id}` | 更新自定义标签 | ⚠️ 待实现 |
| DELETE | `/cases/{case_id}/tags/custom/{tag_id}` | 删除自定义标签 | ⚠️ 待实现 |
| POST | `/cases/{case_id}/tags/files/{file_id}/custom` | 为文件添加标签 | ⚠️ 待实现 |
| DELETE | `/cases/{case_id}/tags/files/{file_id}/custom/{tag_id}` | 移除文件标签 | ⚠️ 待实现 |
| POST | `/cases/{case_id}/tags/batch` | 批量标签操作 | ⚠️ 待实现 |
| GET | `/cases/{case_id}/tags/stats` | 获取标签统计 | ✅ |
| POST | `/cases/{case_id}/tags/cache/refresh` | 刷新标签缓存 | ⚠️ 待实现 |
| POST | `/cases/{case_id}/tags/migrate` | 数据库迁移 | ✅ |
| GET | `/cases/{case_id}/tags/schema/check` | 检查数据库结构 | ⚠️ 待实现 |

## 🔄 下一步工作

### 后端待完成功能
1. 完善所有API端点的实现
2. 添加错误处理和验证
3. 优化查询性能
4. 添加API文档和测试

### 前端开发建议
1. 基于提供的API规范开始前端开发
2. 实现标签管理面板组件
3. 实现标签画廊组件
4. 添加拖拽交互功能
5. 实现响应式布局

## 📞 技术支持

如果在前端开发过程中遇到API相关问题，可以：
1. 参考本文档的API规范
2. 使用提供的测试脚本验证功能
3. 查看后端日志获取详细错误信息

---

**标签管理系统后端API开发完成** ✅  
**准备开始前端开发** 🚀