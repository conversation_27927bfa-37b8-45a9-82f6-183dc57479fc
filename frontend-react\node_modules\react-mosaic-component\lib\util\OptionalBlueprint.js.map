{"version": 3, "file": "OptionalBlueprint.js", "sourceRoot": "", "sources": ["../../src/util/OptionalBlueprint.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,0DAAoC;AACpC,+DAAyC;AACzC,2CAA+B;AAC/B,gDAAgD;AAEhD,IAAiB,iBAAiB,CA6BjC;AA7BD,WAAiB,iBAAiB;IACnB,sBAAI,GAAG,UAAC,EAQpB;YAPC,IAAI,UAAA,EACJ,SAAS,eAAA,EACT,YAAiB,EAAjB,IAAI,mBAAG,UAAU,KAAA;QAMT,IAAA,kBAAkB,GAAK,KAAK,CAAC,UAAU,CAAC,4BAAa,CAAC,mBAApC,CAAqC;QAC/D,OAAO,CACL,8BACE,SAAS,EAAE,IAAA,oBAAU,EAAC,SAAS,EAAE,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,EAAE,UAAG,kBAAkB,mBAAS,IAAI,CAAE,CAAC,GAC9G,CACH,CAAC;IACJ,CAAC,CAAC;IAMF,SAAgB,UAAU,CAAC,kBAA0B;QAAE,eAA0B;aAA1B,UAA0B,EAA1B,qBAA0B,EAA1B,IAA0B;YAA1B,8BAA0B;;QAC/E,OAAO,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,UAAG,kBAAkB,cAAI,IAAA,mBAAS,EAAC,IAAI,CAAC,CAAE,EAA1C,CAA0C,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACnF,CAAC;IAFe,4BAAU,aAEzB,CAAA;IAED,SAAgB,YAAY,CAAC,kBAA0B,EAAE,QAAgC;QACvF,OAAO,UAAG,kBAAkB,mBAAS,IAAA,mBAAS,EAAC,QAAQ,CAAC,CAAE,CAAC;IAC7D,CAAC;IAFe,8BAAY,eAE3B,CAAA;AACH,CAAC,EA7BgB,iBAAiB,GAAjB,yBAAiB,KAAjB,yBAAiB,QA6BjC"}