# src/analysis/image_quality.py
"""
图像质量评估与相似性聚类模块
从原始脚本重构为可集成的API
"""

import os
import cv2
import re
import numpy as np
import pandas as pd
from PIL import Image
import imagehash
from tqdm import tqdm
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

# 默认配置
DEFAULT_WEIGHTS = {
    'sharpness': 0.5,      # 清晰度，高权重
    'num_faces': 0.2,      # 人脸数量，高权重
    'face_sharpness': 0.2, # 人脸清晰度，高权重
    'dynamic_range': 0.1,  # 动态范围，中等权重
    'brightness': 0.0      # 亮度（可选，未指定权重）
}
DEFAULT_PHASH_THRESHOLD = 18
DEFAULT_PHASH_PRESCALE_SIZE = (512, 512)


def parse_filename(filename: str) -> Tuple[Optional[str], Optional[int]]:
    """
    解析文件名，提取卷宗ID和帧号
    
    Args:
        filename: 文件名，如 '于德水_1994_135_1_12.jpg'
        
    Returns:
        tuple: (group_id, frame_number) 或 (None, None)
    """
    match = re.search(r'_(\d+)_(\d+)_(\d+)_(\d+)\.', filename)  # 匹配四个数字段，如 _1994_135_1_12.
    if match:
        group_id = f"{match.group(1)}_{match.group(2)}_{match.group(3)}"  # 年份_胶片尺寸_子组
        frame_number = int(match.group(4))
        return group_id, frame_number
    return None, None


def calculate_phash(image_pil: Image.Image, prescale_size: Tuple[int, int] = DEFAULT_PHASH_PRESCALE_SIZE) -> imagehash.ImageHash:
    """
    计算图像的感知哈希值
    
    Args:
        image_pil: PIL图像对象
        prescale_size: 预缩放尺寸
        
    Returns:
        imagehash.ImageHash: 感知哈希值
    """
    image_for_hash = image_pil.copy()
    image_for_hash.thumbnail(prescale_size, Image.Resampling.LANCZOS)
    return imagehash.phash(image_for_hash)


def analyze_faces(image_cv: np.ndarray) -> Tuple[int, float]:
    """
    分析图像中的人脸
    
    Args:
        image_cv: OpenCV图像数组
        
    Returns:
        tuple: (人脸数量, 最大人脸清晰度)
    """
    # 扩展可能的Haar级联文件路径
    possible_paths = [
        cv2.data.haarcascades + 'haarcascade_frontalface_default.xml',
        r"C:\Users\<USER>\anaconda3\envs\mizzy_star_v0.3\Library\etc\haarcascades\haarcascade_frontalface_default.xml",
        r"C:\Users\<USER>\anaconda3\envs\Yudeshui_AI_Research_tf\Library\etc\haarcascades\haarcascade_frontalface_default.xml",
        "/usr/share/opencv4/haarcascades/haarcascade_frontalface_default.xml",
        "/opt/conda/share/opencv4/haarcascades/haarcascade_frontalface_default.xml"
    ]
    
    face_cascade = None
    for path in possible_paths:
        if os.path.exists(path):
            face_cascade = cv2.CascadeClassifier(path)
            logger.info(f"找到Haar级联文件: {path}")
            break
    
    if face_cascade is None:
        logger.warning("未找到Haar级联文件，跳过人脸检测")
        return 0, 0.0
    
    gray = cv2.cvtColor(image_cv, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(
        gray, 
        scaleFactor=1.1, 
        minNeighbors=3, 
        minSize=(30, 30)
    )
    
    if len(faces) == 0:
        return 0, 0.0
    
    max_face_sharpness = 0.0
    for (x, y, w, h) in faces:
        face_roi = gray[y:y+h, x:x+w]
        if face_roi.size > 0:
            sharpness = cv2.Laplacian(face_roi, cv2.CV_64F).var()
            if sharpness > max_face_sharpness:
                max_face_sharpness = sharpness
    
    return len(faces), max_face_sharpness


def calculate_sharpness(image_cv: np.ndarray) -> float:
    """
    计算图像清晰度（拉普拉斯算子方差）
    
    Args:
        image_cv: OpenCV图像数组
        
    Returns:
        float: 清晰度分数
    """
    gray = cv2.cvtColor(image_cv, cv2.COLOR_BGR2GRAY)
    return cv2.Laplacian(gray, cv2.CV_64F).var()


def calculate_brightness(image_cv: np.ndarray) -> float:
    """
    计算图像亮度
    
    Args:
        image_cv: OpenCV图像数组
        
    Returns:
        float: 平均亮度值
    """
    gray = cv2.cvtColor(image_cv, cv2.COLOR_BGR2GRAY)
    return np.mean(gray)


def calculate_dynamic_range(image_cv: np.ndarray) -> float:
    """
    计算图像动态范围
    
    Args:
        image_cv: OpenCV图像数组
        
    Returns:
        float: 动态范围 (0-1)
    """
    gray = cv2.cvtColor(image_cv, cv2.COLOR_BGR2GRAY)
    hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
    non_zero_indices = np.where(hist > 0)[0]
    
    if len(non_zero_indices) > 1:
        return (non_zero_indices[-1] - non_zero_indices[0]) / 255.0
    return 0.0


def calculate_color_metrics(image_cv: np.ndarray) -> Dict[str, float]:
    """
    计算色彩相关指标
    
    Args:
        image_cv: OpenCV图像数组
        
    Returns:
        dict: 包含色彩范围和倾向的字典
    """
    hsv = cv2.cvtColor(image_cv, cv2.COLOR_BGR2HSV)
    h, s, v = cv2.split(hsv)
    
    # 色彩范围 (hue range, saturation range, value range)
    hue_range = np.ptp(h)
    sat_range = np.ptp(s)
    val_range = np.ptp(v)
    
    # 色彩倾向 (平均HSV)
    dominant_hue = np.mean(h)
    dominant_sat = np.mean(s)
    dominant_val = np.mean(v)
    
    return {
        'hue_range': hue_range / 180.0,  # 归一化 0-1
        'sat_range': sat_range / 255.0,
        'val_range': val_range / 255.0,
        'dominant_hue': dominant_hue,
        'dominant_sat': dominant_sat,
        'dominant_val': dominant_val
    }


def calculate_metrics(image_path: str) -> Dict:
    """
    计算单张图像的所有质量指标
    
    Args:
        image_path: 图像文件路径
        
    Returns:
        dict: 包含所有质量指标的字典
    """
    try:
        # 加载图像
        image_pil = Image.open(image_path).convert('RGB')
        image_cv = cv2.cvtColor(np.array(image_pil), cv2.COLOR_RGB2BGR)
        filename = os.path.basename(image_path)
        
        # 计算各种指标
        phash = calculate_phash(image_pil)
        sharpness = calculate_sharpness(image_cv)
        brightness = calculate_brightness(image_cv)
        dynamic_range = calculate_dynamic_range(image_cv)
        num_faces, face_sharpness = analyze_faces(image_cv)
        color_metrics = calculate_color_metrics(image_cv)
        group_id, frame_number = parse_filename(filename)
        
        # 图像大小范围
        width, height = image_pil.size
        size_range = width * height  # 像素总数作为大小范围
        
        metrics = {
            'filename': filename,
            'phash': phash,
            'sharpness': sharpness,
            'brightness': brightness,
            'dynamic_range': dynamic_range,
            'num_faces': num_faces,
            'face_sharpness': face_sharpness,
            'size_range': size_range,
            'group_id': group_id,
            'frame_number': frame_number,
            'image_pil': image_pil
        }
        metrics.update(color_metrics)  # 添加色彩指标
        
        return metrics
        
    except Exception as e:
        logger.error(f"处理文件 {image_path} 失败: {e}")
        return {}


def run_quality_clustering(
    img_dir: str, 
    weights: Dict[str, float] = None,
    phash_threshold: int = DEFAULT_PHASH_THRESHOLD,
    supported_formats: List[str] = None
) -> pd.DataFrame:
    """
    对目录中的图片进行质量评估和相似性聚类
    
    Args:
        img_dir: 图片目录路径
        weights: 各指标权重字典
        phash_threshold: pHash相似度阈值
        supported_formats: 支持的图片格式列表
        
    Returns:
        pd.DataFrame: 包含质量分数和聚类ID的数据框
    """
    if weights is None:
        weights = DEFAULT_WEIGHTS.copy()
    
    if supported_formats is None:
        supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    
    img_dir_path = Path(img_dir)
    if not img_dir_path.exists():
        raise ValueError(f"目录不存在: {img_dir}")
    
    # 获取所有图片文件
    image_files = []
    for ext in supported_formats:
        image_files.extend(img_dir_path.glob(f"*{ext}"))
        image_files.extend(img_dir_path.glob(f"*{ext.upper()}"))
    
    if not image_files:
        logger.warning(f"在目录 {img_dir} 中未找到任何支持的图片文件")
        return pd.DataFrame()
    
    # 提取所有图片的指标
    all_data = []
    logger.info(f"开始处理 {len(image_files)} 张图片...")
    
    for image_file in tqdm(image_files, desc="提取图像指标"):
        metrics = calculate_metrics(str(image_file))
        if metrics:
            all_data.append(metrics)
    
    if not all_data:
        logger.warning("没有成功处理任何图片")
        return pd.DataFrame()
    
    # 创建DataFrame并进行归一化
    df = pd.DataFrame(all_data)
    
    # 归一化各项指标
    for col in weights.keys():
        if col in df.columns:
            min_val = df[col].min()
            max_val = df[col].max()
            if max_val > min_val:
                df[f'{col}_norm'] = (df[col] - min_val) / (max_val - min_val)
            else:
                df[f'{col}_norm'] = 0.0
    
    df.fillna(0, inplace=True)
    
    # 计算综合质量分数（百分制，保留一位小数）
    df['quality_score'] = sum(
        df[f'{key}_norm'] * weight 
        for key, weight in weights.items() 
        if f'{key}_norm' in df.columns
    ) * 100  # 转换为百分制
    df['quality_score'] = df['quality_score'].round(1)
    
    # 执行相似性聚类
    logger.info("开始执行相似性聚类...")
    df['cluster_id'] = -1
    cluster_counter = 0
    
    # 按质量分数降序排列
    df_sorted = df.sort_values(by='quality_score', ascending=False).reset_index(drop=True)
    
    for i in tqdm(range(len(df_sorted)), desc="聚类分析"):
        if df_sorted.at[i, 'cluster_id'] != -1:
            continue
        
        current_hash = df_sorted.at[i, 'phash']
        current_group = df_sorted.at[i, 'group_id']
        
        # 为当前图片分配聚类ID
        df_sorted.at[i, 'cluster_id'] = cluster_counter
        
        # 如果没有组ID，跳过相似性检查
        if current_group is None:
            cluster_counter += 1
            continue
        
        # 寻找相似图片
        for j in range(i + 1, len(df_sorted)):
            if df_sorted.at[j, 'cluster_id'] != -1:
                continue
            
            other_hash = df_sorted.at[j, 'phash']
            other_group = df_sorted.at[j, 'group_id']
            
            # 同一卷宗且pHash相似
            if (other_group == current_group and 
                current_hash - other_hash <= phash_threshold):
                df_sorted.at[j, 'cluster_id'] = cluster_counter
        
        cluster_counter += 1
    
    # 按聚类ID和质量分数排序
    result_df = df_sorted.sort_values(
        by=['cluster_id', 'quality_score'], 
        ascending=[True, False]
    ).reset_index(drop=True)
    
    logger.info(f"聚类完成！共处理 {len(result_df)} 张图片，分成 {cluster_counter} 个聚类")
    
    return result_df


def generate_cluster_report(df: pd.DataFrame, output_path: str) -> str:
    """
    生成聚类分析报告
    
    Args:
        df: 聚类结果DataFrame
        output_path: 输出文件路径
        
    Returns:
        str: 报告文件路径
    """
    from openpyxl import Workbook
    from openpyxl.drawing.image import Image as OpenpyxlImage
    from io import BytesIO
    
    THUMBNAIL_SIZE = (200, 200)
    
    wb = Workbook()
    ws = wb.active
    ws.title = "Quality Clustering Report"
    
    # 设置表头
    headers = [
        'thumbnail', 'filename', 'cluster_id', 'manual_cluster_override',
        'sharpness', 'num_faces', 'face_sharpness', 'dynamic_range',
        'brightness', 'quality_score', 'phash', 'is_representative'
    ]
    ws.append(headers)
    
    # 设置列宽
    ws.column_dimensions['A'].width = 30
    ws.column_dimensions['B'].width = 40
    ws.column_dimensions['D'].width = 25
    
    # 填充数据
    for _, row in tqdm(df.iterrows(), total=df.shape[0], desc="生成Excel报告"):
        if 'image_pil' in row and row['image_pil'] is not None:
            # 创建缩略图
            img_pil = row['image_pil'].copy()
            img_pil.thumbnail(THUMBNAIL_SIZE)
            img_io = BytesIO()
            img_pil.save(img_io, format='PNG')
            img_obj = OpenpyxlImage(img_io)
            
            # 添加数据行
            ws.append([
                None,  # 缩略图占位
                row['filename'],
                row.get('cluster_id', -1),
                '',  # 手动聚类覆盖
                f"{row.get('sharpness', 0):.2f}",
                row.get('num_faces', 0),
                f"{row.get('face_sharpness', 0):.2f}",
                f"{row.get('dynamic_range', 0):.4f}",
                f"{row.get('brightness', 0):.2f}",
                f"{row.get('quality_score', 0):.1f}",  # 保留一位小数
                str(row.get('phash', '')),
                ''  # 是否代表性图片
            ])
            
            # 添加缩略图
            ws.add_image(img_obj, f'A{ws.max_row}')
            ws.row_dimensions[ws.max_row].height = THUMBNAIL_SIZE[1] * 0.8
    
    # 保存文件
    wb.save(output_path)
    logger.info(f"Excel报告已保存至: {output_path}")
    
    return output_path