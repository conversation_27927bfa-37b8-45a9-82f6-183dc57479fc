# tests/unit/test_crud_cases.py
"""
案例CRUD操作的单元测试
"""
import os
from sqlalchemy.orm import Session

from src.crud.case_crud import create_case, get_cases, get_case, delete_case
from src import schemas, models


class TestCaseCRUD:
    """案例CRUD操作测试类"""
    
    def test_create_case_success(self, test_db: Session, sample_case_data: dict, test_data_dir: str):
        """测试成功创建案例"""
        # 临时修改DATA_DIR为测试目录
        import src.database
        original_data_dir = src.database.DATA_DIR
        src.database.DATA_DIR = test_data_dir
        
        try:
            case_create = schemas.CaseCreate(**sample_case_data)
            created_case = create_case(test_db, case_create)
            
            # 验证返回的案例对象
            assert created_case.id is not None
            assert created_case.case_name == sample_case_data["case_name"]
            assert created_case.description == sample_case_data["description"]
            assert created_case.status == models.CaseStatus.ACTIVE
            # PostgreSQL模式下不使用db_path
            # assert created_case.db_path is not None
            assert created_case.created_at is not None
            
            # 验证案例数据库文件是否创建
            # PostgreSQL模式下不使用db_path
            # db_path = str(created_case.db_path)
            assert os.path.exists(db_path)
            
        finally:
            # 恢复原始DATA_DIR
            src.database.DATA_DIR = original_data_dir
    
    def test_create_case_with_empty_name(self, test_db: Session, test_data_dir: str):
        """测试创建案例时案例名为空的情况"""
        import src.database
        original_data_dir = src.database.DATA_DIR
        src.database.DATA_DIR = test_data_dir
        
        try:
            case_data = {"case_name": "", "description": "测试描述"}
            case_create = schemas.CaseCreate(**case_data)
            
            # 这里应该成功创建，因为目前没有空名称验证
            created_case = create_case(test_db, case_create)
            assert created_case.case_name == ""
            
        finally:
            src.database.DATA_DIR = original_data_dir
    
    def test_get_cases_empty(self, test_db: Session):
        """测试获取空案例列表"""
        cases = get_cases(test_db)
        assert cases == []
    
    def test_get_cases_with_data(self, test_db: Session, sample_case_data: dict, test_data_dir: str):
        """测试获取有数据的案例列表"""
        import src.database
        original_data_dir = src.database.DATA_DIR
        src.database.DATA_DIR = test_data_dir
        
        try:
            # 创建几个测试案例
            case_create1 = schemas.CaseCreate(**sample_case_data)
            case_create2 = schemas.CaseCreate(case_name="测试案例2", description="第二个测试案例")
            
            created_case1 = create_case(test_db, case_create1)
            created_case2 = create_case(test_db, case_create2)
            
            # 获取案例列表
            cases = get_cases(test_db)
            
            # 验证返回的案例数量和顺序（按ID倒序）
            assert len(cases) == 2
            assert cases[0].id == created_case2.id  # 最新的在前面
            assert cases[1].id == created_case1.id
            
        finally:
            src.database.DATA_DIR = original_data_dir
    
    def test_get_cases_pagination(self, test_db: Session, test_data_dir: str):
        """测试案例列表分页功能"""
        import src.database
        original_data_dir = src.database.DATA_DIR
        src.database.DATA_DIR = test_data_dir
        
        try:
            # 创建3个测试案例
            for i in range(3):
                case_data = {"case_name": f"测试案例{i+1}", "description": f"第{i+1}个测试案例"}
                case_create = schemas.CaseCreate(**case_data)
                create_case(test_db, case_create)
            
            # 测试分页
            cases_page1 = get_cases(test_db, skip=0, limit=2)
            cases_page2 = get_cases(test_db, skip=2, limit=2)
            
            assert len(cases_page1) == 2
            assert len(cases_page2) == 1
            
        finally:
            src.database.DATA_DIR = original_data_dir
    
    def test_get_case_by_id_success(self, test_db: Session, sample_case_data: dict, test_data_dir: str):
        """测试通过ID成功获取案例"""
        import src.database
        original_data_dir = src.database.DATA_DIR
        src.database.DATA_DIR = test_data_dir
        
        try:
            # 创建测试案例
            case_create = schemas.CaseCreate(**sample_case_data)
            created_case = create_case(test_db, case_create)
            
            # 通过ID获取案例
            case_id = int(created_case.id)
            retrieved_case = get_case(test_db, case_id)
            
            assert retrieved_case is not None
            assert retrieved_case.id == created_case.id
            assert retrieved_case.case_name == created_case.case_name
            
        finally:
            src.database.DATA_DIR = original_data_dir
    
    def test_get_case_by_id_not_found(self, test_db: Session):
        """测试获取不存在的案例"""
        non_existent_id = 999
        retrieved_case = get_case(test_db, non_existent_id)
        assert retrieved_case is None
    
    def test_delete_case_success(self, test_db: Session, sample_case_data: dict, test_data_dir: str):
        """测试成功删除案例（软删除）"""
        import src.database
        original_data_dir = src.database.DATA_DIR
        src.database.DATA_DIR = test_data_dir
        
        try:
            # 创建测试案例
            case_create = schemas.CaseCreate(**sample_case_data)
            created_case = create_case(test_db, case_create)
            
            # 删除案例
            deleted_case = delete_case(test_db, created_case.id)
            
            assert deleted_case is not None
            assert deleted_case.status == models.CaseStatus.DELETED
            assert deleted_case.deleted_at is not None
            
            # 验证案例不再出现在正常列表中
            active_cases = get_cases(test_db)
            assert len(active_cases) == 0
            
        finally:
            src.database.DATA_DIR = original_data_dir
    
    def test_delete_case_not_found(self, test_db: Session):
        """测试删除不存在的案例"""
        non_existent_id = 999
        deleted_case = delete_case(test_db, non_existent_id)
        assert deleted_case is None
    
    def test_delete_already_deleted_case(self, test_db: Session, sample_case_data: dict, test_data_dir: str):
        """测试删除已删除的案例"""
        import src.database
        original_data_dir = src.database.DATA_DIR
        src.database.DATA_DIR = test_data_dir
        
        try:
            # 创建并删除案例
            case_create = schemas.CaseCreate(**sample_case_data)
            created_case = create_case(test_db, case_create)
            delete_case(test_db, created_case.id)
            
            # 再次尝试删除
            second_delete = delete_case(test_db, created_case.id)  # type: ignore
            assert second_delete is None
            
        finally:
            src.database.DATA_DIR = original_data_dir 