export*from"dnd-multi-backend";import{MultiBackend as l}from"dnd-multi-backend";import{createContext as D,useState as P}from"react";import{DndProvider as m}from"react-dnd";import{jsx as c,jsxs as v}from"react/jsx-runtime";var i=D(null),g=({portal:e,...t})=>{let[r,o]=P(null);return v(i.Provider,{value:e??r,children:[c(m,{backend:l,...t}),e?null:c("div",{ref:o})]})};import{useContext as k}from"react";import{Preview as M,Context as y}from"react-dnd-preview";import{createPortal as C}from"react-dom";import{useContext as S,useEffect as w,useState as x}from"react";import{DndContext as f}from"react-dnd";var s=()=>{let[e,t]=x(!1),r=S(f);return w(()=>{let o=r?.dragDropManager?.getBackend(),n={backendChanged:p=>{t(p.previewEnabled())}};return t(o.previewEnabled()),o.previewsList().register(n),()=>{o.previewsList().unregister(n)}},[r,r.dragDropManager]),e};import{jsx as E}from"react/jsx-runtime";var b=e=>{let t=s(),r=k(i);if(!t)return null;let o=E(M,{...e});return r!==null?C(o,r):o};b.Context=y;import{useDrag as T}from"react-dnd";import{useContext as B}from"react";import{DndContext as R}from"react-dnd";var O=(e,t,r,o)=>{let n=r.getBackend();r.receiveBackend(o);let p=t(e);return r.receiveBackend(n),p},a=(e,t)=>{let r=B(R),o=r?.dragDropManager?.getBackend();if(o===void 0)throw new Error("could not find backend, make sure you are using a <DndProvider />");let n=t(e),p={},d=o.backendsList();for(let u of d)p[u.id]=O(e,t,r.dragDropManager,u.instance);return[n,p]};var ee=e=>a(e,T);import{useDrop as h}from"react-dnd";var ne=e=>a(e,h);import{usePreview as H}from"react-dnd-preview";var ie=e=>{let t=s(),r=H(e);return t?r:{display:!1}};export{g as DndProvider,b as Preview,y as PreviewContext,ee as useMultiDrag,ne as useMultiDrop,ie as usePreview};
