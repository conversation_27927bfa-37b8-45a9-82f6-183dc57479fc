{"version": 3, "file": "pkgOptions.js", "sourceRoot": "", "sources": ["../../src/options/pkgOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["import { TargetSpecificOptions } from \"../core\"\n\n// noinspection SpellCheckingInspection\nexport type BackgroundAlignment = \"center\" | \"left\" | \"right\" | \"top\" | \"bottom\" | \"topleft\" | \"topright\" | \"bottomleft\" | \"bottomright\"\n// noinspection SpellCheckingInspection\nexport type BackgroundScaling = \"tofit\" | \"none\" | \"proportional\"\n\n/**\n * macOS product archive options.\n */\nexport interface PkgOptions extends TargetSpecificOptions {\n  /**\n   * The scripts directory, relative to `build` (build resources directory).\n   * The scripts can be in any language so long as the files are marked executable and have the appropriate shebang indicating the path to the interpreter.\n   * Scripts are required to be executable (`chmod +x file`).\n   * @default build/pkg-scripts\n   * @see [Scripting in installer packages](http://macinstallers.blogspot.de/2012/07/scripting-in-installer-packages.html).\n   */\n  readonly scripts?: string | null\n\n  /**\n   * should be not documented, only to experiment\n   * @private\n   */\n  readonly productbuild?: Array<string> | null\n\n  /**\n   * The install location. [Do not use it](https://stackoverflow.com/questions/12863944/how-do-you-specify-a-default-install-location-to-home-with-pkgbuild) to create per-user package.\n   * Mostly never you will need to change this option. `/Applications` would install it as expected into `/Applications` if the local system domain is chosen, or into `$HOME/Applications` if the home installation is chosen.\n   * @default /Applications\n   */\n  readonly installLocation?: string | null\n\n  /**\n   * Whether can be installed at the root of any volume, including non-system volumes. Otherwise, it cannot be installed at the root of a volume.\n   *\n   * Corresponds to [enable_anywhere](https://developer.apple.com/library/content/documentation/DeveloperTools/Reference/DistributionDefinitionRef/Chapters/Distribution_XML_Ref.html#//apple_ref/doc/uid/**********-CH100-SW70).\n   * @default true\n   */\n  readonly allowAnywhere?: boolean | null\n\n  /**\n   * Whether can be installed into the current user’s home directory.\n   * A home directory installation is done as the current user (not as root), and it cannot write outside of the home directory.\n   * If the product cannot be installed in the user’s home directory and be not completely functional from user’s home directory.\n   *\n   * Corresponds to [enable_currentUserHome](https://developer.apple.com/library/content/documentation/DeveloperTools/Reference/DistributionDefinitionRef/Chapters/Distribution_XML_Ref.html#//apple_ref/doc/uid/**********-CH100-SW70).\n   * @default true\n   */\n  readonly allowCurrentUserHome?: boolean | null\n\n  /**\n   * Whether can be installed into the root directory. Should usually be `true` unless the product can be installed only to the user’s home directory.\n   *\n   * Corresponds to [enable_localSystem](https://developer.apple.com/library/content/documentation/DeveloperTools/Reference/DistributionDefinitionRef/Chapters/Distribution_XML_Ref.html#//apple_ref/doc/uid/**********-CH100-SW70).\n   * @default true\n   */\n  readonly allowRootDirectory?: boolean | null\n\n  /**\n   * The name of certificate to use when signing. Consider using environment variables [CSC_LINK or CSC_NAME](/code-signing) instead of specifying this option.\n   */\n  readonly identity?: string | null\n\n  /**\n   * The path to EULA license file. Defaults to `license.txt` or `eula.txt` (or uppercase variants). In addition to `txt`, `rtf` and `html` supported (don't forget to use `target=\"_blank\"` for links).\n   */\n  readonly license?: string | null\n\n  /**\n   * Options for the background image for the installer.\n   */\n  readonly background?: PkgBackgroundOptions | null\n\n  /**\n   * The path to the welcome file. This may be used to customize the text on the Introduction page of the installer.\n   */\n  readonly welcome?: string | null\n\n  /**\n   * Identifies applications that must be closed before the package is installed.\n   *\n   * Corresponds to [must-close](https://developer.apple.com/library/archive/documentation/DeveloperTools/Reference/DistributionDefinitionRef/Chapters/Distribution_XML_Ref.html#//apple_ref/doc/uid/**********-CH100-SW77).\n   */\n  readonly mustClose?: Array<string> | null\n\n  /**\n   * The path to the conclusion file. This may be used to customize the text on the final \"Summary\" page of the installer.\n   */\n  readonly conclusion?: string | null\n\n  /**\n   * Install bundle over previous version if moved by user?\n   * @default true\n   */\n  readonly isRelocatable?: boolean | null\n\n  /**\n   * Don't install bundle if newer version on disk?\n   * @default true\n   */\n  readonly isVersionChecked?: boolean | null\n\n  /**\n   * Require identical bundle identifiers at install path?\n   * @default true\n   */\n  readonly hasStrictIdentifier?: boolean | null\n\n  /**\n   * Specifies how an existing version of the bundle on disk should be handled when the version in\n   * the package is installed.\n   *\n   * If you specify upgrade, the bundle in the package atomi-cally replaces any version on disk;\n   * this has the effect of deleting old paths that no longer exist in the new version of\n   * the bundle.\n   *\n   * If you specify update, the bundle in the package overwrites the version on disk, and any files\n   * not contained in the package will be left intact; this is appropriate when you are delivering\n   * an update-only package.\n   *\n   * Another effect of update is that the package bundle will not be installed at all if there is\n   * not already a version on disk; this allows a package to deliver an update for an app that\n   * the user might have deleted.\n   *\n   * @default upgrade\n   */\n  readonly overwriteAction?: \"upgrade\" | \"update\" | null\n}\n\n/**\n * Options for the background image in a PKG installer\n */\nexport interface PkgBackgroundOptions {\n  /**\n   * Path to the image to use as an installer background.\n   */\n  file?: string\n\n  /**\n   * Alignment of the background image.\n   * Options are: center, left, right, top, bottom, topleft, topright, bottomleft, bottomright\n   * @default center\n   */\n  alignment?: BackgroundAlignment | null\n\n  /**\n   * Scaling of the background image.\n   * Options are: tofit, none, proportional\n   * @default tofit\n   */\n  scaling?: BackgroundScaling | null\n}\n"]}